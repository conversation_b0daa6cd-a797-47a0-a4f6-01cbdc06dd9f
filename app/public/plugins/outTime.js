// 超时没有操作自动退出登录 xxn
(function(){
  let oldX = 0, oldY = 0, posX = 0, posY = 0;
  let auth_cookie_name = '';
  getCookieName();

  // 动态获取当前分支auth_cookie_name
  function getCookieName(){
    const xhr = new XMLHttpRequest();
    xhr.open("get","/api/getAuthCookieName");
    xhr.send();
    xhr.onreadystatechange = function(){
      if(xhr.readyState===4 && xhr.status===200){
        const res = JSON.parse(xhr.responseText);
        if(res && res.data) {
          auth_cookie_name = res.data;
          window.setInterval(checkCookie, 10000); // 开启定时器
        }
      }
    }
  }

  function checkCookie () {
    const token = getCookie();
    if(token){
      const moved = posX !== oldX || posY !== oldY;
      if(moved){
        // console.log('cookie: ' + token);
        oldX = posX;
        oldY = posY;
        // 延长token有效期
        const xhr=new XMLHttpRequest();
        xhr.open("get","/manage/emptyRequest");
        xhr.send();
      }
    }else{
      logout(); // 退出登录
    }
  }
  /* 鼠标移动事件 */
  document.addEventListener("mouseover", function() {
    const event = window.event;
    if (event.pageX || event.pageY) {
      posX = event.pageX;
      posY = event.pageY;
    } else if (event.clientX || event.clientY) {
      posX = event.clientX + document.documentElement.scrollLeft + document.body.scrollLeft;
      posY = event.clientY + document.documentElement.scrollTop + document.body.scrollTop;
    }
  });

  // 获取cookie中cname的值
  function getCookie(){
    const name = auth_cookie_name + '=';
    const ca = document.cookie.split(';');
    for(let i=0; i<ca.length; i++) {
        var c = ca[i].trim();
        if (c.indexOf(name)==0) { return c.substring(name.length,c.length); }
    }
    return "";
  }

  // 退出登录
  function logout(){
    const xhr = new XMLHttpRequest();
    xhr.open("get","/manage/logout");
    xhr.send();
    xhr.onreadystatechange = function(){
      if(xhr.readyState===4 && xhr.status===200){
        location.href = '/admin/login';
      }
    }
  }

})();
