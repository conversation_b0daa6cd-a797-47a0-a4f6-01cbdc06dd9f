function encrypt(plaintext, therirPublicKey) {
  const { publicKey: publicKey, secretKey: secretKey } = nacl.box.keyPair();
  const myPublicKey = nacl.util.encodeBase64(publicKey);
  const mySecretKey = nacl.util.encodeBase64(secretKey);
  const nonce = nacl.randomBytes(nacl.box.nonceLength);
  const messageUint8 = nacl.util.decodeUTF8(plaintext);
  const theirPublicKey = nacl.util.decodeBase64(therirPublicKey);
  const mySecretKeyUint8 = nacl.util.decodeBase64(mySecretKey);
  const box = nacl.box(messageUint8, nonce, theirPublicKey, mySecretKeyUint8);
  const nonceBase64 = nacl.util.encodeBase64(nonce);
  const boxBase64 = nacl.util.encodeBase64(box);
  const ciphertext = nonceBase64 + ":" + boxBase64;
  return { ciphertext: ciphertext, publicKey: myPub<PERSON><PERSON><PERSON> };
}
