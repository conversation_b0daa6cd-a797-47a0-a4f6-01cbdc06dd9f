!function(e,n){"use strict";"undefined"!=typeof module&&module.exports?module.exports=n():e.nacl?e.nacl.util=n():(e.nacl={},e.nacl.util=n())}(this,function(){"use strict";var e={};return e.decodeUTF8=function(e){var n,t=unescape(encodeURIComponent(e)),r=new Uint8Array(t.length);for(n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r},e.encodeUTF8=function(e){var n,t=[];for(n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return decodeURIComponent(escape(t.join("")))},e.encodeBase64=function(e){if("undefined"==typeof btoa)return new Buffer(e).toString("base64");var n,t=[],r=e.length;for(n=0;r>n;n++)t.push(String.fromCharCode(e[n]));return btoa(t.join(""))},e.decodeBase64=function(e){if("undefined"==typeof atob)return new Uint8Array(Array.prototype.slice.call(new Buffer(e,"base64"),0));var n,t=atob(e),r=new Uint8Array(t.length);for(n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r},e});