<!DOCTYPE html>
<html lang="en">
<!-- 由于single-spa和阿里云SDK的问题，使用原始html上传 -->

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <script src="./plugins/aliyun-upload-sdk-1.5.0/aliyun-upload-sdk-1.5.0.min.js"></script>
  <script src="./plugins/aliyun-upload-sdk-1.5.0/lib/aliyun-oss-sdk-5.3.1.min.js"></script>
  <script src="./plugins/aliyun-upload-sdk-1.5.0/lib/es6-promise.min.js"></script>
  <script src="./plugins/aliyun-upload-sdk-1.5.0/jquery.min.js"></script>
</head>

<body>
  <div>
    <input style="display: none;"  accept="video/mp4" type="file" id="fileUpload">
  </div>
  <script>
    // //兼容IE11
    if (!FileReader.prototype.readAsBinaryString) {
      FileReader.prototype.readAsBinaryString = function (fileData) {
        let binary = "";
        let pt = this;
        let reader = new FileReader();
        reader.onload = function (e) {
          let bytes = new Uint8Array(reader.result);
          let length = bytes.byteLength;
          for (let i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }
          //pt.result  - readonly so assign binary
          pt.content = binary;
          pt.onload()
        }
        reader.readAsArrayBuffer(fileData);
      }
    }

    function test() {
      console.log('调用成功')
    }
    let aliyun = {} // 阿里云配置以及初始化函数
    function init(timeout, partSize, parallel, retryCount, region) {
      aliyun = {
        timeout,
        partSize,
        parallel,
        retryCount,
        region,
      }
      try {
        window.parent["test"]()
        console.log('iframe初始化完成，双向通信测试成功')
      } catch (error) {
        console.erro('iframe初始化完成，双向通信测试失败')
      }
    }


    /** 
     * 创建一个上传对象
     * 使用 UploadAuth 上传方式
     */
    function createUploader(filename, sendFormAgvs) {
      let uploader = new AliyunUpload.Vod({
        timeout: aliyun.timeout || 60000,
        partSize: aliyun.partSize || 1048576,
        parallel: aliyun.parallel || 5,
        retryCount: aliyun.retryCount || 3,
        retryDuration: aliyun.retryDuration || 2,
        region: aliyun.region,
        userId: sendFormAgvs.userId,
        // 添加文件成功
        addFileSuccess: function (uploadInfo) {
          console.log('addFileSuccess', uploadInfo)
          window.parent["buttonStatus"](false, true, false)
          window.parent["shouDialog"]()
          // console.log(filename, name, Tags)
          console.log("addFileSuccess: " + uploadInfo.file.name)
        },
        // 开始上传
        onUploadstarted: function (uploadInfo) {
          // 如果是 UploadAuth 上传方式, 需要调用 uploader.setUploadAuthAndAddress 方法
          // 如果是 UploadAuth 上传方式, 需要根据 uploadInfo.videoId是否有值，调用点播的不同接口获取uploadauth和uploadAddress
          // 如果 uploadInfo.videoId 有值，调用刷新视频上传凭证接口，否则调用创建视频上传凭证接口
          // 注意: 这里是测试 demo 所以直接调用了获取 UploadAuth 的测试接口, 用户在使用时需要判断 uploadInfo.videoId 存在与否从而调用 openApi
          // 如果 uploadInfo.videoId 存在, 调用 刷新视频上传凭证接口(https://help.aliyun.com/document_detail/55408.html)
          // 如果 uploadInfo.videoId 不存在,调用 获取视频上传地址和凭证接口(https://help.aliyun.com/document_detail/55407.html)
          console.log(uploadInfo.videoId, sendFormAgvs)
          if (!uploadInfo.videoId) {
            let createUrl = "/manage/courses/getUploadAuth/withOutID";
            const params = {
              Title: sendFormAgvs.name,
              FileName: filename,
              region: aliyun.region,
              CateId: sendFormAgvs.classification,
              Description: sendFormAgvs.Description,
            }
            if (sendFormAgvs.CoverURL && sendFormAgvs.CoverURL.length) params.CoverURL = sendFormAgvs.CoverURL
            console.log(params)
            if (sendFormAgvs.Tags.length) params.Tags = sendFormAgvs.Tags;
            $.post(createUrl, params, function (data) {
              let uploadAuth = data.UploadAuth
              let uploadAddress = data.UploadAddress
              let videoId = data.VideoId
              uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, videoId)
            }, 'json')
          } else {
            let refreshUrl = "/manage/courses/getUploadAuth/withID";
            $.post(refreshUrl, {
              videoId: uploadInfo.videoId,
            }, function (data) {
              let uploadAuth = data.UploadAuth
              let uploadAddress = data.UploadAddress
              let videoId = data.VideoId
              uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, videoId)
            }, 'json')
          }
        },
        // 文件上传成功
        onUploadSucceed: function (uploadInfo) {
          console.log(uploadInfo)
          console.log("onUploadSucceed: " + uploadInfo.file.name + ", endpoint:" + uploadInfo.endpoint + ", bucket:" + uploadInfo.bucket + ", object:" + uploadInfo.object)
          window.parent["uploadEnd"](uploadInfo);
        },
        // 文件上传失败
        onUploadFailed: function (uploadInfo, code, message) {
          console.log("onUploadFailed: file:" + uploadInfo.file.name + ",code:" + code + ", message:" + message)
        },
        // 取消文件上传
        onUploadCanceled: function (uploadInfo, code, message) {
          console.log("Canceled file: " + uploadInfo.file.name + ", code: " + code + ", message:" + message)
        },
        // 文件上传进度，单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
        onUploadProgress: function (uploadInfo, totalSize, progress) {
          let progressPercent = Math.ceil(progress * 100)
          window.parent["controlProgressBar"](progressPercent)
        },
        // 上传凭证超时
        onUploadTokenExpired: function (uploadInfo) {
          // 上传大文件超时, 如果是上传方式一即根据 UploadAuth 上传时
          // 需要根据 uploadInfo.videoId 调用刷新视频上传凭证接口(https://help.aliyun.com/document_detail/55408.html)重新获取 UploadAuth
          // 然后调用 resumeUploadWithAuth 方法
          let refreshUrl = "/manage/courses/getUploadAuth/withID";
          $.post(refreshUrl, {
            videoId: uploadInfo.videoId,
          }, function (data) {
            let uploadAuth = data.UploadAuth
            let uploadAddress = data.UploadAddress
            let videoId = data.VideoId
            uploader.resumeUploadWithAuth(uploadInfo, uploadAuth, uploadAddress, videoId)
          }, 'json')
        },
        // 全部文件上传结束
        onUploadEnd: function (uploadInfo) {
          console.log('uploadInfo', uploadInfo)
          console.log("onUploadEnd: uploaded all the files")
        }
      })
      return uploader
    }

    let uploader = null
    $('#fileUpload').on('change', function (e) {
      let file = e.target.files[0]
      if (!file) {
        alert("请先选择需要上传的文件!")
        return
      }
      // 获取表单参数
      const sendFormAgvs = window.parent["sendFormAgvs"]()
      let userData = '{"Vod":{}}'
      if (uploader) {
        uploader.stopUpload()
        window.parent["controlProgressBar"](0)
      }
      console.log(sendFormAgvs, file,'进来了啊啊啊啊')
      uploader = createUploader(file.name, sendFormAgvs)
      // 首先调用 uploader.addFile(event.target.files[i], null, null, null, userData)
      uploader.addFile(file, null, null, null, userData)
      window.parent["buttonStatus"](false, true, true)
    })

    // UploadAuth 上传 
    function UploadAuth() {
      if (uploader !== null) {
        uploader.startUpload()
      }
    }
    function resumeUpload() {
      if (uploader !== null) {
        uploader.startUpload()
      }
    }
    function pauseUpload() {
      if (uploader !== null) {
        uploader.stopUpload()
      }
    }

  </script>
</body>

</html>