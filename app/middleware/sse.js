/**
 * SSE middleware function that sets up the server-sent events (SSE) response.
 * @return {Function} The SSE middleware function.
 */
module.exports = () => {
  return async function sse(ctx, next) {
    ctx.req.socket.setTimeout(0);
    ctx.set({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      'Access-Control-Allow-Origin': '*', // 允许跨域
    });
    ctx.status = 200;
    // Generate a unique ID for this client
    const clientId = ctx.session.superUserInfo.clientId;
    console.log('🚀 ~ sse ~ clientId:', clientId);

    // Add this client to the clients list
    ctx.app.sseClients.set(clientId, ctx.res);

    // Remove this client from the clients list when the connection is closed
    ctx.req.on('close', () => {
      ctx.app.sseClients.delete(clientId);
    });
    // 错误监听
    ctx.req.on('error', error => {
      console.log('🚀 ~ sse ~ error', error);
    });

    await next();
  };
};
