const httpProxy = require('http-proxy');
// 定义代理规则
const proxyRules = [
  {
    path: '/_AMapService/',
    target: 'https://restapi.amap.com/',
    appendQueryString: true,
    queryString: 'jscode=aaaee764033afb4a143e4acd63389e30',
    pathRewrite: {
      '^/_AMapService/': '',
    },
  },
  {
    path: '/gdWebApi/',
    target: 'https://webapi.amap.com/',
    pathRewrite: {
      '^/gdWebApi/': '',
    },
  },
  {
    path: '/bdproxy/',
    target: 'https://api.map.baidu.com/',
    pathRewrite: {
      '^/bdproxy/': '',
    },
  },
  {
    path: '/bdWebApi/',
    target: 'https://api.map.baidu.com/',
    pathRewrite: {
      '^/bdWebApi/': '',
    },
  },
  {
    path: '/bdDlswb/',
    target: 'https://dlswbr.baidu.com/',
    pathRewrite: {
      '^/bdDlswb/': '',
    },
  },
  {
    path: '/bdm0proxy/',
    target: 'https://maponline0.bdimg.com/',
    pathRewrite: {
      '^/bdm0proxy/': '',
    },
  },
  {
    path: '/bdm1proxy/',
    target: 'https://maponline1.bdimg.com/',
    pathRewrite: {
      '^/bdm1proxy/': '',
    },
  },
  {
    path: '/bdm2proxy/',
    target: 'https://maponline2.bdimg.com/',
    pathRewrite: {
      '^/bdm2proxy/': '',
    },
  },
  {
    path: '/bdm3proxy/',
    target: 'https://maponline3.bdimg.com/',
    pathRewrite: {
      '^/bdm3proxy/': '',
    },
  },
];


module.exports = () => {
  return async function proxyMiddleware(ctx, next) {
    const oldPath = ctx.request.url.split('?')[0];
    // 匹配代理规则
    const rule = proxyRules.find(rule => oldPath.startsWith(rule.path));
    if (rule) {
      // 设置目标 URL
      const target = rule.target;
      const appendQueryString = rule.appendQueryString || false;
      const queryString = rule.queryString || '';

      let proxyTarget = target;
      let rewitePath = '';
      // 处理路径 按照rules中的pathRewrite规则重写路径
      if (rule.pathRewrite) {
        const pathRewrite = rule.pathRewrite;
        for (const key in pathRewrite) {
          const reg = new RegExp(key);
          rewitePath = ctx.request.url.replace(reg, pathRewrite[key]);
        }
      }
      proxyTarget = proxyTarget + rewitePath;
      // 拼接参数的
      if (appendQueryString) {
        const delimiter = target.includes('?') ? '&' : '?';
        proxyTarget = proxyTarget + delimiter + queryString;
      }
      // 转发请求
      try {
        // 处理body参数
        // 创建代理服务器
        const proxyServer = httpProxy.createProxyServer({
          changeOrigin: true,
          ignorePath: true,
          secure: false,
          logLevel: 'debug',
          headers: ctx.request.headers,
          target: proxyTarget,
        });
        proxyServer.on('proxyReq', function(proxyReq) {
          if (ctx.request.rawBody) {
          //   let bodyData = JSON.stringify(ctx.request.rawBody)
            const bodyData = ctx.request.rawBody;
            // incase if content-type is application/x-www-form-urlencoded -> we need to change to application/json
            //   proxyReq.setHeader('Content-Type', 'application/x-www-form-urlencoded')
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            // stream the content
            proxyReq.write(bodyData);
          }
        });
        await new Promise((resolve, reject) => {
          proxyServer.web(ctx.req, ctx.res, { target: proxyTarget }, err => {
            if (err) {
              reject(err);
            } else {
              resolve();
            }
          });
        });
      } catch (error) {
        ctx.auditLog('代理错误', `${error} 。`, 'error');
      }

    } else {
    // 如果没有匹配的代理规则，继续下一个中间件处理
      await next();
    }
  };

};
