// const _ = require('lodash');
module.exports = () => {
// module.exports = options => {
  return async function crossHeader(ctx, next) {

    // 指定允许其他域名访问
    ctx.set('Access-Control-Allow-Origin', '*');// 一般用法（*，指定域，动态设置），3是因为*不允许携带认证头和cookies
    // 允许的请求头字段
    ctx.set('Access-Control-Allow-Headers', 'Content-Type, Content-Length, Authorization, Accept, X-Requested-With , yourHeaderFeild');
    // 允许的请求类型
    ctx.set('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS');
    // 是否允许后续请求携带认证信息（cookies）,该值只能是true,否则不返回
    ctx.set('Access-Control-Allow-Credentials', true);
    ctx.set('Access-Control-Allow-EventSource', true);
    // 预检结果缓存时间,也就是缓存
    // ctx.set('Access-Control-Max-Age:', 1800);


    if (ctx.method === 'OPTIONS') {
      ctx.body = 200;
    } else {
      await next();
    }

  };

};
