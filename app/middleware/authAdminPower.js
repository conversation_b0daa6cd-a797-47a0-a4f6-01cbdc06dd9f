const _ = require('lodash');
module.exports = (options, app) => {
  let routeWhiteList = [
    'logout',
    'getUserSession',
    'getSitBasicInfo',
    'adminResource/getListByPower',
    'admin/index',
    'adminResource/alllist',
    'getCompany',
    'getDashboardData',
    'selectArea',
    'searchCompany',
    'loadMoreCompany',
    'preventAssessFindAll',
    'getPhysicalExaminationChartData',
    'getUnreadMessages',
    'getHasReadMessages',
    'readMessage',
    'getServiceProject',
    'getBasicMessage',
    'getMainData',
    'adminServiceProject/getProjects', // 技术服务项目列表
    'getGroupID',
    'getdDstrictsData',
    'getTownScope',
    'getVisualizationData', // 获取大屏所需的数据
    'findSupervision', // 获取被监管企业数量
    'warningEvent', // 预警事件
    'harmTotal', // 接害总人数
    'newDashboardData',
    'emptyRequest', // 空请求
    'adminGroup/getList',
    'getPersonInfo',
    'sse',
    'videoReview',
    'testCrypt',
  ];
  return async function checksuperUserPower(ctx, next) {
    const getPluginApiWhiteList = app.getExtendApiList();
    if (!_.isEmpty(getPluginApiWhiteList) && !_.isEmpty(getPluginApiWhiteList.adminApiWhiteList) && routeWhiteList.indexOf(getPluginApiWhiteList.adminApiWhiteList.join(',')) < 0) {
      routeWhiteList = routeWhiteList.concat(getPluginApiWhiteList.adminApiWhiteList);
    }
    const targetApi = (ctx.originalUrl).replace('/manage/', '').split('?')[0];
    const targetResourceObj = await ctx.service.adminResource.find({
      isPaging: '0',
    }, {
      query: {
        type: '1',
        api: targetApi,
      },
      files: '_id api',
    });
    let hasPower = false,
      adminPower = {};
    (!_.isEmpty(ctx.session.superUserInfo)) ? adminPower = await ctx.helper.getAdminPower(ctx) : hasPower = false;
    (_.indexOf(routeWhiteList, targetApi) > -1) ? hasPower = true : targetApi;
    const targetResourceObjCount = targetResourceObj.length;
    for (let i = 0; i < targetResourceObjCount; i++) {
      const element = targetResourceObj[i];
      if (targetResourceObj && adminPower && (_.indexOf(adminPower, element._id) > -1)) {
        hasPower = true;
        break;
      }
    }
    hasPower ? ctx.logger.info('鉴权成功：' + ctx.originalUrl) : ctx.logger.info('鉴权失败：' + ctx.originalUrl);
    hasPower ? await next() : ctx.helper.renderFail(ctx, {
      message: '对不起，您暂无此权限',
    });
  };
};
