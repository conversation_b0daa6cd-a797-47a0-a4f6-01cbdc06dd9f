// 预警处理：职业健康检查结果有复查却超期两个月无复查者改为三级预警，已复查者解除预警
module.exports = app => {
  return {
    schedule: {
      cron: app.config.warningTick, // 每天执行一次
      disable: !app.config.warningTaskEnable, // 默认禁用，只有杭州启用
      immediate: true, // 启动服务时就立即执行一次任务
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // 定时执行的任务
    async task(ctx) {
      const recheckOverTime = app.config.recheckOverTime; // 复查超期时间戳 毫秒
      let recheckOverDays = recheckOverTime / (24 * 60 * 60000);
      recheckOverDays = recheckOverDays >= 30 ? recheckOverDays / 30 + '个月' : recheckOverDays + '天';
      const curTime = new Date().getTime(); // 当前时间戳
      // 1、查找出未解除的需要复查的预警
      const warningList = await ctx.model.Warning.find({
        delete: false,
        type: 2,
        status: { $in: [ 0, 1, 2, 4 ] },
        'content.0': { $regex: '复查' },
        jobHealthId: { $exists: true },
        companyId: { $exists: true },
        createdAt: { $gte: new Date('2022') },
      }, {
        jobHealthId: 1, companyId: 1, lever: 1, createdAt: 1,
      });

      const leverUpgradeWarningIds = []; // 需要升级的预警ids
      // const  revokeWarningIds = []; // 需要解除的预警ids

      for (let i = 0; i < warningList.length; i++) {
        const item = warningList[i];
        const EnterpriseID = item.companyId;

        const curHealthCheck = await ctx.model.Healthcheck.findOne({ _id: item.jobHealthId }, { checkDate: 1, re_examination: 1, suspected: 1, forbid: 1 });
        if (!curHealthCheck) {
          await ctx.model.Warning.updateOne({ _id: item._id }, { delete: true });
          ctx.auditLog(`体检项目${item.jobHealthId}已被删除，却还有预警`, `预警 ${item._id} 已被系统定时器假删。`, 'info');
          continue;
        }
        if (!curHealthCheck.checkDate) {
          ctx.auditLog('体检项目数据异常', `体检项目 ${item.jobHealthId} 没有体检日期`, 'info');
          continue;
        }
        if (curHealthCheck.suspected) continue; // 把有疑似职业病的先筛查掉，不管它

        const curCheckDate = new Date(item.createdAt); // 当前预警的时间 !!!
        // 2、查找该企业之后的所有复查体检项目
        const healthCheckList = await ctx.model.Healthcheck.find({
          reportStatus: true,
          EnterpriseID,
          recheck: true,
          applyTime: { $gt: curCheckDate }, // 按上报日期来算
        }, { checkDate: 1 });
        if (healthCheckList.length) { // 有复查的，如果全部都已经复查就撤销预警
          // if (curHealthCheck.forbid) continue; // 有禁忌证的，不撤销
          // // 查出需要复查的是哪些人
          // const suspects = await ctx.model.Suspect.find({ batch: curHealthCheck._id, CwithO: '复查' }, { employeeId: 1, name: 1, CwithO: 1 });
          // const suspectsEmployeeIds = suspects.map(ele => ele.employeeId);
          // // 查出后面哪些人去复查了
          // const batchIds = healthCheckList.map(ele => ele._id);
          // const recheckSuspects = await ctx.model.Suspect.find({ batch: { $in: batchIds } }, { employeeId: 1, name: 1, CwithO: 1 });
          // const recheckSuspectsEmployeeIds = recheckSuspects.map(ele => ele.employeeId);
          // // 如果要复查的都已经再体检过了，就撤销预警
          // const flag = suspectsEmployeeIds.every(employeeId => recheckSuspectsEmployeeIds.includes(employeeId));
          // if (flag) {
          //   revokeWarningIds.push(item._id);
          // }
        } else { // 没有复查的，如果已经超期，四级改为三级预警
          if (item.lever === 4 && curTime - curCheckDate.getTime() >= recheckOverTime) {
            // 预警从四级变成三级
            leverUpgradeWarningIds.push(item._id);
          }
        }
      }
      // console.log(**********, leverUpgradeWarningIds.length, leverUpgradeWarningIds);
      // console.log(**********, revokeWarningIds.length, revokeWarningIds);
      // 更新预警
      ctx.model.Warning.updateMany(
        { _id: { $in: leverUpgradeWarningIds } },
        { lever: 3,
          $push: { process: {
            time: Date.now(),
            thing: '升级预警',
            remark: `复查人员超期${recheckOverDays}未处理，系统自动升级为三级预警。`,
          } } },
        { new: true }
      ).then(res => {
        if (res && res.nModified) {
          ctx.auditLog(`四级预警升级成功(${res.nModified}个)`, JSON.stringify(leverUpgradeWarningIds), 'info');
        }
      });
      // await ctx.model.Warning.updateMany(
      //   { _id: { $in: revokeWarningIds } },
      //   { status: 3,
      //     $push: { process: {
      //       time: Date.now(),
      //       thing: '解除预警 (系统)',
      //       remark: '复查者已全部复查，系统自动解除预警。',
      //     } } },
      //   { new: true, upsert: true }
      // ).then(res => {
      //   console.log(6666666, res.result);
      // });
    },
  };
};
