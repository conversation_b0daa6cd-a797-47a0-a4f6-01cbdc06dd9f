// 危害因素检测到期预警定时任务
module.exports = app => {
  return {
    schedule: {
      cron: app.config.warningTick2, // 每天执行一次
      disable: !app.config.warningTaskEnable2, // 默认禁用
      immediate: true, // 启动服务时就立即执行一次任务
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // 定时执行的任务
    async task(ctx) {
      const jcOverMonths = app.config.jcOverMonths; // 危害因素检测超期时间 月
      const jcOverTime = new Date().getTime() - jcOverMonths * 30 * 24 * 60 * 60 * 1000; // 超期时间戳
      const adminorgs = await ctx.model.Adminorg.find({ isDelete: false, productionStatus: '2', isactive: '1' }, { _id: 1, cname: 1 });
      console.log('开始执行危害因素检测到期预警定时任务', adminorgs.length, '个组织');
      console.log('超期时间：', jcOverMonths);
      for (let i = 0; i < adminorgs.length; i++) {
        const EnterpriseID = adminorgs[i]._id;
        const EnterpriseName = adminorgs[i].cname;
        // 根据EnterpriseID查询JobHealth表最近的一条数据
        const jobHealthList = await ctx.model.JobHealth.find({ EnterpriseID, completeStatus: 1, projectStop: false }).sort({ completeTime: -1 }).limit(1);
        const jobHealth = jobHealthList && jobHealthList.length ? jobHealthList[0] : null;
        const jcTime = jobHealth ? jobHealth.completeTime : null;
        if (!jcTime) continue;
        // 判断jcTime是否超过jcOverTime
        const checkAssessment = await ctx.model.CheckAssessment.findOne({ EnterpriseID, jobHealthId: jobHealth._id });
        if (!checkAssessment || !checkAssessment._id) {
          console.log(44444, jobHealth._id + '没有CheckAssessment数据，跳过。');
          continue;
        }
        if (jcTime.getTime() < jcOverTime) {
          // 超过了，查询该企业的所有危害因素检测预警
          const oldWarning = await ctx.model.Warning.findOne({ type: 1, companyId: EnterpriseID, jobHealthId: checkAssessment._id, delete: false });
          if (oldWarning && oldWarning._id) {
            const content = oldWarning.content[0];
            if (content.includes('未进行危害因素检测，请尽快检测')) {
              console.log(EnterpriseName + '已经有危害因素检测超期预警，跳过。');
              continue;
            }
          }
          const workPlaces = jobHealth.workPlaces.map(item => item.workAdd);
          const workAddress = []; // 预警区域
          for (let j = 0; j < workPlaces.length; j++) {
            const workPlace = workPlaces[j];
            const newWorkPlace = await ctx.service.district.findParents(workPlace[workPlace.length - 1]);
            if (newWorkPlace && newWorkPlace.length > 0) {
              workAddress.push(newWorkPlace);
            } else {
              workAddress.push(workPlace);
            }
          }
          const warningUpData = {
            type: 1, // 危害因素检测预警
            companyId: EnterpriseID,
            companyName: EnterpriseName,
            jobHealthId: checkAssessment._id,
            modelName: 'CheckAssessment',
            workAddress,
            content: [ EnterpriseName + '已超过' + jcOverMonths + '个月未进行危害因素检测，请尽快检测并提交报告。' ],
            process: [{
              time: new Date(),
              thing: '生成预警',
              remark: '由系统自动生成',
            }],
            source: 'system',
            ctime: new Date(),
          };
          const res = await ctx.model.Warning.create(warningUpData);
          console.log(EnterpriseID + '生成危害因素检测超期预警:' + res._id);
        } else {
          console.log(66666, EnterpriseName + '未超过危害因素检测超期时间，跳过。');
        }
      }
      console.log('危害因素检测到期预警定时任务执行完毕');
    },
  };
};
