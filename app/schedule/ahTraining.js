// 安徽培训大屏统计定时任务 xxn
module.exports = app => {
  const { dpStatisticsTick, ahStatisticalTaskEnable, adcode } = app.config;
  let isLocked = false; // 锁标志

  async function performStatistics(ctx, year) {
    const areaData = await ctx.service.examSyllabus.statisticsForSingleAreaTask({ adcode, year });
    if (!areaData || areaData.query.adname.length >= 4) return;
    await ctx.service.examSyllabus.statisticsForAllsubArea({ adcode, year }, areaData.query.adname.length - 1);
  }

  return {
    schedule: {
      cron: dpStatisticsTick,
      disable: !ahStatisticalTaskEnable,
      immediate: true,
      type: 'worker',
    },
    // 定时执行的任务
    async task(ctx) {
      if (isLocked) {
        ctx.auditLog('安徽培训大屏统计定时任务', '上一个任务尚未完成，跳过本次执行', 'warn');
        return;
      }
      if (!adcode) {
        ctx.auditLog('安徽培训大屏统计定时任务', '没有获取到adcode', 'error');
        return;
      }
      console.log('安徽培训大屏统计定时任务开始执行');
      const startTime = Date.now();
      isLocked = true; // 加锁
      try {
        const curYear = new Date().getFullYear();
        for (let i = 2024; i <= curYear; i++) {
          await performStatistics(ctx, i);
        }
        ctx.auditLog('安徽培训大屏统计定时任务-执行完成', `耗时${Date.now() - startTime}ms`, 'error');
      } catch (err) {
        ctx.auditLog('安徽培训大屏统计定时任务-执行错误', err.message || err, 'error');
      } finally {
        isLocked = false; // 解锁
      }
    },
  };
};
