// 同步福州监管端账号
const axios = require('axios');
// const _ = require('lodash');
module.exports = app => {
  return {
    schedule: {
      cron: app.config.accountSyncTick,
      disable: !app.config.accountSyncEnable,
      immediate: true, // 启动服务时就立即执行一次任务
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // 定时执行的任务
    async task(ctx) {
      console.log('获取当前所在分支', `${app.config.branch}`, 'info');
      // 仅在福州站点执行，但改用conf.fz.js中的配置来enable
      const qs = require('qs');
      const res = await axios.post(app.config.fzUserInfo,
        qs.stringify({
          params: '{"syscode":"zyws"}',
        })
      );
      if (res.data && (res.data.code === 'S')) {
        const userinfos = res.data.data || [];
        ctx.auditLog('所有的福州人员数据', userinfos, 'info');
        const phoneReg = /^1[3-9]\d{9}$/;
        for (let i = 0; i < userinfos.length; i++) {
          const ele = userinfos[i];
          let phoneNum = ele.mobile;
          const name = ele.lastname;
          const userName = ele.username;
          let matchOr = [];
          if (phoneNum) {
            if (phoneReg.test(phoneNum)) {
              matchOr = matchOr.concat([
                { phoneNum },
                { phoneNum: '0' + phoneNum },
                { 'members.phoneNum': phoneNum },
              ]);
            } else {
              ctx.auditLog('福州人员手机号码格式错误', ele, 'error');
              phoneNum = '';
              continue;
            }
          } else if (userName) {
            matchOr = matchOr.concat([
              { userName },
              { 'members.userName': userName },
            ]);
          } else if (name) {
            matchOr = matchOr.concat([
              { name },
              { 'members.name': name },
            ]);
          }
          const item = await ctx.model.SuperUser.aggregate([
            { $match: { state: '1', $or: matchOr } },
          ]);
          if (!item.length && (phoneNum || userName)) {
            // 暂时只有一个总帐号；过来的人员都存member里，不需要创建新的账号，数据要根据福州的接口返回调整
            const newMember = {
              name,
              phoneNum,
              userName: userName || phoneNum, // 默认是手机号
              jobTitle: ele.jobtitleName || '',
            };
            ctx.auditLog('系统中新增人员!!', newMember, 'info');
            const newdata = await ctx.model.SuperUser.findOneAndUpdate(
              { state: '1', userName: 'fzcdc' },
              { $push: { members: newMember } },
              { new: true }
            );
            ctx.auditLog('系统中新增成员结果', newdata, 'info');
          }
        }
      } else {
        ctx.auditLog('获取登录人员token信息失败', res.data, 'error');
      }
    },
  };
};
