// 大屏数据统计，目前只统计杭州监管端的
module.exports = app => {
  return {
    schedule: {
      cron: app.config.xjbthazardStatisticsTick.corn,
      disable: !app.config.xjbthazardStatisticsTick.enable,
      immediate: true, // 启动服务时就立即执行一次任务
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // 定时执行的任务
    async task(ctx) {
      // 调用服务更新统计数据
      const res = await ctx.service.hazardStatisticsService.calculateAndUpdateStatistics({
        timeFrame: 'all',
      });
      console.log('xjbt 统计数据更新成功', res);
    },
  };
};
