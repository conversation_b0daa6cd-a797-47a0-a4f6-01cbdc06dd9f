// 大屏统计数据-定时任务
module.exports = app => {
  return {
    schedule: {
      cron: app.config.dpStatisticsTick,
      disable: !app.config.dpStatisticalTaskEnable,
      immediate: false, // 启动服务时就立即执行一次任务
      type: 'worker',
    },
    // 定时执行的任务
    async task(ctx) {
      if (!app.config.dpStatisticalTaskEnable) return;
      try {
        const adcode = app.config.adcode;
        if (!adcode) {
          ctx.auditLog('大屏数据总线-统计数据-主屏', '没有获取到adcode', 'error');
          return;
        }
        await ctx.service.dpStatistical.coverageStatistics(adcode);
        await ctx.service.dpStatistical.departmentalWorkload(adcode);

        await ctx.service.fzStatictics.declarationStatistical(adcode);
        await ctx.service.fzStatictics.harmfactorStatistical(adcode);
        await ctx.service.fzStatictics.getIndustry(adcode);

        await ctx.service.statisticalFz.getStatisticalFz_JCYRDWZYJKPXTJ(adcode); // 监测用人单位职业健康培训统计
        await ctx.service.statisticalFz.getStatisticalFz_ZYJKJCTJ(adcode); // 职业健康检查统计数据
        await ctx.service.statisticalFz.getStatisticalFz_ZDWHYSJCTJ(adcode); // 重点危害因素监测统计
        await ctx.service.statisticalFz.getStatisticalFz_YRDWKZSTSTJ(adcode); // 用人单位开展三同时统计
        ctx.auditLog('大屏数据总线-统计数据-主屏-执行完成', '统计数据-主屏-执行完成', 'info');
      } catch (err) {
        // console.error(err);
        ctx.auditLog('大屏数据总线-统计数据-主屏-执行错误', err.message, 'error');
      }
    },
  };
};
