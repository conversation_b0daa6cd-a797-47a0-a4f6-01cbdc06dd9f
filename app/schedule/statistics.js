// 大屏数据统计，目前只统计杭州监管端的
module.exports = app => {
  return {
    schedule: {
      cron: app.config.statisticsTick,
      disable: !app.config.statisticalTaskEnable,
      immediate: true, // 启动服务时就立即执行一次任务
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // 定时执行的任务
    async task(ctx) {
      const adcode = app.config.adcodeShort;
      const adname = app.config.adName;
      if (!adcode || !adname) {
        ctx.auditLog('数据统计汇总', '没有获取到adcode或adname', 'error');
        return;
      }
      const curYear = new Date().getFullYear();
      const yearArr = [ curYear ];

      for (let i = 0; i < yearArr.length; i++) {
        const year = yearArr[i];
        // 1、获取检测项目数据
        const jobHealthData = await ctx.service.dashboard.countJobHealthData(adcode, year, 10);
        // 2、获取体检数据和诊断数据
        const healthCheckData = await ctx.service.dashboard.countHealthCheckData(adname, year, 10);
        // 3、获取预警数据(已解除和已撤销的不展示)，按workAddress来细分子区域下的预警可能不准，因为以前很多预警是没有workAddress字段的，也已经无法确定了
        const warningData = await ctx.service.dashboard.countWarningData(adname, year, true);

        // 4、计算中间地图上的区域数据
        const districtsData = {}, // 区域数据
          jobHealthList = jobHealthData.JobHealthList,
          HealthcheckList = healthCheckData.HealthcheckList,
          odiseaseList = healthCheckData.odiseaseList,
          warningList = warningData.warningList;
        const curArea = await ctx.service.dashboard.getCurArea(adcode); // 当前区域
        if (curArea.level === '2') {
        // 杭州的数据到了区县级别就不获取子数据了
          const area_code = curArea.area_code;
          const warning1Num = warningList.filter(ele => ele.type === 1).length;
          districtsData[area_code] = [ curArea.lng, curArea.lat, curArea.name, jobHealthList.length, HealthcheckList.length, odiseaseList.length, warning1Num, warningList.length - warning1Num ]; // 后五个数分别对应检测数量，体检数量，诊断人次，检测预警，体检预警
        } else {
        // 计算中间地图上的子区域数据
          const subArea = await ctx.service.dashboard.getSubArea(adcode); // 所有的子区域
          subArea.forEach(ele => {
            const area_code = ele.area_code,
              area_name = ele.name;
            const jobHealthNum = jobHealthList.filter(ele => ele.workPlaces.some(addr => addr.workAdd.includes(area_code))).length;
            const healthCheckNum = HealthcheckList.filter(ele => ele.workAddress.some(addr => addr.districts.includes(area_name))).length;
            const odiseaseNum = odiseaseList.filter(ele => {
              const Enterprise = ele.adminOrgClient.length ? ele.adminOrgClient[0] : null;
              if (Enterprise) {
              // console.log(888888888, Enterprise.workAddress);
                return Enterprise.workAddress.some(addr => addr.districts.includes(area_name));
              }
              return false;
            }).length;
            const warning1Num = warningList.filter(ele => ele.type === 1 && ele.workAddress.some(addr => addr.includes(area_name))).length;
            const warning2Num = warningList.filter(ele => ele.type === 2 && ele.workAddress.some(addr => addr.includes(area_name))).length;
            districtsData[area_code] = [ ele.lng, ele.lat, area_name, jobHealthNum, healthCheckNum, odiseaseNum, warning1Num, warning2Num ]; // 后五个数分别对应检测数量，体检数量，诊断人次，检测预警，体检预警
          });
        }

        // 返回数据
        delete jobHealthData.JobHealthList;
        delete healthCheckData.HealthcheckList;
        delete healthCheckData.odiseaseList;
        const result = {
          query: { adname, adcode, year }, // 当前的查询条件
          jobHealthData,
          healthCheckData,
          warningData,
          districtsData, // 子区域的统计数据
          updateTime: Date.now(),
        };
        const oldStatistics = await ctx.model.Statistics.findOne({ query: result.query });
        if (oldStatistics && oldStatistics._id) {
          await ctx.model.Statistics.updateOne({ _id: oldStatistics._id }, { $set: result });
        } else {
          await ctx.model.Statistics.create(result);
        }
      }
    },
  };
};
