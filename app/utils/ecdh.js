const crypto = require('crypto');

const ecdh = {
// 创建ECDH密钥对
  generateECDHKeyPair() {
    const ecdh = crypto.createECDH('secp256k1');
    ecdh.generateKeys();
    const publicKeyBuffer = ecdh.getPublicKey();
    const privateKeyBuffer = ecdh.getPrivateKey();
    return {
      publicKey: publicKeyBuffer.toString('base64'),
      privateKey: privateKeyBuffer.toString('base64'),
    };
  },

  // 使用自己的私钥和对方的公钥计算出共享密钥
  generateSharedKey(
    myPrivateKey = '',
    otherPublicKey = ''
  ) {
    const ecdh = crypto.createECDH('secp256k1');
    ecdh.setPrivateKey(myPrivateKey, 'base64'); // 设置私钥
    const sharedKeyBuffer = ecdh.computeSecret(otherPublicKey, 'base64');
    return sharedKeyBuffer.toString('base64');
  },

  // 使用共享密钥进行加密  content：需要加密的信息
  encrypt(content = '', sharedKey = '') {
    if (typeof sharedKey === 'string') {
      sharedKey = Buffer.from(sharedKey, 'base64');
    }
    const cipher = crypto.createCipheriv(
      'aes-256-cbc',
      sharedKey,
      Buffer.alloc(16)
    );
    let encrypted = cipher.update(content, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  },

  // 使用共享密钥进行解密 encrypted:加密后的数据
  decrypt(encrypted = '', sharedKey = '') {
    if (typeof sharedKey === 'string') {
      sharedKey = Buffer.from(sharedKey, 'base64');
    }
    const decipher = crypto.createDecipheriv(
      'aes-256-cbc',
      sharedKey,
      Buffer.alloc(16)
    );
    let decrypted = decipher.update(encrypted, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  },
};
module.exports = ecdh;

