const { sm3 } = require('sm-crypto');
const axios = require('axios');
const qs = require('qs');
// sm3hmc计算

let fzgmToken = null;
const fzgmBaseUrl = process.env.FZGM_BASE_URL || '';
const fzgmKey = process.env.FZGM_KEY || '';
const xjbtBaseUrl = process.env.XJBT_BASE_URL || '';
const xjbtAppId = process.env.XJBT_APP_ID || '';
const xjbtHmacAlgorithm = process.env.HMAC_SIGN_ALGORITHM || '';
async function hashSm3(data, passwordEncryptionAlgorithm) {
  try {
    if (passwordEncryptionAlgorithm === 'FZsm3') {
      // 获取福州token
      if (!fzgmBaseUrl || !fzgmKey) {
        throw new Error('福州国密url或者业务key为空');
      }
      const token = await getFZToken(fzgmBaseUrl);
      const base64Data = Buffer.from(data).toString('base64');
      const encryptData = await axios({
        method: 'post',
        url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm3-hmac`,
        data: {
          data: base64Data,
          appid: fzgmKey,
          keyid: '',
        },
        headers: {
          Authorization: token,
        },
      });
      return encryptData.data.value;
    } else if (passwordEncryptionAlgorithm === 'XJBTsm3') {
      const base64Data = Buffer.from(data).toString('base64');
      const encryptData = await axios({
        method: 'post',
        url: `${xjbtBaseUrl}/key/v3/digest`,
        data: qs.stringify({
          hashAlg: xjbtHmacAlgorithm,
          plaintext: base64Data,
        }),
        headers: {
          appId: xjbtAppId,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      if (encryptData.data.code === 0) {
        return encryptData.data.data;
      }
      throw new Error(encryptData.data.message);
    }
    const hashData = sm3(data, {
      key: 'daac25c1512fe50f79b0e4526b93f5c0e1460cef40b6dd44af13caec62e8c60e0d885f3c6d6fb51e530889e6fd4ac743a6d332e68a0f2a3923f42585dceb93e9', // 要求为 16 进制串或字节数组
    });
    return hashData;
  } catch (error) {
    throw new Error('sm3计算失败', error);
  }
}

// sm3hmc校验
async function verifySm3(data, passwordEncryptionAlgorithm, hash) {
  try {
    if (passwordEncryptionAlgorithm === 'FZsm3') {
      // 获取福州token
      if (!fzgmBaseUrl || !fzgmKey) {
        throw new Error('福州国密url或者业务key为空');
      }
      const token = await getFZToken(fzgmBaseUrl);
      const base64Data = Buffer.from(data).toString('base64');
      const encryptData = await axios({
        method: 'post',
        url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm3-hmac-verify`,
        data: {
          data: base64Data,
          appid: fzgmKey,
          value: hash,
          keyid: '',
        },
        headers: {
          Authorization: token,
        },
      });
      return encryptData.data.isvalid;
    } else if (passwordEncryptionAlgorithm === 'XJBTsm3') {
      const base64Data = Buffer.from(data).toString('base64');
      const encryptData = await axios({
        method: 'post',
        url: `${xjbtBaseUrl}/key/v3/digest`,
        data: qs.stringify({
          hashAlg: xjbtHmacAlgorithm,
          plaintext: base64Data,
        }),
        headers: {
          appId: xjbtAppId,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      if (encryptData.data.code === 0) {
        return encryptData.data.data === hash;
      }
      throw new Error(encryptData.data.message);
    }
    const hashData = sm3(data, {
      key: 'daac25c1512fe50f79b0e4526b93f5c0e1460cef40b6dd44af13caec62e8c60e0d885f3c6d6fb51e530889e6fd4ac743a6d332e68a0f2a3923f42585dceb93e9', // 要求为 16 进制串或字节数组
    });
    return hashData === hash;
  } catch (error) {
    return false;
  }
}

// 获取福州token
async function getFZToken(fzgmBaseUrl) {
  if (!fzgmToken) {
    try {
      if (!fzgmBaseUrl || !fzgmKey) {
        throw new Error('福州国密url或者业务key为空');
      }
      const response = await axios({
        method: 'get',
        url: `${fzgmBaseUrl}/apisix/plugin/jwt/sign`,
        params: {
          key: fzgmKey,
        },
      });
      fzgmToken = response.data;
      return fzgmToken;
    } catch (error) {
      throw new Error('获取福州GMtoken失败1', error);
    }
  } else {
    return fzgmToken;
  }
}

module.exports = {
  hashSm3,
  verifySm3,
  getFZToken,
};
