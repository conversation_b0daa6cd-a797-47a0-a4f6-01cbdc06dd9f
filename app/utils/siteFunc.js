const _ = require('lodash');
// const Core = require('@alicloud/pop-core');
const AipOcrClient = require('baidu-aip-sdk').ocr;
// const moment = require('moment');
// const fs = require('fs');

const siteFunc = {

  randomString(len, charSet) {
    charSet = charSet || 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomString = '';
    for (let i = 0; i < len; i++) {
      const randomPoz = Math.floor(Math.random() * charSet.length);
      randomString += charSet.substring(randomPoz, randomPoz + 1);
    }
    return randomString;
  },

  async foreachAsync(arr, callback) {
    const length = arr.length;
    const O = Object(arr);
    let k = 0;
    while (k < length) {
      if (k in O) {
        const kValue = O[k];
        await callback(kValue, k, O);
      }
      k++;
    }
  },
  async baiduBusinessLicenseOCR(image) {


    // 设置APPID/AK/SK
    const APP_ID = '22119199';
    const API_KEY = 'sEzNNjMttun3NWOXpy3U20Io';
    const SECRET_KEY = 'eAehH1qHuBh7WA5gZrczVGW8kiuk6Exf';

    // 新建一个对象，建议只保存一个对象调用服务接口
    const client = new AipOcrClient(APP_ID, API_KEY, SECRET_KEY);

    return new Promise((resolve, reject) => {
      // 调用营业执照识别
      client.businessLicense(image).then(function(result) {
        resolve(result);
      }).catch(function(err) {
        // 如果发生网络错误
        reject(err);
      });
    });


  },

  // getNoticeConfig(type, value) {
  //   let noticeObj;
  //   if (type === 'reg') {
  //     noticeObj = {
  //       type: '2',
  //       systemSender: 'doraCMS',
  //       title: '用户注册提醒',
  //       content: '新增注册用户 ' + value,
  //       action: type,
  //     };
  //   } else if (type === 'msg') {
  //     noticeObj = {
  //       type: '2',
  //       sender: value.author,
  //       title: '用户留言提醒',
  //       content: '用户 ' + value.author.userName + ' 给您留言啦！',
  //       action: type,
  //     };
  //   }
  //   return noticeObj;
  // },

  // 检索代号：#0001  adminBasePath 后台根路径
  async renderNoPowerMenus(manageCates, adminPower, adminBasePath, buildTree = true) {
    const newResources = [],
      newRootCates = [];
    const rootCates = _.filter(manageCates, doc => {
      return doc.parentId === '0';
    });
    const menuCates = _.filter(manageCates, doc => {
      return doc.type === '0' && doc.parentId !== '0';
    });
    const optionCates = _.filter(manageCates, doc => {
      return doc.type !== '0' || (doc.type === '0' && doc.parentId !== '0');
    });
    if (!_.isEmpty(adminPower)) {
      // 是否显示子菜单
      for (let i = 0; i < menuCates.length; i++) {
        const resourceObj = JSON.parse(JSON.stringify(menuCates[i]));
        // 如果是菜单里某个功能的子功能，不再做权限校验
        const cateFlag = this.checkNoAllPower(
          resourceObj._id,
          optionCates,
          adminPower,
          optionCates
        );
        if (!cateFlag) {
          newResources.push(resourceObj);
        }
      }
      // 是否显示大类菜单  如果大类菜单下有小类菜单，才显示
      for (const cate of rootCates) {
        const fiterSubCates = _.filter(newResources, doc => {
          return doc.parentId === cate._id;
        });
        if (fiterSubCates.length !== 0) {
          newRootCates.push(cate);
        }
      }
    }
    const allResources = newResources.concat(newRootCates);
    const renderResources = buildTree
      ? this.buildTree(allResources, adminBasePath)
      : allResources;
    return renderResources;
  },

  /**
     * 将一维的扁平数组转换为多层级对象
     * @param  {[type]} list 一维数组，数组中每一个元素需包含id和parentId两个属性
     * @param  {[type]} adminBasePath 用于动态与config后台管理根目录保持一致
     * @return {[type]} tree 多层级树状结构
     */
  buildTree(list, adminBasePath) {
    const temp = {}; // 用于存储所有节点的哈希表
    const tree = []; // 存储最终的树结构

    // 将列表数据转为哈希表
    for (const item of list) {
      temp[item._id] = { ...item, children: [] }; // 确保每个节点都有 children 属性
    }

    // 构建树结构
    for (const item of list) {
      const currentItem = temp[item._id]; // 当前节点

      if (currentItem.parentId && currentItem.parentId !== '0') {
      // 如果有父级，则将当前节点挂载到父级的 children 中
        const parentItem = temp[currentItem.parentId];
        if (parentItem) {
          parentItem.children.push(this.renderTemp(adminBasePath, currentItem)); // 挂载子节点
        } else {
          console.warn(
            `Parent item with ID ${currentItem.parentId} not found for item ${currentItem._id}`
          );
        }
      } else {
      // 如果没有父级，则是顶层节点，直接加入树结构
        tree.push(this.renderTemp(adminBasePath, currentItem, true));
      }
    }
    return tree;
  },

  renderTemp(adminBasePath, temp, parent = false) {
    const renderTemp = {};
    // 如果是父级菜单，设置 alwaysShow 属性
    if (parent) {
      renderTemp.alwaysShow = true;
    }
    // 处理路径，避免双斜杠
    const routePath = temp.routePath || ''; // 如果没有 routePath，设置为空字符串
    const path =
    routePath.startsWith('http://') || routePath.startsWith('https://')
      ? routePath // 外部链接，直接使用
      : `${adminBasePath}/${routePath}`.replace(/\/+/g, '/'); // 拼接路径并移除多余斜杠

    renderTemp.path = path;
    renderTemp.hidden = !temp.enable; // 菜单隐藏逻辑
    renderTemp.icon = temp.icon; // 菜单图标
    renderTemp.name = temp.comments; // 菜单名称
    renderTemp.children = temp.children || []; // 子菜单，确保为空时是空数组
    renderTemp.api = temp.api; // API 字段
    renderTemp.redirect = temp.parentId === '0' ? 'noRedirect' : ''; // 重定向设置

    // meta 配置
    renderTemp.meta = {
      title: temp.comments, // 菜单标题
    };
    if (renderTemp.icon) {
      renderTemp.meta.icon = temp.icon; // 如果有图标，写入 meta.icon
    }
    return renderTemp;
  },

  // 子菜单都无权限校验
  checkNoAllPower(resourceId, childCates, power, optionCates) {
    // 初始默认无权限
    let cateFlag = true;

    // 找到当前菜单的直接子菜单
    const subCates = _.filter(childCates, doc => doc.parentId === resourceId);

    // 找到当前菜单直接关联的接口资源
    const relatedOptions = _.filter(optionCates, doc => doc.parentId === resourceId);

    // 如果接口资源中存在用户拥有权限的接口，cateFlag 为 false
    for (const option of relatedOptions) {
      if (power.indexOf(option._id) > -1) {
        cateFlag = false;
        break;
      }
    }

    // 如果当前菜单的直接接口资源没有权限，检查子菜单（递归）
    if (cateFlag) {
      for (const cate of subCates) {
        if (!this.checkNoAllPower(cate._id, childCates, power, optionCates)) {
          cateFlag = false; // 如果任何一个子菜单有权限，父级菜单也应该显示
          break;
        }
      }
    }

    return cateFlag;
  },

  setTempParentId(arr, key) {
    for (let i = 0; i < arr.length; i++) {
      const pathObj = arr[i];
      pathObj.parentId = key;
    }
    return arr;
  },

  getTempBaseFile(path, viewPath = '', themePath = '') {
    const thisType = (path).split('.')[1];
    let basePath;
    if (thisType === 'html') {
      basePath = viewPath;
    } else if (thisType === 'json') {
      basePath = process.cwd();
    } else {
      basePath = themePath;
    }
    return basePath;
  },

  // 扫描某路径下文件夹是否存在
  checkExistFile(tempFilelist, forderArr) {

    let filterForderArr = [],
      distPath = false;
    for (let i = 0; i < forderArr.length; i++) {
      const forder = forderArr[i];
      const currentForder = _.filter(tempFilelist, fileObj => {
        return fileObj.name === forder;
      });
      filterForderArr = filterForderArr.concat(currentForder);
    }

    if (filterForderArr.length > 0 && (tempFilelist.length >= forderArr.length) && (filterForderArr.length >= forderArr.length)) {
      distPath = true;
    }

    return distPath;
  },

  // 筛选内容中的url
  getAHref(htmlStr, type = 'image') {
    let reg = /<img.+?src=('|")?([^'"]+)('|")?(?:\s+|>)/gim;
    if (type === 'video') {
      reg = /<video.+?src=('|")?([^'"]+)('|")?(?:\s+|>)/gim;
    } else if (type === 'audio') {
      reg = /<audio.+?src=('|")?([^'"]+)('|")?(?:\s+|>)/gim;
    }
    const arr = [];
    let tem;
    while ((tem = reg.exec(htmlStr))) {
      arr.push(tem[2]);
    }
    return arr;
  },
  // renderSimpleContent(htmlStr, imgLinkArr, videoLinkArr) {
  //   // console.log('----imgLinkArr-', imgLinkArr);
  //   const renderStr = [];
  //   // 去除a标签
  //   htmlStr = htmlStr.replace(/(<\/?a.*?>)|(<\/?span.*?>)/g, '');
  //   htmlStr = htmlStr.replace(/(<\/?br.*?>)/g, '\n\n');
  //   if (imgLinkArr.length > 0 || videoLinkArr.length > 0) {
  //     // console.log('----1111---')
  //     let delImgStr,
  //       delEndStr;
  //     const imgReg = /<img[^>]*>/gim;
  //     const videoReg = /<video[^>]*>/gim;
  //     if (imgLinkArr.length > 0) {
  //       delImgStr = htmlStr.replace(imgReg, '|I|');
  //     } else {
  //       delImgStr = htmlStr;
  //     }
  //     if (videoLinkArr.length > 0) {
  //       delEndStr = delImgStr.replace(videoReg, '|V|');
  //     } else {
  //       delEndStr = delImgStr;
  //     }
  //     // console.log('--delEndStr--', delEndStr);
  //     const imgArr = delEndStr.split('|I|');
  //     let imgTag = 0,
  //       videoTag = 0;
  //     for (let i = 0; i < imgArr.length; i++) {
  //       const imgItem = imgArr[i];
  //       // console.log('---imgItem---', imgItem);
  //       if (imgItem.indexOf('|V|') < 0) {
  //         // console.log('----i----', imgItem);
  //         imgItem && renderStr.push({
  //           type: 'contents',
  //           content: imgItem,
  //         });
  //         if (imgLinkArr[imgTag]) {
  //           renderStr.push({
  //             type: 'image',
  //             content: imgLinkArr[imgTag],
  //           });
  //           imgTag++;
  //         }
  //       } else { // 包含视频片段
  //         const smVideoArr = imgItem.split('|V|');
  //         for (let j = 0; j < smVideoArr.length; j++) {
  //           const smVideoItem = smVideoArr[j];
  //           smVideoItem && renderStr.push({
  //             type: 'contents',
  //             content: smVideoItem,
  //           });
  //           if (videoLinkArr[videoTag]) {
  //             const videoImg = siteFunc.getVideoImgByLink(videoLinkArr[videoTag]);
  //             renderStr.push({
  //               type: 'video',
  //               content: videoLinkArr[videoTag],
  //               videoImg,
  //             });
  //             videoTag++;
  //           }
  //         }
  //         if (imgLinkArr[imgTag]) {
  //           renderStr.push({
  //             type: 'image',
  //             content: imgLinkArr[imgTag],
  //           });
  //           imgTag++;
  //         }
  //       }
  //     }
  //   } else {

  //     renderStr.push({
  //       type: 'contents',
  //       content: htmlStr,
  //     });
  //   }

  //   return JSON.stringify(renderStr);
  // },

  // checkContentType(htmlStr, type = 'content') {
  //   const imgArr = this.getAHref(htmlStr, 'image');
  //   const videoArr = this.getAHref(htmlStr, 'video');
  //   const audioArr = this.getAHref(htmlStr, 'audio');

  //   let defaultType = '0',
  //     targetFileName = '';
  //   if (videoArr && videoArr.length > 0) {
  //     defaultType = '3';
  //     targetFileName = videoArr[0];
  //   } else if (audioArr && audioArr.length > 0) {
  //     defaultType = '4';
  //     targetFileName = audioArr[0];
  //   } else if (imgArr && imgArr.length > 0) {
  //     // 针对帖子有两种 大图 小图
  //     if (type === 'content') {
  //       defaultType = (Math.floor(Math.random() * 2) + 1).toString();
  //     } else if (type === 'class') {
  //       defaultType = '1';
  //     }
  //     targetFileName = imgArr[0];
  //   } else {
  //     defaultType = '1';
  //   }
  //   let renderLink = targetFileName;
  //   if (type === '3') {
  //     // 视频缩略图
  //     renderLink = siteFunc.getVideoImgByLink(targetFileName);
  //   }
  //   return {
  //     type: defaultType,
  //     defaultUrl: renderLink,
  //     imgArr,
  //     videoArr,
  //   };
  // },
  // getVideoImgByLink(link) {
  //   const oldFileType = link.replace(/^.+\./, '');
  //   return link.replace('.' + oldFileType, '.jpg');
  // },

  // clearUserSensitiveInformation(targetObj) {
  //   targetObj.password && delete targetObj.password;
  //   targetObj.countryCode && delete targetObj.countryCode;
  //   targetObj.phoneNum && delete targetObj.phoneNum;
  //   targetObj.email && delete targetObj.email;
  //   targetObj.watchSpecials && delete targetObj.watchSpecials;
  //   targetObj.watchCommunity && delete targetObj.watchCommunity;
  //   targetObj.praiseCommunityContent && delete targetObj.praiseCommunityContent;
  //   targetObj.praiseMessages && delete targetObj.praiseMessages;
  //   targetObj.praiseContents && delete targetObj.praiseContents;
  //   targetObj.favoriteCommunityContent && delete targetObj.favoriteCommunityContent;
  //   targetObj.favorites && delete targetObj.favorites;
  //   targetObj.despiseCommunityContent && delete targetObj.despiseCommunityContent;
  //   targetObj.despiseMessage && delete targetObj.despiseMessage;
  //   targetObj.despises && delete targetObj.despises;
  //   targetObj.watchers && delete targetObj.watchers;
  //   targetObj.followers && delete targetObj.followers;
  // },

  // sendSMS(messageConfig, params, requestOption = { method: 'POST' }) {
  //   const client = new Core(messageConfig);
  //   params.RegionId = messageConfig.regionId;
  //   params.SignName = messageConfig.signName;
  //   client.request('SendSms', params, requestOption).then(result => {
  //     console.log(JSON.stringify(result));
  //   }, ex => {
  //     console.log(ex);
  //   });
  // },

  // 用来解析json树状结构。obj是json对象，childKeyName是子集的键名
  // 返回一个数组
  flatTree(obj, childKeyName) {
    childKeyName = childKeyName || 'children';
    const listArray = [];
    flat(obj, childKeyName);
    function flat(data, childKeyName) {
      if (_.isArray(data)) {
        for (let i = 0; i < data.length; i++) {
          const children = data[i][childKeyName];
          const tempItem = data[i];
          listArray.push(tempItem);
          if (children && children.length > 0) {
            flat(data[i], childKeyName);
          }
        }
      } else {
        const children = data[childKeyName];
        listArray.push(data);
        if (children && children.length > 0) {
          flat(children, childKeyName);
        }
      }
    }
    if (listArray.length > 0) {
      listArray.forEach(item => {
        item[childKeyName] = null;
        delete item[childKeyName];
      });
    }
    return listArray;
  },

  // OPTION_DATABASE_BEGIN
  // async addSiteMessage(type = '', activeUser = '', passiveUser = '', content = '', params = {
  //   targetMediaType: '0',
  //   recordId: '',
  // }) {

  //   try {
  //     const messageObj = {
  //       type,
  //       activeUser: activeUser._id,
  //       passiveUser,
  //       recordId: params.recordId,
  //       isRead: false,
  //     };

  //     if (params.targetMediaType === '0') {
  //       messageObj.content = content;
  //     } else if (params.targetMediaType === '1') {
  //       messageObj.message = content;
  //     } else if (params.targetMediaType === '2') {
  //       messageObj.communityContent = content;
  //     } else if (params.targetMediaType === '3') {
  //       messageObj.communityMessage = content;
  //     }

  //     // const {
  //     //     siteMessageService
  //     // } = require('@service');
  //     // await siteMessageService.create(messageObj);

  //   } catch (error) {
  //     // logUtil.error(error, {});
  //   }
  // },

  // modifyFileByPath(targetPath, replaceStr, targetStr) {
  //   const readText = fs.readFileSync(targetPath, 'utf-8');
  //   if (readText.indexOf(replaceStr) >= 0) {
  //     let reg = new RegExp(replaceStr, 'g');
  //     if (replaceStr.indexOf('path.join') >= 0 || replaceStr.indexOf('"egg-dora') >= 0 || replaceStr.indexOf('"egg-alinode":') >= 0) {
  //       reg = replaceStr;
  //     }
  //     const newRenderContent = readText.replace(reg, targetStr);
  //     fs.writeFileSync(targetPath, newRenderContent);
  //   }
  // },

  // modifyFileByReplace(targetPath, startStr, endStr) {
  //   const readText = fs.readFileSync(targetPath, 'utf-8');
  //   if (readText.indexOf(startStr) >= 0 && readText.indexOf(endStr) >= 0) {
  //     const star = readText.indexOf(startStr);
  //     const end_ = readText.indexOf(endStr) + endStr.length;
  //     const startContent = readText.substr(0, star);
  //     const endContent = readText.substr(end_);
  //     const newRenderContent = startContent + endContent;
  //     fs.writeFileSync(targetPath, newRenderContent);
  //   }
  // },

  // appendTxtToFileByLine(targetPath, line, targetStr) {
  //   const fileData = fs.readFileSync(targetPath, 'utf8').split('\n');
  //   fileData.splice(fileData.length - line, 0, targetStr);
  //   fs.writeFileSync(targetPath, fileData.join('\n'), 'utf8');
  // },

  // OPTION_DATABASE_END
};
module.exports = siteFunc;
