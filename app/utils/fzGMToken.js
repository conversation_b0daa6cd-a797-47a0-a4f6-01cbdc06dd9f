const axios = require('axios');
const fzgmBaseUrl = process.env.FZGM_BASE_URL || ''; // 替换为实际的 FZGM base URL
const fzgmKey = process.env.FZGM_KEY || ''; // 替换为实际的 FZGM key
let fzgmToken = null;

async function getFzGMToken() {
  try {
    if (!fzgmBaseUrl || !fzgmKey) {
      throw new Error('当前环境未配置福州GM相关信息');
    }
    const response = await axios.get(
      `${fzgmBaseUrl}/apisix/plugin/jwt/sign?key=${fzgmKey}`
    );
    fzgmToken = response.data;
    return fzgmToken;
  } catch (error) {
    console.error('请求失败:', error);
    return null; // 或者根据需要返回错误信息
  }
}

module.exports = { getFzGMToken };
