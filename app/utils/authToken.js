const jwt = require('jsonwebtoken');


const token = {

  checkToken(token, encrypt_key) {
    // return new Promise((resolve, reject) => {
    return new Promise(resolve => {
      jwt.verify(token, encrypt_key, function(err, decoded) {
        if (err) {
          console.log('/utils/authToken.js的错误：');
          console.log(err);
          resolve(false);
        } else {
          // console.log('---decoded---', decoded)
          resolve(decoded);
        }
      });
    });
  },

};
module.exports = token;
