

/**
 * 跨区域权限插件
 */
module.exports = function crossRegionPlugin(schema, { ctx }) {
  schema.pre('save', async function(next) {
    if (this.regAdd) {
      if (Array.isArray(this.regAdd) && Array.isArray(this.regAdd[0])) {
        const shortestRegAdd = this.regAdd.reduce((shortest, current) =>
          (current.length < shortest.length ? current : shortest)
        );
        this.crossRegionAdd = this.regAdd;
        this.regAdd = shortestRegAdd;
      } else if (Array.isArray(this.regAdd)) {
        this.crossRegionAdd = [ this.regAdd ];
      }
    }

    if (this.area_code) {
      if (Array.isArray(this.area_code)) {
        // 如果是一维数组，直接存储到 crossRegionArea
        this.crossRegionArea = this.area_code;

        // 用 area_code 数组中的值查询 District 表
        if (this.regAdd && Array.isArray(this.regAdd)) {
          const lastRegAdd = this.regAdd[this.regAdd.length - 1];
          // 查找所有 area_code 在数组中的记录
          const districts = await ctx.model.District.find({
            area_code: { $in: this.area_code },
          });
          // 从结果中找到 name 匹配的记录
          const matchedDistrict = districts.find(d => d.name === lastRegAdd);
          if (matchedDistrict) {
            this.area_code = matchedDistrict.area_code;
          }
        }
      } else {
        // 如果是字符串，转换为一维数组存储到 crossRegionArea，area_code 保持原样
        this.crossRegionArea = [ this.area_code ];
      }
    }

    next();
  });

  const updateHandler = async function() {
    const update = this.getUpdate();
    const $set = update.$set || update;

    if ($set.regAdd) {
      if (Array.isArray($set.regAdd)) {
        if (Array.isArray($set.regAdd[0])) {
          $set.crossRegionAdd = $set.regAdd;
          $set.regAdd = $set.regAdd.reduce((shortest, current) =>
            (current.length < shortest.length ? current : shortest)
          );
        } else {
          $set.crossRegionAdd = [ $set.regAdd ];
        }
      }
    }

    if ($set.area_code) {
      if (Array.isArray($set.area_code)) {
        // 如果是一维数组，直接存储到 crossRegionArea
        $set.crossRegionArea = $set.area_code;

        // 用 area_code 数组中的值查询 District 表
        if ($set.regAdd && Array.isArray($set.regAdd)) {
          const lastRegAdd = $set.regAdd[$set.regAdd.length - 1];
          // 查找所有 area_code 在数组中的记录
          const districts = await ctx.model.District.find({
            area_code: { $in: $set.area_code },
          });
          // 从结果中找到 name 匹配的记录
          const matchedDistrict = districts.find(d => d.name === lastRegAdd);
          if (matchedDistrict) {
            $set.area_code = matchedDistrict.area_code;
          }
        }
      } else {
        // 如果是字符串，转换为一维数组存储到 crossRegionArea，area_code 保持原样
        $set.crossRegionArea = [ $set.area_code ];
      }
    }
  };

  [ 'findOneAndUpdate', 'updateOne', 'updateMany' ].forEach(method => {
    schema.pre(method, updateHandler);
  });

  schema.set('toJSON', {
    transform(doc, ret) {
      if (ret.crossRegionAdd && ret.crossRegionAdd.length > 0) {
        ret.regAdd = ret.crossRegionAdd;
      }
      if (ret.crossRegionArea && ret.crossRegionArea.length > 0) {
        ret.area_code = ret.crossRegionArea;
      }
      return ret;
    },
  });
};
