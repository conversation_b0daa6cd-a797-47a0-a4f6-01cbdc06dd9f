function handleAreaCode(areaCode, branch) {
  if (branch === 'xjbt') {
    return areaCode;
  }
  return areaCode.length === 12 ? areaCode : areaCode.padEnd(12, '0');
}

function districtPlugin(schema, options) {
  const { config } = options;
  const { branch } = config;
  schema.pre('findOne', async function(next) {
    const conditions = this.getQuery();
    if (conditions && conditions.area_code) {
      conditions.area_code = handleAreaCode(conditions.area_code, branch);
    }
    this.setQuery(conditions);
    next();
  });
  schema.pre('find', async function(next) {
    const conditions = this.getQuery();
    if (conditions && conditions.area_code) {
      conditions.area_code = handleAreaCode(conditions.area_code, branch);
    }
    this.setQuery(conditions);
    next();
  });
}
module.exports = districtPlugin;
