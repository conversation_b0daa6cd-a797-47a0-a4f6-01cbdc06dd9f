function regionQueryRedirectPlugin(schema, options = {}) {
  const regionFieldMap = options._regionFieldMap || {}; // 例如：{ regAdd: 'crossRegionAdd' }

  function getNestedValue(obj, path) {
    return path.split('.').reduce((o, k) => (o ? o[k] : undefined), obj);
  }

  function deleteNestedKey(obj, path) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let parent = obj;
    for (const key of keys) {
      if (!parent[key]) return;
      parent = parent[key];
    }
    delete parent[lastKey];
  }

  function transformRegionCondition(targetField, regionArr) {
    if (!Array.isArray(regionArr[0])) {
      // 如果不是二维数组，将其转换为二维数组
      regionArr = [ regionArr ];
    }
    return regionArr.map(region => ({
      [targetField]: {
        $all: region,
      },
    }));
  }

  function handleQuery(query) {
    for (const [ oldField, newField ] of Object.entries(regionFieldMap)) {
      const cond = getNestedValue(query, oldField);
      if (!cond) continue;

      // 处理直接数组查询的情况
      if (Array.isArray(cond)) {
        const orConditions = transformRegionCondition(newField, [ cond ]);
        deleteNestedKey(query, oldField);
        query.$or = (query.$or || []).concat(orConditions);
        continue;
      }

      // 处理$all查询的情况
      if (cond.$all && Array.isArray(cond.$all)) {
        const orConditions = transformRegionCondition(newField, cond.$all);
        deleteNestedKey(query, oldField);
        query.$or = (query.$or || []).concat(orConditions);
        continue;
      }

      // 处理其他查询条件
      if (typeof cond === 'object') {
        const newCond = {};
        newCond[newField] = cond;
        deleteNestedKey(query, oldField);
        Object.assign(query, newCond);
      }
    }
  }

  // find / findOne
  schema.pre([ 'find', 'findOne' ], function(next) {
    handleQuery(this.getQuery());
    next();
  });

  // aggregate
  schema.pre('aggregate', function(next) {
    const pipeline = this.pipeline();
    pipeline.forEach(stage => {
      if (stage.$match) {
        handleQuery(stage.$match);
      }
    });
    next();
  });
}

module.exports = regionQueryRedirectPlugin;
