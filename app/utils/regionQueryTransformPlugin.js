function regionQueryTransformPlugin(schema, options = {}) {
  // 支持字符串或数组形式的字段配置
  const regionFields = Array.isArray(options._regionFields)
    ? options._regionFields
    : [ options._regionFields ];

  // function getNestedValue(obj, path) {
  //   return path.split('.').reduce((o, k) => (o ? o[k] : undefined), obj);
  // }

  // function deleteNestedKey(obj, path) {
  //   const keys = path.split('.');
  //   const lastKey = keys.pop();
  //   let parent = obj;
  //   for (const key of keys) {
  //     if (!parent[key]) return;
  //     parent = parent[key];
  //   }
  //   delete parent[lastKey];
  // }

  function transformRegionCondition(regionField, regionArr) {
    if (!Array.isArray(regionArr[0])) {
      regionArr = [ regionArr ];
    }

    // 使用$or来匹配任意一个区域路径
    return [{
      $or: regionArr.map(region => {
        // 对于每个region数组，我们需要确保area数组包含这些元素作为前缀
        const conditions = region.map((value, index) => {
          // 处理嵌套字段路径，例如 'managersAndArea.AreaRegAddr'
          const fieldPath = `${regionField}.${index}`;
          return { [fieldPath]: value };
        });

        return {
          $and: [
            // 确保数组长度至少等于region长度
            { [`${regionField}.${region.length - 1}`]: { $exists: true } },
            // 确保前缀匹配
            ...conditions,
          ],
        };
      }),
    }];
  }

  function handleQuery(query) {
    if (!regionFields.length) return;

    // 处理每个配置的字段
    regionFields.forEach(regionField => {
      // 处理嵌套对象中的条件
      function processNestedConditions(obj) {
        if (!obj || typeof obj !== 'object') return;

        // 处理数组操作符 ($or, $and, $nor)
        if (Array.isArray(obj)) {
          obj.forEach(item => processNestedConditions(item));
          return;
        }

        // 遍历对象的所有键
        Object.entries(obj).forEach(([ key, value ]) => {
          // 处理特殊操作符
          if (key === '$or' || key === '$and' || key === '$nor') {
            if (Array.isArray(value)) {
              value.forEach(item => processNestedConditions(item));
            }
            return;
          }

          // 如果值是对象且不是数组，递归处理
          if (value && typeof value === 'object' && !Array.isArray(value)) {
            // 检查是否匹配 regionField
            if (key === regionField && value.$all && Array.isArray(value.$all)) {
              if (Array.isArray(value.$all[0])) {
                // 删除原始条件
                delete obj[key];

                // 转换条件
                const regionConditions = transformRegionCondition(key, value.$all);
                
                // 使用$and来组合区域条件和其他条件
                if (!obj.$and) {
                  obj.$and = [];
                }
                obj.$and = obj.$and.concat(regionConditions);
              }
            } else {
              // 继续递归处理嵌套对象
              processNestedConditions(value);
            }
          }

          // 处理数组条件
          if (Array.isArray(value) && (key === regionField)) {
            // 如果是二维数组，转换为 $and 查询，确保满足所有区域条件
            if (Array.isArray(value[0])) {
              const andConditions = transformRegionCondition(key, value);
              delete obj[key];
              if (!obj.$and) obj.$and = [];
              obj.$and = obj.$and.concat(andConditions);
            }
          }
        });
      }

      processNestedConditions(query);
    });
  }

  function handleAggregateStage(stage) {
    // 处理 $match 阶段
    if (stage.$match) {
      handleQuery(stage.$match);
    }

    // 处理 $lookup 中的 pipeline
    if (stage.$lookup && Array.isArray(stage.$lookup.pipeline)) {
      stage.$lookup.pipeline.forEach(subStage => handleAggregateStage(subStage));
    }

    // 处理 $facet 中的子管道
    if (stage.$facet) {
      Object.values(stage.$facet).forEach(subPipeline => {
        if (Array.isArray(subPipeline)) {
          subPipeline.forEach(subStage => handleAggregateStage(subStage));
        }
      });
    }

    // 处理子管道数组
    if (stage.$unionWith && stage.$unionWith.pipeline) {
      stage.$unionWith.pipeline.forEach(subStage => handleAggregateStage(subStage));
    }

    // 处理 $project 或 $addFields 中的条件表达式
    if (stage.$project || stage.$addFields) {
      const fields = stage.$project || stage.$addFields;
      Object.values(fields).forEach(field => {
        if (field && typeof field === 'object') {
          if (field.$match) {
            handleQuery(field.$match);
          }
        }
      });
    }
  }

  // 拦截所有查询操作
  const queryMethods = [ 'find', 'findOne', 'count', 'countDocuments', 'estimatedDocumentCount' ];
  queryMethods.forEach(method => {
    schema.pre(method, { query: true, document: false }, function() {
      const query = this.getQuery();
      handleQuery(query);
    });
  });

  // 拦截 aggregate 查询
  schema.pre('aggregate', function() {
    const pipeline = this.pipeline();
    pipeline.forEach(stage => handleAggregateStage(stage));
  });
}

module.exports = regionQueryTransformPlugin;
