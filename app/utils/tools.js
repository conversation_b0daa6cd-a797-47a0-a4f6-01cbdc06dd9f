const mkdirp = require('mkdirp');
const IDValidator = require('id-validator-modify');
const GB2260 = require('id-validator-modify/src/GB2260');
const tools = {

  convertToEditJson(json) {
    return JSON.parse(JSON.stringify(json));
  },

  makeEnterpriseDir(savePath) {
    mkdirp.sync(savePath);
  },

  /**
 * <AUTHOR>
 * @description 同步adminuser，user，employee三表数据,当前表跟账号无关的业务以及当前表的更新在各自业务自行处理 如果没有手机号就不需要调这个同步方法
 * @param {Object} ctx 上下文对象
 * @param {String} model 要操作的表 adminUser user employee 如果三个表都需要创建并绑定，那么传employee，比如导入企业
 * @param {Object} param2
 * {
 *    _id:String 当前操作表的id, 必填
 *    EnterpriseID:String, 企业id 只有在操作employee的时候才需要用到
 *    type:String,类型 '1':创建 '2':更新 必填
 *    employeeIsRole:Boolean 员工是否是负责人或者管理人员 只有在更新或创建employee的时候需要传
 *    IDNum:String 身份证号 如果model是employee时传 IDNum主要是通过身份证号获取户籍（如果主业务已经获取过了，那么不传），性别等信息然后更新到employee
 * }
 * @return {String} 返回错误提示
 */
  async updateData(ctx, model, {
    _id = '',
    EnterpriseID = '',
    type = '',
    employeeIsRole = false,
    IDNum = '',
  } = {}) {
    let adminUserInfo = {};
    let employeeInfo = {};
    let userInfo = {};
    let gender = '',
      age = 0,
      nativePlace = '';
    if (IDNum) {
      const Validator = new IDValidator(GB2260);
      const getInfo = Validator.getInfo(IDNum);
      gender = getInfo.sex === 0 ? '1' : '0';
      age = getInfo.age;
      nativePlace = getInfo.addr;
    }
    if (type === '2') {
      if (model === 'adminUser') {
        adminUserInfo = await ctx.model.AdminUser.findOne({ _id });
        // 更新employee
        await ctx.model.Employee.updateMany({ _id: { $in: adminUserInfo.employees } }, { $set: { phoneNum: adminUserInfo.phoneNum, IDNum: adminUserInfo.IDcard, name: adminUserInfo.name } });
        if (adminUserInfo.userId) {
          // 查询这个id是否真实存在，避免删除时没有解绑的情况
          userInfo = await ctx.model.User.findOne({ _id: adminUserInfo.userId });
          // if (!userInfo) { // 如果是个废id那就根据手机号查找
          //   userInfo = await ctx.model.User.findOne({ phoneNum: adminUserInfo.phoneNum });
          // }
          if (userInfo) {
            // 更新user
            if (adminUserInfo.password) {
              await ctx.model.User.updateOne({ _id: userInfo._id }, { $set: { password: adminUserInfo.password, phoneNum: adminUserInfo.phoneNum, idNo: adminUserInfo.IDcard, userName: adminUserInfo.userName, name: adminUserInfo.name } });
            } else {
              await ctx.model.User.updateOne({ _id: userInfo._id }, { $set: { phoneNum: adminUserInfo.phoneNum, idNo: adminUserInfo.IDcard, userName: adminUserInfo.userName, name: adminUserInfo.name } });
            }
            // 如果有userInfo，说明userid不是一个废id
            await ctx.model.Employee.updateMany({ userId: userInfo._id }, { $set: { phoneNum: adminUserInfo.phoneNum, IDNum: adminUserInfo.IDcard, name: adminUserInfo.name } });
          }
        }
      } else if (model === 'user') {
        userInfo = await ctx.model.User.aggregate([
          { $match: { _id } },
          { $lookup: {
            from: 'adminusers',
            localField: '_id',
            foreignField: 'userId',
            as: 'adminuser',
          } },
          {
            $lookup: {
              from: 'employees',
              localField: '_id',
              foreignField: 'userId',
              as: 'employees',
            },
          },
          {
            $addFields: { adminUser: { $arrayElemAt: [ '$adminuser', 0 ] }, employeeIds: {
              $map: {
                input: '$employees',
                as: 'employees',
                in: '$$employees._id',
              },
            } },
          },
        ]);
        userInfo = userInfo[0];
        if (userInfo.adminUser) {
          // 更新adminuser
          if (userInfo.password) {
            await ctx.model.AdminUser.updateOne({ _id: userInfo.adminUser._id }, { $set: { password: userInfo.password, phoneNum: userInfo.phoneNum, name: userInfo.name, userName: userInfo.userName, IDcard: userInfo.idNo } });
          } else {
            await ctx.model.AdminUser.updateOne({ _id: userInfo.adminUser._id }, { $set: { phoneNum: userInfo.phoneNum, name: userInfo.name, userName: userInfo.userName, IDcard: userInfo.idNo } });
          }
        }
        // 更新employees
        await ctx.model.Employee.updateMany({ _id: { $in: userInfo.employeeIds } }, { $set: { phoneNum: userInfo.phoneNum, name: userInfo.name, IDNum: userInfo.idNo } });
      }
    }
    if ((type === '1' || type === '2') && model === 'employee') {
      let adminUserId = '';
      let userId = '';
      // 创建adminUser或者user的时候不需要绑定别的表，所以这里不考虑,创建和更新只考虑employee
      employeeInfo = await ctx.model.Employee.aggregate([
        { $match: {
          _id,
        } },
        { $lookup: {
          from: 'adminusers',
          localField: '_id',
          foreignField: 'employees',
          as: 'adminUser',
        } },
        {
          $addFields: {
            adminUser: { $arrayElemAt: [ '$adminUser', 0 ] } },
        },
      ]);
      employeeInfo = employeeInfo[0];
      // 更新并在employee绑定user
      if (employeeInfo.userId) {
        // 如果employee绑定了userid 更新user
        userInfo = await ctx.model.User.findOneAndUpdate({ _id: employeeInfo.userId }, { $set: { phoneNum: employeeInfo.phoneNum, name: employeeInfo.name, userName: employeeInfo.phoneNum, idNo: employeeInfo.IDNum } }, { new: true });
      } else {
        // 没绑定
        // 根据手机号查找
        userInfo = await ctx.model.User.findOne({ phoneNum: employeeInfo.phoneNum });
        if (userInfo) {
          await ctx.model.User.updateOne({ phoneNum: employeeInfo.phoneNum }, { $set: { phoneNum: employeeInfo.phoneNum, name: employeeInfo.name, userName: employeeInfo.phoneNum, idNo: employeeInfo.IDNum } });
        } else {
          // 没有这个电话号码的user，那么就创建
          userInfo = await ctx.model.User.create({ countryCode: '86', phoneNum: employeeInfo.phoneNum, name: employeeInfo.name, userName: employeeInfo.phoneNum, idNo: employeeInfo.IDNum });
        }
      }
      userId = userInfo._id;
      // 更新userid对应的所有employee
      if (IDNum) {
        await ctx.model.Employee.updateMany({ userId: employeeInfo.userId || userInfo._id }, { $set: {
          age, nativePlace, gender,
        } });
      }

      // 更新绑定 不管是否是普通员工，都需要更新绑定user,只要是手机号一样的employee 都绑上userid
      await ctx.model.Employee.updateMany({ phoneNum: employeeInfo.phoneNum }, { $set: { userId: employeeInfo.userId || userInfo._id } });
      if (employeeInfo.adminUser) {
        adminUserId = employeeInfo.adminUser._id;
        // 如果有就更新
        // 更新amdminUser,并绑定userid
        adminUserInfo = await ctx.model.AdminUser.findOneAndUpdate({ _id: adminUserId }, { $set: { userId: employeeInfo.userId || userInfo._id, phoneNum: employeeInfo.phoneNum, name: employeeInfo.name, userName: employeeInfo.phoneNum, IDcard: employeeInfo.IDNum } }, { new: true });
        // 更新adminorg
        // await ctx.model.Adminorg.updateOne({ EnterpriseID }, { $addToSet: { adminArray: adminUserId } });
      } else if (employeeIsRole) { // adminuser没有绑定当前employee,并且还是管理人员或者负责人的话
        // 通过手机号查询adminuser
        adminUserInfo = await ctx.model.AdminUser.findOne({ phoneNum: employeeInfo.phoneNum });
        if (adminUserInfo) { // 有这个手机号的adminuser,那么就根据电话号码更新
          await ctx.model.AdminUser.updateOne({ _id: adminUserInfo._id }, { $set: { phoneNum: employeeInfo.phoneNum, userId: employeeInfo.userId || userInfo._id, name: employeeInfo.name, userName: employeeInfo.phoneNum, IDcard: employeeInfo.IDNum } });
          // 给这个adminuser绑定employees和userId
          await ctx.model.AdminUser.updateOne({ _id: adminUserInfo._id }, { $addToSet: { employees: employeeInfo._id } });
        } else { // 没有这个手机号的adminuser,那么创建adminuser
          adminUserInfo = await ctx.model.AdminUser.create({ countryCode: '86', employees: [ employeeInfo._id ], userId: employeeInfo.userId || userInfo._id, phoneNum: employeeInfo.phoneNum, name: employeeInfo.name, userName: employeeInfo.phoneNum, IDcard: employeeInfo.IDNum });
        }
        adminUserId = adminUserInfo._id;
        // 更新adminorg
        if (!(await ctx.model.Adminorg.findOne({ _id: EnterpriseID })).adminUserId) {
          await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $set: { adminUserId: adminUserInfo._id } });
        }
        await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $addToSet: { adminArray: adminUserInfo._id } });
      } else {
        // 普通员工，查看注册的账号
        adminUserInfo = await ctx.model.AdminUser.findOne({ phoneNum: employeeInfo.phoneNum });
        if (adminUserInfo && ((!employeeInfo.IDNum || !adminUserInfo.IDcard) || adminUserInfo.IDcard === employeeInfo.IDNum)) {
          // 如果有电话号码情况下 身份证信息也对上了 那么就可以关联这个adminuser
          // 如果手机号对上了 没有身份证 那只有直接关联了
          // 更新注册的adminUser
          const adminorg = await ctx.model.Adminorg.findOne({ _id: EnterpriseID });
          if (adminorg) {
            if (!adminorg.adminUserId) {
              await ctx.model.AdminUser.updateOne({ _id: adminUserInfo._id }, { $set: { phoneNum: employeeInfo.phoneNum, userId: employeeInfo.userId || userInfo._id, name: employeeInfo.name, userName: employeeInfo.phoneNum, IDcard: employeeInfo.IDNum } });
              // 给这个adminuser绑定employees和userId
              await ctx.model.AdminUser.updateOne({ _id: adminUserInfo._id }, { $addToSet: { employees: employeeInfo._id } });
              // 更新adminorg
              await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $set: { adminUserId: adminUserInfo._id } });
              adminUserId = adminUserInfo._id;
            } else if (adminorg.adminUserId === adminUserInfo._id) {
              await ctx.model.AdminUser.updateOne({ _id: adminUserInfo._id }, { $set: { phoneNum: employeeInfo.phoneNum, userId: employeeInfo.userId || userInfo._id, name: employeeInfo.name, userName: employeeInfo.phoneNum, IDcard: employeeInfo.IDNum } });
              // 给这个adminuser绑定employees和userId
              await ctx.model.AdminUser.updateOne({ _id: adminUserInfo._id }, { $addToSet: { employees: employeeInfo._id } });
              adminUserId = adminUserInfo._id;
            }
          }
        }
      }
      // 同步更新密码
      if (adminUserInfo) {
        if (adminUserInfo.password) {
          await ctx.model.User.updateOne({ _id: userInfo._id }, { $set: { password: adminUserInfo.password } });
        } else if (userInfo.password) {
          await ctx.model.AdminUser.updateOne({ _id: adminUserInfo._id }, { $set: { password: userInfo.password } });
        }
      }
      return { adminUserId, userId };
    }
  },

  /**
 * <AUTHOR>
 * @description 校验手机号和身份证号
 * @param {Object} ctx 上下文对象
 * @param {String} model 要操作的表 adminUser user employee 如果三个表都需要创建并绑定，那么传employee，比如导入企业
 * @param {Object} param2
 * {
 *    _id:String 当前操作表的id 更新一定要传 创建不传
 *    EnterpriseID:String, 企业id 只有在操作employee的时候才需要用到
 *    phoneNum:String, 需要修改得电话号码 当有手机号的操作时传，
 *    oldPhoneNum:String 修改前得电话号码 创建的时候不传 当有手机号的操作时传，
 *    IDNum:String 身份证号 ①当有关身份证得操作时传 ②当model为employee得时候传，因为我要校验employee对应的adminuser是否是同一个，即手机号加身份证双重校验
 *    oldIDNum:String 修改前的身份证  ①当有关身份证得操作时传 ②当model为employee得时候传，因为我要校验employee对应的adminuser是否是同一个，即手机号加身份证双重校验
 *    employeeIsRole:Boolean  员工是否是负责人或者管理人员 只有在更新或创建employee的时候需要传
  * }
 * @return {String} 返回错误提示
 *    101:手机号再adminuser里面被占用
 *    100:手机号格式错误
 *    102:手机号再user里面被占用
 *    103:手机号再employee里面被占用
 *    300:身份证号码格式错误
 *    304:身份证无效
 *    301:身份证号再adminuser被占用
 *    302:身份证号再user被占用
 *    303:身份证号再employee被占用
 */
  async validPhoneAndIDNum(ctx, model, {
    _id = '',
    EnterpriseID = '',
    phoneNum = '',
    oldPhoneNum = '',
    IDNum = '',
    oldIDNum = '',
    employeeIsRole = false,
  } = {}) {
    let sameAdminuser = {},
      sameUser = {},
      sameEmployee = {},
      employeeInfo = {},
      userId = '',
      adminUserId = '';
    IDNum = (IDNum + '').trim();
    oldIDNum = (oldIDNum + '').trim();
    phoneNum = (phoneNum + '').trim();
    oldPhoneNum = (oldPhoneNum + '').trim();
    if (IDNum) {
      if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(IDNum)) return '300:身份证号码格式错误';
      const Validator = new IDValidator(GB2260);
      const isValid = Validator.isValid(IDNum);
      if (!isValid) {
        return '304:该身份证号无效，请检查';
      }
      if (IDNum !== oldIDNum) {
        if (model === 'adminUser' || model === 'user') {
          if (model === 'adminUser') {
            sameAdminuser = await ctx.model.AdminUser.findOne({ IDcard: IDNum });
            if (sameAdminuser) {
              return '301:身份证已被占用';
            }
          } else if (model === 'user') {
            sameUser = await ctx.model.User.findOne({ idNo: IDNum });
            if (sameUser) {
              return '302:身份证已被占用';
            }
          }
        } else if (model === 'employee') {
          if (_id) {
            employeeInfo = await ctx.model.Employee.aggregate([
              { $match: {
                _id,
              } },
              { $lookup: {
                from: 'adminusers',
                localField: '_id',
                foreignField: 'employees',
                as: 'adminuser',
              } },
              {
                $addFields: { adminUser: { $arrayElemAt: [ '$adminuser', 0 ] } },
              },
            ]);
            employeeInfo = employeeInfo[0];
            if (employeeInfo.adminUser) {
              adminUserId = employeeInfo.adminUser._id;
            }
            userId = employeeInfo.userId || '';
            if (userId) {
              sameUser = await ctx.model.User.findOne({ idNo: IDNum });
              if (sameUser) {
                return '302:身份证已被占用';
              }
            }
            if (adminUserId) {
              sameAdminuser = await ctx.model.AdminUser.findOne({ IDcard: IDNum });
              if (sameAdminuser) {
                return '301:身份证已被占用';
              }
            }
          }
          sameEmployee = await ctx.model.Employee.findOne({ EnterpriseID, IDNum, enable: true });
          if (sameEmployee) {
            return '303:身份证已被 ' + sameEmployee.name + ' 占用';
          }
        }
      }
    }
    // 如果电话号码没改那就不校验电话号码
    if (phoneNum && oldPhoneNum !== phoneNum) {
      if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(phoneNum)) return '100:电话号码格式错误';
      if (model === 'adminUser' || model === 'user') {
        if (model === 'adminUser') {
          sameAdminuser = await ctx.model.AdminUser.findOne({ phoneNum });
          if (sameAdminuser) {
            return '101:您的手机号已被其他账号注册，请检查手机号码是否准确';
          }
        } else if (model === 'user') {
          sameUser = await ctx.model.User.findOne({ phoneNum });
          if (sameUser) {
            return '102:您的手机号已被其他移动端账号注册，请检查手机号码是否准确';
          }
        }
      } else if (model === 'employee') {
        sameUser = await ctx.model.User.findOne({ phoneNum });
        sameAdminuser = await ctx.model.AdminUser.findOne({ phoneNum });
        if (_id) {
          employeeInfo = await ctx.model.Employee.aggregate([
            { $match: {
              _id,
            } },
            { $lookup: {
              from: 'adminusers',
              localField: '_id',
              foreignField: 'employees',
              as: 'adminuser',
            } },
            {
              $addFields: { adminUser: { $arrayElemAt: [ '$adminuser', 0 ] } },
            },
          ]);
          employeeInfo = employeeInfo[0];
          if (employeeInfo.adminUser) {
            adminUserId = employeeInfo.adminUser._id;
          }
          userId = employeeInfo.userId || '';
          if (userId) {
            if (sameUser) {
              return '102:您的手机号已被其他移动端账号注册，请检查手机号码是否准确';
            }
          } else if (sameUser && sameUser.idNo && oldIDNum && sameUser.idNo !== oldIDNum) { // 未绑定user
            // 手机号一样，但是身份证号不一致，判定不是同一个人
            return '102:您的手机号已被其他移动端账号注册，该账号身份证信息与您当前的身份证信息不匹配，请检查手机号和身份证号是否准确';
          }
          if (adminUserId) {
            if (sameAdminuser) {
              return '101:您的手机号已被其他账号注册，请检查手机号码是否准确';
            }
          } else if (employeeIsRole && sameAdminuser && sameAdminuser.IDcard && IDNum && sameAdminuser.IDcard !== IDNum) { // 未绑定adminuser
            // 手机号一样，但是身份证号不一致，判定不是同一个人
            return '101:您的手机号已被其他账号注册，该账号身份证信息与您当前的身份证信息不匹配，请检查手机号和身份证号是否准确';
          }
        } else { // 在employee未绑定adminuser和user得情况下
          // 如果没有身份证号，那么employee只能靠手机号去关联adminuser了
          if (employeeIsRole && sameAdminuser && sameAdminuser.IDcard && IDNum && sameAdminuser.IDcard !== IDNum) {
            // 手机号一样，但是身份证号不一致，判定不是同一个人
            return '101:您的手机号已被其他账号注册，该账号身份证信息与您当前的身份证信息不匹配，请检查手机号和身份证号是否准确';
          }
          // 如果没有身份证号，那么employee只能靠手机号去关联user了
          if (sameUser && sameUser.idNo && IDNum && sameUser.idNo !== IDNum) {
          // 手机号一样，但是身份证号不一致，判定不是同一个人
            return '102:您的手机号已被其他移动端账号注册，该账号身份证信息与您当前的身份证信息不匹配，请检查手机号和身份证号是否准确';
          }
        }
        sameEmployee = await ctx.model.Employee.findOne({ EnterpriseID, phoneNum, enable: true });
        if (sameEmployee) {
          return '103:经系统检测您的手机号已被 ' + sameEmployee.name + ' 占用';
        }
      }
    }

  },
};
module.exports = tools;

