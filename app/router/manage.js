// const { convertToEditJson } = require('../utils/tools');

module.exports = app => {

  const { router, controller, config, middleware } = app;
  // 检索代号：#0001 后台管理根目录 例如：config.user_base_path
  router.get([ `${config.admin_base_path}`, `${config.qy_base_path}`, `${config.user_base_path}` ], controller.api.admin.index);

  // 后台管理界面  qy  user
  router.get([ `${config.admin_base_path}/:page`, `${config.admin_base_path}/:page/:page1`, `${config.admin_base_path}/:page/:page1/:id` ], controller.manage.adminUser.dashboard);

  router.get([ `${config.qy_base_path}/:page`, `${config.qy_base_path}/:page/:page1`, `${config.qy_base_path}/:page/:page1/:id` ], controller.manage.adminUser.dashboard);

  router.get([ `${config.user_base_path}/:page`, `${config.user_base_path}/:page/:page1`, `${config.user_base_path}/:page/:page1/:id` ], controller.manage.adminUser.dashboard);

  // 管理员退出
  router.get('/manage/logout', controller.manage.adminUser.logOutAction);

  // 获取管理员信息
  router.get('/manage/getUserSession', controller.manage.adminUser.getUserSession);

  // 获取后台基础信息
  router.get('/manage/getSitBasicInfo', controller.manage.adminUser.getBasicSiteInfo);

  /**
   * 每个企业的主页
   */
  // 得到图表数据
  router.post('/manage/enterpriseHomeBack/getChartData', controller.manage.enterpriseHome.getChartData);
  // 初始化或更新统计表
  router.get('/manage/enterpriseHomeBack/setStatistical', controller.manage.enterpriseHome.setStatistical);
  // 获取代办事项的数据
  router.get('/manage/enterpriseHomeBack/getBasicMessage', controller.manage.enterpriseHome.getBasicMessage);
  // 获取员工性别比例，职业卫生负责人等信息
  router.get('/manage/enterpriseHomeBack/getMainData', controller.manage.enterpriseHome.getMainData);
  // 获取评估信息
  router.get('/manage/enterpriseHomeBack/getAssessmentInfo', controller.manage.enterpriseHome.getAssessmentInfo);
  // 获取危害申报情况
  router.get('/manage/enterpriseHomeBack/hazardDeclaration', controller.manage.enterpriseHome.hazardDeclaration);
  router.get('/manage/findSupervision', controller.manage.home.findSupervision);
  router.get('/manage/warningEvent', controller.manage.home.warningEvent);
  router.get('/manage/harmTotal', controller.manage.home.harmTotal);


  /**
   * 发送消息的api
   */
  router.post('/manage/sendMessage/send', controller.manage.messageNotification.addNew);
  router.post('/manage/sendMessage/getList', controller.manage.messageNotification.findMessageList); // 通知记录
  router.get('/manage/getGroupID', controller.manage.messageNotification.getAllGroup);
  router.post('/manage/sendMessage/changeState', controller.manage.messageNotification.changeState); // 删除通知
  router.post('/manage/sendMessage/sendSms', controller.manage.messageNotification.sendSms); // 短信提醒
  router.post('/api/sendMessage/uploadFiles', controller.manage.messageNotification.uploadFiles); // 上传通知文件
  // 短信相关
  router.get('/manage/sendMessage/getSmsTemplateList', controller.manage.messageNotification.getSmsTemplateList); // 短信记录
  router.post('/manage/sendMessage/addSmsTemplate', controller.manage.messageNotification.addSmsTemplate); // 添加短信模板
  router.post('/manage/sendMessage/deleteSmsTemplate', controller.manage.messageNotification.deleteSmsTemplate); // 删除短信模板
  router.post('/manage/sendMessage/modifySmsTemplate', controller.manage.messageNotification.modifySmsTemplate); // 修改短信模板
  router.get('/manage/sendMessage/getSmsSignList', controller.manage.messageNotification.getSmsSignList); // 获取签名列表

  // 获取未读消息
  router.get('/manage/getUnreadMessages', controller.manage.home.getUnreadMessages);
  // 获取已读消息
  router.get('/manage/getHasReadMessages', controller.manage.home.getHasReadMessages);
  // 标记已读消息
  router.post('/manage/readMessage', controller.manage.home.readMessage);
  router.get('/manage/getPersonInfo', controller.manage.home.getPersonInfo);
  router.post('/api/mySystemMessage', controller.manage.home.mySystemMessage);
  // 获取辖区企业
  router.get('/manage/sendMessage/getAllOrgList', controller.manage.messageNotification.getAllOrgList);
  router.get('/manage/sendMessage/getMsgOne', controller.manage.messageNotification.getMsgOne); // 获取阅读信息
  /**
   * 角色管理
   */
  router.get('/manage/adminGroup/getList', controller.manage.adminGroup.list);

  router.get('/manage/adminGroup/getOne', controller.manage.adminGroup.getOne);

  router.post('/manage/adminGroup/addOne', controller.manage.adminGroup.create);

  router.post('/manage/adminGroup/updateOne', controller.manage.adminGroup.update);

  router.get('/manage/adminGroup/deleteGroup', controller.manage.adminGroup.removes);

  // 鉴权用，勿删！
  router.get('/manage/adminResource/getListByPower', controller.manage.adminResource.listByPower);


  /**
   * 系统配置
   * 此api名称尽量不要改
   */
  //   router.get('/manage/systemConfig/getConfig', controller.manage.systemConfig.list);

  //   router.post('/manage/systemConfig/updateConfig', controller.manage.systemConfig.update);

  // ManageRouters

  // dashboard
  router.post('/manage/getWkzwyDashboardData', controller.manage.home.getWkzwyDashboardData); // 卫康职位云大屏数据
  router.post('/manage/newDashboardData', controller.manage.home.newDashboardData); // xxn add 首页统计数据
  router.post('/manage/getDashboardData', controller.manage.home.getDashboardData);
  router.post('/manage/getdDstrictsData', controller.manage.home.getdDstrictsData);
  router.get('/manage/getTownScope', controller.manage.home.getTownScope);
  router.post('/manage/jurisdictionList', controller.manage.home.jurisdictionList);
  router.post('/manage/selectArea', controller.manage.home.selectArea);
  router.post('/manage/searchCompany', controller.manage.home.searchCompany);
  router.post('/manage/loadMoreCompany', controller.manage.home.loadMoreCompany);
  router.post('/manage/preventAssessFindAll', controller.manage.home.preventAssessFindAll); // 防止自查结果

  router.post('/manage/getVisualizationData', controller.manage.home.getVisualizationData); // 获取大屏所需的数据
  // router.post('/manage/refreshData', controller.manage.home.refreshData);
  // 得到 职业病体检企业数量（率） 表 所需的数据
  router.post('/manage/getPhysicalExaminationChartData', controller.manage.home.getPhysicalExaminationChartData);
  // router.get('/manage/dataHandle', controller.manage.home.dataHandle);
  router.post('/manage/getServiceProject', controller.manage.home.getServiceProject);
  // 投诉举报
  router.post('/manage/complaints/getBySuperId', controller.manage.complaints.getBySuperId);
  router.post('/manage/complaints/update', controller.manage.complaints.update);
  // 监管单位 - 管理成员
  router.post('/manage/user/addMember', controller.manage.adminUser.addMember);
  router.post('/manage/user/delMember', controller.manage.adminUser.delMember);
  // router.post('/manage/user/updateMember', controller.manage.adminUser.updateMember);
  router.get('/manage/user/getMembers', controller.manage.adminUser.getMembers);
  // 更新当前用户信息
  router.post('/manage/user/update', controller.manage.adminUser.update);

  // 行政用户管理
  router.post('/manage/superUser/getList', controller.manage.superUser.list);
  router.get('/manage/superUser/getOne', controller.manage.superUser.getOne);
  router.post('/manage/superUser/addOne', controller.manage.superUser.create);
  router.post('/manage/superUser/updateOne', controller.manage.superUser.update);
  router.get('/manage/superUser/deleteUser', controller.manage.superUser.removes);
  router.get('/manage/superUser/getDistrict', controller.manage.superUser.getAdministrativeDivision);
  router.post('/manage/superUser/editPower', controller.manage.superUser.editPower);
  router.get('/manage/adminGroup/getSuperGroupID', controller.manage.superUser.getDefaultGroup);
  router.get('/manage/adminResource/superPowers', controller.manage.adminResource.superPowers); // 获取当前监管端的菜单权限和资源
  // 退回功能
  router.post('/manage/projectGiveBack/uploadImage', controller.manage.projectGiveBack.uploadImage);
  router.post('/manage/projectGiveBack/giveBack', controller.manage.projectGiveBack.giveBack);
  router.post('/manage/projectGiveBack/giveBackDelImgs', controller.manage.projectGiveBack.giveBackDelImgs);
  router.post('/manage/projectGiveBack/giveBackRecord', controller.manage.projectGiveBack.giveBackRecord);
  // 空请求
  router.get('/manage/emptyRequest', controller.api.admin.emptyRequest);

  router.post('/manage/recordMake/autoLogin', controller.manage.home.autoLogin);
  // 课程管理
  router.get('/manage/courses/getClassList', controller.manage.courses.getClassification);
  router.post('/manage/courses/addClass', controller.manage.courses.createClassification);
  router.post('/manage/courses/updateClass', controller.manage.courses.updateClassification);
  router.post('/manage/courses/deleteClass', controller.manage.courses.deleteClassification);
  router.post('/manage/courses/createCourse', controller.manage.courses.createCourse);
  router.post('/manage/courses/updateCourse', controller.manage.courses.updateCourse);
  router.get('/manage/courses/getCoursesList', controller.manage.courses.getCoursesList);
  router.get('/manage/courses/getCourseOne', controller.manage.courses.getCourseOne);
  router.post('/manage/courses/insertContent', controller.manage.courses.insertContent);
  router.post('/manage/courses/updateContent', controller.manage.courses.updateContent);
  router.post('/manage/courses/deleteContent', controller.manage.courses.deleteContent);
  router.post('/manage/courses/uploadEditorImage', controller.manage.courses.uploadEditorImage);
  router.get('/manage/courses/getQuestionBank', controller.manage.courses.getQuestionBank);
  router.get('/manage/courses/getAllVideos', controller.manage.courses.getAllVideos);
  router.post('/manage/courses/editContent', controller.manage.courses.editContent);
  router.get('/manage/courses/deleteCourse', controller.manage.courses.deleteCourse);
  router.get('/manage/courses/findPlanQuote', controller.manage.courses.findPlanQuote);
  router.post('/manage/courses/getAllOrgs', controller.manage.courses.getAllOrgs); // 获取所有端的机构
  router.post('/manage/courses/saveCoursePower', controller.manage.courses.saveCoursePower);
  // 用于视频上传至阿里云，获取上传凭证或者超时刷新凭证账号ID
  router.post('/manage/courses/getUploadAuth/withID', controller.manage.courses.getUploadAuthWithID);
  router.post('/manage/courses/getUploadAuth/withOutID', controller.manage.courses.getUploadAuthWithOutID);
  router.get('/manage/courses/getVideoClassification', controller.manage.courses.getVideoClassification);
  router.get('/manage/courses/GetVideoPlayAuth', controller.manage.courses.GetVideoPlayAuth);
  router.get('/manage/courses/getUserID', controller.manage.courses.getUserID);
  router.get('/manage/superUser/checkPhoneLog', controller.manage.superUser.checkPhoneLog);
  router.get('/manage/superUser/checkIDNumLog', controller.manage.superUser.checkIDNumLog);
  router.get('/manage/superUser/checkNameLog', controller.manage.superUser.checkNameLog);
  // 数据大屏sse
  router.get('/sse', middleware.sse(), controller.manage.sseNotice.sse);
  // 云监督sse
  router.get('/sseCloud/:channel', middleware.cloudSse(), controller.manage.sseCloud.sse);
  // 视频审核
  router.get('/videoReview/:meetingId', controller.manage.home.getVideoReviewPage);

  router.get('/manage/decryptField', controller.manage.commonApi.decryptField); // 解密字段
  // router.get('/manage/testCrypt', controller.manage.commonApi.test); // 测试接口
  // 云监督视频审核
  router.get('/cloudSupervision/:meetingId', controller.manage.home.getCloudSupervisionPage);
  // 问卷系统用户校验
  router.get('/manage/checkWjUser', controller.manage.home.checkWjUser);
  // 职业健康专家分类
  router.get('/manage/expert/classifier', controller.manage.expertClassifier.classifierList);
  router.post('/manage/expert/classifier', controller.manage.expertClassifier.addClassifier);
  router.put('/manage/expert/classifier', controller.manage.expertClassifier.updateClassifier);
  router.delete('/manage/expert/classifier', controller.manage.expertClassifier.deleteClassifier);
  // 职业健康专家管理
  router.get('/manage/expert', controller.manage.expert.list);
  router.post('/manage/expert', controller.manage.expert.add);
  router.put('/manage/expert', controller.manage.expert.update);
  router.get('/manage/expert/detail', controller.manage.expert.detail);
  router.get('/manage/expert/certificate', controller.manage.expertCertificate.list);
  router.post('/manage/expert/certificate', controller.manage.expertCertificate.add);
  // router.put('/manage/expert/certificate', controller.manage.expertCertificate.update);
  router.delete('/manage/expert/certificate', controller.manage.expertCertificate.delete);
  router.post('/manage/expert/recommend', controller.manage.expert.recommend); // 专家推荐
  router.post('/manage/expert/dismissal', controller.manage.expert.dismissal); // 专家解聘
  router.get('/manage/expert/reviewList', controller.manage.expert.reviewList); // 专家审核列表
  router.put('/manage/expert/declarationReview', controller.manage.expert.declarationReview); // 专家申报审核
  router.get('/manage/expert/extractionList', controller.manage.expert.extractionList); // 专家抽取记录
  router.get('/manage/expert/work/list', controller.manage.expert.workList); // 专家工作任务列表
  // 职业健康专家统计
  router.get('/manage/expertStatistics/byType', controller.manage.expertStatistics.statisticsByType);
  router.get('/manage/expertStatistics/byAge', controller.manage.expertStatistics.statisticsByAge);
  router.get('/manage/expertStatistics/byEducation', controller.manage.expertStatistics.statisticsByEducation);
  router.get('/manage/expertStatistics/byMobility', controller.manage.expertStatistics.statisticsByMobility);

  // 职业病诊断
  router.get('/manage/diagnosis/getDiaList', controller.manage.diagnosis.getDiaList);
  router.get('/manage/diagnosis/getDiagnosisTemplate', controller.manage.diagnosis.getDiagnosisTemplate);
  router.post('/manage/diagnosis/addDiagnosisTemplate', controller.manage.diagnosis.addDiagnosisTemplate);
  router.get('/manage/diagnosis/detailDiagnosisTemplate', controller.manage.diagnosis.detailDiagnosisTemplate);
  router.put('/manage/diagnosis/editDiagnosisTemplate', controller.manage.diagnosis.editDiagnosisTemplate);
  router.delete('/manage/diagnosis/deleteDiagnosisTemplate', controller.manage.diagnosis.deleteDiagnosisTemplate);
  router.get('/manage/diagnosis/getDiagnosisTemplateCategory', controller.manage.diagnosis.getDiagnosisTemplateCategory);
  router.post('/manage/diagnosis/addDiagnosisTemplateCategory', controller.manage.diagnosis.addDiagnosisTemplateCategory);
  router.put('/manage/diagnosis/editDiagnosisTemplateCategory', controller.manage.diagnosis.editDiagnosisTemplateCategory);
  router.delete('/manage/diagnosis/deleteDiagnosisTemplateCategory', controller.manage.diagnosis.deleteDiagnosisTemplateCategory);
  router.get('/manage/diagnosis/statisticDiagnosisByYear', controller.manage.diagnosis.statisticDiagnosisByYear);
  router.get('/manage/diagnosis/statisticDiagnosisByDiseaseAndYear', controller.manage.diagnosis.statisticDiagnosisByDiseaseAndYear);
  router.get('/manage/diagnosis/getDiagnosisInstitution', controller.manage.diagnosis.getDiagnosisInstitution);
  router.get('/manage/diagnosis/statisticDiagnosisByConclusionAndYear', controller.manage.diagnosis.statisticDiagnosisByConclusionAndYear);
  router.get('/manage/diagnosis/getDictData', controller.manage.diagnosis.getDictData);
  router.get('/manage/diagnosis/getDiseaseByInstitutionId', controller.manage.diagnosis.getDiseaseByInstitutionId);


  // 申报查询申报统计
  router.get('/manage/xjbt/declarationStatistics', controller.manage.xjbtOB.getDeclarationStatistics);

  // 职业病鉴定
  router.get('/manage/identify/getIdentificationList', controller.manage.identify.getIdentificationList);
  router.get('/manage/identify/statisticByYear', controller.manage.identify.statisticByYear);
  router.get('/manage/identify/statisticByResultAndYear', controller.manage.identify.statisticByResultAndYear);
  router.get('/manage/identify/getInstitutionInfo', controller.manage.identify.getInstitutionInfo);

  // 新疆大屏数据展示
  router.get('/manage/display/researchData', controller.manage.display.researchData);
  // 就诊预约相关路由
  router.get('/manage/display/appointmentByArea', controller.manage.display.appointmentByArea);
  router.get('/manage/display/appointmentByDiseaseType', controller.manage.display.appointmentByDiseaseType);
  router.get('/manage/display/appointmentByIndustry', controller.manage.display.appointmentByIndustry);
  router.get('/manage/display/appointmentByStation', controller.manage.display.appointmentByStation);

  // 诊疗服务相关路由
  router.get('/manage/display/treatmentInformationCountByArea', controller.manage.display.treatmentInformationCountByArea);
  router.get('/manage/display/treatmentInformationCountByDiseaseType', controller.manage.display.treatmentInformationCountByDiseaseType);
  router.get('/manage/display/treatmentInformationCountByIndustry', controller.manage.display.treatmentInformationCountByIndustry);
  router.get('/manage/display/treatmentInformationCountByStation', controller.manage.display.treatmentInformationCountByStation);

  // 用药服务相关路由
  router.get('/manage/display/averagePriceByArea', controller.manage.display.averagePriceByArea);
  router.get('/manage/display/averagePriceByDiseaseType', controller.manage.display.averagePriceByDiseaseType);
  router.get('/manage/display/averagePriceByIndustry', controller.manage.display.averagePriceByIndustry);
  router.get('/manage/display/averagePriceByStation', controller.manage.display.averagePriceByStation);

  router.get('/manage/display/medicationGuidanceCountByArea', controller.manage.display.medicationGuidanceCountByArea);
  router.get('/manage/display/medicationGuidanceCountByDiseaseType', controller.manage.display.medicationGuidanceCountByDiseaseType);
  router.get('/manage/display/medicationGuidanceCountByIndustry', controller.manage.display.medicationGuidanceCountByIndustry);
  router.get('/manage/display/medicationGuidanceCountByMedicineType', controller.manage.display.medicationGuidanceCountByMedicineType);
  router.get('/manage/display/medicationGuidanceCountByStation', controller.manage.display.medicationGuidanceCountByStation);

  // 随访记录相关路由
  router.get('/manage/display/followUpByArea', controller.manage.display.followUpByArea);
  router.get('/manage/display/followUpByDiseaseType', controller.manage.display.followUpByDiseaseType);
  router.get('/manage/display/followUpByIndustry', controller.manage.display.followUpByIndustry);
  router.get('/manage/display/followUpByStage', controller.manage.display.followUpByStage);

  // 康复指导相关路由
  router.get('/manage/display/recoveryInfoByArea', controller.manage.display.recoveryInfoByArea);
  router.get('/manage/display/recoveryInfoByDiseaseType', controller.manage.display.recoveryInfoByDiseaseType);
  router.get('/manage/display/recoveryInfoByIndustry', controller.manage.display.recoveryInfoByIndustry);
  router.get('/manage/display/recoveryInfoByStation', controller.manage.display.recoveryInfoByStation);


};
