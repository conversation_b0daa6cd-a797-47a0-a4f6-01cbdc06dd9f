module.exports = app => {

  const {
    router,
    controller,
    config,
  } = app;
  const branch = config.branch && config.branch !== 'master' ? config.branch : ''; // 分支
  router.get('/xjbtcallback', controller.api.admin.xjbtCallback);

  // console.log(55555555, branch);
  router.get('/', branch && controller.api.admin[branch] ? controller.api.admin[branch] : controller.api.admin.login);

  router.get([ '/jk-admin', `${config.admin_base_path}/login`, `${config.qy_base_path}/login`, `${config.user_base_path}/login` ], controller.api.admin.login);

  router.post([ `/api${config.admin_base_path}/doLogin`, `/api${config.qy_base_path}/doLogin`, `/api${config.user_base_path}/doLogin` ], controller.api.admin.loginAction);

  router.post([ `/api${config.admin_base_path}/sendVerificationCode`, `/api${config.qy_base_path}/sendVerificationCode`, `/api${config.user_base_path}/sendVerificationCode` ], controller.api.admin.sendVerificationCode);

  router.post([ `/api${config.admin_base_path}/SmdoLogin`, `/api${config.qy_base_path}/SmdoLogin`, `/api${config.user_base_path}/SmdoLogin` ], controller.api.admin.smloginAction);

  router.post([ `/api${config.admin_base_path}/updateInfo`, `/api${config.qy_base_path}/updateInfo`, `/api${config.user_base_path}/updateInfo` ], controller.api.admin.updateInfo);

  router.get('/api/address/list', controller.api.systemConfig.addressList);
  // 企业详细信息页面上传营业执照
  router.post('/api/enterprise/upload/images', controller.api.systemConfig.uploadEnterpriseImage);

  // 单点登录 - 对接释普
  router.post('/api/admin/signIn', controller.api.admin.signIn);
  // 单点登录 - 对接福州
  router.post('/api/admin/fzSignIn', controller.api.admin.fzSignIn);
  // 判断数据库连接断开问题
  router.get('/api/heartbeat', controller.api.admin.adoConnection);

  // 企业、行政、运营用户头像上传
  router.post('/api/operateUser/uploadLogo', controller.api.operateUser.uploadLogo);
  router.get('/api/getBranch', controller.api.systemConfig.getBranch);
  router.get('/api/getPEISIntergrationEnable', controller.api.systemConfig.getPEISIntergrationEnable);

  router.get('/api/getAuthCookieName', controller.api.admin.getAuthCookieName);

  // 获取验证码
  router.get('/api/getImgCode', controller.api.admin.getImgCode);
  router.get('/api/getSlideCode', controller.api.admin.getSlideCode);

  // 证书二维码真伪验证
  router.get('/certificatePreview', controller.api.certificatePreview.preview);
  router.post('/api/certificatePreview/getCertificate', controller.api.certificatePreview.getCertificate);

  // 监管端通知公告
  router.get('/n/:id', controller.api.notice.preview);
  router.post('/n/:id', controller.api.notice.getNotice);

  // 判断当前系统是否可登录
  router.get('/api/getAuthLogin', controller.api.admin.getAuthLogin);

  // xxn add 监控预警短信监控情况
  app.io.route('watchSMSnotification', app.io.controller.default.watchSMSnotification);
  app.io.route('fromVue', app.io.controller.default.fromVue);

  // 云监督相关

  // 获取当前视频的云监督信息
  router.get('/api/getCloudSuperInfo', controller.api.cloudWatch.getCloudSuperInfo);

  // 签名
  router.post('/api/cloudSupervisionSign', controller.api.cloudWatch.cloudSupervisionSign);

  // 新疆字典表数据
  router.get('/api/dictionary/educational-qualifications', controller.api.dictionary.getEducationalQualifications); // 获取学历字典表数据
  router.get('/api/dictionary/area-code', controller.api.dictionary.getAreaCode); // 获取行政区划字典表数据
  router.get('/api/dictionary/ID-type', controller.api.dictionary.getIDtype); // 获取身份证明文件类型

  // xxn add 获取在线监测的设备最新数据
  app.io.route('onlineMonitoring', app.io.controller.progress.onlineMonitoring);
  app.io.route(
    'closeOnlineMonitoring',
    app.io.controller.progress.closeOnlineMonitoring
  );

  // 专家端获取自己审核的示范单位审核
  router.post('/api/expert/healthDecalaration', controller.api.admin.healthDecalaration);
  // 专家打分
  router.post('/api/expert/healthDecalarationScoring', controller.api.admin.healthDecalarationScoring);
  // 添加天地图API相关路由
  router.get('/api/tiandimap/geocode', controller.api.tiandimap.geocode);
  router.get('/api/tiandimap/reverseGeocode', controller.api.tiandimap.reverseGeocode);
  router.get('/api/tiandimap/searchPOI', controller.api.tiandimap.searchPOI);
  router.get('/api/tiandimap/staticMapUrl', controller.api.tiandimap.getStaticMapUrl);
  router.get('/api/tiandimap/district', controller.api.tiandimap.district);

  // 职业病危害项目检查数据API
  router.get('/api/hazardInspectionData/mapMarkers', controller.api.hazardStatistics.getMapMarkers);
  router.get('/api/hazardInspectionData/hazardFactorStats', controller.api.hazardStatistics.getHazardFactorStats);
  router.get('/api/hazardInspectionData/industryStats', controller.api.hazardStatistics.getIndustryStats);
  router.get('/api/hazardInspectionData/regionStats', controller.api.hazardStatistics.getRegionStats);
  router.get('/api/hazardInspectionData/unitDetail', controller.api.hazardStatistics.getUnitDetail);
  // 统计数据管理API
  router.get('/api/hazard/updateStatistics', controller.api.hazardStatistics.updateStatistics);
  router.post('/api/file', controller.api.admin.uploadFile);
  router.delete('/api/file', controller.api.admin.deleteFile);

  // 天地图API接口
  router.get('/api/tianditu/geocode', controller.api.tiandimap.geocode);
  router.get('/api/tianditu/reverseGeocode', controller.api.tiandimap.reverseGeocode);
  router.get('/api/tianditu/searchPOI', controller.api.tiandimap.searchPOI);
  router.get('/api/tianditu/getStaticMapUrl', controller.api.tiandimap.getStaticMapUrl);
  router.get('/api/tianditu/district', controller.api.tiandimap.district);
  router.get('/api/tianditu/convertToGeoJSON', controller.api.tiandimap.convertToGeoJSON);


  // 杭州H5预约服务页面
  router.get('/api/reservation/list', controller.api.hzReservation.list);
  router.post('/api/reservation/update', controller.api.hzReservation.update);
};
