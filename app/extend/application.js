

const fs = require('fs');
const path = require('path');
const _ = require('lodash');


module.exports = {

  // 获取插件api白名单
  getExtendApiList() {
    const app = this;
    const pluginFile = path.join(app.config.baseDir, 'config/plugin.js');
    const pluginInfo = require(pluginFile);
    const plugins = [];
    const pluginAdminApiWhiteList = [];
    for (const pluginItem in pluginInfo) {

      // 1、开启插件，2、已成功加载，3、内部(jk)插件
      // hashOwnProperty 判断自身属性是否存在
      if (pluginInfo.hasOwnProperty(pluginItem) && pluginInfo[pluginItem].enable && !_.isEmpty(app.config[pluginItem]) && pluginItem.indexOf('jk') === 0) {

        console.log(pluginItem);
        const {
          adminApi,
        } = app.config[pluginItem];

        // 获取后台接口白名单

        for (const item of adminApi) {
          if (item && item.noPower && item.url) {
            pluginAdminApiWhiteList.push(item.url);
          }
        }
        plugins.push(pluginItem);
      }
    }
    return {
      plugins,
      adminApiWhiteList: pluginAdminApiWhiteList,
    };
  },

  // 初始化数据模型
  initExtendModel(modelsPath) {
    const app = this;
    fs.readdirSync(modelsPath).forEach(function(extendName) {
      console.log(`挂载 ${path.basename(extendName, '.js')} 模型成功！`);
      if (extendName) {
        const filePath = `${modelsPath}/${extendName}`;
        if (fs.existsSync(filePath)) {
          const modelKey = path.basename(extendName.charAt(0).toUpperCase() + extendName.slice(1), '.js');
          if (_.isEmpty(app.model[modelKey])) {
            const targetModel = app.loader.loadFile(filePath);
            app.model[modelKey] = targetModel;
          }
        }
      }
    });
  },

  // 初始化插件路由
  async initPluginRouter(ctx, pluginConfig = {}, pluginManageController = {}, pluginApiController = {}, next = {}) {
    console.log('\r\n\r\n3.数据传入公共方法并进行接口验证而后定向请求  app>extend>application>initPluginRouter\r\n');
    const app = this;
    let isFontApi = false;
    let isAdminApi = false;
    let targetControllerName = '';
    let targetApiItem = {};
    if (!_.isEmpty(pluginConfig)) {
      const {
        adminApi,
        fontApi,
      } = pluginConfig;

      const targetRequestUrl = ctx.request.url;
      console.log(`请求路径：${targetRequestUrl}`);
      if (targetRequestUrl.indexOf('/api/') >= 0) {
        console.log('api路径，进行fontApi处理');
        for (const fontApiItem of fontApi) {
          const {
            url,
            method,
            controllerName,
          } = fontApiItem;

          const targetApi = targetRequestUrl.replace('/api/', '').split('?')[0];
          if (ctx.request.method === method.toUpperCase() && targetApi === url && controllerName) {
            isFontApi = true;
            targetControllerName = controllerName;
            targetApiItem = fontApiItem;
            break;
          }

        }

      } else if (targetRequestUrl.indexOf('/manage/') >= 0) {

        console.log('manage路径，进行adminApi处理');
        // console.log(adminApi, 'adminApi');
        for (const adminApiItem of adminApi) {
          // console.log(adminApiItem, 'adminApiItem');
          const {
            url,
            method,
            controllerName,
          } = adminApiItem;

          const targetApi = targetRequestUrl.replace('/manage/', '').split('?')[0];
          if (ctx.request.method === method.toUpperCase() && targetApi === url && controllerName) {
            isAdminApi = true;
            targetControllerName = controllerName;
            targetApiItem = adminApiItem;
            break;
          }
        }
      }
    }
    if (isAdminApi && !_.isEmpty(pluginManageController) && targetControllerName) {
      console.log('处理成功！请求AdminApi');
      await pluginManageController[targetControllerName](ctx, app);
    } else if (isFontApi && !_.isEmpty(pluginApiController) && targetControllerName) {
      if (targetApiItem.authToken) {
        if (ctx.session.logined) {
          console.log('处理成功(authToken)！请求FontApi');
          await pluginApiController[targetControllerName](ctx, app, next);
        } else {
          ctx.helper.renderFail(ctx, {
            message: '请先登录',
          });
        }
      } else {
        console.log('处理成功！请求FontApi');
        await pluginApiController[targetControllerName](ctx, app, next);
      }
    }
  },

};
