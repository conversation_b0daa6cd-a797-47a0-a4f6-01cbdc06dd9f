require('module-alias/register');
const Axios = require('axios');
const _ = require('lodash');
const mkdirp = require('mkdirp');
const path = require('path');
const PizZip = require('pizzip');
const Docxtemplater = require('docxtemplater');
const expressions = require('angular-expressions');
const assign = require('lodash/assign');
const sm3 = require('sm-crypto').sm3;
const qs = require('qs');
expressions.filters.lower = function(input) {
  if (!input) return input;
  return input.toLowerCase();
};
// 文件操作对象
const fs = require('fs');
// const stat = fs.stat;
const CryptoJS = require('crypto-js');
const url = require('url');
const awaitWriteStream = require('await-stream-ready').write;
const streamWormhole = require('stream-wormhole');

function angularParser(tag) {
  if (tag === '.') {
    return {
      get(s) {
        return s;
      },
    };
  }
  const expr = expressions.compile(
    tag.replace(/(’|‘)/g, "'").replace(/(“|”)/g, '"')
  );
  return {
    get(scope, context) {
      let obj = {};
      const scopeList = context.scopeList;
      const num = context.num;
      for (let i = 0, len = num + 1; i < len; i++) {
        obj = assign(obj, scopeList[i]);
      }
      return expr(scope, obj);
    },
  };
}


// 阿里云初始化
const RPCClient = require('@alicloud/pop-core').RPCClient;

function initVodClient(accessKeyId, secretAccessKey) {
  const regionId = 'cn-shanghai'; // 点播服务接入区域
  const client = new RPCClient({
    accessKeyId,
    secretAccessKey,
    endpoint: 'http://vod.' + regionId + '.aliyuncs.com',
    apiVersion: '2017-03-21',
  });
  return client;
}

module.exports = {
  async reqJsonData(url, params = {}, method = 'get') {
    let responseData;

    let targetUrl = '';
    if (url.indexOf('manage/') === 0) {
      targetUrl = this.app.config.server_path + '/' + url;
    } else if (url.indexOf('http') === 0) {
      targetUrl = url;
    } else {
      targetUrl = this.app.config.server_api + '/' + url;
    }

    if (method === 'get') {
      responseData = await Axios.get(targetUrl, {
        params,
      });
    } else if (method === 'post') {
      responseData = await Axios.post(targetUrl, params);
    }
    if (
      responseData &&
      responseData.status === 200 &&
      !_.isEmpty(responseData.data) &&
      responseData.data.status === 200
    ) {
      return responseData.data;
    }

    // throw new Error(responseData.data.message);
    // 便于单元测试，改为如下，结果是一样的
    return {
      status: 500,
      message: responseData.data.message,
      data: {},
    };
  },
  clearCache(str, cacheKey) {
    // console.log('cacheStr', str);
    const currentKey = this.app.config.session_secret + cacheKey + str;
    this.setCache(currentKey, '', 2000);
  },
  setCache(key, value, time) {
    // 多进程消息同步
    this.app.messenger.sendToApp('refreshCache', {
      key,
      value,
      time,
    });
  },
  getCache(key) {
    return this.app.cache.get(key);
  },
  renderSuccess(ctx, { data = {}, message = '' } = {}) {
    ctx.body = {
      status: 200,
      data,
      message,
    };
    ctx.status = 200;
  },
  renderCustom(ctx, { status = 200, data = {}, message = '' } = {}) {
    ctx.body = {
      status,
      data,
      message,
    };
    ctx.status = status;
  },
  renderFail(
    ctx,
    { message = '哎呀！网页开小差了！', data = {} } = {},
    status = 500
  ) {
    if (message instanceof Object) {
      message = message.message;
    }
    ctx.body = {
      status,
      message,
      data,
    };
    ctx.status = 200;
  },

  // 此函数仅限一对多模式
  async getAdminPower(ctx) {
    const superUserInfo = await ctx.service.superUser.item(ctx, {
      query: {
        _id: ctx.session.superUserInfo._id,
      },
      populate: [
        {
          path: 'group',
          select: 'power _id enable name',
        },
        {
          path: 'crossRegionRoles',
          select: 'power _id enable name',
        },
        {
          path: 'members.group',
          select: 'power _id enable name',
        },
        {
          path: 'members.crossRegionRoles',
          select: 'power _id enable name',
        },
      ],
      files: 'group crossRegionRoles powerStatus power date enable user_role_power_hmac userName members',
    });

    let adminPower;
    if (superUserInfo.powerStatus) {
      // 开通了菜单权限配置
      adminPower = superUserInfo.power || [];
    } else {
      // 判断是管理员还是成员
      if (ctx.session.superUserInfo.userName !== superUserInfo.userName) {
        // 是成员用户
        const member = superUserInfo.members.find(m => m.userName === ctx.session.superUserInfo.userName);
        if (member) {
          const roleField = ctx.app.config.crossRegionManage ? 'crossRegionRoles' : 'group';
          if (member[roleField]) {
            // 直接使用已经填充的数据
            adminPower = _.uniq(_.flatMap(member[roleField], g =>
              ((g.power && Array.isArray(g.power)) ? g.power : [])
            ));
          } else {
            adminPower = [];
          }
        } else {
          adminPower = [];
        }
      } else {
        // 是管理员，直接使用已填充的数据
        const roleField = ctx.app.config.crossRegionManage ? 'crossRegionRoles' : 'group';
        adminPower = _.uniq(_.flatMap(superUserInfo[roleField], g =>
          ((g.power && Array.isArray(g.power)) ? g.power : [])
        ));
      }
    }
    return adminPower;
  },

  deleteFolder(path) {
    // console.log("---del path--" + path);
    return new Promise(resolve => {
      let files = [];
      if (fs.existsSync(path)) {
        // console.log("---begin to del--");
        if (fs.statSync(path).isDirectory()) {
          const walk = function(path) {
            files = fs.readdirSync(path);
            files.forEach(function(file) {
              const curPath = path + '/' + file;
              if (fs.statSync(curPath).isDirectory()) {
                // recurse
                walk(curPath);
              } else {
                // delete file
                fs.unlinkSync(curPath);
              }
            });
            fs.rmdirSync(path);
          };
          walk(path);
          // console.log("---del folder success----");
          resolve();
        } else {
          fs.unlink(path, function(err) {
            if (err) {
              console.log(err);
            } else {
              console.log('del file success');
              resolve();
            }
          });
        }
      } else {
        resolve();
      }
    });
  },

  hashSha256(data, salt) {
    return CryptoJS.SHA256(data + salt).toString();
  },

  // sm3hmc计算
  async hashSm3(data, passwordEncryptionAlgorithm) {
    try {
      if (passwordEncryptionAlgorithm === 'FZsm3') {
        const { fzgmBaseUrl, fzgmKey } = this.app.config;
        // 获取福州token
        if (!fzgmBaseUrl || !fzgmKey) {
          this.ctx.auditLog(
            '福州GM地址或者key未配置',
            '福州GM地址或者key未配置',
            'error'
          );
          throw new Error('福州GM地址或者key未配置');
        }
        const fzgmToken = await this.getFZToken(fzgmBaseUrl, fzgmKey);
        const base64Data = Buffer.from(data).toString('base64');
        const encryptData = await Axios({
          method: 'post',
          url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm3-hmac`,
          data: {
            data: base64Data,
            appid: fzgmKey,
            keyid: '',
          },
          headers: {
            Authorization: fzgmToken,
          },
        });
        this.ctx.auditLog(
          `fz密码sm3计算成功2${encryptData}`,
          `HMAC计算成功${data}`,
          'info'
        );
        return encryptData.data.value;
      } else if (passwordEncryptionAlgorithm === 'XJBTsm3') {
        const { xjbtGM } = this.app.config;
        const base64Data = Buffer.from(data).toString('base64');
        const encryptData = await Axios({
          method: 'post',
          url: `${xjbtGM.baseUrl}/key/v3/digest`,
          data: qs.stringify({
            hashAlg: xjbtGM.hmacAlgorithm,
            plaintext: base64Data,
          }),
          headers: {
            appId: xjbtGM.appId,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        });
        this.ctx.auditLog(
          `xjbt密码sm3计算成功${encryptData}`,
          `HMAC计算成功${data}`,
          'info'
        );
        if (encryptData.data.code === 0) {
          return encryptData.data.data;
        }
        throw new Error(encryptData.data.message);
      }
      const hashData = sm3(data, {
        key: 'daac25c1512fe50f79b0e4526b93f5c0e1460cef40b6dd44af13caec62e8c60e0d885f3c6d6fb51e530889e6fd4ac743a6d332e68a0f2a3923f42585dceb93e9', // 要求为 16 进制串或字节数组
      });
      return hashData;
    } catch (error) {
      await this.ctx.service.operateLog.create('SuperUser', {
        optType: 'hsm',
        supplementaryNotes: `HMAC计算失败${error}`,
      });
      this.ctx.auditLog('HMAC计算失败', `HMAC计算失败${error}`, 'error');
    }
  },

  // sm3hmc校验
  async verifySm3(data, passwordEncryptionAlgorithm, hash) {
    try {
      if (passwordEncryptionAlgorithm === 'FZsm3') {
        const { fzgmBaseUrl, fzgmKey } = this.app.config;
        // 获取福州token
        if (!fzgmBaseUrl || !fzgmKey) {
          this.ctx.auditLog(
            '福州GM地址或者key未配置',
            '福州GM地址或者key未配置',
            'error'
          );
          throw new Error('福州GM地址或者key未配置');
        }
        const fzgmToken = await this.getFZToken(fzgmBaseUrl, fzgmKey);
        const base64Data = Buffer.from(data).toString('base64');
        const encryptData = await Axios({
          method: 'post',
          url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm3-hmac-verify`,
          data: {
            data: base64Data,
            appid: fzgmKey,
            value: hash,
            keyid: '',
          },
          headers: {
            Authorization: fzgmToken,
          },
        });
        this.ctx.auditLog(
          `fzGMsm3校验明文${data}`,
          `HMAC校验哈希${hash}`,
          'info'
        );
        return encryptData.data.isvalid;
      } else if (passwordEncryptionAlgorithm === 'XJBTsm3') {
        const { xjbtGM } = this.app.config;
        const base64Data = Buffer.from(data).toString('base64');
        const encryptData = await Axios({
          method: 'post',
          url: `${xjbtGM.baseUrl}/key/v3/digest`,
          data: qs.stringify({
            hashAlg: xjbtGM.hmacAlgorithm,
            plaintext: base64Data,
          }),
          headers: {
            appId: xjbtGM.appId,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        });
        if (encryptData.data.code === 0) {
          this.ctx.auditLog(
            `xjbt密码sm3校验成功${encryptData}`,
            `HMAC校验成功${hash}`,
            'info'
          );
          return encryptData.data.data === hash;
        }
        throw new Error(encryptData.data.message);
      }
      const hashData = sm3(data, {
        key: 'daac25c1512fe50f79b0e4526b93f5c0e1460cef40b6dd44af13caec62e8c60e0d885f3c6d6fb51e530889e6fd4ac743a6d332e68a0f2a3923f42585dceb93e9', // 要求为 16 进制串或字节数组
      });
      return hashData === hash;
    } catch (error) {
      await this.ctx.service.operateLog.create('SuperUser', {
        optType: 'hsm',
        supplementaryNotes: `HMAC校验失败${error}, ${error}`,
      });
      this.ctx.auditLog('HMAC校验失败', `HMAC校验失败${error}`, 'error');
      return false;
    }
  },

  // 获取福州token
  async getFZToken(fzgmBaseUrl, fzgmKey) {
    let fzgmToken = await this.getCache('fzgmToken');
    if (!fzgmToken) {
      try {
        const response = await Axios({
          method: 'get',
          url: `${fzgmBaseUrl}/apisix/plugin/jwt/sign`,
          params: {
            key: fzgmKey,
          },
        });
        fzgmToken = response.data;
        this.ctx.auditLog(
          '获取福州GMtoken成功',
          `获取福州GMtoken成功${fzgmToken}`,
          'info'
        );
        this.setCache('fzgmToken', fzgmToken, 1000 * 60);
        return fzgmToken;
      } catch (error) {
        throw new Error('获取福州GMtoken失败1', error);
      }
    } else {
      return fzgmToken;
    }
  },

  encrypt(data, key) {
    // 密码加密
    return CryptoJS.AES.encrypt(data, key).toString();
  },

  decrypt(data, key) {
    // 密码解密
    try {
      const bytes = CryptoJS.AES.decrypt(data, key);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.log('密码解密失败：' + error);
      return '';
    }
  },

  // 获取监管当前用户登录信息
  async getSuperAdminInfo() {
    const ctx = this.ctx;
    const LoginInfo = ctx.session.superUserInfo;
    if (!LoginInfo) {
      return null;
    }
    let loginUserInfo = {};
    const _id = LoginInfo._id;
    const isManage = LoginInfo.isManage;
    if (isManage) {
      loginUserInfo = await ctx.model.SuperUser.findOne({ _id: LoginInfo._id });
    } else {
      loginUserInfo = await ctx.model.SuperUser.aggregate([
        { $match: { _id: LoginInfo._id } },
        {
          $unwind: '$members',
        },
        {
          $match: { 'members.userName': LoginInfo.userName },
        }]);
      if (loginUserInfo && loginUserInfo.length && loginUserInfo[0]._id === _id) {
        loginUserInfo = loginUserInfo[0].members;
      }
    }
    return loginUserInfo;
  },
  // 填充word模板文件通用方法
  async fillWord(ctx, templateFileName, wordData) {
    const temPath = ctx.app.config.report_template_path;
    // console.info('系统默认报告文件路径', temPath);
    await mkdirp(temPath);
    const content = fs.readFileSync(
      path.resolve(temPath, templateFileName + '.docx'),
      'binary'
    );
    const zip = new PizZip(content);
    const doc = new Docxtemplater();
    doc.setOptions({
      parser: angularParser,
    });
    doc.loadZip(zip);
    doc.setData(wordData);
    let res = {};
    try {
      await doc.render();
      const fileName = templateFileName + new Date().getTime();
      const buf = doc.getZip().generate({
        type: 'nodebuffer',
        compression: 'DEFLATE',
      });
      const superID = ctx.session.superUserInfo
        ? ctx.session.superUserInfo._id
        : '';
      const configFilePath = path.resolve(
        path.join(ctx.app.config.super_path, `/${superID}`)
      );
      await mkdirp(configFilePath);
      await fs.writeFileSync(
        path.resolve(configFilePath, fileName + '.docx'),
        buf
      );
      res = {
        originName: fileName,
        staticName: fileName + '.docx',
        path:
          '/static' +
          ctx.app.config.super_http_path +
          '/' +
          superID +
          '/' +
          fileName +
          '.docx',
      };
    } catch (error) {
      console.log('生成word文件出错！', error);
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties,
      };
      // console.log(e.properties.errors);
      res = {
        code: 500,
        e,
        message: '请关闭正在操作的word文件',
      };
    }
    return res;
  },

  // 阿里云视频SDK封装
  async request_alivod(action, param) {
    const aliclient = initVodClient(
      this.app.config.aliVideo.accessKeyId,
      this.app.config.aliVideo.secretAccessKey
    );
    return await aliclient
      .request(action, param, {})
      .then(response => {
        return response;
      })
      .catch(response => {
        return response;
      });
  },

  // 获取redis缓存信息
  async getRedis(key) {
    if (!key || typeof key !== 'string') return;
    const { ctx, app } = this;
    try {
      let result = await app.redis.get(key);
      if (result) {
        result = JSON.parse(result);
      } else {
        const { data } = await ctx.curl(`${app.config.iService2Host}/redis`, {
          method: 'GET',
          dataType: 'json',
          data: { key },
        });
        if (data.code === 200) {
          result = data.data;
        } else {
          throw new Error('未找到相关缓存信息，key: ' + key);
        }
      }
      return result;
    } catch (err) {
      ctx.auditLog('获取缓存信息失败', JSON.stringify(err), 'error');
    }
  },

  // 设置redis缓存信息
  // key：string 缓存key
  // value: any, // 缓存值, 无需序列化，iService2会自动处理
  // ttl: number 过期时间，单位秒,不设置的话默认为永久有效
  async setRedis(key, value, ttl) {
    const { ctx, config } = this;
    if (!key || !value) {
      throw new Error('key或value不能为空');
    }
    const { data } = await ctx.curl(`${config.iService2Host}/redis`, {
      method: 'POST',
      dataType: 'json',
      headers: {
        'Content-Type': 'application/json',
      },
      data: { key, value, ttl },
    });
    if (data.code === 200) {
      return data.data || 'OK';
    }
    throw new Error(data.message || '缓存设置失败');
  },

  // 处理iService返回的数据
  async handleIserviceRes(ctx, data, successMessage = '操作成功', failMessage = '操作失败') {
    if (data.code === 200) {
      return this.renderSuccess(ctx, {
        data: data.data,
        message: successMessage,
        status: 200,
      });
    }
    return this.renderCustom(ctx, {
      message: data.message || failMessage,
      status: data.statusCode || data.code || 400,
      data: data.error || data,
    });
  },

  /*
   * @description: stream写入
   * @param {*} readableStream 可读流 必填
   * @param {*} target 写入路径 必填 例
   * @param {*} bucketObj bucket配置对象 非必填 app.oss.buckets.xxx ，默认app.oss.buckets.default
   * @return {*}
   */
  async pipe({ readableStream, target, bucketObj }) {
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;
    const option = app.config.oss;
    if (storageType && storageType.includes('oss')) {
      try {
        const targetSplit = target.replace(/\\/gi, '/').split('/public/');
        let filePath = targetSplit[targetSplit.length - 1];
        if (filePath.startsWith('/')) {
          filePath = filePath.substring(1);
        }

        let bucketName = '';
        let accessPolicy = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
          accessPolicy = bucketObj.accessPolicy;
        } else {
          bucketName = option.buckets.default.name;
          accessPolicy = option.buckets.default.accessPolicy;
        }
        const client = await this.initCilent();

        const parsedUrl = url.parse(option.endPoint);

        const protocol = parsedUrl.protocol;
        const hostname = parsedUrl.hostname;
        const port = parsedUrl.port;

        const res = await client.putObject(
          bucketName,
          filePath,
          readableStream
        );
        // console.log('ossPipeRes', res);

        let fileUrl = '';
        if (accessPolicy === 'private') {
          fileUrl = await client.presignedUrl(
            'GET',
            bucketName,
            filePath,
            24 * 60 * 60
          );
        } else {
          fileUrl = `${protocol}//${hostname}${
            port ? ':' + port : ''
          }/${bucketName}/${filePath}`;
        }
        console.log('ossPipeFileUrl', fileUrl);

        if (res && res.etag) {
          return {
            status: 200,
            type: storageType,
            url: fileUrl,
          };
        }
      } catch (error) {
        console.log('pipeError', error);
        return {
          status: 500,
          message: 'error',
          type: storageType,
          url: '',
        };
      }
    } else {
      // const fileName = path.basename(target)
      // 解析路径，创建缺少的目录结构
      const dirname = path.dirname(target);
      await mkdirp(dirname);
      // 默认本地存储
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(readableStream.pipe(writeStream));
        return {
          status: 200,
          type: storageType ? storageType : 'local',
        };
      } catch (error) {
        console.log(4444444, error);
        await streamWormhole(writeStream);
        writeStream.destroy();
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    }
  },

};
