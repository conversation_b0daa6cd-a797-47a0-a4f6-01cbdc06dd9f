module.exports = {
  auditLog(title = '警告：未输入任何记录！', message = '警告：未输入任何记录！', level = 'debug') {
    const { session, request, logger } = this;
    // console.log('session.superUserInfo数据:', session.superUserInfo);
    try {
      const loggerTitle = '操作记录：',
        userInfo = session.superUserInfo,
        header = request.header,
        json = {
          title,
          userID: (userInfo && userInfo._id) || '暂未获取到当前用户ID！',
          message,
          userAgent: {
            IP: header['x-real-ip'] || header.host,
            AGENT: header['user-agent'],
          },
        };

      switch (level) {
        case 'debug':
          logger.debug(loggerTitle, json);
          break;
        case 'info':
          logger.info(loggerTitle, json);
          break;
        case 'warn':
          logger.warn(loggerTitle, json);
          break;
        case 'error':
          if (json.message instanceof Object) {
            json.message = JSON.stringify(json.message, null, 2);
          }
          json.message = json.message.replace(/\n/g, '');
          logger.error(loggerTitle, new Error(JSON.stringify(json)).message);
          break;
        default:
          break;
      }
    } catch (err) {
      logger.error('ctx.auditLog报错了', JSON.stringify(err.stack, null, 2));
    }
  },
};
