module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const StatisticalFzSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    table: { // 大屏数据的表名, 用于区分不同的数据
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    adcode: { // 当前所统计的行政区划代码
      type: String,
      maxlength: 12,
      minlength: 12,
    },
    data: Array, // 统计的数据
  }, { timestamps: true });

  return mongoose.model('DpStatistical', StatisticalFzSchema, 'dpStatistical');
};
