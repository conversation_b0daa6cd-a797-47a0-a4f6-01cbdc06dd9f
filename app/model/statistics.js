// 大屏数据统计
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const StatisticsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    query: {
      adname: Array, // [ '浙江省', '杭州市' ]
      adcode: String, // ！必传参 eg：'330100'
      year: Number, // ！必传参 查询的年份 2022
      point: [ String ], // 经纬度
    },
    jobHealthData: {
      serviceOrgs: [], // 机构列表
      abnormalPercent: Number, // 检测合格率
      JobHealthCount: Number, // 检测项目数
      monthTrend: Array,
      riskAssessmentStatistics: Object,
      JobHealthQyNumber: Number, // 检测企业家数
    },
    healthCheckData: {
      peopleCounting: Object,
      healthcheckCount: Number, // 体检项目数量
      physicalExamOrgs: Array, // 体检机构列表
      monthTrend: Array,
      healthcheckQyCount: Number, // 体检企业家数
    },
    warningData: {
      warningList: Array,
      count1: Number, // 检测预警数
      count2: Number, // 体检预警数
      totalCount: Number, // 预警总数
    },
    trainingData: Object, // 培训数据
    districtsData: Object, // 中间子区域的数据
    updateTime: { // 数据更新时间
      type: Date,
      default: Date.now,
    },
  });


  return mongoose.model('Statistics', StatisticsSchema, 'statistics');

};
