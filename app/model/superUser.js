// 行政端用户对象
// const { defaultTo } = require('lodash');
// const helpPersonnel = require('../../lib/plugin/egg-jk-enterpriseServe/app/model/helpPersonnel');
const encryptionPlugin = require('../utils/encryptionPlugin');
const regionQueryTransformPlugin = require('../utils/regionQueryTransformPlugin');
const crossRegionPlugin = require('../utils/crossRegion');
module.exports = app => {
  const mongoose = app.mongoose;
  const ctx = app.createAnonymousContext();
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');
  const moment = require('moment');

  require('./adminGroup');
  const { dbEncryption = false, crossRegionManage = false } = app.config;

  const SuperUserSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      type: {
        // 单位类型：1 卫生健康委 2 监督所 3 疾控中心 4 职防院 5 社区卫生服务中心
        type: Number,
        require: true,
        enum: [ 1, 2, 3, 4, 5 ],
        set(val) {
          return Number(val);
        },
      },
      cname: String, // 单位名称
      regAdd: [ String ], // 单位的管辖区域范围, 目前存的是中文名
      crossRegionAdd: {
        type: [[ String ]],
        default: [],
      },
      // 如果开关启动，area_code为数组，否则为字符串
      area_code: {
        // 所在辖区区号 12位
        type: String,
        require: true,
      },
      crossRegionArea: {
        type: [ String ],
        default: [],
      },
      orgCode: {
        type: String,
        unique: true,
        default() {
          return this.constructor.generateOrgCode();
        },
      }, // 机构编码（唯一）
      userName: String, // 账户名
      name: String, // 超级管理员姓名
      nameForStore: {
        // 加密姓名
        type: String,
      },
      nameSplitEncrypted: {
        // 分段加密的姓名
        type: String,
      },
      jobTitle: String, // 超级管理员职位
      email: String, // 邮箱
      phoneNum: String, // 超级管理员手机号码
      phoneNumForStore: {
        // 用于加密存储的手机号
        type: String,
      },
      phoneNumSplitEncrypted: {
        // 分段加密的手机号
        type: String,
      },
      countryCode: {
        // 手机号前国家代码
        type: String,
        default: '86',
      },
      password: {
        // 超级管理员密码
        type: String,
        set(val) {
          if (!dbEncryption) {
            return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
          }
          return val;
        },
      },
      // 存储密码有效期 val为new Date()
      passwordExpiresAt: {
        type: Date,
      },
      loginAttempts: Number, // 记录登录尝试次数
      loginAttemptsTimestamp: Array, // 记录登录尝试时间
      comments: String, // 备注
      landline: String, // 座机
      date: {
        // 渐渐弃用
        type: Date,
        default: Date.now,
      },
      logo: {
        type: String,
        default: '/static/upload/images/defaultlogo.png',
      },
      enable: {
        // 账号是否受限
        type: Boolean,
        default: false,
      },
      state: {
        // 账号状态
        type: String,
        default: '1', // 1正常，0删除
      },
      group: {
        // 资源所属组ID
        type: [ String ],
        ref: 'AdminGroup',
        default: [ app.config.groupID.superGroupID ],
      },
      // 跨区域的角色
      crossRegionRoles: {
        // 资源所属组ID
        type: [ String ],
        ref: 'AdminGroup',
        default: [ app.config.groupID.superGroupID ],
      },
      members: [
        {
          // 本账号的其他管理成员, 也是可以登录的
          _id: {
            type: String,
            default: shortid.generate,
          },
          name: String, // 姓名
          nameForStore: {
            // 加密姓名
            type: String,
          },
          nameSplitEncrypted: {
            // 分段加密的姓名
            type: String,
          },
          phoneNum: String,
          phoneNumForStore: {
            // 用于加密存储的手机号
            type: String,
          },
          phoneNumSplitEncrypted: {
            // 分段加密的手机号
            type: String,
          },
          userName: String, // 默认是手机号
          jobTitle: String, // 职位
          password: {
            type: String,
            set(val) {
              if (!dbEncryption) {
                return CryptoJS.SHA256(
                  val + app.config.salt_sha2_key
                ).toString();
              }
              return val;
            },
          },
          passwordEncryptionAlgorithm: {
            type: String,
          },
          landline: String, // 座机
          roles: [
            {
              // 角色
              type: String,
              enum: [ 'warning' ],
            },
          ],
          group: [
            {
              type: String,
              ref: 'AdminGroup',
            },
          ],
          crossRegionRoles: {
            type: [ String ],
            default: [],
          },
          user_role_power_hmac: String, // 用户角色权限的hmac
          openId: String, // 对接其他认证中心用户id
        },
      ],
      powerStatus: { type: Boolean, default: false }, // 是否已开启菜单权限配置
      power: [{ type: String, ref: 'AdminResource' }], // 菜单权限
      source: { type: String, default: 'jg' }, // 来源 (福州同步过来：'fz')
      // 人员角色权限hmac
      user_role_power_hmac: String,
      passwordEncryptionAlgorithm: {
        // 密码加密hmac算法
        type: String,
      },
      // 人员角色权限hmac算法
      user_role_power_hmac_algorithm: {
        type: String,
      },
      encryptionAlgorithm: {
        // 加密算法
        type: String,
      },
      openId: String, // 对接其他认证中心用户id

      // helpPersonnels: {
      //   type: [String],
      //   default: []
      // },
      helpPersonnels: [
        {
          creatorSuperId: String,
          idNumber: String,
        },
      ],
    },
    { timestamps: true }
  );

  dbEncryption && SuperUserSchema.plugin(encryptionPlugin, {
    fields: {
      name: 3,
      phoneNum: 11,
    },
    model: 'SuperUser',
    ctx,
  });

  // crossRegionManage &&
  //   SuperUserSchema.plugin(regionQueryUpgradePlugin, {
  //     _regionFieldMap: {
  //       regAdd: 'crossRegionAdd', // 老字段 => 新字段
  //     },
  //   });

  crossRegionManage && SuperUserSchema.plugin(regionQueryTransformPlugin, {
    _regionFields: 'regAdd',
  });
  SuperUserSchema.plugin(crossRegionPlugin, {
    ctx,
  });
  /**
   * 生成机构编码
   * 规则：ORG + 年月日 + 4位序号
   */
  SuperUserSchema.statics.generateOrgCode = function() {
    const date = moment().format('YYYYMMDD');
    const prefix = 'ORG' + date;

    // 生成4位随机数
    const random = Math.floor(1000 + Math.random() * 9000);
    const sequence = random.toString().padStart(4, '0');

    return prefix + sequence;
  };

  return mongoose.model('SuperUser', SuperUserSchema, 'superusers');

};
