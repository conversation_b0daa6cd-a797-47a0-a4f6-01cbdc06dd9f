module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const OnlineDeclarationFilesSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { type: String }, // 企业id
    year: Date, // 年度
    monthD: Date, // 申报日期
    supervise: String, // 监管部门
    tableFiles: { // 申报表格及回执
      type: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: String,
        url: String,
      }],
    },
  });

  return mongoose.model('OnlineDeclarationFiles', OnlineDeclarationFilesSchema, 'onlineDeclarationFiles');

};
