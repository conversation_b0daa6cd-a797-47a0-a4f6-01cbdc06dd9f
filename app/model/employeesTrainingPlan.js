module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 企业员工培训
  const EmployeesTrainingPlanSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 名称
      type: String,
      require: true,
    },

    authorID: { // 企业端管理员id
      type: String,
      ref: 'AdminUser',
      default: '',
    }, // 创建人的ID

    EnterpriseID: { // 企业端id
      type: String,
      ref: 'Adminorg',
      default: '',
    }, // 创建人的ID

    employees: [{
      type: String,
      ref: 'Employees',
    }], // 仅用于员工培训，需要培训的员工

    completedEmployees: [{
      type: String,
      ref: 'Employees',
    }], // 培训完成的员工

    coursesID: [{ // 所有课程
      type: String,
      default: '',
      ref: 'Courses',
    }],
    // lesson: { // 总课时，所有课程学时总和
    //   type: Number,
    // },
    requiredCoursesHours: {
      type: Number,
      default: 0,
    }, // 必修课学时
    electivesCoursesHours: {
      type: Number,
      default: 0,
    }, // 选修课学时最低条件

    introduction: { // 简介
      type: String,
      default: '',
    },
    date: { // 创建时间
      type: Date,
      default: Date.now(),
    },
    updateTime: [{ // 更新时间，每更新一次记录一次时间
      type: Date,
    }],
    completeTime: {
      type: Date,
    }, // 计划完成时间
    examination: { // 考试信息
      singleChoice: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      },
      multipleChoice: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      },
      Judgment: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      }, // 判断
      fillBlank: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      }, // 填空
      essayQuestion: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      }, // 综合大题
      passingGrate: {
        type: Number,
        default: 60,
      }, // 及格分
      limitTime: {
        type: Number,
        default: 30,
      }, // 考试时长，分钟
    },

    allowTestOnly: {
      type: Boolean,
      default: false,
    }, // 是否允许直接参与考试
    needExam: {
      type: Boolean,
      default: true,
    }, // 是否需要考试
  }, {
    timestamps: true,
  });


  return mongoose.model('employeesTrainingPlan', EmployeesTrainingPlanSchema, 'employeesTrainingPlan');
};

