/*
* @Description: 乡镇区域的geoJson数据,根据县级area_code标识
* @Author: wangxi
* @Date: 2022-07-18
* @LastEditTime: 2022-07-18
* @LastEditors: Please set LastEditors
*/
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const townshipBoundarySchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    area_code: {// 县区域代码
      type: String,
      default: '',
    },
    geoJson: {// 乡镇区域的geoJson用于echarts map 绘制
      type: {
        type: String,
      },
      name: String,
      features: [
        {
          type: {
            type: String,
          },
          properties: {
            name: String,
            childrenNum: Number,
            level: String,
            subFeatureIndex: Number,

          },
          geometry: {
            type: {
              type: String,
            },
            coordinates: Array,

          },
        },
      ],

    },
  });
  return mongoose.model('TownshipBoundary', townshipBoundarySchema, 'townshipBoundary');

};

