module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  // const path = require('path');
  // const regionQueryTransformPlugin = require(path.join(process.cwd(), 'app/utils/regionQueryTransformPlugin'));

  /** 更改
   * 绝大部分字段是直接复制过来，名称没变
   * 但是原jobHelth表的name字段表示机构名称，而adminProjects表的name字段表示项目名称，我就采用了项目表的，机构名改为serviceName
   * 删除了adminProjects表的 corp、adminOrgID两个字段，我发现这个端没人用到
   */
  const jobHealthSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    zyjkPostReportData: {
      status: Number, // 0 为对接 1已对接
      completeTime: Date, // 对接时间
    },

    RCId: String, // 汝成系统中该项目的id
    // targetStandardId:'',// 危害因素标准
    numberOfVictims: Number, // 接害总人数
    employeeCount: Number, // 职工总人数

    postReportDataStatus: Number, // 对接省疾控状态 0 未对接 1已对接
    // 备注
    comment: String,
    QR_CODE: String,
    // =========== 受检单位相关信息 =============
    EnterpriseID: {
      type: String,
      ref: 'Adminorg',
    }, // 企业id
    companyID: {
      type: String,
    }, // 用人单位ID ，取信用代码
    companyName: {
      type: String,
      index: true,
      default: '',
    }, // 用人单位名称
    companyAddress: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      districts: Array, // 营业执照注册地址
      address: String, // 具体地址
    }], // 用人单位地址
    companyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    companyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 联系人手机号码
    companyContactEmail: {
      type: String,
      default: '',
    }, // 邮箱
    companyIndustry: {
      type: Array,
      default: [],
    }, // '所属行业',
    riskLevel: {
      type: String,
    }, // 风险等级，从adminorg中的level来
    districtRegAdd: {
      type: Array,
      default: [],
    }, // 工作场所

    // regAdd: String, // 注册地址
    corp: String, // 受检单位法人
    regType: String, // 受检单位注册类型/经济类型


    // ==================== 委托单位信息
    anthemEnterpriseID: { type: String, ref: 'Adminorg' }, // 企业id
    anthemCompanyID: {
      type: String,
      index: true,
    }, // 委托单位ID ，取信用代码
    newAnthemCompany: {
      type: String,
    }, // 新增的委托单位
    anthemCompanyName: {
      type: String,
      default: '',
    }, // 委托单位名称
    anthemCompanyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    anthemCompanyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 联系人手机号码
    anthemCompanyContactEmail: {
      type: String,
      default: '',
    }, // 邮箱
    anthemCompanyAddress: [{
      districts: Array, // 营业执照注册地址
      address: String, // 具体地址
      _id: String,
    }], // 委托单位地址

    // ===================== 机构相关信息  ==============================
    // serviceName: { 讲道理这个更符合，但是为了兼容老数据，只能以这个为准了，毕竟现在机构端还没有真实的数据
    name: {
      type: String,
      default: '',
    }, // 机构名字
    serviceAddress: {
      type: Array,
      default: [],
    }, // 机构地址
    serviceCorp: {
      type: String,
      default: '',
    }, // 机构法人
    serviceCorpPhoneNumber: {
      type: String,
      default: '',
    }, // 机构法人手机号码
    serviceOrgLevel: {
      type: String,
      default: '',
    }, // 机构资质等级

    // 委托时间 发起合同审核的时间，如果是老项目,委托时间设为检测时间的前一个月
    entrustDate: { type: Date },

    serviceTerm: {
      type: String,
      default: '',
    }, // 机构资质有效期
    serviceRanges: {
      type: String,
      default: '',
    }, // 机构服务范围
    serviceOrgID: {
      type: String,
      index: true,
    }, // 信用代码 - 字段名字是历史遗留问题，请注意
    serviceID: {
      type: String,
      index: true,
      ref: 'ServiceOrg',
    }, //  服务机构ID - 请和上面区划id的大小写
    serviceUserID: {
      type: String,
      ref: 'ServiceUser',
    }, // 当前操作人员ID。


    // ================== 关于项目的信息 =======================
    projectName: { // 对应上面机构名的妥协，这里也妥协
      type: String,
      default: '',
    }, // 项目名称
    serviceType: {
      type: String,
    }, // 技术服务类型
    serviceCon: {
      type: String,
      default: '',
    }, // 服务内容
    date: {
      type: Date,
      default: Date.now,
      index: true,
    }, // 项目创建时间
    expectStartTime: {
      type: Date,
    }, // 预计开始时间
    expectStopTime: {
      type: Date,
    }, // 预计结束时间,
    requiredTime: {
      type: Date,
    }, // 要求完成的时间
    applyTime: {
      type: Date,
    }, // 上报时间
    completeTime: {
      type: Date,
    }, // 实际完成时间
    status: {
      type: Number,
      default: 0,
    }, // 申报状态,0，未报送，1，已报送，2. 已退回 -1 已退回
    postToHZedStatus: {
      type: Number,
      default: 0,
    }, // 数据是否对接到了杭州数据库
    completeStatus: {
      type: Number,
      default: 0,
    }, // 完成状态，0，未完成； 1，已完成
    projectStop: {
      type: Boolean,
      dafault: false,
    }, // 项目是否暂停，false：未终止，true：终止
    projectCancel: {
      type: Boolean,
      dafault: false,
    }, // 项目是否终止，false：未终止，true：终止
    completeUpdate: {
      type: Number,
      default: 0,
    }, // 上报后更新状态
    InformationChange: [{
      _id: String,
      content: {
        type: Array,
        default: [],
      }, // 变更内容
      superUser: [ // 监管端id
        {
          type: String,
          ref: 'SuperUser',
        },
      ],
      nowTime: {
        type: Date,
        default: Date.now,
      }, // 变更内容
    }], // 信息变更 数组
    projectGroup: {
      type: String,
      default: '',
      index: true,
      ref: 'serviceDingtrees',
    }, // '项目检测组',
    EvaluationProjectManager: [{
      type: String,
      index: true,
      default: '',
      ref: 'ServiceEmployee',
    }], // '评价项目成员
    EvaluationProjectManagerAdmin: {
      type: String,
      default: '',
      index: true,
      ref: 'ServiceEmployee',
    }, // 评价组经理
    EvaluationGroup: {
      type: String,
      default: '',
      index: true,
      ref: 'serviceDingtrees',
    }, // 评价组
    personInCharge: {
      type: String,
      default: '',
      index: true,
      ref: 'ServiceEmployee',
    }, // '项目负责人ID',
    personsOfProject: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // '项目组成员ID',
    personsOfCompiling: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 编制人成员ID
    personsOfReviewer: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 审核人成员ID
    projectNumber: {
      type: String,
      unique: true,
      dropDups: true,
      index: true,
    }, // 项目编号
    declareAdds: { // 上报地址
      type: Array,
      default: [],
    },
    salesman: {
      type: String,
    },
    farmOut: { // 是否分包lht+(chenke)
      type: Boolean,
    },
    farmOutParams: { // 分包参数
      type: String,
    },
    description: { // 项目简介
      type: String,
    },
    vipRequirement: { // vip需求
      type: String,
    },
    // 申报地址，由于用户可以自行修改，所以和下面的workAdd区别开，
    // 存area_code，该code用于后注册的行政端也能读取申报的项目列表，
    // 存area_code前六位，即区县代码有效位数，其父级单位通过代码即可找到，全国的则需特殊处理
    workPlaces: [{ // 受检单位的工作场所地址
      _id: {
        type: String,
        default: shortid.generate,
      },
      workAddName: String, // 工作场所的中文地址

      name: String, // 工作场所的名称
      workAdd: {
        type: Array,
      }, // 工作场所具体地址，area_code
      checked: {
        type: Boolean,
        default: true,
      }, // 是否被检测，默认一般是检测了
    }], // 检测的工作场所，用于识别
    superUserID: {
      type: Array,
      default: [],
    }, // 机构/行政用户，申报时，如果区域内有行政用户，那就会存进去
    backInfo: [{
      _id: String,
      superUser: {
        type: String,
        ref: 'SuperUser',
      },
      backTime: {
        type: Date,
      }, // 回执时间
      cname: { // 监管端
        type: String,
        default: '',
      },
      name: { // 监管端经办人
        type: String,
        default: '',
      },
    }],
    wordFileName: { // 实验室检测报告单的word
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },

    // =============钱money
    projectPrice: {
      type: Number,
      default: 0,
    }, // 项目价格
    cooperationFee: {
      type: Number,
      default: 0,
    }, // 合作费
    reviewFee: {
      type: Number,
      default: 0,
    }, // 评审费
    otherFee: {
      type: Number,
      default: 0,
    }, // 其他费
    netEarnings: {
      type: Number,
      default: 0,
    }, // 净赚=价格-合作费-评审费-其他费


    // ============== 快递
    mailingAddress: {
      address: String,
      districts: Array,
    }, // 邮寄地址
    recipient: {
      type: String,
      default: '',
    }, // 收件人
    recipientPhoneNum: {
      type: String,
      default: '',
    }, // 收件号码

    // =============银行
    accountsBank: {
      type: String,
      default: '',
    },
    accountsAddress: {
      type: String,
      default: '',
    },
    accountsNumber: {
      type: String,
      default: '',
    },
    accountsPhoneNum: {
      type: String,
      default: '',
    },

    tariffNumber: {
      type: String,
      default: '',
    },


    // ========================= 原jobHelth表数据，除了机构名和项目名=================
    // 所有文件都加来源字段，为了方便不同端读取这个文件，不然不知道文件在哪个端存着
    // 其中默认值在不同端应该放不一样，拷贝过去后应该修改
    formData: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      type: {
        type: String,
      },
      // 工种
      workplace: {
        type: String,
      },
      // 工作地点
      factors: {
        type: String,
      },
      // 危害因素
      number: {
        type: String,
      },
      // 接触人数
      csystem: {
        type: String,
      },
      // 班制
      operation: {
        type: String,
      },
      // 运行情况
      time: {
        type: String,
      },
      // 接触时间
      protectiveUse: String, // 个人防护用品发放及使用情况
    }],
    year: Date, // 年度
    detectionCustom: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
        default: '',
      },
      url: {
        type: String,
        default: '',
      },
      source: {
        type: String,
        default: 'super', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
      // 一旦这个字段不是enterprise，那么企业端就无权修改
    }], // 自定义机构资质
    contract: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'super', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 合同书
    summary: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'super', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 汇总表
    report: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'super', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 报告书
    labReport: [{ // 检测结果报告单
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'super', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }],
    invoice: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'super', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 发票
    reportTime: {
      type: Date,
    }, // 现场采样/检测时间
    samplingDates: Array, // 只用于报送 现场采样日期
    checkPointImg: String, // 检测点分布示意图
    isVIP: { // 会员类型：战略客户、vip客户、普通客户
      type: String, // svip、vip、ordinary
      default: 'ordinary',
    },
    source: {
      type: String,
      default: 'super', // super || enterprise || operate
    }, // 数据来源
    isUrgent: {
      type: Boolean,
      default: false,
    }, // 是否加急

    // 审批start=========================
    // 合同审批状态以及审批实例id
    approvedStatus: {
      type: String,
      default: '0',
      index: true,
    }, // , 0 未发起审批 NEW：新创建 RUNNING：审批中 TERMINATED：被终止 COMPLETED：完成 CANCELED：取消 REFUSE:已拒绝
    approvedTime: Date, // 审批时间（状态变更时都要更新）
    process_instance_id: String, // 审批编号
    approvedStartTime: Date, // 合同审批发起时间
    approvedWordFileName: String, // 生成word文件名称
    approvedFormData: {
      legitimate: String, // 是否符合法律法规的要求
      qualificationOK: String, // 本公司和服务人员资质是否满足要求
      instrumentOK: String, // 检测检验设备是否满足要求
      standardOK: String, // 检测检验方法、标准适用
      isPackage: String, // 是否分包
      packageInfo: String, // 分包具体项目
      packageOrg: String, // 拟选分包机构
      packageBeInformed: String, // 合同或协议是否已明确分包内容并告知客户
      controllableRisk: String, // 技术风险和商业风险是否可控
      chargeOK: String, // 项目收费是否能满足工作要求
      progressOK: String, // 能否按合同要求进度完成项目
      logisticsOK: String, // 后勤保障能否满足要求
      secrecyOK: String, // 是否满足项目和合同保密要求
    },
    approvedComment: String, // 最总评审结论
    approvedUsers: [{ // 所有评审人员,评审负责人：如果项目已经完成了，那么就是最后一个人
      _id: String,
      signPath: String, // 签字
      // name:String,//名字
      serviceEmployeeId: { type: String, ref: 'ServiceEmployee' },
      userId: String, // 钉钉id
    }],

    // 报告审批状态以及审批实例id
    reportProcessInstanceId: String, // 报告单审批实例id
    reportApprovedStatus: { type: String, index: true }, // 报告单审批状态，同上
    // , 0 未发起审批 NEW：新创建 RUNNING：审批中 TERMINATED：被终止 COMPLETED：完成 CANCELED：取消 REFUSE:已拒绝
    reportApprovedTime: Date, // 报告单审批时间（状态变更时都要更新）
    reportApprovedStartTime: Date, // 报告单审批发起时间

    reportFinalProcessInstanceId: String, // 报告审批实例id
    reportFinalApprovedStatus: { type: String, index: true }, // 报告审批状态
    reportFinalApprovedTime: Date, // 报告审核时间（状态变更时都要更新）
    // finishTime: Date, // 报告审核完成时间
    approvalPerson: String, // 报告审核批准人（通过钉钉接口获取的钉钉通讯录的姓名）
    // 审批 end========================
    checkResultStatus: Number, // 检测结果计算状态 0 未计算 1 已计算

    sampleList: { default: false, type: Boolean }, // 交样单是否完成

    reportStatus: { // 报告生成状态 0未生成  1已生成  2已上传
      type: String,
      default: '0',
    },
    reportGenerateFileName: { // 生成报告文件名
      type: String,
      default: '',
    },
    reportUploadFile: { // 上传报告文件
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },
    officialReportUploadFile: { // 正式稿文件
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },

    reportRecordUploadFile: { // 职位记录上传
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },

    labReportRecordUploadFile: { // 实验室记录上传
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },
    serviceArea: [{
      type: String,
      match: /^[0-9]*$/,
      enum: [ '1', '2', '3', '4', '5', '6' ],
    }], // 关于此次项目的企业涉及的技术服务领域，存得是编码，报送信息中要用 [采矿业:'1','化工、石化及医药':'2','冶金、建材':'3','机械制造、电力、纺织、建筑和交通运输等行业领域':'4','核设施':'5','核技术应用':'6']
    companyScale: { // 企业规模
      type: String,
      enum: [ '大型', '中型', '小型', '微型', '其他' ],
    },
    investigationTime: Date, // 现场调查时间
    detectionTime: Date, // 现场采样/检测时间
    preparer: {
      type: String,
      ref: 'ServiceEmployee',
    }, // 对接省疾控填表人
    serviceMatters: [[ String ]], // 项目组成员的服务事项
    serviceMatters2: [{ // 先用这个字段，后面serviceMatters结构需要调整
      _id: {
        type: String,
        default: shortid.generate,
      },
      employeeId: String, // 员工id
      serviceItemList: [{ type: String }], // 服务事项
    }], // 项目组成员的服务事项
    issuer: { // 报告签发人
      name: String,
      employeeId: String, // serviceEmployee id
    },
    issueDate: Date, // 报告签发日期
    // 退回状态
    giveBackStatus: {
      type: Boolean,
      default: false,
    },
    giveBackTime: Date, // 退回时间
    // 退回备注
    giveBackRemark: {
      type: String,
      default: '',
    },
  });
  // jobHealthSchema.plugin(regionQueryTransformPlugin, {
  //   _regionFields: 'workPlaces.workAdd',
  // });
  return mongoose.model('jobHealth', jobHealthSchema);
};
