/**
 * Created by Zhanglc on 2022/3/18.
 * 危害申报底部信息
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const OnlineDeclarationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    address: { // 申报地址
      type: String,
    },
    guidance: { // 申报指导
      type: String,
    },
    phone: String, // 人工指导
  });

  return mongoose.model('OnlineDeclaration', OnlineDeclarationSchema, 'onlineDeclaration');
};
