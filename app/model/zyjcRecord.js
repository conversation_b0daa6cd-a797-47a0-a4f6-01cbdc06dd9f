module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const zyjcRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { type: String },
    recordId: { type: String, ref: 'Record' },
    otherData: Object,
    systemGenerate: [{
      sort: String, // 是否是系统生成
      fileType: String, // 文件类别
      name: String, // 用户上传文件名称
      staticName: String, // 后台处理后存储到public静态资源的文件名,
      staticSrc: String, // 后台处理后存储到public静态资源的路径名
    }],
    clientUpload: [{
      sort: {
        type: String,
        default: '用户上传',
      },
      fileType: String, // 文件类别
      name: String, // 用户上传文件名称
      staticName: String, // 后台处理后存储到public静态资源的文件名
      staticSrc: String, // 后台处理后存储到public静态资源的路径名
    }],
  }, { timestamps: true });
  return mongoose.model('zyjcRecord', zyjcRecordSchema, 'zyjcRecord');
};
