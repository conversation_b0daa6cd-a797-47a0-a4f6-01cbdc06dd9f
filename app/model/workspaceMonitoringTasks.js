/**
 * 各省的工作场所职业病危害因素监测任务表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceMonitoringTaskSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 任务名称
      type: String,
      trim: true,
      required: true,
    },
    year: { // 任务所属年份
      type: Number,
      default: new Date().getFullYear(),
    },
    startTime: { // 任务开始时间
      type: Date,
      default: Date.now,
    },
    endTime: { // 任务结束时间
      type: Date,
      required: true,
    },
    level: { // 任务层级：1-省，2-市，3-区
      type: Number,
      required: true,
      enum: [ 1, 2, 3 ],
    },
    areaName: [ String ], // 地区名称
    nationalTaskId: { // 国家任务ID
      type: String,
      ref: 'NationalWorkspaceMonitoringTask',
      required: true,
    },
    parentTaskId: { // 上级任务ID
      type: String,
      ref: 'WorkspaceMonitoringTask',
      default: '',
    },
    customHazardFactors: [{ // 每个级别额外的危害因素检测列表
      type: String,
      ref: 'OccupationalExposureLimits',
    }],
    orgId: { // 实际执行任务的单位ID（仅3级任务需要）
      type: String,
      ref: 'SuperUser',
    },
    // 最后一次修改者id
    lastModifierId: {
      type: String,
    },
    // 用人单位名单
    employerList: [
      {
        type: String,
        ref: 'Adminorg',
      },
    ],
    taskNum: { // 任务数
      type: Number,
      set: v => parseInt(v),
    },
    isDeleted: { // 是否删除
      type: Boolean,
      default: false,
    },
  }, { timestamps: true });

  return mongoose.model('WorkspaceMonitoringTask', WorkspaceMonitoringTaskSchema, 'workspaceMonitoringTask');
};
