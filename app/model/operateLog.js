
/**
 * 体检端-体检端资质资质表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const operateLogSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    optType: {// 操作类型
      type: String,
      require: true,
      enum: [ '删除', '创建', '更新' ],
    },
    optIds: {
      type: Array, // 操作的数据id
      require: true,
    },
    source: {// 来源
      type: String,
      default: 'super',
      enum: [ 'super', 'enterprise', 'operate', 'physicalExam' ],
    },
    optUserId: {// 操作用户id
      type: String,
      require: true,
    },
    optUserModelName: {// 操作用户model名称 首字母大写
      type: String,
      require: true,
    },
    filterJson: String, // 更新或者删除的过滤（查询）条件
    deleteData: { // 删除的文档
      type: Array,
    },
    updateInfoJson: { // 更新的字段以及内容 json
      type: String,
    },
    modelName: {// 操作记录的model 首字母大写
      type: String,
      require: true,
    },
    supplementaryNotes: { // 补充说明
      type: String,
    },
  }, { timestamps: true });

  return mongoose.model('operateLogs', operateLogSchema, 'operateLog');

};

