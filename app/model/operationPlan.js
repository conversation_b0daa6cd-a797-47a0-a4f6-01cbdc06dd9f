/**
 * 运营端用户对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const OperationPlanSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    datePeriod: [{
      type: Date,
    }], // 限制时间段
    runTime: [{
      type: String,
    }], // 运行时间
    platform: {
      type: Array,
    }, // 平台
    notice: String, // 通知
  }, { timestamps: true });
  return mongoose.model('OperationPlan', OperationPlanSchema, 'OperationPlan');
};
