/**
 * 培训机构端-注册机构表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const upload_http_path = app.config.upload_http_path || '';


  const PxOrgSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 机构名称
      type: String,
      trim: true,
      require: true,
    },
    organization: { // 社会统一信用代码
      type: String,
      trim: true,
      require: true,
      index: true,
    },
    regAddr: { // 注册的省市区
      default: [],
      type: Array,
    },
    address: { // 注册详细地址
      type: String,
      trim: true,
    },
    corp: String, // 法人代表
    corpPhone: String, // 法人电话
    managers: [ // 管理人员id集合
      {
        type: String,
        ref: 'PxOrgUser',
      },
    ],
    trainingFormat: {
      type: String, // 线上 online  线下 offline
      enum: [ 'online', 'offline' ],
    },
    img: { // 营业执照
      type: String,
      // require: true,
      trim: true,
      set(val) {
        return (val && val.indexOf(upload_http_path) !== -1) ? val.split(upload_http_path)[1] : val;
      },
    },
    logo: String, // 平台logo
    orgUrl: String, // 平台网址
    introduce: String, // 平台简介
    message: String, // 审核结果
    status: { // 状态
      type: Number,
      default: 0, // 0未注册；1已上传营业执照；2已上传资质证书；3审核通过；4审核不通过；5注销
      enum: [ 0, 1, 2, 3, 4, 5 ],
    },
    administrator: { // 超级管理员，第一个用户，也是绑定了社会统一信用代码的人
      type: String,
      ref: 'PxOrgUser',
    },
    sourse: { // 哪个端创建的
      type: String,
      default: 'super',
    },
    archivalMaterials: [{ // 备案材料
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileType: String, // 文件类型
      name: String, // 文件名称
      url: String, // 文件储存名称
    }],
  }, { timestamps: true });


  return mongoose.model('PxOrg', PxOrgSchema, 'pxOrg');

};

