
module.exports = app => {
  // 档案
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const record = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      EnterpriseID: String, // 公司id
      sort: { type: String, ref: 'recordSort' },
      // name: String, // 档案名称
      num: String, // 档案编号
      year: Date, // 年度
      organizationPerson: [{ type: String, ref: 'Employees' }], // 编制人
      projects: [{ type: String, ref: 'project' }], // 项目
      recordFile: String, // 档案文件
      customRecordFile: String, // 客户上传的档案文件
      promiseFile: String, // 承诺书文件
      customPromiseFile: String, // 客户上传的承诺书文件
      organizationTime: Date, // 编制时间
      isNeedUpdate: Number, //  0 为档案未生成，1 为档案已生成，2 为档案需更新，-1 为生成档案失败 wzq+
      updateList: Array,
    }, { timestamps: true });
  return mongoose.model('Record', record, 'records');
};
