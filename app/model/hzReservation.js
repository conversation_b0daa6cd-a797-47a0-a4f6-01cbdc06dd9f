/**
 * 杭州H5预约服务
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const HzReservationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 姓名
      type: String,
      trim: true,
      required: true,
    },
    // 手机号
    phoneNum: {
      type: String,
      trim: true,
      required: true,
    },
    // 是否需要上门
    isNeedVisit: {
      type: Boolean,
      default: false,
      required: true,
    },
    // 单位名称
    company: {
      type: String,
      trim: true,
    },
    // 所在区域
    area: [ String ],
    // 详细地址
    address: String,
    // 问题描述
    description: {
      type: String,
      trim: true,
      required: true,
    },
    // 受理情况
    acceptDescription: {
      type: String,
      trim: true,
    },
    // 预约状态
    status: {
      type: String,
      enum: [ '0', '1', '2', '3' ], // 0:待受理 1:已受理 2:已完成 3:已取消
      default: '0',
    },

  }, { timestamps: true });

  return mongoose.model('HzReservation', HzReservationSchema, 'hzReservation');

};

