/**
 * 国家年度工作场所职业病危害因素监测任务
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const NationalWorkspaceMonitoringTaskSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 任务名称
      type: String,
      trim: true,
      required: true,
    },
    year: { // 任务所属年份
      type: Number,
      default: new Date().getFullYear(),
    },
    startTime: { // 任务开始时间
      type: Date,
    },
    endTime: { // 任务结束时间
      type: Date,
    },
    industries: [{ // 监测行业 && 危害因素 && 岗位/工种
      industry: [{
        type: String, // 行业分类编码
      }],
      requiredHazardFactors: [{ // 必检危害因素名称
        type: String,
        trim: true,
      }],
      optionalHazardFactors: [{ // 选检危害因素名称
        type: String,
        trim: true,
      }],
      workType: [{
        type: String,
        ref: 'MonitoringWorkTypes',
      }],
    }],
    isDeleted: { // 是否删除
      type: Boolean,
      default: false,
    },
  }, { timestamps: true });

  return mongoose.model('NationalWorkspaceMonitoringTask', NationalWorkspaceMonitoringTaskSchema, 'nationalWorkspaceMonitoringTask');
};
