/**
 * Created by xxn on 2025/3/21.
 * 场所监测工种/岗位字典对照表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const MonitoringWorkTypesSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 工种名称
      type: String,
      trim: true,
      required: true,
    },
    code: { // 工种编码
      type: String,
      trim: true,
    },
    // 所属行业编码
    industryCode: {
      type: String,
    },

  }, { timestamps: true });

  // 所属行业编码+工种名称唯一
  MonitoringWorkTypesSchema.index({ industryCode: 1, name: 1 }, { unique: true });

  return mongoose.model('MonitoringWorkTypes', MonitoringWorkTypesSchema, 'monitoringWorkTypes');
};
