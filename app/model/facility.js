module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const facilitySchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { type: String },
    // 企业id
    formData: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      // 设备名称
      model: { type: String },
      // 型号
      number: { type: String },
      // 数量
      shop: { type: String },
      // 所在车间
      protection: { type: String },
      // 防护用途
      installation: { type: String },
      // 安装单位
      time: { type: String },
      // 验收时间
    }],
  });
  return mongoose.model('facility', facilitySchema);
};
