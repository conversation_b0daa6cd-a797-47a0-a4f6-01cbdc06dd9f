module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const zygrRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String, // 公司id
    employeeId: { type: String, ref: 'Employees' }, // 员工
    recordId: { type: String, ref: 'Record' },
    recordNum: String, // 员工档案编号
    mergeWord: { // 合并的word
      sort: String, // 是否是系统生成
      fileType: String, // 文件类别
      originName: String, // 用户上传文件名称
      staticName: String, // 后台处理后存储到public静态资源的文件名,
    },
    recordFiles: [
      {
        name: { type: String },
        isGenerate: Boolean, // 是否是系统生成
        systemGenerate: {
          sort: String, // 是否是系统生成
          fileType: String, // 文件类别
          originName: String, // 用户上传文件名称
          staticName: String, // 后台处理后存储到public静态资源的文件名,
        },
        clientUpload: [{
          sort: String,
          fileType: String, // 文件类别
          originName: String, // 用户上传文件名称
          staticName: String, // 后台处理后存储到public静态资源的文件名
        }],

      },
    ],
  }, { timestamps: true });
  return mongoose.model('zygrRecord', zygrRecordSchema, 'zygrRecord');
};
