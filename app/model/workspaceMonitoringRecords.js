/**
 * 场所监测记录 created on 2025/3/24
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceMonitoringRecordsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    taskId: { // 监测任务ID
      type: String,
      ref: 'WorkspaceMonitoringTask',
      required: true,
    },
    // 委托单位
    entrustOrgId: {
      type: String,
      ref: 'SuperUser',
      required: true,
    },
    // 监测单位
    monitoringOrgId: {
      type: String,
      ref: 'SuperUser',
      required: true,
    },
    // 用人单位
    employerId: {
      type: String,
      ref: 'Adminorg',
      required: true,
    },
    // 检测机构(全流程的机构id)
    serviceOrgId: {
      type: String,
      ref: 'ServiceOrg',
      required: true,
    },
    // 检测报告（全流程的项目id）
    jobHealthId: {
      type: String,
      ref: 'JobHealth',
    },
    // 现场调查（调查表）id
    investigationId: {
      type: String,
    },
    // 创建者
    creator: {
      type: String,
    },
    status: { // 状态 0未开始 1进行中 2已完成
      type: Number,
      default: 0,
    },
    isDeleted: { // 是否删除
      type: Boolean,
      default: false,
    },
  }, { timestamps: true });

  return mongoose.model('WorkspaceMonitoringRecords', WorkspaceMonitoringRecordsSchema, 'workspaceMonitoringRecords');
};
