module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 证书
  const CertificateSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    number: String, // 编号
    // 公开课的证书编号：ZYWSPX-DP课程编号-完成日期-序号；
    // 行政监督创建的培训计划对应的证书编号：省市区行政代码-创建培训计划日期-批次-顺序编号，如杭州市组织的第一批次的培训，ZYWSPX-3301002021031001-0001，进入代发放证书列表，可以批量处理。
    name: { // 名称
      type: String,
      default: '职业卫生培训合格证明',
    },
    type: { // 培训人员类型
      type: Number,
      default: 1, // 1职业卫生管理人员 2主要负责人 3 员工
    },
    trainingType: { // 培训类型
      type: Number,
      default: 1, // 1管理员培训 2自主培训
    },
    template: { // 证书模板类型 仅仅用于生成证书
      type: Number,
    },
    certificateStatus: { // 证书领取状态
      type: Number,
      default: 1,
      enum: [ 1, 2, 3 ], // 1是已申请审核中 2 审核通过 3审核未通过
    },
    unit: String, // 发证单位名称
    superID: { // 监管id, 监管端创建的培训则需要
      type: String,
      ref: 'SuperUser',
      default: '',
    },
    img: { // 证书图片
      type: String,
    },
    issuanceTime: { // 发证时间
      type: Date,
    },
    effectiveTime: { // 有效时间，从发证时间时间算起三年内
      type: Date,
    },
    auditor: { // 审核人员的id, 分为监管端和运营端，具体的要看trainingType
      type: String,
    },
    winner: { // 证书获得者的信息存储
      employeeId: String,
      headImg: String,
      IDNum: String,
      companyName: String,
      name: String,

      companyRegAdd: String,
      districtRegAdd: Array,
    },
    trainingDetail: { // 培训详情
      coursesList: Array, // 课程列表
      name: String, // 培训的名称
      time: Date, // 培训的时间
    },
    adminUserId: {
      type: String,
      ref: 'AdminUser',
    },
    employeeId: {
      type: String,
      ref: 'Employees',
    },
    personalTrainingId: {
      type: String,
      ref: 'PersonalTraining',
    },
    EnterpriseID: {
      type: String,
      ref: 'Adminorg',
    },
    source: { // 证书来源
      type: Number,
      default: 1, // 1是培训的，2是导入的
    },
  }, { timestamps: true });


  return mongoose.model('Certificate', CertificateSchema, 'certificate');
};
