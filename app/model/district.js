/**
 * 行政区划表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const districtPlugin = require('../utils/districtPlugin');

  const DistrictSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    id: String, // 原库id
    parent_code: {
      type: String,
    },
    name: {
      type: String,
    },
    merger_name: String,
    area_code: {
      type: String,
    },
    city_code: String,
    lat: String,
    lng: String,
    level: String,
    short_name: String,
    pinyin: String,
    zip_code: String, // 邮编
  });

  DistrictSchema.index({ parent_code: 1 });
  DistrictSchema.index({ name: 1 });
  DistrictSchema.index({ area_code: 1 });
  DistrictSchema.plugin(districtPlugin, {
    config: app.config,
  });

  return mongoose.model('District', DistrictSchema, 'district');

};
