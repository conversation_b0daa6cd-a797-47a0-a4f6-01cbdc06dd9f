

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const moment = require('moment');

  const HazardStatisticsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    // 时间维度
    timeFrame: {
      type: String,
      enum: [ 'daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'all' ],
      required: true,
    },
    timePoint: {
      type: Date,
      required: true,
    },

    // 企业数量统计
    enterpriseCount: {
      total: Number, // 总企业数
      checked: Number, // 已检查企业数
      exceeding: Number, // 超标企业数
    },

    // 危害因素统计
    hazardFactorsStats: [{
      type: {
        type: String,
        enum: [ 'dust', 'chemical', 'physical', 'radiation', 'biological', 'noise', 'heat' ],
      },
      name: String, // 危害因素显示名称
      total: Number, // 检测点总数
      exceed: Number, // 超标点数
      exceedRate: Number, // 超标率
      affectedEnterprises: Number, // 影响企业数
      unit: String, // 单位
    }],

    // 行业分布统计
    industryStats: [{
      code: String, // 行业编码
      name: String, // 行业名称
      level: Number, // 行业级别(1,2,3级)
      parentCode: String, // 父级编码
      count: Number, // 企业数量
      exceedCount: Number, // 超标企业数量
      percentage: Number, // 行业占比(0-100)
      ranking: Number, // 排名
    }],

    // 区域分布统计
    regionStats: [{
      code: String, // 区域编码
      name: String, // 区域名称
      level: Number, // 区域级别(省市区)
      parentCode: String, // 父级编码
      count: Number, // 企业数量
      exceedCount: Number, // 超标企业数量
      percentage: Number, // 区域占比(0-100)
      ranking: Number, // 排名
    }],

    // 企业地图数据
    mapData: [{
      enterpriseId: {
        type: String,
        ref: 'Adminorg',
      },
      name: String, // 企业名称
      industry: [], // 行业名称
      address: String, // 详细地址
      location: { // 坐标
        longitude: Number,
        latitude: Number,
      },
      hazardFactors: [{
        name: String,
        value: Number,
        unit: String,
        isExceed: Boolean,
      }],
      employeeCount: Number,
    }],

    // 筛选条件记录
    filterConditions: {
      hazardFactors: [ String ], // 危害因素筛选
      regionCode: String, // 区域筛选
      industryCode: String, // 行业筛选
      dateRange: { // 时间筛选
        start: Date,
        end: Date,
      },
    },

    // 元数据
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
    lastCalculationTime: Date, // 最后一次计算时间
  });

  // 设置索引
  HazardStatisticsSchema.index({ timeFrame: 1, timePoint: -1 }, { unique: true });
  HazardStatisticsSchema.index({ 'hazardFactorsStats.type': 1 });
  HazardStatisticsSchema.index({ 'industryStats.code': 1 });
  HazardStatisticsSchema.index({ 'regionStats.code': 1 });
  HazardStatisticsSchema.index({ 'mapData.enterpriseId': 1 });
  HazardStatisticsSchema.index({ 'filterConditions.regionCode': 1 });
  HazardStatisticsSchema.index({ 'filterConditions.industryCode': 1 });

  // 格式化日期
  HazardStatisticsSchema.path('createdAt').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  HazardStatisticsSchema.path('updatedAt').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  // 虚拟字段 - 数据新鲜度(分钟)
  HazardStatisticsSchema.virtual('dataStaleness').get(function() {
    const now = new Date();
    const updated = this.updatedAt instanceof Date ? this.updatedAt : new Date(this.updatedAt);
    return Math.floor((now - updated) / (1000 * 60));
  });

  HazardStatisticsSchema.set('toJSON', { getters: true, virtuals: true });
  HazardStatisticsSchema.set('toObject', { getters: true, virtuals: true });

  return mongoose.model('HazardStatistics', HazardStatisticsSchema, 'hazardStatistics');
};
