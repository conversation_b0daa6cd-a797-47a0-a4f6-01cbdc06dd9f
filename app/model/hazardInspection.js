const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const shortid = require('shortid');

const HazardInspectionSchema = new Schema({
  _id: {
    type: String,
    default: shortid.generate,
  },
  enterpriseId: {
    type: String,
    ref: 'Adminorg',
    required: true,
    index: true,
  },
  // 企业基本信息缓存
  enterpriseInfo: {
    name: String, // 企业名称
    industryCategory: [{ // 行业分类
      type: Array,
    }],
    workAddress: [{ // 工作地址
      districts: Array, // 区域
      address: String, // 详细地址
      point: Array, // 经纬度
    }],
  },

  // 检查基本信息
  inspectionTime: { // 检查时间
    type: Date,
    required: true,
    index: true,
  },
  inspectionType: { // 检查类型(常规检查/专项检查)
    type: String,
    enum: [ 'regular', 'special' ],
    default: 'regular',
  },

  // 危害因素检查结果
  hazardFactors: [{
    type: { // 危害因素类型
      type: String,
      enum: [ 'dust', 'chemical', 'physical', 'radiation', 'biological', 'noise', 'heat' ],
      required: true,
    },
    name: String, // 具体危害因素名称
    checkPoints: Number, // 检测点数
    exceedPoints: Number, // 超标点数
    exceedRate: Number, // 超标率
    workspace: String, // 车间/工作区域
    station: String, // 工位
    value: Number, // 检测值
    unit: String, // 单位
    standardValue: Number, // 标准值
    isExceed: Boolean, // 是否超标
  }],

  // 合规状态
  complianceStatus: {
    status: { // 合规状态
      type: String,
      enum: [ 'compliant', 'nonCompliant' ],
      default: 'compliant',
    },
    issues: [{ // 不合规问题
      description: String, // 问题描述
      severity: { // 严重程度
        type: String,
        enum: [ 'low', 'medium', 'high' ],
      },
      remedyPlan: String, // 整改计划
      remedyStatus: { // 整改状态
        type: String,
        enum: [ 'pending', 'inProgress', 'completed' ],
        default: 'pending',
      },
      remedyDeadline: Date, // 整改期限
    }],
  },

  // 统计数据 - 预计算字段用于快速查询
  statistics: {
    totalEmployees: Number, // 总员工数
    exposedEmployees: Number, // 接触职业病危害的员工数
    hazardFactorTypes: [ String ], // 所有危害因素类型
    totalCheckPoints: Number, // 总检测点数
    totalExceedPoints: Number, // 总超标点数
    overallExceedRate: Number, // 整体超标率
  },

  // 元数据
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  createdBy: {
    type: String,
    ref: 'AdminUser',
  },
  lastUpdatedBy: {
    type: String,
    ref: 'AdminUser',
  },
});

// 索引优化查询性能
HazardInspectionSchema.index({ 'enterpriseInfo.industryCategory': 1 });
HazardInspectionSchema.index({ 'enterpriseInfo.workAddress.districts': 1 });
HazardInspectionSchema.index({ 'hazardFactors.type': 1 });
HazardInspectionSchema.index({ 'statistics.hazardFactorTypes': 1 });
HazardInspectionSchema.index({ inspectionTime: -1, enterpriseId: 1 });

module.exports = mongoose.model('HazardInspection', HazardInspectionSchema, 'hazardInspections');
