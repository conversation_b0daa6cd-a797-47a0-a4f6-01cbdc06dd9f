
module.exports = app => {
  // 生物检测结果
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const checkResult = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    year: Date, // 年份
    source: {
      type: String,
      default: 'enterprise',
    }, // 数据来源，enterprise、service、super、operate  代码不改，在不同端，这个默认值需要修改
    EnterpriseID: { type: String }, // 企业id
    jobHealthId: { type: String, ref: 'jobHealth' }, // 检测项目id
    biologicalFactors: { // 生物
      name: {
        type: String,
        default: '生物',
      }, //
      value: {
        type: String, default: 'biologicalFactors',
      },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: String, // 工种
        checkAddress: { type: String }, // 监测点位置
        checkProject: { type: String }, // 检测项目
        MAC: String,
        TWA: String,
        STEL: String,
        checkResult: { type: String }, // 判定结果
      },

      ],
    },
    chemistryFactors: { // 化学
      name: { type: String, default: '化学物质' },
      value: { type: String, default: 'chemistryFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: { type: String }, // 工种
        workspace: String, // 车间
        station: String, // 岗位
        // checkAddressDetail: String, // 具体监测点位置
        level: String, // 危害因素浓度等级 0:<=1%OEL; I:>1% <=10%OEL; II:>10% <=50%OEL; III:>50% <100%OEL; IV:>100%OEL
        percent: Number, // 危害因素浓度百分比
        checkProject: { type: String }, // 检测项目(危害因素)
        MAC: String,
        TWA: String,
        STEL: String,
        PE: String, // 峰接触浓度
        excursionLimits: { type: String }, // 超限倍数
        checkResult: { type: String }, // 判定结果
      }],
    },
    dustFactors: { // 粉尘
      name: { type: String, default: '粉尘' },
      value: { type: String, default: 'dustFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: { type: String }, // 工种
        workspace: String, // 车间
        station: String, // 岗位
        checkAddressDetail: String, // 具体监测点位置
        checkProject: { type: String }, // 检测项目
        MAC: String,
        TWA: String,
        STEL: String,
        PE: String, // 峰接触浓度
        excursionLimits: { type: String }, // 超限倍数
        AllDustPercent: Number, // 总尘百分比
        respirableDustPercent: Number, // 呼尘百分比
        checkResult: { type: String }, // 判定结果
        level: String, // 危害因素浓度等级 0:<=1%OEL; I:>1% <=10%OEL; II:>10% <=50%OEL; III:>50% <100%OEL; IV:>100%OEL
        percent: Number, // 危害因素浓度百分比
      }],
    },
    handBorneVibrationFactors: { // 手传震动
      name: { type: String, default: '手传震动' },
      value: { type: String, default: 'handBorneVibrationFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: String, // 工种
        checkAddress: { type: String }, // 监测点位置
        dayTouchTime: { type: String }, // 日接触时间
        touchLimit: { type: String }, // 接触限值
        ahw: String, // 频率计权振动加速度测量值ahw（m/s2）
        fourHoursAccelerated: String, // 4h等能量频率计权振动加速度值（m/s2）
        checkResult: { type: String }, // 判定结果
      },

      ],
    },
    heatFactors: { // 高温
      name: { type: String, default: '高温' },
      value: { type: String, default: 'heatFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workspace: String, // 车间
        station: String, // 岗位
        checkAddressDetail: String, // 具体监测点位置
        workType: String, // 工种
        averageValue: { type: String }, // WBGT平均值
        touchTime: { type: String }, // 接触时间
        labourIntensity: { type: String }, // 体力劳动强度
        touchLimit: String, // 职业接触限值（℃）
        conclusion: { type: String }, // 评价结论
      }],
    },
    highFrequencyEleFactors: { // 高频电磁场检测结果
      name: { type: String, default: '高频电磁场' },
      value: { type: String, default: 'highFrequencyEleFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: String, // 工种
        checkAddress: { type: String }, // 监测点位置
        harmFactors: String, // 职业病危害因素名称
        radiationHZ: String, // 辐射频率（MHz）
        electricIntensity: String, // 电场强度（V/m）
        electricIntensityData: String, // 电场强度测量值（V/m）
        magneticIntensity: String, // 磁场强度（A/m）
        magneticIntensityData: String, // 磁场强度测量值（A/m）
        checkResult: { type: String }, // 判定结果
      }],
    },
    laserFactors: { // 激光辐射
      name: { type: String, default: '激光辐射' },
      value: { type: String, default: 'laserFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: String, // 工种
        checkAddress: { type: String }, // 监测点位置
        touchLimit: { type: String }, // 接触限值
        testAverage: String, // 平均检测值（W/cm2）
        irradiance: String, // 辐射度
        checkResult: { type: String }, // 判定结果
      }],
    },
    microwaveFactors: { // 微波辐射
      name: { type: String, default: '微波辐射' },
      value: { type: String, default: 'microwaveFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: String, // 工种
        checkAddress: { type: String }, // 监测点位置
        shortTimeContactPowerDensity: String, // 短时间接触功率密度最大值（mW/cm2）
        shortTimeLimit: String, // 短时间接触功率密度接触限值（mW/cm2）
        average: String, // 平均值（μW/cm2）
        averagePowerDensityLimit: String, // 8h平均功率密度接触限值（μW/cm2）
        checkResult: { type: String }, // 判定结果
      }],
    },
    noiseFactors: { // 噪声
      name: { type: String, default: '噪声' },
      value: { type: String, default: 'noiseFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: { type: String }, // 工种
        workspace: String, // 车间
        station: String, // 岗位
        checkAddressDetail: String, // 具体监测点位置
        touchTime: { type: String }, // 接触时间
        equalLevel: { type: String }, // 8/40h等效声级检测数值[dB(A)]
        checkData: String, // 检测值[dB(A)]
        touchLimit: { type: String }, // 职业接触限值
        checkResult: { type: String }, // 判定结果
      }],
    },
    powerFrequencyElectric: {
      name: { type: String, default: '工频电场' },
      value: { type: String, default: 'powerFrequencyElectric' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: { type: String }, // 工种
        workspace: String, // 车间
        station: String, // 岗位
        checkAddressDetail: String, // 具体监测点位置
        electricIntensity: String, // 电场强度测量值（kV/m）
        electricIntensityLimit: String, // 电场强度职业接触限值（kV/m）
        checkResult: { type: String }, // 判定结果
      }],
    },
    SiO2Factors: {
      name: { type: String, default: '游离二氧化硅' },
      value: { type: String, default: 'SiO2Factors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: { type: String }, // 工种
        checkAddress: String, // 监测点位置
        checkProject: { type: String }, // 检测项目
        checkResult: { type: String }, // 判定结果
        checkType: String, // 检测结果类型 >=10%矽尘 其余是呼尘
      }],
    },
    ultraHighRadiationFactors: {
      name: { type: String, default: '超高频辐射' },
      value: { type: String, default: 'ultraHighRadiationFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: { type: String }, // 工种
        workspace: String, // 车间
        station: String, // 岗位
        checkAddressDetail: String, // 具体监测点位置
        electricAverage: String, // 脉冲波电场强度平均值（V/m）
        eightHoursTouchLimit: String, // 8h职业接触限值（V/m）
        checkResult: { type: String }, // 判定结果
      }],
    },
    ultravioletFactors: {
      name: { type: String, default: '紫外辐射' },
      value: { type: String, default: 'ultravioletFactors' },
      formData: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: String, // 工种
        checkAddress: { type: String }, // 监测点位置
        irradiance: String, // 有效辐照度（μW/cm2）
        eightHoursTouchLimit: String, // 8h职业接触限值（μW/cm2）
        checkResult: { type: String }, // 判定结果
      }],
    },

  });
  return mongoose.model('CheckResult', checkResult, 'checkResult');
};
