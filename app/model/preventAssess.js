
module.exports = app => {
  const shortid = require('shortid');
  // 防治自查评价
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const preventAssess = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: {
      type: String,
      index: true,
    }, // 企业ID
    year: Date, // 年度
    assessDate: Date, // 具体自查日期
    unConformityCount: Number, // 不符合项目
    reasonableDeficiencyCount: Number, // 合理缺项
    mark: { type: Number, default: 0 }, // 得分
    level: { type: String, default: 'C' }, // 职业健康管理状况,等级
    realityMark: Number, // 去除合理缺项后实际得分
    allMark: Number, // 总分值
    jgChange: {
      type: Boolean,
      default: false,
    }, // 是否被监管端修改过
    formData: [
      // 自查项目分类
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        preventAssessProjectId: {
          type: String,
          ref: 'preventAssessProjects',
        }, // 自查项目id
        mark: Number, // 分值
        inconformityText: String, // 不符合项目备注说明
        selected: String, // 选中项
        jgChange: {
          type: Boolean,
          default: false,
        }, // 该项是否被监管端修改
      },
    ],
    // 公式文件
    formulaFile: [
      {
        // 公式文件
        _id: {
          type: String,
          default: shortid.generate,
        },
        files: [
          {
            // 公示文件
            _id: {
              type: String,
              default: shortid.generate,
            },
            name: String,
            url: String, // 只上传文件名
          },
        ],
        submitDate: {
          // 提交日期(企业端)
          type: Date,
          default: Date.now,
        },
        approvedDate: {
          // 审核/验收日期(监管端)
          type: Date,
        },
        approvedResult: {
          // 审核/验收结果(监管端/企业端)
          type: Number,
          enum: [ 0, 1, 2 ], // 0：未审核 1：不通过 2：通过
        },
        // 审核人
        approvedUser: {
          manageId: {
            type: String,
            ref: 'SuperUser',
          },
          approvedUserId: {
            type: String,
            ref: 'SuperUser',
          },
        },
        // 提交人
        submitUser: {
          type: String,
          ref: 'AdminUser',
        },
        reason: {
          default: '',
          type: String,
        },
        // 退回理由(监管端/企业端)
        times: {
          type: Number,
          default: 0,
        }, // 0次提交，1次提交
      },
    ],
  });
  preventAssess.index(
    { year: 1 }
  );
  return mongoose.model('preventAssess', preventAssess, 'preventAssess');
};
