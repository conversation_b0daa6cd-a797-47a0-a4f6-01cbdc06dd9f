/* eslint-disable jsdoc/require-param */
/**
 * Created by <PERSON><PERSON> on 2021/10/29.
 * 全流程数据
 */

module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const jcqlcProjectSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    RCId: String, // 汝成系统中该项目的id
    parentId: { // 项目父级id，根级是0
      type: String,
      default: '0',
    },
    publicityStatus: {
      type: Boolean,
      default: false,
    }, // 公示状态
    isSubProjects_type: {
      type: String,
      default: '',
    }, // 是否是子项目，如果是那么属于哪一类，Y是预采样，F是复测，B是补测，Z是子项目，正式项目则为空字符串
    pigeonholeFile: { // 归档文件夹
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    pigeonholeCatalogue: String, // 归档目录文件
    pigeonholeAuthor: { // 归档中各文件负责人
      approvedWord: String, // 合同审批
      sceneInvestigationFiles: String, // 现场调查
      SamplingPlan: String, // 方案计划单
      instrumentApplyForm: String, // 仪器校准
      resexportSpotRecord: String, // 现场检测
      sampleList: String, // 流转单
      generateDocx: String, // 实验室原始记录单
      labReportRecordUploadFile: String, // 检测结果报告单审核记录表
      reportRecordUploadFile: String, // 检测报告审核记录表
      report: String, // 检测结果报告单
    },
    pigeonholeNumber_of_pages: { // 归档中各文件页数
      approvedWord: {
        type: Number,
        default: 0,
      }, // 合同审批
      sceneInvestigationFiles: {
        type: Number,
        default: 0,
      }, // 现场调查
      SamplingPlan: {
        type: Number,
        default: 0,
      }, // 方案计划单
      instrumentApplyForm: {
        type: Number,
        default: 0,
      }, // 仪器校准
      resexportSpotRecord: {
        type: Number,
        default: 0,
      }, // 现场检测
      scenePhotosLength: {
        type: Number,
        default: 0,
      }, // 现场照片
      sampleList: {
        type: Number,
        default: 0,
      }, // 流转单
      generateDocx: {
        type: Number,
        default: 0,
      }, // 实验室原始记录单
      labReportRecordUploadFile: {
        type: Number,
        default: 0,
      }, // 检测结果报告单审核记录表
      reportRecordUploadFile: {
        type: Number,
        default: 0,
      }, // 检测报告审核记录表
      report: {
        type: Number,
        default: 0,
      }, // 检测结果报告单
    },
    postReportDataStatus: { // 项目报送状态，报送到省疾控
      default: false,
      type: Boolean,
    },
    postReportDataTime: { // 项目报送状态，报送到省疾控（浙健）时间
      type: Date,
    },
    preparer: {
      type: String,
      ref: 'ServiceEmployee',
    }, // 对接省疾控填表人
    issuer: { // 报告签发人
      name: String,
      employeeId: String, // serviceEmployee id
    },
    issueDate: Date, // 报告签发日期
    samplingDates: Array, // 只用于报送 现场采样日期
    surveyFiles: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        documentType: String, // 文件类别
        fileType: String, // 文件类型
        originName: String, // 原文件名称
        staticName: String, // 服务器文件名
        createdAt: { // 上传日期
          type: Date,
          default: Date.now,
        },
      },
    ], // 评价项目的调查文件
    sceneInvestigationFiles: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileType: String, // 文件类型
        originName: String, // 原文件名称
        staticName: String, // 服务器文件名
        createdAt: { // 上传日期
          type: Date,
          default: Date.now,
        },
      },
    ], // 现场调查文件
    sceneTime: Date, // 现场调查时间
    firstSceneTime: Date, // 评价项目现场调查时间
    sceneWordFileName: String, // 现场调查word文件名
    // ==================== 受检单位信息
    EnterpriseID: { type: String, ref: 'Adminorg' }, // 企业ID
    EnterpriseName: { type: String }, // 企业名称
    companyID: {
      type: String,
    }, // 用人单位ID ，取信用代码
    companyAddress: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      districts: Array,
      address: String, // 具体地址
    }], // 用人单位地址 为什么下面还有一个用人单位地址，是否可以合并？？？？
    corp: String, // 受检单位法人
    regType: String, // 受检单位注册类型/经济类型
    companyScale: {
      type: String,
      enum: [ '大型', '中型', '小型', '微型', '其他' ],
    },
    companyContact: {
      type: String,
      default: '',
    }, // 受检单位联系人
    companyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 受检单位联系人手机号码
    companyContactEmail: {
      type: String,
      default: '',
    }, // 受检单位邮箱
    companyIndustry: {
      type: Array,
      default: [],
    }, // 受检单位'所属行业',
    riskLevel: {
      type: String,
    }, // 受检单位风险等级，从adminorg中的level来
    regAdd: String, // 注册地址
    districtRegAdd: { // 注册地址 ['']
      type: Array,
      default: [],
    }, // 受检单位注册地址
    // 申报地址，由于用户可以自行修改，所以和下面的workAdd区别开，
    // 存area_code，该code用于后注册的行政端也能读取申报的项目列表，
    // 存area_code前六位，即区县代码有效位数，其父级单位通过代码即可找到，全国的则需特殊处理
    workPlaces: [{ // 受检单位的工作场所地址
      _id: {
        type: String,
        default: shortid.generate,
      },
      workAddName: String, // 工作场所的中文地址

      name: String, // 工作场所的名称
      workAdd: {
        type: Array,
      }, // 工作场所具体地址，area_code
      checked: {
        type: Boolean,
        default: true,
      }, // 是否被检测，默认一般是检测了
    }], // 检测的工作场所，用于识别
    // ==================== 委托单位信息
    anthemEnterpriseID: {
      type: String,
      ref: 'Adminorg',
    }, // 委托单位id
    anthemCompanyID: {
      type: String,
    }, // 委托单位ID ，取信用代码
    newAnthemCompany: {
      type: String,
    }, // 新增的委托单位
    anthemCompanyName: {
      type: String,
      default: '',
    }, // 委托单位名称
    anthemCompanyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    anthemCompanyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 委托单位联系人手机号码
    anthemCompanyContactEmail: {
      type: String,
      default: '',
    }, // 委托单位邮箱
    anthemCompanyAddress: [{
      districts: Array, // 营业执照注册地址
      address: String, // 具体地址
      _id: {
        type: String,
        default: shortid.generate,
      },
    }], // 委托单位地址
    serviceOrgId: { type: String, ref: 'ServiceOrg' }, // 机构id
    serviceCon: {
      type: String,
      default: '',
    }, // 服务内容
    jobHealthId: { type: String, ref: 'jobHealth' }, // 检测项目id
    // ============================================项目相关信息
    projectName: { type: String }, // 检测项目名称
    shortProjectName: { type: String }, // 检测项目简称
    projectSN: { type: String }, // 检测项目编号
    serviceType: { type: String }, // 检测类别
    expectStartTime: {
      type: Date,
    }, // 预计开始时间
    expectStopTime: {
      type: Date,
    }, // 预计结束时间,
    requiredTime: {
      type: Date,
    }, // 要求完成的时间
    applyTime: {
      type: Date,
    }, // 上报时间
    completeTime: {
      type: Date,
    }, // 实际完成时间
    status: {
      type: Number,
      default: 0,
    }, // 申报状态,0，未报送，1，已报送，
    completeStatus: {
      type: Number,
      default: 0,
    }, // 完成状态，0，未完成； 1，已完成
    projectStop: {
      type: Boolean,
      dafault: false,
    }, // 项目是否暂停，false：未暂停，true：暂停
    projectCancel: {
      type: Boolean,
      dafault: false,
    }, // 项目是否终止，false：未终止，true：终止
    completeUpdate: {
      type: Number,
      default: 0,
    }, // 上报后更新状态
    InformationChange: [{
      _id: String,
      content: {
        type: Array,
        default: [],
      }, // 变更内容
      superUser: [ // 监管端id
        {
          type: String,
          ref: 'SuperUser',
        },
      ],
      nowTime: {
        type: Date,
        default: Date.now,
      }, // 变更内容
    }], // 信息变更 数组
    projectGroup: {
      type: String,
      default: '',
      ref: 'serviceDingtrees',
    }, // '项目检测组',
    subProjects_group: {
      projectGroup: [{
        type: String,
        default: '',
        ref: 'serviceDingtrees',
      }],
      personInCharge: [{
        type: String,
        default: '',
        ref: 'ServiceEmployee',
      }],
      EvaluationGroup: [{
        type: String,
        default: '',
        ref: 'serviceDingtrees',
      }],
      EvaluationProjectManagerAdmin: [{
        type: String,
        default: '',
        ref: 'ServiceEmployee',
      }],
    }, // '子项目的检测组、项目负责人、评价组、评价组经理',
    EvaluationProjectManager: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // '评价项目成员
    EvaluationProjectManagerAdmin: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 评价组经理
    EvaluationGroup: {
      type: String,
      default: '',
      ref: 'serviceDingtrees',
    }, // 评价组
    personInCharge: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // '项目负责人ID',
    personChange: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 项目负责人ID，体现在项目信息中 上面的项目负责人是项目经理
    personsOfProject: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // '项目组成员ID',
    personsOfCompiling: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 编制人成员ID
    personsOfReviewer: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 审核人
    personsOfIssuer: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 签发人成员ID
    salesman: { // 业务经理
      type: String,
      default: '',
    },
    farmOut: { // 是否分包lht+(chenke)
      type: Boolean,
    },
    farmOutParams: { // 分包参数
      type: String,
    },
    description: { // 项目简介
      type: String,
    },
    vipRequirement: { // vip需求
      type: String,
    },
    wordDingDentryId: String, // 钉盘中实验室报告单文件id
    wordFileName: { // 实验室检测报告单的word
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
      signedUrl: String, // 签名后的路径
      noStampUrl: { type: String, default: '' }, // 未盖章文件下载地址 pdf
    },
    noPassWordFileName: { // 实验室未通过参数认证的检测报告单的word
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
      signedUrl: String, // 签名后的路径
      noStampUrl: { type: String, default: '' }, // 未盖章文件下载地址 pdf
    },
    // chemistryWordFileName: { // 实验室检测报告单的word
    //   name: { type: String, default: '' }, // 原文件名
    //   url: { type: String, default: '' }, // 文件下载地址
    //   signedUrl: String, // 签名后的路径
    //   noStampUrl: { type: String, default: '' }, // 未盖章文件下载地址 pdf
    // },
    samplingSchemesFilesName: String, // 方案word文件名称
    samplingPlanFileName: String, // 计划单word文件名称
    samplingSchemesAndPlanFileName: String, // 方案和计划单文件名称
    instrumentsApplyFileName: String, // 仪器校准单文件名称
    exceedStandardFileName: String, // 疑似超标文件名称
    projectDetailsFileName: String, // 项目详情文件名称
    electronicFileType: Number, // (八骏字段)电子档案类型 1：评价项目附带 2：检测项目附带 3：单独制作
    // =============钱money
    projectPrice: {
      type: Number,
      default: 0,
    }, // 项目价格(合同金额)
    cooperationFee: {
      type: Number,
      default: 0,
    }, // 合作费
    expectedCooperationFee: {
      type: Number,
      default: 0,
    }, // 预计合作费
    reviewFee: {
      type: Number,
      default: 0,
    }, // 评审费
    expectedReviewFee: {
      type: Number,
      default: 0,
    }, // 预计评审费
    otherFee: {
      type: Number,
      default: 0,
    }, // 其他费
    expectedOtherFee: {
      type: Number,
      default: 0,
    }, // 预计其他费
    netEarnings: {
      type: Number,
      default: 0,
    }, // 净赚=价格-合作费-评审费-其他费
    expectedNetEarnings: {
      type: Number,
      default: 0,
    }, // 预计净赚=价格-合作费-评审费-其他费
    outsourceFee: { // 分包费
      type: Number,
      default: 0,
    },
    expectedOutsourceFee: { // 预计分包费
      type: Number,
      default: 0,
    },

    // ============== 快递
    mailingAddress: {
      address: String,
      districts: Array,
    }, // 邮寄地址
    recipient: {
      type: String,
      default: '',
    }, // 收件人
    recipientPhoneNum: {
      type: String,
      default: '',
    }, // 收件号码

    // =============银行
    accountsBank: {
      type: String,
      default: '',
    }, // 开户行
    accountsAddress: {
      type: String,
      default: '',
    }, // 开户地址
    accountsNumber: {
      type: String,
      default: '',
    }, // 开户账号
    accountsPhoneNum: {
      type: String,
      default: '',
    }, // 开票电话
    tariffNumber: {
      type: String,
      default: '',
    }, // 税号
    year: Date, // 年度
    detectionCustom: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
        default: '',
      },
      url: {
        type: String,
        default: '',
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
      // 一旦这个字段不是enterprise，那么企业端就无权修改
    }], // 自定义机构资质
    contract: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 合同书
    summary: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 汇总表
    report: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 报告书
    invoice: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: {
        type: String,
      },
      url: {
        type: String,
      },
      source: {
        type: String,
        default: 'service', // super || enterprise || operate
      }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
    }], // 发票
    reportTime: {
      type: Date,
    }, // 检测时间
    checkPointImg: String, // 检测点分布示意图
    isVIP: { // 会员类型：战略客户、vip客户、普通客户
      type: String, // svip、vip、ordinary
      default: 'ordinary',
    },
    source: {
      type: String,
      default: 'service', // ck || service
    }, // 数据来源
    isUrgent: {
      type: Boolean,
      default: false,
    }, // 是否加急
    comment: String, // 整个项目的备注

    process_instance_id: String, // 合同审批实例id
    // approvedStartTime: Date, // 合同审批发起时间
    approvedWordFileName: String, // 合同审批word文件名称
    approvedFormData: {
      legitimate: String, // 是否符合法律法规的要求
      qualificationOK: String, // 本公司和服务人员资质是否满足要求
      instrumentOK: String, // 检测检验设备是否满足要求
      standardOK: String, // 检测检验方法、标准适用
      isPackage: String, // 是否分包
      packageInfo: String, // 分包具体项目
      packageOrg: String, // 拟选分包机构
      packageBeInformed: String, // 合同或协议是否已明确分包内容并告知客户
      controllableRisk: String, // 技术风险和商业风险是否可控
      chargeOK: String, // 项目收费是否能满足工作要求
      progressOK: String, // 能否按合同要求进度完成项目
      logisticsOK: String, // 后勤保障能否满足要求
      secrecyOK: String, // 是否满足项目和合同保密要求
    },
    approvedComment: String, // 最总评审结论

    // 方案审批状态以及审批实例id
    samplingPlanApproveId: String, // 方案审批实例id
    samplingPlanApproveUser: {
      type: String,
      ref: 'ServiceEmployee',
      default: '',
    }, // 方案审核人
    samplingPlanApproverSign: {
      type: String,
      ref: 'ServiceEmployee',
      default: '',
    }, // 方案审核人签名
    samplingPlanModifiable: { // 方案是否通过修改审核
      type: Boolean,
      default: false,
    },

    labModifiable: { // 实验室是否通过修改审核
      type: Boolean,
      default: true,
    },
    labApprodPattern: { // 开启审批模式（控制按钮的）Running、End
      type: String,
      default: 'End',
    },
    isSampling: { // 是否需要采样
      type: Boolean,
      default: true,
    },

    // 报告审批状态以及审批实例id
    reportProcessInstanceId: String, // 报告单审批实例id
    reportApprovedStartTime: Date, // 报告单审批发起时间

    // 数据修改审批实例id 3处
    // 1.计划单
    samplingPlanProcessInstanceId: String, // 审批实例id
    samplingPlanApprovedStartTime: Date, // 审批发起时间
    // 2.现场检测
    spotRecordProcessInstanceId: String, // 审批实例id
    spotRecordApprovedStartTime: Date, // 审批发起时间
    // 3.实验室
    labProcessInstanceId: String, // 审批实例id
    labApprovedStartTime: Date, // 审批发起时间

    // 完成项目审批
    finishProjectProcessInstanceId: String, // 审批实例id
    finishProjectApprovedStartTime: Date, // 审批发起时间

    reportFinalProcessInstanceId: String, // 报告审批实例id
    reportGenerateFileName: { // 生成报告文件名
      type: String,
      default: '',
    },
    reportUploadDingDentryId: String, // 钉盘中初稿文件id
    labTestReportUploadFile: {
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    }, // 检测结果报告单上传报告文件
    reportUploadFile: { // 初稿上传报告文件
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },
    // officialReport:{ // 正式稿生成文件
    //   name: { type: String, default: '' }, // 原文件名
    //   url: { type: String, default: '' }, // 文件下载地址
    //   noStampUrl: { type: String, default: '' }, // 未盖章文件下载地址 pdf
    // },
    officialReportUploadFile: { // 正式稿上传文件
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
      noStampUrl: { type: String, default: '' }, // 未盖章文件下载地址 pdf
      stampUrl: { type: String, default: '' }, // 盖章文件下载地址 pdf
    },
    hazardDeclarationFile: { // 职业病危害申报记录表
      name: String, // 原文件名
      url: String, // 文件下载地址
      createTime: Date,
    },
    officialReportUploadFiles: [ // 正式稿文件 数组 用于评价列表 wzq
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        name: { type: String, default: '' }, // 原文件名
        url: { type: String, default: '' }, // 文件下载地址
        staticName: { type: String, default: '' },
      },
    ],
    // 职位实验室记录单 jhw++
    reportRecordUploadFile: { // 职位记录上传
      staticName: { type: String },
      url: { type: String },
    },

    labReportRecordUploadFile: { // 实验室记录上传
      staticName: { type: String },
      url: { type: String },
    },
    submitSampleListFile: { // 交样收样单
      staticName: { type: String },
      url: { type: String },
    },
    // 实验室相关资料 jhw ++
    sampleListFile: { // 收样单
      staticName: { type: String },
      url: { type: String },
    },
    labRecordFile: { // 实验室原始记录单
      staticName: { type: String },
      url: { type: String },
    },
    labSampleScheduleFile: { // 排样单
      staticName: { type: String },
      url: { type: String },
    },
    harmFactorDistributeConclusionFile: {
      staticName: { type: String },
      url: { type: String },
    },
    // end
    serviceArea: [{
      type: String,
      match: /^[0-9]*$/,
    }], // 关于此次项目的企业涉及的技术服务领域，存得是编码，报送信息中要用 [采矿业:'1','化工、石化及医药':'2','冶金、建材':'3','机械制造、电力、纺织、建筑和交通运输等行业领域':'4','核设施':'5','核技术应用':'6']
    qfServiceType: [{
      type: String,
    }], // 企服类型
    detectionTime: Date, // 现场采样/检测时间
    serviceMatters: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      employeeId: String, // 员工id
      serviceItemList: [{ type: String }], // 服务事项
    }], // 项目组成员的服务事项
    // serviceMatters: [[ String ]], // 项目组成员的服务事项
    contractDate: { type: Date }, // 签订日期
    entrustDate: { type: Date }, // 委托时间 发起合同审核的时间，如果是老项目,委托时间设为检测时间的前一个月
    schemeSampleDate: { type: Date }, // 方案的采样开始日期
    samplingDays: { type: Number, default: 1 }, // 一批样品需要采样的天数
    samplePrepareId: { type: String, ref: 'SamplePreparation' }, // 样品备样单Id
    instrumentsApplyId: { type: String, ref: 'DeviceApply' }, // 仪器申请Id
    // isFinish: { // 项目是否已结束，若已经结束那么这个项目的所有阶段数据不可再更改，默认为false，未结束
    //   type: Boolean,
    //   default: false,
    // },
    labReportAuthor: { type: String }, // 实验室检测报告单编制人


    // 签名+++++++++++++++++++++++++++++
    // 项目经理签名组 如果项目经理（评价或检测经理）是老师 [项目经理签名]，是学生[学生，老师]
    personInChargSigns: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signType: String, // 学生(trainee)或老师(tutor)
      serviceEmployeeId: String, // serviceEmployeeId
      fileName: String, // 文件名
    }],
    // 项目经理的配对组的老师的签名
    personInChargMatcherSign: {
      serviceEmployeeId: String, // serviceEmployeeId
      fileName: String, // 文件名
    },
    contractApprovedUsers: [{ // 合同审批人
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: String, // employeeName
      signType: String, // 审批类型 approvedUsers(评审人员)|reviewTeamLeader（评审负责人）
      serviceEmployeeId: String, // serviceEmployeeId
      fileName: String, // 文件名
    }],

    labReportApproverSign: String, // 实验室检测结果报告单批准人
    labReportAuthorSgin: { type: String }, // 实验室检测报告单编制人的签名
    labReportAuditor: {
      fileName: String, // 签名文件名
      serviceEmployeeId: String, // 审核人id，serviceEmployee表内的id
    }, // 实验室检测报告单审核人
    reviewReportAuditor: {
      fileName: String, // 签名文件名
      serviceEmployeeId: String, // 审核人id，serviceEmployee表内的id
    }, // 初稿审核人
    reviewReportsigner: {
      fileName: String, // 签名文件名
      serviceEmployeeId: String, // 签发人id，serviceEmployee表内的id
    }, // 初稿签发人
    labBasicsInfo: [
      { // 实验室原始记录单每个参数的基础信息
        _id: {
          type: String,
          default: shortid.generate,
        },
        sample_date: Date, // 采样日期
        inspector: String, // 检测人员
        reviewer: String, // 审核人
        dustComment: String, // 粉尘备注
        SampleAnalyseNumber: { // 单个危害因素已分析的样品数量
          type: Number,
          default: 0,
        },
        allSampleAnalyseNumber: { // 单个危害因素样品总数量
          type: Number,
          default: 0,
        },
        batch: Number, // 批次

        QualityControlSampleId: { // 质控样
          type: String,
          ref: 'labInfoCopys',
        },
        // StandardSolutionsId: { // 标准液
        //   type: String,
        //   ref: 'labInfoCopys',
        // },
        curveManageId: String, // 曲线id

        parameter: String, // 参数(危害因素)
        sample_count: String, // 样品数量 已废弃??
        sample_send_time: Date, // 送样时间 (sample_send_time)
        sample_temperature: String, // 做样温度
        sample_humidity: String, // 做样湿度

        comment: String, // 备注

        equipment: Array, // 仪器 目前只有气相有用到
        detectTime: [ Date ], // 设备分析时间

        // DUST
        Preparing_temperature: String, // 备样温度
        Preparing_humidity: String, // 备样湿度

        // FG
        Abs_volume: String, // 吸收液体积
        take_sample_volume: String, // 取样体积

        // ISE
        potential_value_f: String, // 标准曲线电位值

        // 游离二氧化硅
        baking_temperature: String, // 烘干温度
        ashing_temperature: String, // 灰化温度

        // GLC
        DPI: String, // 解吸率

        // 油雾
        flag_volume: String, // 标杆体积

        // 洗脱效率
        efficiencyOfElution: String,

        createAt: { // 创建日期
          type: Date,
          default: Date.now,
        },

        labRecordFile: { // 实验室原始记录单 单个危害因素
          staticName: { type: String },
          url: { type: String },
        },
        labRecordReview: { // 实验室记录单复核
          status: { // 0 未完成 1 未复核 2 已复核
            type: Number,
            default: 0,
          },
          completedTime: Date, // 完成时间
        },
      },
    ],
    subProjects: [ // 项目-子项目-批次的基本信息
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        batch: Number, // 批次
        expectTimeStart: Date, // 预取开始时间
        expectTimeEnd: Date, // 预取结束时间
        samplingTime: Date, // 实际采样开始时间
        weatherScene: { // 天气
          weather: String, // 天气情况
          pressure: String, // 气压
          temperature: String, // 温度
          humidity: String, // 湿度
        },
        calibration: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          instrumentID: { type: String, ref: 'instrument' }, // 仪器id
          internalNumber: String, // 仪器编号
          standardSoundSource: String, // 标准声源
          soundSourceSN: String, // 声源编号
          calibrationValue: String, // 声源编号
          deviationValue: String, // 偏差值
          staff: String, // 校准人
          isCalibration: Boolean, // 是否校准
          calibrationTime: Date, // 校准时间
        }], //
        samplingCompletedStatus: { // 该批现场检测流转单完成状态
          type: Number, // 0 未完成 1 进行中 2 已完成
          default: 0,
        },
        samplingCompletedTime: Date, // 该批现场检测流转单提交时间
        spotProjectCompletedStatus: { // 该批现场检测完成状态
          type: Number, // 0 未完成 1 进行中 2 已完成
          default: 0,
        },
        spotProjectCompletedTime: Date, // 该批现场检测完成时间
        signManage: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          signRole: String, // 陪同人 采样人 测量人
          name: String, // 姓名
          signPath: String, // 签名文件储存位置
          signTime: Date, // 签名时间
        }], // 签名
        // 后续拓展 实验室 相关 交样收样字段
        scenePhotos: [
          {
            _id: {
              type: String,
              default: shortid.generate,
            },
            fileName: String,
            src: String,
            describe: String,
            // station: String, // 岗位名称
            stationId: String, // 岗位id
            // workPlace: String, // 车间名称
            workspaceId: String, // 车间id
            latitude: String,
            longitude: String,
            address: String,
            createTime: Date,
          },
        ],
        // 流转单 ++
        jcUser: String, // 送样人 ==> 点完成检测的人
        sampleManager: String, // 流转单确认人 收养单交样人
        sampleAdministratorEmployeeId: String, // 样品管理员 htt+ 需要跑库 2022-7-14 - 2022-12-1 是洪毓 除此之外是王秀凤
        samplingConfirmDate: Date, // 流转单确认时间
        sampleListIsReceived: { // 流转单是否接收
          type: Boolean,
          default: false,
        },
        receiveManager: String, // 实验室接受样品人
        samplingReceiveDate: Date, // 样品接收时间
        labSampleListIsReceived: { // 收样单是否接收
          type: Boolean,
          default: false,
        },
      },
    ],
    sceneMarkerPhotos: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: String,
        src: String,
        describe: String,
        stationId: String, // 岗位id
        workspaceId: String, // 车间id
        latitude: String,
        longitude: String,
        address: String,
        createTime: Date,
      },
    ], // 标志物前照片
    completeReportArchive: {
      status: { // 0 未完成 1 已完成
        type: Number,
        default: 0,
      }, // 是否确认签收归档 默认不签收0 已经签收1
      completedTime: Date, // 完成时间
    },
    reportArchiveSigner: {
      signPath: String, // 签名
      serviceEmployeeId: String,
    }, // 档案签收人
    offlineCompleted: {
      type: Boolean,
      default: false,
    }, // 线下已经完成 如果线下完成的话 需要将项目复制一份到jobhealth（已完成项目）
    progress: { // 进度
      // 项目创建时间
      createProject_time: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 2,
        },
        completedTime: {
          type: Date,
          default: Date.now,
        },
      },
      // 合同评审状态
      approved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止  5 已取消
          type: Number,
          default: 0,
        },
        completedTime: Date, // 状态变化时间
      },
      projectStart: {
        status: { // 0 未启动 1 不启动 2 已启动
          type: Number,
          default: 0,
        },
        completedTime: Date, // 状态变化时间
        unstartedReason: String, // 未启动说明
      },
      // 评价组分配状态
      ProjectPj: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 状态变化时间
      },
      // 评价项目经理分配状态
      EvaluationProjectManagerAdmin: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 状态变化时间
      },
      // 检测组分配状态
      projectGroup: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 检测项目经理分配状态
      PersonInCharge: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 现场调查状态
      scene: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 评价结果
      evaluationResult: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 评价方案的 初步调查状态
      firstScene: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 采样方案整体完成进度
      samplingSchemes: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 采样计划单整体完成进度
      samplingPlans: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 采样方案审核完成进度
      samplingPlanApprove: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      samplePrepare: { // 样品备样单
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      instrumentsApply: { // 仪器申请单
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 现场记录单整体完成进度
      spotRecord: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      sampleList: { // 检测交样
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      labSampleList: { // 实验室收样
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      labRecord: { // 实验室分析
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      labTestReport: { // 实验室检测报告单
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间 每次保存检测结果报告单的时间
      },
      // labTestReportFile:{ // 实验室检测上传报告
      //   status: { // 0 未保存 1 进行中 2 已保存
      //     type: Number,
      //     default: 0,
      //   },
      //   completedTime: Date, // 完成时间 每次保存检测结果报告单的时间
      // },
      // 报告单审批状态
      reportApproved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },


      /** 数据修改审批 3处*/
      // 1.计划单
      samplingPlanAmendApproved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 2.现场检测
      spotRecordAmendApproved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 3.实验室
      labAmendApproved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },


      // 完成项目审批
      finishProjectApproved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间

      },


      // 检测结果计算状态
      checkResult: {
        status: { // 0 未完成 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      reviewReport: { // 检评报告初稿
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      // 检评报告审批状态
      reportFinalApproved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止 5 取消
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      firstDraft: { // 初稿
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      firstTrial: { // 初审
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      internalAudit: { // 内审
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      publishCheck: { // 出版前校核
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      isProjectPreview: { // 是否评审 （评价）
        status: { // 0 不需要评审 1 需要评审但未评审 2 需要评审且已评审 3 不评审
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      printReviewDraft: { // 打印评审稿
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      expertReview: { // 专家评审
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      printStatus: { // 打印状态
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      officialReport: { // 正式稿
        status: { // 0 未保存 1 进行中 2 已保存
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      // pjOfficialReport: { // 评价正式稿
      //   status: { // 0 未保存 1 进行中 2 已保存
      //     type: Number,
      //     default: 0,
      //   },
      //   completedTime: Date, // 完成时间
      //   operator: {
      //     type: String,
      //     ref: 'ServiceEmployee',
      //   },
      // },
      // printOfficialDraft: { // 打印正式稿 （与检测的节点一致但分为俩个字段了，我把合并成一个字段了<reportPrint>jhw）
      //   status: { // 0 未保存 1 进行中 2 已保存
      //     type: Number,
      //     default: 0,
      //   },
      //   completedTime: Date, // 完成时间
      //   operator: {
      //     type: String,
      //     ref: 'ServiceEmployee',
      //   },
      // },
      reportPrint: { // 报告打印
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      reportArchive: { // 报告归档
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        operator: {
          type: String,
          ref: 'ServiceEmployee',
        },
      },
      // pjReportArchive: { // 评价报告归档
      //   status: { // 0 未进行 1 进行中 2 已完成
      //     type: Number,
      //     default: 0,
      //   },
      //   completedTime: Date, // 完成时间
      //   operator: {
      //     type: String,
      //     ref: 'ServiceEmployee',
      //   },
      // },

      // 评价
      projectPjFirstDraft: { // 评价项目初稿
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjFirstReview: { // 评价项目报告初审
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjInternalAudit: { // 评价项目报告内审
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjThreeReview: { // 评价报告三审
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjPublishCheck: { // 评价项目出版前校核
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPreview: { // 评价项目评审
        status: { // 0 未进行 1 进行中 2 已完成 3 不评审
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjProfessorReview: { // 评价报告专家审
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      projectPjModification: { // 评价报告修正
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
      postToRC: { // 对接到汝成 数据
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
        reason: String, // 对接失败原因
      },
      postToRCFile: { // 对接到汝成 附件
        status: { // 0 未进行 1 进行中 2 已完成
          type: Number,
          default: 0,
        },
        completedTime: Date, // 完成时间
      },
    },
    evaluationSchemeId: String, // 评价方案审核id
    allSampleNumber: { // 整个项目样品总个数 不包含添加空白样
      type: Number,
      default: 0,
    },
    LaballSampleNumber: { // 实验室收样后的样品总数
      type: Number,
      default: 0,
    },
    submitSampleNumber: { // 交样的样品总数
      type: Number,
      default: 0,
    },
    top: { // 实验室列表一键置顶
      default: 0,
      type: Number,
    },
    allSampleAnalyseNumber: { // 实验室分析的样品数量 不包含添加空白样
      type: Number,
      default: 0,
    },
    station: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      workTimeWeek: {
        type: Number,
        default: 0,
      }, // 每周工作天数(天)
      time: {
        type: Number,
        default: 0,
      }, // 每班接触时间
      workShopName: String, // 厂房
      // workShopId: String, // 厂房Id
      workspaceName: String, // 车间
      // workspaceId: String, // 车间Id
      oldStationName: String, // 在点数变多之前的岗位名称，方案要用到
      stationName: String, // 岗位
      stationId: String, // 岗位Id
      workTypeName: String, // 工种
      workTypeId: String, // 工种id
      baseWorkTypeId: String, // 如果创建个体，添加为依赖哪个岗位的ID
      // customizePeopleNumber: String, // 作业人数（手填）
      // batchEmployeeRefStationId: [ String ], // 同一批员工引用的厂房、车间、岗位id
      // dailyProduce: { type: String }, // 生产班制
      // workTimeDay: String, // 每天工作时间(小时)
      // workDayWeek: String, // 每周接触几天
      // workWay: { type: String }, // 作业方式
      // protectiveEquipment: { type: String }, // 个人防护用品
      // protectiveFacilities: { type: String }, // 职业病防护设施
      // fountainhead: { type: String }, // 危害因素来源
      // equipCount: Number, // 设备总数

      pointNum: Number, // 测点编号

      productionStatus: {
        type: Array,
        default: [ 1, 1 ],
      }, // 生产状况 [ 设备总数，正常运行数 ]
      protecFacilitieRun: {
        type: String,
        // default: '是',
      }, // 防护设置是否运行
      personalProtecRun: {
        type: String,
        // default: '是',
      }, // 个人是否运行

      isCounts: {
        type: Number,
        default: 0,
      }, // 是否是多个点生成的岗位。方案需要根据该字段判断是否需要显示，0表示是第一个点的，方案需要显示，大于0则表示是多个点的，方案不显示。方案后的流程正常获取数据
      parentStationId: String, // 对应主项目的stations._id
      harmFactors: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          category: String, // 危害因素所属类别   粉尘/物理等
          harmFactorsName: String, // 危害因素字符串格式 sio2
          harmFactorsNameArray: [{ // 危害因素数组格式
            _id: {
              type: String,
              default: shortid.generate,
            },
            name: String, // 危害因素名 如果有总呼的话会带（总尘）/（呼尘）
            harmFactorId: String, // 危害因素id
            oelId: String, // 大表限值名 好像没用到
            // QualityControlSampleId: String, // 质控样id
            // StandardSolutionsId: String, // 标准溶液id
            // labInstrument: Array, // 实验室仪器
            temperature: String, // 温度
            humidity: String, // 湿度
            analyzeDuration: Array, // 实验室分析时间段

            // 流转单 ++
            sampleListNote: String, // 备注
          }],
          count: String, // 点数
          sampleNumber: String, // 样品数 除去空白样后该危害因素的样品总数?
          dayNumber: String, // 天数
          sampleMode: String, // 采样方式，定点/个体
          sampleFrame: String, // 采样时机/时段
          sampleTime: String, // 采样时间
          sampleFlow: String, // 采样流量
          airCollector: String, // 空气收集器
          sampleEquipment: String, // 采样设备
          sampleStorage: String, // 样品保存期限和保存条件
          remark: String, // 备注
          individual: { type: Boolean, default: false }, // 判断该危害因素是否生成了个体采样
          dustOriginName: [{
            _id: {
              type: String,
              default: shortid.generate,
            },
            // workShopId: String, // 厂房
            // workspaceId: String, // 车间
            // stationId: String, // 岗位
            harmFactorsName: String, // 检测项目
          }], // 关联到这个游离sio2的粉尘的数据
          spotRecordSort: String, // 类型
          // stationsList: [ // 个体对应的岗位列表
          //   {
          //     _id: {
          //       type: String,
          //       default: shortid.generate,
          //     },
          //     stationName: String, // 岗位
          //     stationId: String, // 岗位Id
          //   },
          // ],
          sample: [
            {
              _id: {
                type: String,
                default: shortid.generate,
              },
              planSampleDate: {
                type: Date,
              }, // 计划采样日期
              batch: {
                type: Number,
              }, // 批次
              subProjectsId: { type: String }, // 最外层subProjects字段对应批次的id
              sampleSN: {
                unique: true,
                type: String,
              }, // 样品编号
              samplingReceiveDate: Date, // 每个样品的收样时间
              actualReceiveDate: Date, // 每个样品实际收样时间 自动获取
              isBlankSample: { type: Boolean }, // 是否是空白样品，true是，false不是
              sampleProcessing: { type: Date }, // 该二氧化硅的留样是否处理过，处理时间

              // 现场记录单 ++++++++++++++++++++++++++
              samplingConfirmDate: Date, // 每个样品交样日期
              isCompleteConfirm: Boolean, // 是否完成交样
              isCompleteReceive: Boolean, // 是否完成收样
              isSendNoticeConfirm: Boolean, // 是否已经发送即将过期的钉钉通知
              isSendNoticeReceive: Boolean, // 是否已经发送即将过期的钉钉通知
              // compare_date: Date, // 收样日期
              // isComfirm: { Boolean, default: false }, // 是否确认流转单
              // isReceived: { Boolean, default: false }, // 是否确认收样单
              instrument: [{
                instrumentName: String, // 仪器名
                internalNumber: String, // 仪器编号
                instrumentID: String, // 仪器id
                instrumentModel: String, // 仪器型号
                completeSampleTime: String, // 填写时间
                instrumentAccuracy: Number, // 仪器精度
                _id: {
                  type: String,
                  default: shortid.generate,
                },
              }], // 仪器
              sampleId: String, // sample _id 用于后续更新查询
              samplingPlace: String,
              samplingFlow: String, // 采样流量 - 定点
              samplingFlowStart: String, // 采样前流量 - 个体
              samplingFlowEnd: String, // 采样后流量 - 个体

              smaplingTimeStart: Date, // 采样起始时间
              smaplingTimeEnd: Date, // 采样结束时间

              samplingArr: [
                {
                  _id: {
                    type: String,
                    default: shortid.generate,
                  },
                  smaplingTimeStart: Date, // 采样起始时间
                  smaplingTimeEnd: Date, // 采样结束时间
                  samplingFlowStart: String, // 采样前流量 - 个体
                  samplingFlowEnd: String, // 采样后流量 - 个体
                },
              ],

              smaplingTimeSlot: String, // 采样时间段 （仅限 定点 存在）个体噪声

              temperature: String, // 温度
              humidity: String, // 湿度

              selfNumber: String, // 自编号 福州

              wearerName: String, // 佩戴人姓名/作业人员姓名（个体 存在）
              isComplete: {
                type: Boolean,
                default: false,
              }, // 是否完成
              completeSampleTime: Date, // 实际填写数据的时间
              measureTime: Date, // 测量or采样时间
              measureEndTime: Date, // 测量结束时间
              contactPosition: String, // 接触位置
              contactTime: String, // 接触时间 - 高温 手传振动 个体噪声
              // contactTimeForArr: Array, // 接触时间数组 - 高频电磁场
              contactTimes: {
                type: Array,
                default: undefined,
              }, // 接触时间 h/d d/w
              WBGTValues: {
                type: Array,
                default: undefined,
              }, // WBGT指数(℃)2
              WBGT: String, // WBGT指数(℃)
              WBGTAvg: String, // WBGT指数平均值(℃)
              WBGTWeight: String, // 加权WBGT指数

              measureValue: {
                type: Array,
                default: undefined,
              }, // 测量值 - 工频电场 手传振动 照度
              measureAvg: String, // 平均值
              measureMax: String, // 最大值 - 工频电场
              contactPersons: String, // 接触人数

              CTWA: String, // CTWA结果 Co
              CSTEL: String, // CSTEL Co
              measureValueC1: String, // 测量值C1
              measureValueC2: String, // 标况浓度

              windSpeed: String, // 风速
              pressure: String, // 气压

              ahw: String, //
              measurePosition: String, // 测量方位
              TARCMeasurePosition: [ String ], // 紫外的测量方位
              vibrateSpeedWeight: String, // 4h加权振动加速度

              WeightFor8h: String, // 8小时加权值

              eyeMeasureValue: {
                type: Array,
                default: undefined,
              }, // 眼部测量值
              eyeEffectValue: String, // 眼部有效辐照度
              faceMeasureValue: {
                type: Array,
                default: undefined,
              }, // 面部测量值
              faceEffectValue: String, // 面部有效辐照度
              limbsMeasureValue: {
                type: Array,
                default: undefined,
              }, // 肢体测量值
              limbsEffectValue: String, // 肢体有效辐照度
              otherMeasureValue: {
                type: Array,
                default: undefined,
              }, // 其他测量值
              otherEffectValue: String, // 其他有效辐照度

              lightSource: String, // 光源

              measureHeight: String, // 高度

              LAeqValue: String, // LAeq 检测值
              LEXValue: String, // LEX 等效声级

              nosiseType: String, // 噪声类型
              pulseOfNumber: String, // 脉冲次数
              exhaustHoodType: String, // 排风罩形式
              coverArea: String, // 罩口面积

              waveType: String, // 波形
              waveMode: Array, // 辐射方式
              headMeasureValue: String, // 头部测量值
              chestMeasureValue: String, // 胸部测量值
              abdomenMeasureValue: String, // 腹部测量值
              localMeasureValue: String, // 局部测量值

              // magneticMeasureValue: {
              //   type: Array,
              //   default: undefined,
              // }, // 磁场测量值
              magneticAvg: String, // 磁场测量平均值
              // electricMeasureValue: {
              //   type: Array,
              //   default: undefined,
              // }, // 电场测量值
              electricAvg: String, // 电场测量值平均值

              // measureValueAndTime: {
              //   type: Array,
              //   default: undefined,
              // }, // 测量时间与值
              measureValueAndTime: [{
                _id: {
                  type: String,
                  default: shortid.generate,
                },
                measureTime: Date, // 测量时间
                measureValue: String, // 测量值
                measureValueC2: String, // C2值
                contactTime: String, // 接触时间
                measurePosition: String, // 方位
                laserWave: String, // 波长
                eyeMeasureValue: String, // 眼部测量值
                faceMeasureValue: String, // 面部测量值
                limbsMeasureValue: String, // 肢体测量值
                otherMeasureValue: String, // 其它测量值
                measureValue1: String, // 测量值1
                measureValue2: String, // 测量值2
                measureValue3: String, // 测量值3
                measureValueAvg: String, // 测量平均值
                electricMeasureValue: String, // 电场测量值
                magneticMeasureValue: String, // 磁场测量值
              }],

              pointImg: String, // 检查点示意图

              laserModel: String, // 激光器型号
              laserWave: String, // 激光波长
              exposure: String, // 照射量
              exposureTime: String, // 照射时间
              irradiance: String, // 辐照度
              photosensitiveArea: String, // 激光仪器感光面积

              equipment: String, // 被测设备名称 - 高频和超高频 工频电场
              frequency: String, // 被测设备参数/频率 - 高频和超高频
              posture: String, // 操作姿势 超高频

              laborIntensity: String, // 劳动强度
              protectiveMeasures: String, // 防护措施
              remarks: String, // 备注

              samplingStaff: String, // 采样人员

              needUpdateSampleList: Boolean, // 是否是交样后又重新修改的

              // 现场记录单 ++++++++++++++++++++++++++

              // 收样单 ++
              sampleIsReceive: { type: Boolean, default: false },

              // 实验室++
              chromatogramsPdf: {
                originName: {
                  type: String,
                },
                staticName: {
                  type: String,
                },
                url: {
                  type: String,
                },
              }, // 图谱pdf文件
              chromatogramsXml: {
                originName: {
                  type: String,
                },
                staticName: {
                  type: String,
                },
                url: {
                  type: String,
                },
              }, // 图谱xml文件
              chromatogramsExcel: {
                originName: {
                  type: String,
                },
                staticName: {
                  type: String,
                },
                url: {
                  type: String,
                },
              }, // 图谱excel文件
              lab: [{
                _id: {
                  type: String,
                  default: shortid.generate,
                },
                analysisDate: {
                  type: Date,
                }, // 分析时间 实验室分析保存
                pdfAnalysisDate: {
                  type: Date,
                }, // 原子吸收图谱中样品分析时间
                // 通用
                harmFactor: String,
                Abs: String, // 吸光值
                uno_Abs: String, // 不氧化吸光值
                sample_content: String, // 样品含量
                sample_volume: String, // 采样体积
                conversion_coefficient: String, // 转换系数
                air_concentration: String, // 空气中浓度
                duration_of_exposure: String, // 接触时间
                comment: String, // 备注  稀释倍数
                // ISE 电极
                potential_value: String, // 电位值
                sample_content_log: String, // 样品溶液中含量对应对数值
                sample_content_ise: String, // 样品溶液中浓度

                // 三氯化磷
                compared_Abs: String, // 减空白后对照吸光值
                // AFS
                emptyFluorescenceIntensity: String,
                compared_emptyFluorescenceIntensity: String,

                // AAS 原子吸收法
                sample_solution: String, // 样品溶液浓度
                sample_solution2: String,

                // DUST 粉尘
                before_volume: String, // 采样前滤膜重
                after_volume: String, // 采样后滤膜重
                extra_volume: String, // 滤膜增重

                // 石蜡烟
                empty_beaker_weight: String,
                volatilize_sample_weigth: String,
                empty_beaker_weighting: String,

                // GLC 气相
                peakArea_front: String, // 峰面积前段
                peakArea_back: String, // 峰面积后段

                peakArea_front2: String, // 2-丁氧基乙醇 用到
                peakArea_back2: String,

                subtract_peakArea: String, // 减空白后峰面积
                sample_concentration: String, // 样品浓度

                // FSiO2 游离二氧化硅
                sample_weight: String, // 样品质量G
                crucible_weight: String, // 坩埚质量m1 恒重2  参与计算
                crucible_weight1: String, // 坩埚质量m1 恒重1 不参与计算
                ashing_crucible_weight: String, // 灰化后坩埚加沉渣质量m2 恒重2  参与计算
                ashing_crucible_weight1: String, // 灰化后坩埚加沉渣质量m2 恒重1 不参与计算
                acid_crucible_weight: String, // 氢氟酸处理后坩埚加沉渣质量m3 恒重2  参与计算
                acid_crucible_weight1: String, // 氢氟酸处理后坩埚加沉渣质量m3 恒重1 不参与计算
                SiO2_concentration: String, // 游离二氧化硅含量SiO2

                // YW 油雾
                filter_concentration1: String, // 滤膜清洗液油份浓度
                filter_concentration2: String, // 滤膜清洗液油雾浓度
                analysis_num: String, // 分析编号


                // IC
                peakArea_IC: String, // 峰面积S
                subtract_peakArea_IC: String, // 减空白后峰面积S
                sample_concentration_IC: String, // 取样样品浓度C

                excursion_limit: String, // 超限倍数

                // FG 氟及其化合物&&氟化氢 使用
                logarithm: String, // 样品溶液中含量对应对数值

                // 焦炉逸散物
                constant_volume: String,
                take_sample_volume: String,
              }],

            },
          ],
        },
      ],
    }],
    onlyChromatogramsPdf: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        manageLab: {
          label: String,
          value: String,
        },
        // 小组
        originName: {
          type: String,
        },
        staticName: {
          type: String,
        },
        url: {
          type: String,
        },
        isReview: {
          type: Boolean,
          default: false,
        }, // 是否已审核
        // 图谱
      },
    ],
    spotRecordSigns: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      serviceEmployeeId: String, // serviceEmployeeId
      fileName: String, // 文件名
    }],
    // 编制人签名
    personsOfCompilingSign: {
      serviceEmployeeId: String, // serviceEmployeeId
      fileName: String, // 文件名
    },
    // 审核人签名
    personsOfReviewerSign: {
      serviceEmployeeId: String, // serviceEmployeeId
      fileName: String, // 文件名
    },
    // 现场调查的陪同人签名
    sceneInfoSignManage: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人 采样人 测量人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
      signType: {
        type: String,
        default: 'sceneInfo',
      }, // 来源：现场调查: sceneInfo，现场检测: spotRecord
    }],
    // 预上传的现场照片
    prevScenePhotos: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: String,
        src: String,
        describe: String,
        latitude: String,
        longitude: String,
        address: String,
        createTime: Date,
      },
    ],
    // 预上传的标志物前照片
    prevSceneMarkerPhotos: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: String,
        src: String,
        describe: String,
        latitude: String,
        longitude: String,
        address: String,
        createTime: Date,
      },
    ],
    // 陪同人签名库
    companionLib: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人 采样人 测量人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
      signType: {
        type: String,
        default: 'spotRecord',
      }, // 来源：现场调查: sceneInfo，现场检测: spotRecord
    }],
    // 项目提醒配置
    remindConfig: {
      cycle: String, // 周期 天d 周W 月M 季Q
      cycleValue: Number, // 值
    },
    progressWarning: Boolean, // 工作进度预警
    esFiles: [{ // 企服资料
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String, default: '' }, // 原文件名
      staticName: { type: String, default: '' },
      time: Date, // 上传时间
    }], // 企服资料
    recordMakeWay: String, // 档案制作方式
    recordMakeProgress: {
      node: Array, // 节点
      validPeriod: Date, // 时间
    }, // 档案制作进度
    isDelete: { // 项目是否删除 false 未删除 true 已删除
      type: Boolean,
      default: false,
    },
    explainReason: String, // 说明原因 终止 or 暂停
    pauseHistory: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      actionType: String, // pause, resume, terminate, terminateResume
      explainReason: String, // 说明原因 终止 or 暂停
      actionTime: Date, // 操作时间
      operator: String, // 操作人
    }],
  });

  // 实验室查询优化 jhw
  jcqlcProjectSchema.index({ projectSN: 1 });
  jcqlcProjectSchema.index({ category: 1, sampleFlow: 1 });

  // jcqlcProjectSchema.index({ "station.harmFactors.harmFactorsNameArray.name": 1});
  // lab end
  jcqlcProjectSchema.index({ parentId: 1 });
  jcqlcProjectSchema.index({ serviceOrgId: 1 });
  jcqlcProjectSchema.index({ projectGroup: 1 });
  jcqlcProjectSchema.index({ EvaluationProjectManager: 1 });
  jcqlcProjectSchema.index({ EvaluationProjectManagerAdmin: 1 });
  jcqlcProjectSchema.index({ EvaluationGroup: 1 });
  jcqlcProjectSchema.index({ personInCharge: 1 });
  // jcqlcProjectSchema.index({ approvedStatus: 1 });
  jcqlcProjectSchema.index({ reportApprovedStatus: 1 });
  jcqlcProjectSchema.index({ reportFinalApprovedStatus: 1 });
  jcqlcProjectSchema.index({ 'progress.createProject_time.completedTime': 1 });
  jcqlcProjectSchema.index({ 'station.harmFactors.sample.planSampleDate': 1 });
  jcqlcProjectSchema.index({ 'station.harmFactors.sample.batch': 1 });
  jcqlcProjectSchema.index({ 'station.harmFactors.sample.sampleSN': 1 });
  return mongoose.model('JcqlcProject', jcqlcProjectSchema, 'jcqlcProject');
};
