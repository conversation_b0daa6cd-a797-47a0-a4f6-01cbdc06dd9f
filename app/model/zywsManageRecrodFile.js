module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const zywsManageRecrodFileSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    recordId: { type: String, ref: 'Record' },
    recordFiles: [
      { name: { type: String },
        systemGenerate: {
          sort: String, // 是否是系统生成
          fileType: String, // 文件类别
          originName: String, // 用户上传文件名称
          staticName: String, // 后台处理后存储到public静态资源的文件名,
        },
        systemGenerate2: {
          sort: String, // 是否是系统生成
          fileType: String, // 文件类别
          originName: String, // 用户上传文件名称
          staticName: String, // 后台处理后存储到public静态资源的文件名,
        },
        clientUpload: [{
          sort: String,
          fileType: String, // 文件类别
          originName: String, // 用户上传文件名称
          staticName: String, // 后台处理后存储到public静态资源的文件名
        }] },
    ],
  }, { timestamps: true });
  return mongoose.model('zywsManageRecrodFile', zywsManageRecrodFileSchema, 'zywsManageRecrodFiles');
};
