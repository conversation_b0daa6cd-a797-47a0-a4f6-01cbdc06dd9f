// 职业病
const encryptionPlugin = require('../utils/encryptionPlugin');
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const ctx = app.createAnonymousContext();
  const Schema = mongoose.Schema;
  const { dbEncryption = false } = app.config;

  const odiseaseSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    physicalExaminationOrgID: { // 诊断机构id
      type: String,
      ref: 'PhysicalExamOrg',
    },
    name: String, // 被诊断人姓名
    nameForStore: {
      // 加密姓名
      type: String,
    },
    nameSplitEncrypted: {
      // 分段加密的姓名
      type: String,
    },
    IDNum: String, // 身份证
    IDNumForStore: {
      // 用于加密存储的身份证号
      type: String,
    },
    IDNumSplitEncrypted: {
      // 分段加密的身份证号
      type: String,
    },
    EnterpriseID: { // 所在单位
      type: String,
      ref: 'Adminorg',
      require: true,
    },
    enterpriseContactsName: { // 企业联系人
      type: String,
      default: '',
    },
    enterpriseContactsPhonNumber: { // 企业联系电话
      type: String,
      default: '',
    },
    workAddress: [{ // 工作场所
      _id: {
        type: String,
        default: shortid.generate,
      },
      districts: [ String ],
      address: {
        type: String,
        default: '',
      },
      point: [ Number ], // 经纬度
    }],
    cname: String, // 所在单位
    phoneNum: String, // 联系方式
    phoneNumForStore: {
      // 用于加密存储的手机号
      type: String,
    },
    phoneNumSplitEncrypted: {
      // 分段加密的手机号
      type: String,
    },
    reportTime: Date, // 报告出具时间
    station: String, // 岗位
    harmFactors: String, // 接触的危害因素
    sex: Number, // 性别 0是女 1是男
    actualCname: String, // 实际用工单位
    year: { type: String }, // 年度
    employeeId: { type: String, ref: 'Employees' }, // 员工id
    age: { type: String }, // 接害工龄
    birthday: { type: String }, // 年龄
    diseaseName: { type: Array }, // 职业病名
    hospital: { type: String }, // 诊断机构
    workspace: { type: String }, // 车间
    decideDate: Date, // 诊断时间
    status: { type: String }, // 处理情况
    annex_certificate: [{ // 诊断证明书
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileType: String, // 文件类型
      name: String, // 文件名称
      staticName: String, // 文件储存名称
    }],
    applyTime: { type: Date }, // 上报时间
    applyStatus: Number, // 是否上报 0 未上报，1 上报，-1 被退回
    // 退回状态
    giveBackStatus: {
      type: Boolean,
      default: false,
    },
    giveBackTime: Date, // 退回时间
    // 退回备注
    giveBackRemark: {
      type: String,
      default: '',
    },
    source: { // 来源
      type: String,
    },
    isOccupatDisease: {
      type: Boolean,
      default: true,
    }, // 是否职业病[福州对接数据中需要该字段]
    encryptionAlgorithm: {
      type: String,
    },
  }, { timestamps: true });
  dbEncryption &&
    odiseaseSchema.plugin(encryptionPlugin, {
      fields: {
        name: 3,
        IDNum: 18,
        phoneNum: 11,
      },
      model: 'Odisease',
      ctx,
    });


  return mongoose.model('odisease', odiseaseSchema);
};
