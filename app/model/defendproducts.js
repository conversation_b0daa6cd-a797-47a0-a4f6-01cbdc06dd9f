module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const defendproductsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { type: String },
    year: { type: String },
    formData: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      studio: { type: String },
      pname: { type: String },
      sku: { type: String },
      quantity: { type: String },
      date: { type: String },
      receiveMan: { type: String },
    }],
    invoice: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
    certifications: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
  });
  return mongoose.model('defendproducts', defendproductsSchema);
};
