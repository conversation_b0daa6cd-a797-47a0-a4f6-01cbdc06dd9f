
module.exports = app => {
  const shortid = require('shortid');
  // 厂房结构
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const millConstruction = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String, // 企业ID
    companyId: String, // 公司id
    name: String, // 厂房名称
    category: String, // 分类名称
    children: [{ // 车间
      _id: {
        type: String,
        default: shortid.generate,
      },
      category: { type: String, default: 'workspaces' },
      workType: String, // 工种
      name: String, // 车间名称
      harmFactors: { type: Array }, // 危害因素
      customizeHarm: String,
      workWay: { type: String }, // 作业方式
      peopleNumber: { type: Number }, // 作业人数
      dailyProduce: { type: String }, // 生产班制
      customizeProduce: String, // 自定义班制
      workTimeDay: Number, // 每天工作时间(小时)
      workTimeWeek: Number, // 每周工作时间(小时)
      contactTime: { type: String }, // 每班接触时间
      protectiveEquipment: { type: String }, // 个人防护用品
      protectiveFacilities: { type: String }, // 职业病防护设施
      time: { type: Number }, // 时间
      value: { type: String }, // 时间单位
      customizePeopleNumber: { type: Number }, // 作业人数

      children: [{ // 岗位
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: String, // 工种
        employees: { type: String, ref: 'Employees' },
        harmFactors: { type: Array }, // 危害因素
        customizeHarm: String,
        workWay: { type: String }, // 作业方式
        peopleNumber: { type: Number }, // 作业人数
        dailyProduce: { type: String }, // 生产班制
        customizeProduce: String, // 自定义班制
        workTimeDay: Number, // 每天工作时间(小时)
        workTimeWeek: Number, // 每周工作时间(小时)
        contactTime: { type: String }, // 每班接触时间
        protectiveEquipment: { type: String }, // 个人防护用品
        protectiveFacilities: { type: String }, // 职业病防护设施
        time: { type: Number }, // 时间
        value: { type: String }, // 时间单位
        category: { type: String },
        name: String, // 岗位名称
        children: [{ _id: {
          type: String,
          default: shortid.generate,
        },
        employees: { type: String, ref: 'Employees' } }], // 员工
        customizePeopleNumber: { type: Number }, // 作业人数
      }],
    }],
  });
  return mongoose.model('MillConstruction', millConstruction, 'millConstructions');
};
