/**
 * 运营端用户对象
 */
const encryptionPlugin = require('../utils/encryptionPlugin');
module.exports = app => {
  const mongoose = app.mongoose;
  const ctx = app.createAnonymousContext();
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');

  require('./adminGroup');
  const { dbEncryption = false } = app.config;

  const OperateUserSchema = new Schema({
    id: String,
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String,
    nameForStore: {
      // 加密姓名
      type: String,
    },
    nameSplitEncrypted: {
      // 分段加密的姓名
      type: String,
    },
    userName: String,
    password: {
      type: String,
      set(val) {
        if (!dbEncryption) {
          return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
        }
        return val;
      },
    },
    email: String,
    cname: String, // 单位名称
    regAdd: String, // 注册地址
    area_code: {
      type: String,
    }, // 所在辖区区号
    phoneNum: String,
    phoneNumForStore: {
      // 用于加密存储的手机号
      type: String,
    },
    phoneNumSplitEncrypted: {
      // 分段加密的手机号
      type: String,
    },
    countryCode: {
      type: String,
    }, // 手机号前国家代码
    comments: String,
    date: {
      type: Date,
      default: Date.now,
    },
    logo: {
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    enable: {
      type: Boolean,
      default: false,
    },
    state: {
      type: String,
      default: '1', // 1正常，0删除
    },
    group: {
      type: String,
      ref: 'AdminGroup',
    },
    // 人员角色权限hmac
    user_role_power_hmac: String,
    passwordEncryptionAlgorithm: { // 密码加密hmac算法
      type: String,
    },
    // 人员角色权限hmac算法
    user_role_power_hmac_algorithm: {
      type: String,
    },
    encryptionAlgorithm: { // 加密算法
      type: String,
    },
  });
  dbEncryption &&
    OperateUserSchema.plugin(encryptionPlugin, {
      fields: {
        name: 3,
        phoneNum: 11,
      },
      model: 'OperateUser',
      ctx,
    });


  return mongoose.model('OperateUser', OperateUserSchema, 'operateusers');

};
