

module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const attachmentSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    year: { type: String },
    EnterpriseID: { type: String },
    fileList: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
    license: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
    result: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
    identify: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
    invoice: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
  });
  return mongoose.model('attachment', attachmentSchema);
};

