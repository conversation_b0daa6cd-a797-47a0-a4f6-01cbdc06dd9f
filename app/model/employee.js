const encryptionPlugin = require('../utils/encryptionPlugin');
module.exports = app => {
  const shortid = require('shortid');
  // 员工信息表
  const mongoose = app.mongoose;
  const ctx = app.createAnonymousContext();
  const Schema = mongoose.Schema;
  const { dbEncryption = false } = app.config;
  const employee = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userId: { // 对应的是users表中的id
      type: String,
      ref: 'User',
    },
    signatrue: String,
    hasRecocrd: { type: Boolean, default: false }, // 是否已生成档案
    departs: [[{ type: String, ref: 'Dingtree', index: true }]], // 部门
    EnterpriseID: { // 企业id
      type: String,
      ref: 'Adminorg',
    },
    status: { type: Number, default: 1 }, // 1表示在岗、0表示离岗
    name: {
      type: String,
      index: true,
    }, // 员工姓名
    nameForStore: {
      // 加密姓名
      type: String,
    },
    nameSplitEncrypted: {
      // 分段加密的姓名
      type: String,
    },
    gender: String, // 员工性别
    nativePlace: String, // 籍贯
    IDNum: String, // 身份证号
    IDNumForStore: {
      // 用于加密存储的身份证号
      type: String,
    },
    IDNumSplitEncrypted: {
      // 分段加密的身份证号
      type: String,
    },
    phoneNum: String, // 手机号码
    phoneNumForStore: {
      // 用于加密存储的手机号
      type: String,
    },
    phoneNumSplitEncrypted: {
      // 分段加密的手机号
      type: String,
    },
    education: String, // 文化程度
    hobby: String, // 嗜好
    workingSystem: String, // 工作制度
    station: String, // 岗位
    workType: String, // 工种
    workStart: Date, // 开始工作时间
    workYears: String, // 工龄
    headImg: String, // 头像
    marriage: String, // 婚姻
    email: String, // 邮箱
    age: Number, // 年龄
    dingId: String, // 钉钉id
    departIds: [[{ type: String }]], // 钉钉部门id
    enable: { type: Boolean, default: true }, // 是否可用，用于假删除
    encryptionAlgorithm: {
      type: String,
    },
  }, { timestamps: true });
  const phoneNumField = dbEncryption ? 'phoneNumForStore' : 'phoneNum';

  employee.index({ departs: 1 });
  employee.index({ name: 1 });
  employee.index({ EnterpriseID: 1 });
  employee.index({ createAt: 1 });
  employee.index(
    { [phoneNumField]: -1 },
    {
      unique: true,
      partialFilterExpression: { [phoneNumField]: { $exists: true } },
    }
  );
  employee.index(
    { [phoneNumField]: 1 },
    {
      unique: true,
      partialFilterExpression: { [phoneNumField]: { $exists: true } },
    }
  );
  employee.index(
    { userName: -1 },
    {
      unique: true,
      partialFilterExpression: { userName: { $exists: true } },
    }
  );
  employee.index(
    { userName: 1 },
    {
      unique: true,
      partialFilterExpression: { userName: { $exists: true } },
    }
  );
  dbEncryption && employee.plugin(encryptionPlugin, {
    fields: {
      name: 3,
      phoneNum: 11,
      IDNum: 18,
    },
    model: 'Employee',
    ctx,
  });
  return mongoose.model('Employees', employee, 'employees');
};
