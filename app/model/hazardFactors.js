
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const HazardFactorsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    harmFactors: {
      type: String,
      index: true,
    },
    sort: {
      type: String,
      index: true,
    },
    CAS: {
      type: String,
      index: true,
    },
    toxic: Boolean,
    ename: {
      type: String,
      index: true,
    },
    MAC: String,
    'PC-TWA': String,
    'PC-STEL': String,
    alias: String,
  });


  return mongoose.model('HazardFactors', HazardFactorsSchema, 'hazardFactors');

};
