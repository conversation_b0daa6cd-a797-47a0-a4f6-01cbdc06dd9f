module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const SanitaryInspectionSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { type: String }, // 企业id
    mainPlant: String, // 厂房
    plantName: String, // 车间名称
    plantPeople: String, // 车间负责人
    checkPlace: String, // 检查地点
    checkRecord: String, // 检查情况记录
    rectifyComments: String, // 整改意见
    checkTime: Array, // 检查时间
    rectifyPeople: String, // 整改负责人
    implementationCondition: String, // 整改落实情况
  });

  return mongoose.model('SanitaryInspection', SanitaryInspectionSchema, 'sanitaryInspection');

};
