// 企业 数据统计 xxn add 2024-4-19 暂时不用！！！
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const EnterpriseStatisticsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { // 企业id
      type: String,
      ref: 'Adminorg',
      required: true,
    },
    year: { // 年份
      type: Number,
      required: true,
    },
    month: { // 月份, 0 - 11，当统计年份数据时，这个字段可以不填
      type: Number,
      enum: [ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11 ],
    },
    trainingData: { // 培训数据
      employeesNum: { // 员工数/应培训人数
        type: Number,
        default: 0,
      },
      trainedPeople: { // 已培训人数
        type: Number,
        default: 0,
      },
      certificateNum: { // 证书数
        type: Number,
        default: 0,
      },
      trainingRate: { // 企业的培训率 = 证书数 / 员工数
        type: String,
        default: '0%',
      },
      overallTrainingStatus: { // 企业整体培训情况
        type: Number,
        enum: [ 0, 1, 2 ], // 0 未开始 1 进行中 2 已完成
      },
    },
    jobHealthData: Object, // 检测数据
    healthCheckData: Object, // 体检数据

  }, { timestamps: true });

  return mongoose.model('EnterpriseStatistics', EnterpriseStatisticsSchema, 'EnterpriseStatistics');
};
