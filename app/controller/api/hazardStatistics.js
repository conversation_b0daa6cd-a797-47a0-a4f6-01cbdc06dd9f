

const Controller = require('egg').Controller;

class HazardStatisticsController extends Controller {
  /**
   * 获取地图标记数据
   */
  async getMapMarkers() {
    const { ctx } = this;

    try {
      // 获取查询参数
      const params = ctx.query;

      // 调用服务获取统计数据
      const stats = await ctx.service.hazardStatisticsService.getStatistics(params);

      // 返回地图数据
      ctx.body = {
        success: true,
        data: stats ? stats.mapData : [],
      };
    } catch (error) {
      ctx.logger.error('获取地图标记数据失败:', error);
      ctx.body = {
        success: false,
        message: '获取地图标记数据失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取危害因素统计
   */
  async getHazardFactorStats() {
    const { ctx } = this;

    try {
      // 获取查询参数
      const params = ctx.query;

      // 调用服务获取统计数据
      const stats = await ctx.service.hazardStatisticsService.getStatistics(params);

      // 返回危害因素统计
      ctx.body = {
        success: true,
        data: stats ? stats.hazardFactorsStats : [],
      };
    } catch (error) {
      ctx.logger.error('获取危害因素统计失败:', error);
      ctx.body = {
        success: false,
        message: '获取危害因素统计失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取行业分布统计
   */
  async getIndustryStats() {
    const { ctx } = this;

    try {
      // 获取查询参数
      const params = ctx.query;

      // 调用服务获取统计数据
      const stats = await ctx.service.hazardStatisticsService.getStatistics(params);

      // 返回行业分布统计
      ctx.body = {
        success: true,
        data: stats ? stats.industryStats : [],
      };
    } catch (error) {
      ctx.logger.error('获取行业分布统计失败:', error);
      ctx.body = {
        success: false,
        message: '获取行业分布统计失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取区域分布统计
   */
  async getRegionStats() {
    const { ctx } = this;

    try {
      // 获取查询参数
      const params = ctx.query;

      // 调用服务获取统计数据
      const stats = await ctx.service.hazardStatisticsService.getStatistics(params);

      // 返回区域分布统计
      ctx.body = {
        success: true,
        data: stats ? stats.regionStats : [],
      };
    } catch (error) {
      ctx.logger.error('获取区域分布统计失败:', error);
      ctx.body = {
        success: false,
        message: '获取区域分布统计失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取单位详情
   */
  async getUnitDetail() {
    const { ctx } = this;

    try {
      const { id } = ctx.query;

      if (!id) {
        ctx.body = {
          success: false,
          message: '单位ID不能为空',
        };
        return;
      }

      // 查询企业详情
      const enterprise = await ctx.model.Adminorg.findById(id);

      if (!enterprise) {
        ctx.body = {
          success: false,
          message: '未找到该单位',
        };
        return;
      }

      // 获取行业名称
      const industryName = [];
      if (enterprise.industryCategory && enterprise.industryCategory.length > 0 && enterprise.industryCategory[0][0]) {
        for (const industry of enterprise.industryCategory) {
          const industryRes = await ctx.model.IndustryCategory.findOne({ value: industry[0] });
          industryName.push(industryRes.label);
        }
      }

      // 提取危害因素
      const hazardFactors = [];
      const checkResult = enterprise.checkResult || {};

      for (const key of Object.keys(checkResult)) {
        if (checkResult[key] && checkResult[key].name) {
          if (checkResult[key] !== 'all') {
            hazardFactors.push({
              name: checkResult[key].name,
              point: checkResult[key].point || '0',
              exceed: checkResult[key].exceed || '0',
            });
          }
        }
      }
      const employeeCount = await ctx.model.Employee.count({
        enterpriseId: enterprise._id,
        status: 1,
      });
      // 获取地址
      let address = '';
      if (enterprise.workAddress && enterprise.workAddress.length > 0) {
        address = enterprise.workAddress[0].address || '';
      }

      ctx.body = {
        success: true,
        data: {
          id: enterprise._id,
          name: enterprise.cname,
          address,
          industry: industryName,
          employeeCount: employeeCount || 0,
          hazardFactors,
        },
      };
    } catch (error) {
      ctx.logger.error('获取单位详情失败:', error);
      ctx.body = {
        success: false,
        message: '获取单位详情失败',
        error: error.message,
      };
    }
  }

  /**
   * 手动更新统计数据
   */
  async updateStatistics() {
    const { ctx } = this;

    try {
      // // 检查权限
      // const user = ctx.session.user;
      // if (!user || !user.isAdmin) {
      //   ctx.body = {
      //     success: false,
      //     message: '没有权限执行此操作',
      //   };
      //   return;
      // }

      // 获取参数
      const { timeFrame = 'all' } = ctx.request.body;

      // 调用服务更新统计数据
      await ctx.service.hazardStatisticsService.calculateAndUpdateStatistics({
        timeFrame,
      });

      ctx.body = {
        success: true,
        message: '统计数据更新成功',
      };
    } catch (error) {
      ctx.logger.error('更新统计数据失败:', error);
      ctx.body = {
        success: false,
        message: '更新统计数据失败',
        error: error.message,
      };
    }
  }
}

module.exports = HazardStatisticsController;
