const Controller = require('egg').Controller;
const path = require('path');
const fs = require('fs');
const awaitWriteStream = require('await-stream-ready').write;
const sendToWormhole = require('stream-wormhole');
const _ = require('lodash');
const url = require('url');

class OperateUserController extends Controller {

  setFullPath(dest) {
    const { ctx } = this;
    const date = new Date();

    const map = {
      t: date.getTime(), // 时间戳
      m: date.getMonth() + 1, // 月份
      d: date.getDate(), // 日
      h: date.getHours(), // 时
      i: date.getMinutes(), // 分
      s: date.getSeconds(), // 秒
    };

    dest = dest.replace(/\{([ymdhis])+\}|\{time\}|\{rand:(\d+)\}/g, function(all, t, r) {
      let v = map[t];
      if (v !== undefined) {
        if (all.length > 1) {
          v = '0' + v;
          v = v.substr(v.length - 2);
        }
        return v;
      } else if (t === 'y') {
        return (date.getFullYear() + '').substr(6 - all.length);
      } else if (all === '{time}') {
        return map.t;
      } else if (r >= 0) {
        return Math.random().toString().substr(2, r);
      }
      return all;
    });

    const userid = url.parse(ctx.url).query.split('=')[1];
    dest = dest.replace('{USERID}', (userid && userid !== 'undefined') ? userid : 'default');
    return dest;
  }

  getFileInfoByStream(urlFormat, stream) {
    const pathFormat = this.setFullPath(urlFormat).split('/');
    const newFileName = pathFormat.pop();

    const uploadForder = path.join('.', ...pathFormat);
    // 所有表单字段都能通过 `stream.fields` 获取到
    const fileName = path.basename(stream.filename); // 文件名称
    const extname = path.extname(stream.filename).toLowerCase(); // 文件扩展名称
    if (!extname) {
      throw new Error(this.res.__('validate_error_params'));
    }
    // 生成文件名
    // let ms = (new Date()).getTime().toString() + extname;
    return {
      uploadForder,
      uploadFileName: newFileName,
      fileName,
      fileType: extname,
    };
  }

  async uploadLogo() {
    const {
      ctx,
      config,
    } = this;
    try {
      const configFileType = config.imageType || [];
      const publicDir = !_.isEmpty(config.upload_path) ? config.upload_path : '';

      const stream = await ctx.getFileStream();

      const beforeUploadFileInfo = await this.getFileInfoByStream('{USERID}/{time}{rand:6}', stream);
      const {
        uploadForder,
        uploadFileName,
        fileType,
      } = beforeUploadFileInfo;

      if (configFileType.includes(fileType)) {
        const uploadPath = `${publicDir}/${uploadForder}`;
        if (!fs.existsSync(uploadPath)) {
          fs.mkdirSync(uploadPath);
        }
        const target = path.join(uploadPath, `${uploadFileName}${fileType}`);
        console.log(target);
        const writeStream = fs.createWriteStream(target);
        try {
          await awaitWriteStream(stream.pipe(writeStream));
        } catch (err) {
          // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
          await sendToWormhole(stream);
          throw err;
        }

        ctx.helper.renderSuccess(ctx, {
          data: {
            path: `${config.static.prefix}${config.upload_http_path}/${uploadForder}/${uploadFileName}${fileType}`,
          },
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '抱歉！上传图片只能是 JPG,PNG,JPEG,BMP,JFIF 格式哦！',
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

}

module.exports = OperateUserController;
