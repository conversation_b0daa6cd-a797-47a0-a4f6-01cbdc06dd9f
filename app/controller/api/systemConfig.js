/*
 * @Author: doramart
 * @Date: 2019-07-07 13:07:27
 * @Last Modified by: mikey.zhaopeng
 * @Last Modified time: 2022-06-10 16:41:21
 */
const Controller = require('egg').Controller;
const _ = require('lodash');
const sendToWormhole = require('stream-wormhole');
const awaitWriteStream = require('await-stream-ready').write;
const path = require('path');
const fs = require('fs');
const mkdirp = require('mkdirp');
const { siteFunc } = require('@utils');
const {
  // eslint-disable-next-line no-unused-vars
  config,
  // upload,
} = require('../../utils/upload');

// 处理Ueditor上传保存路径
function setFullPath(dest) {
  const date = new Date();

  const map = {
    t: date.getTime(), // 时间戳
    m: date.getMonth() + 1, // 月份
    d: date.getDate(), // 日
    h: date.getHours(), // 时
    i: date.getMinutes(), // 分
    s: date.getSeconds(), // 秒
  };

  dest = dest.replace(/\{([ymdhis])+\}|\{time\}|\{rand:(\d+)\}/g, function(all, t, r) {
    let v = map[t];
    if (v !== undefined) {
      if (all.length > 1) {
        v = '0' + v;
        v = v.substr(v.length - 2);
      }
      return v;
    } else if (t === 'y') {
      return (date.getFullYear() + '').substr(6 - all.length);
    } else if (all === '{time}') {
      return map.t;
    } else if (r >= 0) {
      return Math.random().toString().substr(2, r);
    }
    return all;
  });

  return dest;
}
const getUploadConfig = userUploadConfig => {
  const conf = Object.assign({}, config, userUploadConfig || {});
  const uploadType = {
    [conf.imageActionName]: 'image',
    [conf.scrawlActionName]: 'scrawl',
    [conf.catcherActionName]: 'catcher',
    [conf.videoActionName]: 'video',
    [conf.fileActionName]: 'file',
  };
  const listType = {
    [conf.imageManagerActionName]: 'image',
    [conf.fileManagerActionName]: 'file',
  };
  return {
    conf,
    uploadType,
    listType,
  };
};
const getFileInfoByStream = (ctx, uploadOptions, stream) => {

  const {
    conf,
    uploadType,
  } = getUploadConfig(uploadOptions);
  // console.log('======================================2222')
  console.log(uploadOptions);
  const fileParams = stream.fields;
  const askFileType = fileParams.action || 'uploadimage'; // 默认上传图片
  // console.log(fileParams)
  // console.log(askFileType)
  if (Object.keys(uploadType).includes(askFileType)) {
    const actionName = uploadType[askFileType];
    console.log('上传路径拼接：', conf[actionName + 'PathFormat']);
    const pathFormat = setFullPath(conf[actionName + 'PathFormat']).split('/');
    const newFileName = pathFormat.pop();
    const uploadForder = path.join('.', ...pathFormat);
    // 所有表单字段都能通过 `stream.fields` 获取到
    const fileName = path.basename(stream.filename); // 文件名称
    const extname = path.extname(stream.filename).toLowerCase(); // 文件扩展名称
    if (!extname) {
      throw new Error('文件扩展名有问题');
    }

    return {
      uploadForder,
      uploadFileName: newFileName + extname,
      fileName,
      fileType: extname,
    };

  }
  throw new Error(ctx.__('validate_error_params'));


};


class SystemConfigController extends Controller {

  async list() {
    const ctx = this.ctx;
    const systemConfigList = await ctx.service.systemConfig.find({
      isPaging: '0',
    }, {
      files: 'siteName ogTitle siteDomain siteDiscription siteKeywords siteAltKeywords registrationNo showImgCode statisticalCode siteLogo',
    });
    ctx.helper.renderSuccess(ctx, {
      data: systemConfigList[0],
    });
  }

  renderTreeData(result) {
    // console.log(result)
    // return
    const childArr = _.filter(result, doc => {
      return doc.level !== '0';
    });
    for (let i = 0; i < childArr.length; i++) {
      const child = childArr[i];
      for (let j = 0; j < result.length; j++) {
        const treeItem = result[j];
        if (treeItem.area_code === child.parent_code) {
          if (!treeItem.children) treeItem.children = [];
          treeItem.children.push(child);
          break;
        }
      }
    }
    return _.filter(result, doc => {
      return doc.level === '0';
    });
  }
  async addressList() {
    const ctx = this.ctx;
    const { crossRegionManage } = ctx.app.config;
    try {
      const payload = {
        current: 1,
        pageSize: 10000,
        isPaging: '0',
      };
      let query = ctx.query;
      if (query.root) {
        query = {
          parent_code: 0,
        };
        const area_code = ctx.session.superUserInfo.area_code;
        if (area_code) {
          if (crossRegionManage) {
            query = {
              area_code: { $in: area_code },
            };
          } else {
            query = {
              area_code,
            };
          }
        }
      } else {
        query = {
          parent_code: query.area_code,
        };
      }

      const addlist = await ctx.service.district.find(payload, {
        sort: {
          id: -1,
        },
        files:
          'id parent_code name lng lat zip_code area_code level merger_name short_name',
        query,
      });
      // gs ++++
      if (addlist.length > 0 && addlist[0].level === '2') {
        for (let i = 0; i < addlist.length; i++) {
          const res = await ctx.model.District.find({ parent_code: addlist[i].area_code }).count();
          if (res === 0) {
            const item = JSON.parse(JSON.stringify(addlist[i]));
            item.hasChildren = true;
            addlist[i] = JSON.parse(JSON.stringify(item));
          }
        }
      }
      await ctx.helper.renderSuccess(ctx, {
        message: '地址列表',
        data: addlist,
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }
  // async addressList() {
  //   const ctx = this.ctx;

  //   try {
  //     const payload = {
  //       current: 1,
  //       pageSize: 10000,
  //       isPaging: '0',
  //     };
  //     // console.log('取地址列表，当前入参', ctx.query);
  //     let query = ctx.query;
  //     if (query.root) {
  //       query = {
  //         parent_code: 0,
  //       };
  //     } else {
  //       query = {
  //         parent_code: query.area_code,
  //       };
  //     }

  //     const addlist = await ctx.service.district.find(payload, {
  //       sort: {
  //         id: -1,
  //       },
  //       files:
  //         'id parent_code name lng lat zip_code area_code level merger_name short_name',
  //       query,
  //     });
  //     // gs ++++
  //     // console.log(addlist, 'addlist66666666666666666666');
  //     if (addlist.length > 0 && addlist[0].level === '2') {
  //       for (let i = 0; i < addlist.length; i++) {
  //         const res = await ctx.model.District.find({ parent_code: addlist[i].area_code }).count();
  //         if (res === 0) {
  //           const item = JSON.parse(JSON.stringify(addlist[i]));
  //           item.hasChildren = true;
  //           addlist[i] = JSON.parse(JSON.stringify(item));
  //           // console.log(addlist[i], '77777777777777777777addlist');
  //         }
  //       }
  //     }
  //     // console.log(addlist, 'res-------------');
  //     // addlist.forEach(item => {
  //     //   while (item.level = 2) {

  //     //   }
  //     // });
  //     await ctx.helper.renderSuccess(ctx, {
  //       message: '地址列表',
  //       data: addlist,
  //     });
  //   } catch (e) {
  //     ctx.helper.renderFail(ctx, {
  //       message: e,
  //     });
  //   }
  // }

  // async get(parentNodes, districts, i) {
  //   const { ctx } = this;
  //   if (i <= districts.length - 1) {
  //     const districtInfo = await ctx.model.District.findOne({ name: districts[i] }, { parent_code: 1 });
  //     for (let j = 0; j < parentNodes.length; j++) {
  //       if (parentNodes[j].area_code === districtInfo.parent_code) {
  //         parentNodes[j].children = await ctx.model.District.find({ parent_code: districtInfo.parent_code });
  //         await this.get(parentNodes[j].children, districts, ++i);
  //       }
  //     }
  //   }
  // }

  async getList(query, payload) {
    const ctx = this.ctx;
    if (query.root) {
      query = {
        parent_code: 0,
      };
    } else {
      query = {
        parent_code: query.area_code,
      };
    }
    const addlist = await ctx.service.district.find(payload, {
      sort: {
        id: -1,
      },
      files:
        'id parent_code name lng lat zip_code area_code level merger_name short_name',
      query,
    });
    if (addlist.length > 0 && addlist[0].level === '2') {
      for (let i = 0; i < addlist.length; i++) {
        const res = await ctx.model.District.find({ parent_code: addlist[i].area_code }).count();
        if (res === 0) {
          const item = JSON.parse(JSON.stringify(addlist[i]));
          item.hasChildren = true;
          addlist[i] = JSON.parse(JSON.stringify(item));
          // console.log(addlist[i], '77777777777777777777addlist');
        }
      }
    }
    return addlist;
  }

  async uploadEnterpriseImage() {
    const { ctx, app, config } = this;
    try {

      const options = !_.isEmpty(config.doraUploadFile.uploadFileFormat) ? config.doraUploadFile.uploadFileFormat : {};
      // const dataType = 'stream';
      // let  returnPath;
      // const uploadConfigInfo = await ctx.service.uploadFile.create({
      //   type: 'local',
      //   uploadPath: process.cwd() + '/app/public',
      // });
      const stream = await ctx.getFileStream();

      const beforeUploadFileInfo = await getFileInfoByStream(ctx, options, stream);
      const {
        uploadForder,
        uploadFileName,
      } = beforeUploadFileInfo;

      // console.log('上传图片地址enterprise_path',app.config.enterprise_path)
      // const publicDir = options.upload_path || (process.cwd() + '/app/public');

      const uploadPath = `${app.config.upload_path}/${uploadForder}`;
      // const uploadPath = `${app.config.enterprise_path}/${uploadForder}`;
      // const uploadPath1 = `${app.config.enterprise_path}/${uploadForder}`;
      // console.log('上传图片地址uploadPath',app.config.enterprise_path,uploadPath1)

      if (!fs.existsSync(uploadPath)) {
        mkdirp.sync(uploadPath);
      }
      const target = path.join(uploadPath, `${uploadFileName}`);
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(stream.pipe(writeStream));
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        throw err;
      }
      const image = fs.readFileSync(target).toString('base64');
      const baiduAPIBack = await siteFunc.baiduBusinessLicenseOCR(image);
      // console.log('百度识别返回', baiduAPIBack);
      const returnPath = `${app.config.static.prefix}${app.config.upload_http_path}/${uploadForder}/${uploadFileName}`;
      // const returnPath = `${app.config.static.prefix}${app.config.enterprise_http_path}/${uploadForder}/${uploadFileName}`;
      console.log('returnpath:', returnPath);
      await ctx.helper.renderSuccess(ctx, {
        message: '上传图片控制器',
        data: {
          baiduAPIBack,
          path: returnPath,
        },
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }
  // 获取当前所在Branch
  async getBranch() {
    const { ctx, config } = this;
    try {
      const data = {
        branch: config.branch,
        systemName: config.systemName || '',
      };
      await ctx.helper.renderSuccess(ctx, {
        data,
        message: '获取当前所在分支成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getPEISIntergrationEnable() {
    const { ctx, config } = this;
    try {
      const data = {
        PEISIntergrationEnable: config.PEISIntergrationEnable,
        systemName: config.systemName || '',
      };
      await ctx.helper.renderSuccess(ctx, {
        data,
        message: '获取当前是否启用体检系统集成成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

}

module.exports = SystemConfigController;
