/**
 * 危害因素检测数据控制器
 */
const Controller = require('egg').Controller;

class HazardInspectionDataController extends Controller {
  /**
   * 处理筛选条件
   * @param {Object} query 查询参数
   * @return {Object} 筛选条件
   */
  _processFilter(query) {
    const filter = { isDelete: false }; // 只查询未删除的企业

    // 处理行业筛选
    if (query.industry) {
      filter.industry = query.industry;
    }

    // 处理区域筛选
    if (query.region) {
      filter['workAddress.region'] = query.region;
    }

    // 处理危害因素筛选
    if (query.hazardFactors && query.hazardFactors.length > 0) {
      const hazardFactors = Array.isArray(query.hazardFactors)
        ? query.hazardFactors
        : query.hazardFactors.split(',');

      // 构建危害因素查询条件
      const hazardFactorFilters = hazardFactors.map(factor => ({
        [`checkResult.${factor}.exceed`]: true,
      }));

      if (hazardFactorFilters.length > 0) {
        filter.$or = hazardFactorFilters;
      }
    }

    return filter;
  }

  /**
   * 获取地图标记点数据
   * @return {Object} 地图标记点数据
   */
  async getMapMarkers() {
    const { ctx } = this;
    const query = ctx.query;

    try {
      const filter = this._processFilter(query);
      // 查询符合条件的数据，只取企业名称、地址、经纬度等必要信息
      const result = await ctx.model.Adminorg.aggregate([
        { $match: filter },
        { $project: {
          name: 1,
          industry: 1,
          workAddress: 1,
          checkResult: 1,
        } },
      ]);

      // 按行业分组
      const industries = {};

      result.forEach(item => {
        const industry = item.industry || '其他';

        if (!industries[industry]) {
          industries[industry] = [];
        }

        // 获取危害因素超标情况
        const hazardFactors = [];
        if (item.checkResult) {
          const factorKeys = Object.keys(item.checkResult);
          factorKeys.forEach(key => {
            const factor = item.checkResult[key];
            if (factor && factor.exceed) {
              hazardFactors.push({
                name: key,
                value: factor.value,
                limit: factor.limit,
                unit: factor.unit,
              });
            }
          });
        }

        // 添加到对应行业分组
        if (item.workAddress && item.workAddress.longitude && item.workAddress.latitude) {
          industries[industry].push({
            id: item._id,
            name: item.name,
            address: item.workAddress.address,
            longitude: item.workAddress.longitude,
            latitude: item.workAddress.latitude,
            hazardFactors,
          });
        }
      });

      ctx.body = { success: true, data: industries };
      return;
    } catch (error) {
      ctx.logger.error('获取地图标记点数据错误', error);
      ctx.body = { success: false, message: '获取数据失败' };
    }
  }

  /**
   * 获取危害因素统计数据
   * @return {Object} 危害因素统计数据
   */
  async getHazardFactorStats() {
    const { ctx } = this;
    const query = ctx.query;

    try {
      const filter = this._processFilter(query);
      const result = await ctx.model.Adminorg.aggregate([
        { $match: filter },
        { $project: { checkResult: 1 } },
      ]);

      // 统计各种危害因素的企业数量
      const stats = {};

      result.forEach(item => {
        if (item.checkResult) {
          Object.keys(item.checkResult).forEach(factor => {
            const data = item.checkResult[factor];
            if (!stats[factor]) {
              stats[factor] = {
                total: 0,
                exceed: 0,
                unit: data.unit || '',
              };
            }

            stats[factor].total++;

            if (data.exceed) {
              stats[factor].exceed++;
            }
          });
        }
      });

      // 转换为数组格式
      const hazardFactorStats = Object.keys(stats).map(key => ({
        name: key,
        ...stats[key],
      }));

      ctx.body = { success: true, data: hazardFactorStats };
      return;
    } catch (error) {
      ctx.logger.error('获取危害因素统计数据错误', error);
      ctx.body = { success: false, message: '获取数据失败' };
    }
  }

  /**
   * 获取行业统计数据
   * @return {Object} 行业统计数据
   */
  async getIndustryStats() {
    const { ctx } = this;
    const query = ctx.query;

    try {
      const filter = this._processFilter(query);
      const result = await ctx.model.Adminorg.aggregate([
        { $match: filter },
        { $group: {
          _id: '$industry',
          count: { $sum: 1 },
        } },
      ]);

      // 转换为所需格式
      const industryStats = result.map(item => ({
        name: item._id || '其他',
        count: item.count,
      }));

      ctx.body = { success: true, data: industryStats };
      return;
    } catch (error) {
      ctx.logger.error('获取行业统计数据错误', error);
      ctx.body = { success: false, message: '获取数据失败' };
    }
  }

  /**
   * 获取区域统计数据
   * @return {Object} 区域统计数据
   */
  async getRegionStats() {
    const { ctx } = this;
    const query = ctx.query;

    try {
      const filter = this._processFilter(query);
      const result = await ctx.model.Adminorg.aggregate([
        { $match: filter },
        { $group: {
          _id: '$workAddress.region',
          count: { $sum: 1 },
        } },
      ]);

      // 转换为所需格式
      const regionStats = result.map(item => ({
        name: item._id || '未知区域',
        count: item.count,
      }));

      ctx.body = { success: true, data: regionStats };
      return;
    } catch (error) {
      ctx.logger.error('获取区域统计数据错误', error);
      ctx.body = { success: false, message: '获取数据失败' };
    }
  }

  /**
   * 获取单位详情
   * @return {Object} 单位详情
   */
  async getUnitDetail() {
    const { ctx } = this;
    const { id } = ctx.params;

    try {
      const unit = await ctx.model.Adminorg.findById(id);

      if (!unit) {
        ctx.body = { success: false, message: '未找到单位信息' };
        return;
      }

      // 处理危害因素数据
      const hazardFactors = [];
      if (unit.checkResult) {
        Object.keys(unit.checkResult).forEach(key => {
          const factor = unit.checkResult[key];
          hazardFactors.push({
            name: key,
            value: factor.value,
            limit: factor.limit,
            unit: factor.unit,
            exceed: factor.exceed,
            checkDate: factor.checkDate,
          });
        });
      }

      const data = {
        id: unit._id,
        name: unit.name,
        industry: unit.industry,
        address: unit.workAddress ? unit.workAddress.address : '',
        region: unit.workAddress ? unit.workAddress.region : '',
        hazardFactors,
      };

      ctx.body = { success: true, data };
      return;
    } catch (error) {
      ctx.logger.error('获取单位详情错误', error);
      ctx.body = { success: false, message: '获取数据失败' };
    }
  }
}

module.exports = HazardInspectionDataController;
