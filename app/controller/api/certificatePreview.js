const Controller = require('egg').Controller;

class CertificateController extends Controller {
  async preview() {
    // 处理页面请求的逻辑
    const { ctx } = this;
    await ctx.render('manage/certificatePreview'); // 渲染名为 'certificatePreview' 的模板
  }

  async getCertificate() {
    const { ctx } = this;

    // 从请求体中获取证书编号
    const { certificateNumber } = ctx.request.body;

    // console.log('证书编号', certificateNumber);
    // 根据证书编号查询证书的详细信息
    const certificateDetails = await ctx.service.certificatePreview.getCertificateDetails(certificateNumber);

    try {
      // 返回证书详细信息给前端
      ctx.body = certificateDetails;
      ctx.status = 200;
    } catch (e) {
      console.log('e', e);
    }
  }
}

module.exports = CertificateController;
