const Controller = require('egg').Controller;
// 字典表数据

class DictionaryController extends Controller {
  // 获取学历字典
  async getEducationalQualifications(ctx) {
    const data = [
      // { id: 1, name: '小学' },
      // { id: 2, name: '初中' },
      { id: 3, name: '高中（含中专）及以下' },
      { id: 4, name: '专科（大专）' },
      { id: 5, name: '本科' },
      { id: 6, name: '硕士研究生' },
      { id: 7, name: '博士研究生' },
    ];
    ctx.helper.renderCustom(ctx, {
      data,
      message: '获取学历字典表数据成功',
    });
  }

  // 获取行政区划字典
  async getAreaCode(ctx) {
    let code = ctx.query.code;
    if (code && code.length < 12) {
      code = code.padEnd(12, '0');
    }
    const query = code ? { parent_code: code } : { level: '0' };
    const data = await ctx.service.district.findList(query);
    ctx.helper.renderCustom(ctx, {
      data: data.map(item => ({
        code: item.area_code.slice(0, 9),
        name: item.name,
      })),
      message: '获取行政区划字典表数据成功',
    });
  }

  // 获取身份证明文件类型
  async getIDtype(ctx) {
    const data = [
      { id: 1, name: '身份证' },
      { id: 2, name: '护照' },
      { id: 3, name: '驾驶证' },
      { id: 4, name: '军官证' },
      { id: 5, name: '港澳通行证' },
      { id: 6, name: '台胞证' },
      { id: 7, name: '户口簿' },
      { id: 8, name: '居住证' },
      { id: 9, name: '其他' },
    ];
    ctx.helper.renderCustom(ctx, {
      data,
      message: '获取身份证明文件类型成功',
    });
  }
}
module.exports = DictionaryController;
