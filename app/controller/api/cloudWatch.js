
const Controller = require('egg').Controller;

class CloudWatchController extends Controller {

  async getCloudSuperInfo(ctx) {
    const { _id } = ctx.query;
    const res = await ctx.model.CloudSupervision.findOne({ _id });
    ctx.helper.renderSuccess(ctx, {
      message: '获取成功',
      data: res,
    });
  }

  async cloudSupervisionSign(ctx) {
    const { _id, base64, year, month, day } = ctx.request.body;
    const params = {};
    const res = await ctx.model.CloudSupervision.findOne({ _id });
    const createUnitId = res.createUnitId;
    const data = await ctx.model.SuperUser.findOne({ _id: createUnitId }).select('cname');
    params.cname = data.cname;
    params.year = year;
    params.month = month;
    params.day = day;
    params.meetingId = res.meetingId;
    params.tableData = res.advice;
    params.contact = res.contact;
    params.phoneNum = res.phoneNum;
    params.address = res.address;
    params.unitName = res.unitName;
    params.superman = res.creatorName;
    params.superdate = res.startTime.substring(0, 10);
    params.base64 = base64;
    params.isSign = true;

    await ctx.service.cloudReport.createCloudPdf(params);
    const endRes = await ctx.model.CloudSupervision.findOne({ _id });
    ctx.helper.renderSuccess(ctx, {
      message: '签名成功',
      data: endRes,
    });
  }
}

module.exports = CloudWatchController;
