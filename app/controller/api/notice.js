const Controller = require('egg').Controller;

class NoticeController extends Controller {
  async preview() {
    // 处理页面请求的逻辑
    const { ctx, app } = this;

    await ctx.render('manage/notice', {
      staticRootPath: app.config.static.prefix,
      siteSeo: app.config.siteSeo,
    }); // 渲染名为 'notice' 的模板
  }
  async getNotice() {
    const { ctx, app } = this;
    const id = ctx.params.id;

    try {
      let doc = await ctx.model.MessageNotification.findOne({ _id: id }, { reader: 0 });
      if (doc) {
        doc = JSON.parse(JSON.stringify(doc));
        doc.files = doc.files.map(e => {
          return {
            name: e,
            url: `/static${app.config.upload_http_path}/messageNotification/${id}/${e}`,
          };
        });
      }

      ctx.body = {
        data: doc,
        msg: '获取成功',
      };
    } catch (error) {
      console.log(error);
      ctx.body = {
        data: [],
        msg: '获取失败',
      };
    }

  }

}

module.exports = NoticeController;
