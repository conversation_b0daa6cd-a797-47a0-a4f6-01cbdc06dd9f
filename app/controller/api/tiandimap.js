/**
 * 天地图API接口控制器
 * 提供地理编码、反向地理编码等功能
 */

const { Controller } = require('egg');
const axios = require('axios');

// 天地图API密钥

class TiandiMapController extends Controller {
  /**
   * 地理编码接口 - 将地址转换为坐标
   * @return {Promise<void>}
   */
  // const TIANDITU_KEY =

  async geocode() {
    const { ctx } = this;
    const { keyWord } = ctx.query;

    try {
      if (!keyWord) {
        ctx.body = {
          success: false,
          error: '地址关键词不能为空',
        };
        return;
      }

      const requestParams = { keyWord };
      const url = `${this.config.tiandimap.url}/geocoder?ds=${encodeURIComponent(JSON.stringify(requestParams))}&tk=${this.config.tiandimap.key}`;

      const response = await axios.get(url);
      const data = response.data;

      if (data.status === '0' && data.location) {
        ctx.body = {
          success: true,
          data: {
            location: data.location,
            address: data.location.keyWord,
            longitude: parseFloat(data.location.lon),
            latitude: parseFloat(data.location.lat),
            level: data.location.level,
            score: data.location.score,
          },
        };
      } else if (data.status === '101') {
        ctx.body = {
          success: false,
          error: '未找到对应的地理坐标，请检查地址是否正确',
        };
      } else {
        ctx.body = {
          success: false,
          error: data.msg || '地理编码查询失败',
        };
      }
    } catch (error) {
      ctx.logger.error('地理编码查询异常:', error);
      ctx.body = {
        success: false,
        error: error.message || '地理编码查询发生错误',
      };
    }
  }

  /**
   * 反向地理编码接口 - 将坐标转换为地址
   * @return {Promise<void>}
   */
  async reverseGeocode() {
    const { ctx } = this;
    const { lon, lat } = ctx.query;

    try {
      if (!lon || !lat) {
        ctx.body = {
          success: false,
          error: '经度和纬度不能为空',
        };
        return;
      }

      const requestParams = { lon, lat, ver: 1 };
      const url = `${this.config.tiandimap.url}/geocoder?postStr=${encodeURIComponent(JSON.stringify(requestParams))}&type=geocode&tk=${this.config.tiandimap.key}`;

      const response = await axios.get(url);
      const data = response.data;

      if (data.status === '0' && data.result) {
        // 提取地址组件
        const addressComponent = data.result.addressComponent || {};

        ctx.body = {
          success: true,
          data: {
            formatted_address: data.result.formatted_address,
            location: {
              longitude: parseFloat(lon),
              latitude: parseFloat(lat),
            },
            addressComponent: {
              province: addressComponent.province || '',
              city: addressComponent.city || '',
              district: addressComponent.district || '',
              township: addressComponent.township || '',
              street: addressComponent.street || '',
              streetNumber: addressComponent.streetNumber || '',
            },
            poiList: data.result.pois || [],
            roadList: data.result.roads || [],
          },
        };
      } else {
        ctx.body = {
          success: false,
          error: data.msg || '反向地理编码查询失败',
        };
      }
    } catch (error) {
      ctx.logger.error('反向地理编码查询异常:', error);
      ctx.body = {
        success: false,
        error: error.message || '反向地理编码查询发生错误',
      };
    }
  }

  /**
   * 搜索POI接口
   * @return {Promise<void>}
   */
  async searchPOI() {
    const { ctx } = this;
    const {
      keyWord,
      start = 0,
      count = 10,
      queryType,
      mapBound,
      pointLonlat,
      queryRadius,
      level,
    } = ctx.query;

    try {
      if (!keyWord) {
        ctx.body = {
          success: false,
          error: '搜索关键词不能为空',
        };
        return;
      }

      if (!queryType) {
        ctx.body = {
          success: false,
          error: '查询类型不能为空',
        };
        return;
      }

      // 构建搜索参数
      const searchParams = {
        keyWord,
        start: parseInt(start, 10),
        count: parseInt(count, 10),
        queryType,
      };

      // 根据搜索类型设置不同参数
      if (queryType === '2' && mapBound) {
        // 视野内搜索
        searchParams.mapBound = mapBound;
        searchParams.level = level || 12;
      } else if (queryType === '3' && pointLonlat && queryRadius) {
        // 周边搜索
        searchParams.pointLonlat = pointLonlat;
        searchParams.queryRadius = queryRadius;
      }

      const url = `${this.config.tiandimap.url}/v2/search?postStr=${encodeURIComponent(JSON.stringify(searchParams))}&type=query&tk=${this.config.tiandimap.key}`;

      const response = await axios.get(url);
      const data = response.data;

      if (data.status && data.status.infocode === 1000 && data.pois) {
        // 处理搜索结果
        const pois = data.pois.map(item => {
          const coordinates = item.lonlat.split(',');
          return {
            name: item.name,
            address: item.address || '',
            location: {
              longitude: parseFloat(coordinates[0]),
              latitude: parseFloat(coordinates[1]),
            },
            type: item.typeCode,
            typeName: item.typeName,
            province: item.province,
            city: item.city,
            county: item.county,
          };
        });

        ctx.body = {
          success: true,
          data: {
            total: data.count,
            pois,
            keyword: keyWord,
          },
        };
      } else if (data.prompt) {
        // 检查是否有提示信息
        ctx.body = {
          success: false,
          error: `搜索提示: ${data.prompt.type}`,
        };
      } else {
        ctx.body = {
          success: false,
          error: '未找到搜索结果',
        };
      }
    } catch (error) {
      ctx.logger.error('POI搜索查询异常:', error);
      ctx.body = {
        success: false,
        error: error.message || 'POI搜索查询发生错误',
      };
    }
  }

  /**
   * 获取静态地图URL
   * @return {void}
   */
  getStaticMapUrl() {
    const { ctx } = this;
    const {
      longitude,
      latitude,
      width = 400,
      height = 300,
      zoom = 12,
    } = ctx.query;

    try {
      if (!longitude || !latitude) {
        ctx.body = {
          success: false,
          error: '经度和纬度不能为空',
        };
        return;
      }

      // 构建标记点参数
      const markers = `${longitude},${latitude}`;

      // 生成静态地图URL
      const staticMapUrl = `${this.config.tiandimap.url}/staticimage?center=${longitude},${latitude}&width=${width}&height=${height}&zoom=${zoom}&markers=${markers}&markerStyles=l,A,0xFF0000&tk=${this.config.tiandimap.key}`;

      ctx.body = {
        success: true,
        data: {
          url: staticMapUrl,
        },
      };
    } catch (error) {
      ctx.logger.error('获取静态地图URL异常:', error);
      ctx.body = {
        success: false,
        error: error.message || '获取静态地图URL发生错误',
      };
    }
  }
  /**
   * 获取行政区划数据
   * @return {Promise<void>}
   */
  async district() {
    const { ctx } = this;
    const { adcode, subdistrict = 1, tk = this.config.tiandimap.key } = ctx.query;

    try {
      if (!adcode) {
        ctx.body = {
          success: false,
          error: '行政区划编码不能为空',
        };
        return;
      }

      const requestParams = {
        keyWord: adcode,
        level: 'district',
        subdistrict: parseInt(subdistrict, 10),
      };
      const url = `${this.config.tiandimap.url}/administrative?postStr=${encodeURIComponent(JSON.stringify(requestParams))}&tk=${tk}`;

      const response = await axios.get(url);
      const data = response.data;

      if (data && data.status === '0' && data.results) {
        // 处理结果为GeoJSON格式
        const geoJSON = this.convertToGeoJSON(data.results);
        ctx.body = {
          success: true,
          data: geoJSON,
        };
      } else {
        ctx.body = {
          success: false,
          error: (data && data.message) || '获取行政区划数据失败',
        };
      }
    } catch (error) {
      ctx.logger.error('获取行政区划数据异常:', error);
      ctx.body = {
        success: false,
        error: error.message || '获取行政区划数据发生错误',
      };
    }
  }
  /**
   * 将天地图行政区划API结果转换为GeoJSON格式
   * @param {Object} results - 天地图API返回的行政区划数据
   * @return {Object} GeoJSON格式的数据
   */
  convertToGeoJSON(results) {
    if (!results || !results.length) {
      return {
        type: 'FeatureCollection',
        features: [],
      };
    }

    const features = [];

    results.forEach(district => {
      if (district.geometry) {
        // 解析几何信息（多边形）
        let coordinates = [];
        try {
          if (typeof district.geometry === 'string') {
            const geometryStr = district.geometry.replace(/;/g, '],[');
            coordinates = JSON.parse(`[[${geometryStr}]]`);
          } else if (Array.isArray(district.geometry)) {
            coordinates = district.geometry;
          }
        } catch (e) {
          this.ctx.logger.error('解析几何信息失败:', e);
        }
        if (coordinates.length > 0) {
          features.push({
            type: 'Feature',
            properties: {
              name: district.name,
              adcode: district.adminCode,
              center: district.center ? district.center.split(',').map(Number) : [ 0, 0 ],
              level: district.level,
              parent: district.parent || '',
            },
            geometry: {
              type: 'Polygon',
              coordinates: [ coordinates ],
            },
          });
        }
      }

      // 处理子区域
      if (district.districts && district.districts.length > 0) {
        const childFeatures = this.convertToGeoJSON(district.districts).features;
        features.push(...childFeatures);
      }
    });

    return {
      type: 'FeatureCollection',
      features,
    };
  }
}

module.exports = TiandiMapController;
