const Controller = require('egg').Controller;

class HzReservationController extends Controller {
  // 查询预约列表
  async list() {
    const { ctx, service } = this;
    try {
      const { page = 1, pageSize = 10, status, keyword } = ctx.query;
      const result = await service.hzReservation.getReservationList({
        page: Number(page),
        pageSize: Number(pageSize),
        status,
        keyword,
        createdAt: -1,
      });
      ctx.body = { code: 200, message: '查询成功', data: result };
    } catch (err) {
      ctx.body = { code: 500, message: err.message };
    }
  }

  // 更新预约状态
  async update() {
    const { ctx, service } = this;
    try {
      const { id, status, acceptDescription } = ctx.request.body;
      
      // 参数校验
      if (!id) {
        ctx.body = { code: 400, message: '缺少预约ID' };
        return;
      }
      
      if (!status) {
        ctx.body = { code: 400, message: '缺少状态值' };
        return;
      }
      
      const result = await service.hzReservation.updateOne(id, status, acceptDescription);
      ctx.body = { code: 200, message: '更新成功', data: result };
    } catch (err) {
      ctx.body = { code: 500, message: err.message };
    }
  }
}

module.exports = HzReservationController;
