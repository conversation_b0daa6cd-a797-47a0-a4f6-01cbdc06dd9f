const Controller = require('egg').Controller;
const axios = require('axios');
// const fs = require('fs');
// const path = require('path');
class ProxyController extends Controller {
  // async index() {
  //   const { ctx } = this;
  //   const { query, body } = ctx.request;
  //   const targetUrl = decodeURIComponent(ctx.params.url); // 解码动态参数的值
  //   try {
  //     let newUrl = '';
  //     console.log('targetUrl', targetUrl, query, ctx.request.headers);
  //     if (targetUrl.includes('v4/map/styles')) {
  //       newUrl = 'https://webapi.amap.com/v4/map/styles';
  //       query.jscode = 'aaaee764033afb4a143e4acd63389e30';
  //     } else if (targetUrl.includes('v3/vectormap')) {
  //       newUrl = 'https://fmap01.amap.com/v3/vectormap';
  //       query.jscode = 'aaaee764033afb4a143e4acd63389e30';
  //     } else if (targetUrl.includes('webapi.amap.com')) {
  //       newUrl = targetUrl;
  //       query.jscode = 'aaaee764033afb4a143e4acd63389e30';
  //     } else {
  //       newUrl = 'https://restapi.amap.com/' + targetUrl;
  //       query.jscode = 'aaaee764033afb4a143e4acd63389e30';
  //     }
  //     console.log('targetUrl', newUrl, targetUrl, query, body);
  //     // 转发请求到目标URL
  //     const response = await axios.request({
  //       method: ctx.request.method,
  //       url: newUrl,
  //       params: query,
  //       data: body,
  //       headers: ctx.request.headers,
  //     });
  //     // 返回目标URL的响应
  //     console.log('response', response.headers);
  //     ctx.status = response.status;
  //     ctx.set(response.headers);
  //     // 设置响应头content-type 为application/octet-stream
  //     if (newUrl.includes('v3/log/init')) {
  //       ctx.set('content-type', 'application/octet-stream');
  //     }
  //     ctx.body = response.data;
  //   } catch (error) {
  //     console.log('error121212', error);
  //     // 处理错误情况
  //     if (error.response) {
  //       ctx.status = error.response.status;
  //       ctx.body = error.response.data;
  //     } else {
  //       ctx.status = 500;
  //       ctx.body = error.message;
  //     }
  //   }
  // }
  // 对应/_AMapService/ { set $args "$args&jscode=aaaee764033afb4a143e4acd63389e30"; proxy_pass https://restapi.amap.com/; }
  async _AMapService() {
    const { ctx } = this;
    const { query, body } = ctx.request;
    const targetUrl = decodeURIComponent(ctx.params.url); // 解码动态参数的值
    try {
      const newUrl = 'https://restapi.amap.com/' + targetUrl;
      query.jscode = 'aaaee764033afb4a143e4acd63389e30';
      // 转发请求到目标URL
      const response = await axios.request({
        method: ctx.request.method,
        url: newUrl,
        params: query,
        data: body,
        headers: ctx.request.headers,
      });
      // 返回目标URL的响应
      console.log('response', response.headers);
      ctx.status = response.status;
      ctx.set(response.headers);
      // 设置响应头content-type 为application/octet-stream
      console.log('进入了_AMapService', targetUrl);
      if (newUrl.includes('v3/log/init')) {
        ctx.set('content-type', 'application/octet-stream');
      }
      ctx.body = response.data;
    } catch (error) {
      // 处理错误情况
      if (error.response) {
        ctx.status = error.response.status;
        ctx.body = error.response.data;
      } else {
        ctx.status = 500;
        ctx.body = error.message;
      }
    }
  }
  async gdWebApi() {
    const { ctx } = this;
    const { query, body } = ctx.request;
    const targetUrl = decodeURIComponent(ctx.params.url); // 解码动态参数的值
    try {
      const newUrl = 'https://webapi.amap.com/' + targetUrl;
      // 转发请求到目标URL
      const response = await axios.request({
        method: ctx.request.method,
        url: newUrl,
        params: query,
        data: body,
        headers: ctx.request.headers,
      });
      // 返回目标URL的响应
      console.log('response', response.headers);
      ctx.status = response.status;
      ctx.set(response.headers);
      // 设置响应头content-type 为application/octet-stream
      ctx.body = response.data;
    } catch (error) {
      // 处理错误情况
      if (error.response) {
        ctx.status = error.response.status;
        ctx.body = error.response.data;
      } else {
        ctx.status = 500;
        ctx.body = error.message;
      }
    }
  }
  async bdWebApi() {
    const { ctx } = this;
    const { query, body } = ctx.request;
    const targetUrl = ctx.params.url; // 解码动态参数的值
    try {
      console.log('targetUrl', targetUrl, ctx.request.method, ctx.request.headers);
      const newUrl = 'https://api.map.baidu.com/' + targetUrl;
      console.log('进入了bdWebApi', newUrl, query);
      delete ctx.request.headers.cookie;
      // 转发请求到目标URL
      const response = await axios.request({
        method: ctx.request.method,
        url: newUrl,
        params: query,
        data: body,
        // headers: ctx.request.headers,
      });
      // 返回目标URL的响应
      // console.log('response', response);
      ctx.status = response.status;
      ctx.set(response.headers);
      // 设置响应头content-type 为application/octet-stream
      // ctx.set('content-type', 'application/octet-stream');
      ctx.body = response.data;
    } catch (error) {
      // 处理错误情况、
      console.log('errorbdWebApi', error);
      if (error.response) {
        ctx.status = error.response.status;
        ctx.body = error.response.data;
      } else {
        ctx.status = 500;
        ctx.body = error.message;
      }
    }
  }
  async bdDlswb() {
    const { ctx } = this;
    const { query, body } = ctx.request;
    const targetUrl = decodeURIComponent(ctx.params.url); // 解码动态参数的值
    try {
      const newUrl = 'https://dlswbr.baidu.com/' + targetUrl;
      console.log('进入了bdDlswb', newUrl, query);
      // ctx.request.headers = 去掉Cookie
      delete ctx.request.headers.cookie;
      // 转发请求到目标URL
      const response = await axios.request({
        method: ctx.request.method,
        url: newUrl,
        params: query,
        data: body,
        // headers: ctx.request.headers,
      });
      // 返回目标URL的响应
      // console.log('bdDlswbRsponse', response);
      ctx.status = response.status;
      ctx.set(response.headers);
      // 设置响应头content-type 为application/octet-stream
      ctx.body = response.data;
    } catch (error) {
      // 处理错误情况
      if (error.response) {
        ctx.status = error.response.status;
        ctx.body = error.response.data;
      } else {
        ctx.status = 500;
        ctx.body = error.message;
      }
    }
  }
  async bdm0proxy() {
    const { ctx } = this;
    const { query } = ctx.request;
    const targetUrl = (ctx.params.url); // 解码动态参数的值
    try {
      const queryString = Object.keys(query)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`)
        .join('&');
      // const newUrl = 'https://maponline0.bdimg.com/' + targetUrl;
      // 将query中的参数作为params拼接到newUrl中
      const newUrl = `https://maponline0.bdimg.com/${targetUrl}?${queryString}`;
      console.log('进入了bdm0proxy', newUrl, targetUrl, query);
      const config = {
        method: ctx.request.method,
        url: newUrl,
        headers: {
          Accept: '*/*',
          Host: 'maponline0.bdimg.com',
          Connection: 'keep-alive',
        },
      };
      const response = await axios(config);
      // 返回目标URL的响应
      ctx.status = response.status;
      // // 判断Content-Type 是不是image/png
      // if (JSON.stringify(response.headers['content-type']).indexOf('image/png') > -1) {
      //   ctx.set('Content-Type', 'image/png');
      //   const buffer = Buffer.from(response.data, 'binary');
      //   const tempFilePath = '/app/' + new Date().getTime() + '.png'; // 临时文件路径
      //   fs.writeFileSync(tempFilePath, buffer); // 将Buffer对象写入到临时文件中
      //   ctx.body = fs.createReadStream(tempFilePath); // 读取临时文件并作为响应体返回
      //   // fs.unlinkSync(tempFilePath); // 删除临时文件
      //   return;
      // }
      for (const key in response.headers) {
        ctx.set(key, response.headers[key]);
      }
      ctx.set('Content-Type', response.headers['content-type']); // 添加这一行
      ctx.rawBody = response.data;
    } catch (error) {
      // 处理错误情况
      console.log('errorbdm0proxy', error);
      if (error.response) {
        ctx.status = error.response.status;
        ctx.body = error.response.data;
      } else {
        ctx.status = 500;
        ctx.body = error.message;
      }
    }
  }
}

module.exports = ProxyController;
