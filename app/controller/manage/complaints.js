
const Controller = require('egg').Controller;
class CommentController extends Controller {
  // 获取监管端的投诉举报列表
  async getBySuperId() {
    const { ctx, app } = this;
    const userId = ctx.session.superUserInfo._id;
    const params = ctx.request.body;
    console.log('投诉举报查询条件====', params);
    const list = await ctx.service.complaints.getBySuperId(userId, params);
    const count = [ 0, 0, 0, 0, 0 ];
    const uploadPathBefor = `/static${app.config.upload_http_path}/complaints_files/`;
    if (list) {
      for (const i of [ 0, 1, 2, 3, 4 ]) {
        count[i] = await ctx.service.complaints.count(userId, {
          status: i,
          date: params.date,
          adminUserId: params.adminUserId,
        });
      }
      const resList = list.map(ele => {
        const newEle = JSON.parse(JSON.stringify(ele));
        newEle.uploadPath = ele.userId && ele.userId._id ? uploadPathBefor + ele.userId._id + '/' : '';
        return newEle;
      });
      ctx.helper.renderSuccess(ctx, { data: { list: resList, count }, message: '数据获取成功' });
    } else {
      ctx.helper.renderFail(ctx, { message: '数据获取失败' });
    }
  }
  // 更新数据
  async update() {
    const { ctx } = this;
    const { _id, status, comments } = ctx.request.body;
    const replyMsg = ctx.request.body.replyMsg || '';
    const res = await ctx.service.complaints.update(_id, status, comments, replyMsg);
    if (res.ok === 1) {
      ctx.helper.renderSuccess(ctx, { data: res, message: '数据更新成功' });
    } else {
      ctx.helper.renderFail(ctx, { data: res, message: '数据更新失败' });
    }
  }
}
module.exports = CommentController;
