const Controller = require('egg').Controller;

const {
  superUserRule,
} = require('@validate');
const {
  validatorUtil,
} = require('@utils');

const _ = require('lodash');


class AdminUserController extends Controller {
  // 行政管理员管理==========================================
  async list() {
    const {
      ctx,
      service,
    } = this;
    try {

      const payload = ctx.request.body;
      const query = { state: '1' };
      if (payload.type) {
        if (typeof payload.type === 'object') {
          if (payload.type.length) {
            query.type = { $in: payload.type };
          } else {
            delete payload.type;
          }
        } else {
          query.type = +payload.type;
        }
      } else {
        delete payload.type;
      }
      if (payload.enable) {
        query.enable = +payload.enable === 1;
      } else {
        delete payload.enable;
      }
      if (payload.regAddr && payload.regAddr.length) {
        query.regAdd = { $all: payload.regAddr.map(ele => ele.name) };
      } else {
        query.regAdd = { $all: ctx.session.superUserInfo.regAdd };
      }
      const collections = [
        {
          name: 'admingroups',
          selfKey: 'group',
          foreignKey: '_id',
          asKey: 'group',
        }, {
          name: 'apiUsers',
          selfKey: '_id',
          foreignKey: 'orgId',
          asKey: 'apiUser',
        },
      ];
      const searchKeys = [ '_id', 'userName', 'phoneNum', 'name', 'cname', 'members.name', 'members.phoneNum', 'members.userName' ];
      const params = {
        collections,
        query,
        sort: { date: -1 },
        searchKeys,
        files: {
          _id: 1,
          userName: 1,
          cname: 1,
          name: 1,
          regAdd: 1,
          phoneNum: 1,
          email: 1,
          enable: 1,
          'group._id': 1,
          'group.name': 1,
          'apiUser._id': 1,
          'apiUser.group': 1,
          'apiUser.status': 1,
          members: 1,
          jobTitle: 1,
          powerStatus: 1,
          type: 1,
          date: 1,
          createdAt: 1,
        },
      };
      const superUserList = await service.superUser.unionQuery(payload, params);
      // delete query.type;
      const total = {
        allType: await service.superUser.count(query),
        type1: await service.superUser.count({ ...query, type: 1 }),
        type2: await service.superUser.count({ ...query, type: 2 }),
        type3: await service.superUser.count({ ...query, type: 3 }),
        type4: await service.superUser.count({ ...query, type: 4 }),
      };
      ctx.helper.renderSuccess(ctx, {
        data: {
          ...superUserList,
          total,
        },
      });

    } catch (err) {

      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
  }

  // 获取完整的区域路径
  async getFullRegionPath(areaCode) {
    const { ctx } = this;
    
    // 使用 $graphLookup 进行递归查询
    const [ result ] = await ctx.model.District.aggregate([
      {
        $match: { area_code: areaCode }
      },
      {
        $graphLookup: {
          from: 'district',
          startWith: '$parent_code',
          connectFromField: 'parent_code',
          connectToField: 'area_code',
          as: 'ancestors',
          maxDepth: 10
        }
      },
      {
        $project: {
          allRegions: {
            $concatArrays: [
              '$ancestors',
              [{ 
                name: '$name', 
                area_code: '$area_code', 
                parent_code: '$parent_code', 
                level: '$level' 
              }]
            ]
          }
        }
      }
    ]);

    if (!result) return null;

    // 按层级排序并提取名称
    return result.allRegions
      .sort((a, b) => a.level - b.level)
      .map(region => region.name);
  }

  // 获取区域地址
  async getRegAdd(fields) {
    const { ctx } = this;
    const { crossRegionManage } = ctx.app.config;
    
    if (crossRegionManage && Array.isArray(fields.regAdd) && Array.isArray(fields.regAdd[0])) {
      const fullPath = await this.getFullRegionPath(fields.area_code);
      return fullPath || fields.regAdd;
    }
    return fields.regAdd;
  }

  async create() {

    const {
      ctx,
      service,
      config,
    } = this;
    try {

      const fields = ctx.request.body || {},
        logo = fields.logo || '';

      const checkPhoneNum = await service.superUser.item(ctx, {
        query: {
          state: '1',
          phoneNum: fields.phoneNum,
        },
      });
      if (checkPhoneNum) {
        if (checkPhoneNum instanceof Array) {
          ctx.helper.renderFail(ctx, {
            message: '该手机号已被使用',
          });
          return;
        }
        ctx.helper.renderFail(ctx, {
          message: '该手机号已被使用',
        });
        return;
      }


      const checkCname = await service.superUser.item(ctx, {
        query: {
          state: '1',
          cname: fields.cname,
        },
      });
      if (checkCname) {
        ctx.helper.renderFail(ctx, {
          message: '该机构已存在',
        });
        return;
      }

      const configSuperGroupID = config.groupID.superGroupID;
      if (_.isEmpty(configSuperGroupID)) {
        throw new Error('哎呀！网络开小差了！要不,稍后再试试？');
      }
      fields.group = configSuperGroupID;

      const formObj = {
        userName: fields.userName,
        name: fields.name,
        email: fields.email,
        phoneNum: fields.phoneNum,
        countryCode: fields.countryCode,
        landline: fields.landline,
        confirm: fields.confirm,
        group: fields.group,
        enable: fields.enable,
        comments: fields.comments,
        cname: fields.cname,
        area_code: fields.area_code,
        regAdd: await this.getRegAdd(fields),
        powerStatus: !!fields.powerStatus,
        type: fields.type,
        jobTitle: fields.jobTitle || '',
        members: fields.members ? fields.members.map(ele => {
          return {
            ...ele,
            userName: ele.phoneNum,
          };
        }) : [],
      };

      if (!_.isEmpty(logo)) {
        formObj.logo = logo;
      }

      ctx.body = {
        status: 200,
      };

      ctx.validate(superUserRule.form(ctx), formObj);

      // 单独判断密码
      if (fields.password) {
        if (!validatorUtil.checkPwd(fields.password)) {
          ctx.__('validate_inputCorrect', [ ctx.__('label_password') ]);
        } else {
          formObj.password = fields.password;
        }
      }

      const oldItem = await service.superUser.item(ctx, {
        query: {
          userName: fields.userName,
        },
      });

      if (!_.isEmpty(oldItem)) {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_hadUse_userName'),
        });
        return;
      }

      await service.superUser.create(formObj);

      ctx.helper.renderSuccess(ctx);
    } catch (err) {

      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }


  async getOne() {
    const {
      ctx,
      service,
    } = this;
    try {
      const _id = ctx.query.id;
      const password = ctx.query.password;
      const queryObj = {
        _id,
      };

      if (password) {
        _.assign(queryObj, {
          password,
        });
      }
      const targetItem = await service.superUser.item(ctx, {
        query: queryObj,
        files: '-password-power',
      });
      // 此时需要根据code查找对应行政区划
      const district = [];
      if (targetItem.area_code !== this.config.China.area_code) {
        let districtLast = await service.district.item(ctx, {
          query: {
            area_code: targetItem.area_code,
          },
        });
        district.push(districtLast);
        while (districtLast.level !== '0' && districtLast.parent_code !== '0') {
          districtLast = await service.district.item(ctx, {
            query: {
              area_code: districtLast.parent_code,
            },
          });
          district.push(districtLast);
        }
        district.reverse();
      } else {
        district.push(this.config.China);
      }
      district.reverse();

      ctx.helper.renderSuccess(ctx, {
        data: {
          group: targetItem.group,
          targetItem,
          district,
        },
      });

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }

  }

  async update() {

    const {
      ctx,
      service,
      config,
    } = this;
    try {

      const fields = ctx.request.body || {};
      // const checkPhoneNum = await service.superUser.item(ctx, {
      //   query: {
      //     $or: [
      //       { phoneNum: { $in: phoneNumArr } },
      //       { members: { $elemMatch: { phoneNum: { $in: phoneNumArr } } } },
      //     ],
      //   },
      // });
      const phoneNumArr = fields.members ? [ fields.phoneNum, ...fields.members.map(ele => ele.phoneNum) ] : [ fields.phoneNum ];
      const phoneNumSet = new Set(phoneNumArr);
      if (phoneNumSet.size < phoneNumArr.length) {
        return ctx.helper.renderFail(ctx, {
          message: '手机号重复',
        });
      }
      const findPhoneNum = await ctx.model.SuperUser.find({
        state: '1',
        $or: [
          { phoneNum: { $in: phoneNumArr } },
          { members: { $elemMatch: { phoneNum: { $in: phoneNumArr } } } },
        ],
      });
      if (findPhoneNum && findPhoneNum.length) {
        // if (findPhoneNum.length > 1) {
        //   ctx.helper.renderFail(ctx, {
        //     message: '手机号重复',
        //   });
        //   return;
        // }
        const checkPhoneNum = findPhoneNum[0];
        if (checkPhoneNum._id !== fields._id) {
          ctx.helper.renderFail(ctx, {
            message: '该手机号已被使用',
          });
          return;
        }
      }

      const checkCname = await service.superUser.item(ctx, {
        query: {
          state: '1',
          cname: fields.cname,
        },
      });
      if (checkCname && checkCname._id !== fields._id) {
        ctx.helper.renderFail(ctx, {
          message: '该单位已存在',
        });
        return;
      }

      const configSuperGroupID = config.groupID.superGroupID;
      if (_.isEmpty(configSuperGroupID)) {
        throw new Error('哎呀！网络开小差了！要不,稍后再试试？');
      }
      fields.group = configSuperGroupID;

      const formObj = {
        userName: fields.userName,
        name: fields.name,
        email: fields.email,
        logo: fields.logo,
        phoneNum: fields.phoneNum,
        countryCode: fields.countryCode,
        landline: fields.landline,
        group: fields.group,
        enable: fields.enable,
        comments: fields.comments,
        cname: fields.cname,
        area_code: fields.area_code,
        regAdd: await this.getRegAdd(fields),
        date: new Date(),
        members: fields.members.map(ele => {
          return {
            ...ele,
            userName: ele.phoneNum,
          };
        }),
        powerStatus: !!fields.powerStatus,
        type: fields.type,
        jobTitle: fields.jobTitle || '',
      };
      ctx.validate(superUserRule.form(ctx), formObj);

      // 单独判断密码
      if (fields.password) {
        if (!validatorUtil.checkPwd(fields.password)) {
          ctx.__('validate_inputCorrect', [ ctx.__('label_password') ]);
        } else {
          formObj.password = fields.password;
        }
      }

      const oldResource = await service.superUser.item(ctx, {
        query: {
          userName: fields.userName,
        },
      });

      if (!_.isEmpty(oldResource) && oldResource._id !== fields._id) {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_hadUse_userName'),
        });
        return;
      }
      const old = await service.superUser.update(ctx, fields._id, formObj);

      const newData = await ctx.model.SuperUser.findOne({ _id: fields._id });

      ctx.helper.renderSuccess(ctx, {
        data: {
          formObj,
          old,
          newData,
        },
      }
      );

    } catch (err) {

      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }

  }

  // 注销，假删
  async removes() {
    const {
      ctx,
      service,
    } = this;
    try {
      const targetId = ctx.query.ids;
      const oldUser = await service.superUser.item(ctx, {
        query: { _id: targetId },
      });
      if (_.isEmpty(oldUser)) {
        throw new Error(ctx.__('validate_error_params'));
      }
      // await service.superUser.removes(ctx, targetId);
      await service.superUser.update(ctx, targetId, { state: '0' });
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // ================================================================

  async getAdministrativeDivision() {
    try {
      const payload = this.ctx.query || {};
      payload.pageSize = 100;
      const parent_code = payload.parent_code || '0';
      // const level = payload.level || '0';
      let ret = await this.service.district.find(payload, {
        query: {
          parent_code,
          // level,
        },
      });
      if (ret.docs[0].name === '直辖区') {
        ret = await this.ctx.service.district.find(payload, {
          query: {
            parent_code: ret.docs[0].area_code,
          },
        });
      }
      const result = ret;
      for (let index = 0; index < ret.docs.length; index++) {
        if (ret.docs[index].name === '市辖区') {
          result.docs.splice(index, 1);
          break;
        }
      }
      this.ctx.helper.renderSuccess(this.ctx, {
        data: ret,
      });
    } catch (error) {
      throw error;
    }
  }

  async getDefaultGroup() {
    const { ctx } = this;
    const superUserInfo = this.ctx.session.superUserInfo;
    const superUser = await ctx.model.SuperUser.findOne({ _id: superUserInfo._id }, { area_code: 1, type: 1 });
    ctx.helper.renderSuccess(ctx, {
      data: {
        superGroupID: this.config.groupID.superGroupID,
        China: {
          area_code: superUser.area_code,
          name: superUserInfo.regAdd,
          type: superUser.type,
          _id: superUserInfo._id,
        },
      },
    });
  }
  // 查询/编辑监管单位的power
  async editPower() {
    const {
      ctx,
    } = this;
    const { _id, power } = ctx.request.body;
    let result;
    if (power) { // 编辑
      // console.log(3333333333, _id, power);
      // result = await ctx.model.SuperUser.update({ _id }, { $set: { power } });
      result = await ctx.service.db.updateOne('SuperUser', { _id }, { $set: { power } });
    } else if (_id) { // 查询
      result = await ctx.model.SuperUser.findOne({ _id }, { power: 1, powerStatus: 1 });
    }
    ctx.helper.renderSuccess(ctx, { data: result });
  }
  // 查看手机号记录到日志中
  async checkPhoneLog() {
    const phoneNumber = this.ctx.query.phoneNum;
    const modelName = this.ctx.query.model;
    const { ctx } = this;
    const user = ctx.session.superUserInfo;
    ctx.service.operateLog.create(modelName, { optType: 'check', supplementaryNotes: `${user.name}查看了${phoneNumber}手机号` });
    // ctx.service.operateLog.create('SuperUser', { optType: 'check', supplementaryNotes: `${user.name}查看了${phoneNumber}手机号`, optUserId: id });

  }
  // 查看身份号记录到日志中
  async checkIDNumLog() {
    const IDNum = this.ctx.query.IDNum;
    const modelName = this.ctx.query.model;
    const { ctx } = this;
    const user = ctx.session.superUserInfo;
    ctx.service.operateLog.create(modelName, { optType: 'check', supplementaryNotes: `${user.name}查看了${IDNum}身份证号` });
  }
  // 查看姓名记录到日志中
  async checkNameLog() {
    const name = this.ctx.query.name;
    const modelName = this.ctx.query.model;
    const { ctx } = this;
    const user = ctx.session.superUserInfo;
    ctx.service.operateLog.create(modelName, { optType: 'check', supplementaryNotes: `${user.name}查看了${name}名字` });
  }
}

module.exports = AdminUserController;
