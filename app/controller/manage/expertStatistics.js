// 职业健康专家统计
const expertStatisticsController = {
  // 获取专家列表
  async statisticsByType(ctx) {
    const { app } = this;
    try {
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/statistics/byType`,
        {
          method: 'GET',
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '专家类型分布情况获取成功', '专家类型分布情况获取失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '专家类型分布情况获取失败',
        data: err,
      });
    }
  },

  // 专家年龄、性别分布情况
  async statisticsByAge(ctx) {
    const { app } = this;
    try {
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/statistics/byAge`,
        {
          method: 'GET',
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '专家年龄、性别分布情况获取成功', '专家年龄、性别分布情况获取失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '专家年龄、性别分布情况获取失败',
        data: err,
      });
    }
  },

  // 专家年龄、学历分布情况
  async statisticsByEducation(ctx) {
    const { app } = this;
    try {
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/statistics/byEducation`,
        {
          method: 'GET',
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '专家年龄、学历分布情况获取成功', '专家年龄、学历分布情况获取失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '专家年龄、学历分布情况获取失败',
        data: err,
      });
    }
  },
  // 专家流动情况
  async statisticsByMobility(ctx) {
    const { app } = this;
    try {
      const { name, phone, level, curPage, pageSize } = ctx.query;
      if (level && ![ 1, 2, 3 ].includes(+level)) {
        throw new Error('level传参错误，只能为1,2,3。');
      }
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/statistics/byMobility?level=${level || ''}&&name=${name || ''}&&phone=${phone || ''}&&curPage=${curPage || 1}&&pageSize=${pageSize || 10}`,
        {
          method: 'GET',
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '专家流动情况获取成功', '专家流动情况获取失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '专家流动情况获取失败',
        data: err,
      });
    }
  },


};

module.exports = expertStatisticsController;
