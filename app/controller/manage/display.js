const Controller = require('egg').Controller;

// 大屏数据展示
class DisplayController extends Controller {

  // 获取科研外部协作列表
  async researchData() {
    const { ctx } = this;
    try {
      const {
        page = 1,
        limit = 10,
      } = ctx.query;
      const query = {
        page,
        limit,
      };
      const list = await ctx.curl(
        `${this.config.iServiceKyxzHost}/project/find`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 就诊预约-各区域就诊预约人次
  async appointmentByArea() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/appointment/byArea`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 就诊预约 - 各职业病就诊预约人次
  async appointmentByDiseaseType() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/appointment/byDiseaseType`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 就诊预约- 各行业就诊预约人次
  async appointmentByIndustry() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/appointment/byIndustry`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 就诊预约- 各机构就诊预约人次
  async appointmentByStation() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/appointment/byStation`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 诊疗服务 - 各区域诊疗服务人次
  async treatmentInformationCountByArea() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/treatmentInformation/countByArea`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 诊疗服务 - 各职业病种类诊疗服务人次
  async treatmentInformationCountByDiseaseType() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/treatmentInformation/countByDiseaseType`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 诊疗服务 - 各行业诊疗服务人次
  async treatmentInformationCountByIndustry() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/treatmentInformation/countByIndustry`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 诊疗服务 - 各机构诊疗服务人次
  async treatmentInformationCountByStation() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/treatmentInformation/countByStation`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }


  // 职业病病人全程服务数据展示 - 用药服务 - 各地区平均用药费用
  async averagePriceByArea() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/averagePriceByArea`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 用药服务 - 各职业病种类平均用药费用
  async averagePriceByDiseaseType() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/averagePriceByDiseaseType`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 用药服务 - 各行业平均用药费用
  async averagePriceByIndustry() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/averagePriceByIndustry`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  //  职业病病人全程服务数据展示 - 用药服务 - 各机构平均用药费用
  async averagePriceByStation() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/averagePriceByStation`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 用药服务 - 各区域诊用药服务人次
  async medicationGuidanceCountByArea() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/countByArea`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 用药服务 - 各职业病种用药服务人次
  async medicationGuidanceCountByDiseaseType() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/countByDiseaseType`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 用药服务 - 各行业用药服务人次
  async medicationGuidanceCountByIndustry() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/countByIndustry`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 用药服务 - 各类药品用药情况
  async medicationGuidanceCountByMedicineType() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/countByMedicineType`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 用药服务 - 各机构用药服务人次
  async medicationGuidanceCountByStation() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/medicationGuidance/countByStation`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 随访记录 - 各区域职业病病人跟踪随访人次
  async followUpByArea() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/followUp/byArea`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 随访记录 - 各区域职业病病人跟踪随访人次
  async followUpByDiseaseType() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/followUp/byDiseaseType`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 随访记录 - 各行业职业病病人跟踪随访人次
  async followUpByIndustry() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/followUp/byIndustry`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 随访记录 - 职业病病人各跟踪随访阶段人数
  async followUpByStage() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/followUp/byStage`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 康复指导 - 各区域康复指导人次
  async recoveryInfoByArea() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/rehabGuidanceService/countByArea`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 康复指导 - 各职业病种类康复指导人次
  async recoveryInfoByDiseaseType() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/rehabGuidanceService/countByDiseaseType`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 康复指导 - 各行业康复指导人次
  async recoveryInfoByIndustry() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/rehabGuidanceService/countByIndustry`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病病人全程服务数据展示 - 康复指导 - 各机构康复指导人次
  async recoveryInfoByStation() {
    const { ctx } = this;
    const {
      administerArea = '',
    } = ctx.query;
    const query = {
      administerArea,
    };
    try {
      const list = await ctx.curl(
        `${this.config.iServiceBzHost}/datav/rehabGuidanceService/countByStation`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );


      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }


}

module.exports = DisplayController;
