const Controller = require('egg').Controller;
const _ = require('lodash');

class enterpriseHomeController extends Controller {
  async getChartData() {
    const { ctx, service } = this;
    // const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const { EnterpriseID } = ctx.request.body;
    // console.log(23456,ctx.request.body)
    const res = await service.adminUser.getChartData({ EnterpriseID });
    const sexCensus = await service.adminUser.getSexCensus({ EnterpriseID });
    const data = {
      res,
      sexCensus,
    };
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }

  async setStatistical() {
    const {
      ctx,
      service,
    } = this;
    const { EnterpriseID } = ctx.query;
    const res = await service.adminUser.setStatistical({
      EnterpriseID,
    });
    if (res) {
      ctx.helper.renderSuccess(ctx, {});
    }
  }

  async getBasicMessage() {
    const data = {};
    const {
      ctx,
      service,
    } = this;
    const { EnterpriseID } = ctx.query;
    const basicData = [ 'Employee', 'Roles', 'MillConstruction' ];
    const projectData = [ 'Ledger', 'Facility', 'Defendproducts', 'DiseasesOverhaul', 'SanitaryInspection', 'JobHealth', 'OnlineDeclaration', 'AnnualPlan', 'Propagate', 'WarnNotice' ];
    const promises = [];
    // const promises2 = [];
    basicData.forEach(item => {
      const data = service.adminUser.getData({
        collection: item,
        EnterpriseID,
        type: 'basicData',
      });
      promises.push(data);
    });
    projectData.forEach(item => {
      let data;
      if ([ 'Facility', 'Defendproducts', 'DiseasesOverhaul', 'SanitaryInspection', 'OnlineDeclaration' ].includes(item)) {
        data = service.adminUser.getData({
          collection: item,
          EnterpriseID,
          year: ctx.query.year,
          type: item,
        });
      } else {
        data = service.adminUser.getData({
          collection: item,
          EnterpriseID,
          year: ctx.query.year,
          type: 'projectData',
        });
      }
      promises.push(data);
    });
    // console.log(promises);
    const basicMessage = [];
    const allDataName = [ ...basicData, ...projectData ];
    await Promise.allSettled(promises).then(async doc => {
      doc.forEach((item, i) => {
        basicMessage.push({
          name: allDataName[i],
          complete: item.status === 'fulfilled' && item.value > 0,
        });
      });
    });

    // console.log(basicMessage);
    data.basicMessage = basicMessage;

    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }

  async getMainData() {
    const {
      ctx,
      service,
    } = this;
    const { EnterpriseID } = ctx.query;
    const personNum = await service.adminUser.getPersonNum({
      EnterpriseID,
    });
    const sexCensus = await service.adminUser.getSexCensus({
      EnterpriseID,
    });
    const healthHeaders = await service.adminUser.getHealthHeader({
      EnterpriseID,
    });
    const healthData = await service.adminUser.getHealthData({
      EnterpriseID,
    });
    const diseaseNum = await service.adminUser.getDiseaseNum({
      EnterpriseID,
    });

    const factors = await service.adminUser.getHarmCheck({
      EnterpriseID,
    });

    const data = {
      sexCensus,
      healthHeaders,
      healthData,
      personNum,
      diseaseNum,
      factors,
    };
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }

  async getAssessmentInfo() {

    try {

      const { ctx } = this;
      const { EnterpriseID } = ctx.query || '';

      // const model = ctx.model.Adminorg;
      const model = ctx.model.MillConstruction;
      const payload = {
        isPaging: '0',
      };

      const query = {
        EnterpriseID,
      };

      const params = {
        query,

        sort: {
          createTime: -1,
        },

      };


      const millBack = await ctx.service.general._unionQuery(model, payload, params);
      console.log(ctx.model.HazardFactors);
      const hazarBack = await ctx.service.general._unionQuery(ctx.model.HazardFactors, { pageSize: 500 }, {
        query: { toxic: 1 },
      });
      const companyTestDataBack = await ctx.service.adminorg.item(ctx, {
        query: { _id: EnterpriseID },
        files: {
          PID: 0,
          code: 0,
          regAdd: 0,
          workAdd: 0,
          corp: 0,
          industryCategory: 0,
          licensePic: 0,
          adminUserId: 0,
          createTime: 0,
          updateTime: 0,
          message: 0,
          isactive: 0,
          _id: 0,
          area: 0,
          districtRegAdd: 0,
          districtWorkAdd: 0,
          img: 0,
          introduce: 0,
          level: 0,
          money: 0,
          type: 0,
          workAddress: 0,
          id: 0,
        },
      });
      const dust = companyTestDataBack.dust;
      const chemical = companyTestDataBack.chemical;
      const physical = companyTestDataBack.physical;
      const radiation = companyTestDataBack.radiation;
      const biological = companyTestDataBack.biological;


      let harmFactors = [];
      const millCons = millBack.docs;
      const harmWorkplaces = [];
      millCons.forEach(item => {
        if (item.children && item.children.length > 0) {
          const millChildren = item.children;
          millChildren.forEach(item => {

            if (item.harmFactors && item.harmFactors.length > 0) {
              harmWorkplaces.push(item);
              harmFactors.push(item.harmFactors);
            }

            if (item.children && item.children.length > 0) {
              const millChildren2 = item.children;
              millChildren2.forEach(item => {

                if (item.harmFactors && item.harmFactors.length > 0) {
                  harmWorkplaces.push(item);
                  harmFactors.push(item.harmFactors);
                }

              });
            }
          });
        }
      });


      harmFactors = _.flattenDepth(harmFactors, 1);

      harmFactors.forEach((item, i) => {
        harmFactors[i] = harmFactors[i][1];
      });
      harmFactors = _.uniq(harmFactors);

      const highHarm = [];


      _.forEach(hazarBack.docs, function(item) {
        if (_.includes(harmFactors, item.name)) {
          highHarm.push(item);
        }
      });


      // console.log('高毒因素：%s ', highHarm);
      let isHighHarm = false;
      if (highHarm && highHarm.length > 0) {
        isHighHarm = true;
      }

      let harmPeopleCounts = 0;
      let employeesIds = [];
      let highEmployeesIds = [];

      harmWorkplaces.forEach(item => {
        if (item.children && item.children.length > 0) {

          harmPeopleCounts = harmPeopleCounts + item.children.length;
          let tempHarms = item.harmFactors;
          tempHarms = _.flattenDepth(tempHarms, 1);


          let isHighharmTemp = false;

          tempHarms.forEach(item => {
            const highharmTemp = _.find(highHarm, { name: item });

            if (!_.isEmpty(highharmTemp)) {
              isHighharmTemp = true;

            }
          });
          item.children.forEach(person => {
            employeesIds.push(person.employees);
          });
          if (isHighharmTemp) {
            item.children.forEach(person => {
              highEmployeesIds.push(person.employees);
            });
          }

          // console.log('该车间 %s 的危害因素为 %s ,接触人数为%s ，该车间高毒因素 %s', item.name, tempHarms.join('、'), item.children.length, isHighharmTemp);

        }
      });

      employeesIds = _.uniq(employeesIds);
      highEmployeesIds = _.uniq(highEmployeesIds);

      const harmEmployeesCounts = employeesIds.length;

      // console.log('按害总人次数为：%s ，实际接害人数为 %s 其中接触高毒危害因素的人数为 %s .', harmPeopleCounts, employeesIds.length, highEmployeesIds.length);

      // console.log(
      //   '读取超标数据：\n粉尘检测点数%s,超标点数：%s;\n化学检测点数%s,超标点数：%s;\n物理检测点数%s,超标点数：%s;\n放射检测点数%s,超标点数：%s;\n生物及其它检测点数%s,超标点数：%s;',
      //   dust.point, dust.exceed,
      //   chemical.point, chemical.exceed,
      //   physical.point, physical.exceed,
      //   radiation.point, radiation.exceed,
      //   biological.point, biological.exceed
      // );

      let isExceed = false;
      if ((dust.exceed > 0) || (chemical.exceed > 0) || (physical.exceed > 0) || (biological.exceed > 0) || (radiation.exceed > 0)) {
        isExceed = true;
      }


      let assessmentResult = 0;


      if (isHighHarm) {
        // 高危
        if (isExceed) {
          // 超标
          assessmentResult = 2;
        } else {
          // 不超标
          harmEmployeesCounts < 49 ? assessmentResult = 1 : assessmentResult = 2;
        }
      } else {
        // 非高危
        if (isExceed) {
          // 超标
          // 判断人数 0-49 人，中风险 ，50人以上，高风险
          harmEmployeesCounts < 49 ? assessmentResult = 1 : assessmentResult = 2;
        } else {
          // 不超标
          assessmentResult = 0;
        }
      }


      // console.log('企业风险等级：%s', assessmentResult);


      ctx.helper.renderSuccess(ctx, {
        data: {
          highHarm,
          allHarmFactors: harmFactors,
          harmPeopleCounts: employeesIds.length,
          highHarmCounts: highEmployeesIds.length,
          assessmentResult,
        },
      });

    } catch (err) {
      // throw err
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }

  }

  async hazardDeclaration() {
    const { ctx, service } = this;
    try {
      let { EnterpriseID, year } = ctx.query;
      if (year === undefined) {
        const myDate = new Date();
        year = String(myDate.getFullYear() - 4);
      }
      year = Number(year);
      const data = [];
      for (let index = 0; index < 5; year++, index++) {
        const selectYear = String(year);
        const temp = await service.adminUser.getData({
          collection: 'OnlineDeclarationFiles',
          EnterpriseID,
          year: selectYear,
          type: 'projectData',
        });
        data.push(temp);
      }

      ctx.helper.renderSuccess(ctx, {
        data,
      });
    } catch (error) {
      throw error;
    }
  }

}

module.exports = enterpriseHomeController;
