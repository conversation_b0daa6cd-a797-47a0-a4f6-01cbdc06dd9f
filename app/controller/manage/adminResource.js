const Controller = require('egg').Controller;
const {
  siteFunc,
} = require('@utils');

class AdminResourceController extends Controller {

  async listByPower() {
    const {
      ctx,
      service,
    } = this;
    try {
      const payload = {
        isPaging: '0',
      };
      const manageCates = await service.adminResource.find(payload, {
        files: 'api _id label enable routePath parentId type icon comments',
      });
      const adminPower = await ctx.helper.getAdminPower(ctx);
      // 检索代号：#0001  ctx.session.basePath 后台根路径
      const currentCates = await siteFunc.renderNoPowerMenus(manageCates, adminPower, ctx.session.basePath);

      ctx.helper.renderSuccess(ctx, {
        data: currentCates,
      });

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // 获取监管端的权限和资源
  async superPowers() {
    const {
      ctx,
      // service,
    } = this;
    // const targetGroup = await service.adminGroup.item(ctx, {
    //   query: {
    //     $or: [
    //       { name: '政府端用户' },
    //       { name: '监管端用户' },
    //     ],
    //   },
    // });
    // const powerList = targetGroup ? targetGroup.power : [];
    const powerList = await ctx.helper.getAdminPower(ctx);
    const resource = await ctx.model.AdminResource.find(); // 所有资源
    const allResource = JSON.parse(JSON.stringify(resource));
    const superResource = allResource.filter(ele => powerList.includes(ele._id)); // 监管端所有接口资源
    const leverOne = allResource.filter(ele => ele.parentId === '0');
    for (let i = 0; i < leverOne.length; i++) {
      leverOne[i].children = allResource.filter(ele => ele.parentId === leverOne[i]._id);
      leverOne[i].children.forEach(level2 => {
        level2.children = superResource.filter(ele => ele.parentId === level2._id);
      });
      leverOne[i].children = leverOne[i].children.filter(ele => ele.children.length);
    }
    const result = leverOne.filter(ele => ele.children.length);
    ctx.helper.renderSuccess(ctx, {
      data: result,
    });
  }

}

module.exports = AdminResourceController;
