// 职业健康专家分类管理
const expertClassifierController = {
  // 获取专家分类列表
  async classifierList(ctx) {
    const { app } = this;
    try {
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/dictionary/expert-classifier`,
        {
          method: 'GET',
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '获取成功', '获取失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '数据获取失败',
        data: err,
      });
    }
  },

  // 新增专家分类
  async addClassifier(ctx) {
    const { app } = this;
    const fields = ctx.request.body || {};
    try {
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/dictionary/expert-classifier`,
        {
          method: 'POST',
          contentType: 'json',
          data: fields,
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '新增成功', '新增失败');

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '新增失败',
        data: err,
      });
    }
  },

  // 修改专家分类
  async updateClassifier(ctx) {
    const { app } = this;
    const fields = ctx.request.body || {};
    try {
      const { id, classification_name } = fields;
      if (!id) throw new Error('id不能为空');
      if (!classification_name) throw new Error('classification_name不能为空');

      const { data } = await ctx.curl(
        `${app.config.iService2Host}/dictionary/expert-classifier/${id}`,
        {
          method: 'PUT',
          contentType: 'json',
          data: fields,
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '修改成功', '修改失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '修改失败',
        data: err,
      });
    }
  },
  // 删除专家分类
  async deleteClassifier(ctx) {
    const { app } = this;
    const fields = ctx.request.body || {};
    try {
      const { id } = fields;
      if (!id) throw new Error('id不能为空');
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/dictionary/expert-classifier/${+id}`,
        {
          method: 'DELETE',
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '删除成功', '删除失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '删除失败',
        data: err,
      });
    }
  },
};

module.exports = expertClassifierController;
