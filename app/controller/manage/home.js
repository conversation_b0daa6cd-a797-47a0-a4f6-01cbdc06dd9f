const Controller = require('egg').Controller;
const moment = require('moment');
const jwt = require('jsonwebtoken');
const { ecdh } = require('@utils');


class HomeController extends Controller {
  // async dataHandle() {
  //   const { service } = this;
  //   await service.home.dataHandle();
  // }

  async preventAssessFindAll(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.home.preventAssessFindAll(params);
    ctx.body =
      res === 500
        ? {
          code: 500,
          message: '服务器出错',
        }
        : {
          code: 200,
          data: res,
        };
  }

  async getDashboardData() {
    const { ctx, service } = this;
    let {
      dataType,
      year,
      adcode = null,
      jurisdictionList = false,
    } = ctx.request.body;
    const EnterpriseID = ctx.session.superUserInfo
      ? ctx.session.superUserInfo._id
      : '';
    let districtRegAdd;
    if (adcode) {
      districtRegAdd = await service.home.getAdCodeName(adcode);
    } else {
      districtRegAdd = await service.home.getJurisdiction(EnterpriseID);
      adcode = await service.home.getAdCode(EnterpriseID);
    }
    const EnterpriseData = await service.home.getEnterpriseData({
      districtRegAdd,
      dataType,
      year,
      adcode,
      jurisdictionList,
    }); // 获取近三年该辖区数据
    // const serviceOrgData = await service.home.getserviceOrgData({ adcode, districtRegAdd, year });
    const data = {
      ...EnterpriseData,
      // ...serviceOrgData,
    };
    console.timeEnd('getDashboardData');
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }
  async getVisualizationData() {
    const { ctx, service } = this;
    let { adcode } = ctx.request.body;
    let currentYear = new Date().getFullYear();
    if ('year' in ctx.request.body) {
      currentYear = ctx.request.body.year;
    }
    adcode = '' + adcode;
    const EnterpriseID = ctx.session.superUserInfo
      ? ctx.session.superUserInfo._id
      : '';
    if (!adcode) {
      adcode = await service.home.getAdCode(EnterpriseID);
    }

    const visualizationData = await service.home.getVisualizationData({
      adcode,
      year: currentYear,
    });
    ctx.helper.renderSuccess(ctx, {
      data: visualizationData,
    });
  }

  async getTownScope() {
    const { ctx, service } = this;
    const { area_code } = ctx.query;
    const res = await service.townshipBoundary.getTownScope(area_code);
    ctx.helper.renderSuccess(ctx, {
      data: res,
    });
  }

  async getdDstrictsData() {
    const { ctx, service } = this;
    let { year, adcode = null, viewType } = ctx.request.body;
    console.time('getdDstrictsData');

    const EnterpriseID = ctx.session.superUserInfo
      ? ctx.session.superUserInfo._id
      : '';
    let districtRegAdd;
    if (adcode) {
      districtRegAdd = await service.home.getAdCodeName(adcode);
    } else {
      districtRegAdd = await service.home.getJurisdiction(EnterpriseID);
      adcode = await service.home.getAdCode(EnterpriseID);
    }

    console.log(4444, adcode, districtRegAdd);

    // const subAreas = await ctx.service.dashboard.getSubArea(adcode)
    // const districtsData = {}
    // for (let i = 0; i < subAreas.length; i++) {
    //   const subArea = subAreas[i]
    //   const subAdcod = subArea.area_code.slice(0, 6)
    //   const jobHealthData = await ctx.service.dashboard.countJobHealthData(subAdcod, 2021)
    //   const healthCheckData = await ctx.service.dashboard.countHealthCheckData(districtRegAdd, 2021)
    //   console.log(111111111, subArea, jobHealthData, healthCheckData)
    //   districtsData[subAdcod] = [
    //     subArea.lng,
    //     subArea.lat,
    //     subArea.name,
    //     jobHealthData.JobHealthCount,
    //     healthCheckData.
    //   ]
    // }
    // const res3 = await ctx.service.dashboard.countWarningData(["杭州市"], 2022)
    // console.log(92, res3)

    let districtsData = await service.home.getdDstrictsData({
      adcode,
      year,
      viewType,
      districtRegAdd,
    }); // 获取该辖区下的数据统计

    // 如果是四级行政单位，则只筛选出目标单位
    if (districtRegAdd.length === 4) {
      for (const key in districtsData) {
        if (districtsData[key] && districtsData[key][2] === districtRegAdd[3]) {
          districtsData = {
            key: districtsData[key],
          };
          break;
        }
      }
    }

    const data = {
      cityName: districtRegAdd,
      adcode,
      districtsData,
    };
    console.timeEnd('getdDstrictsData');
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }
  async selectArea() {
    const { ctx, service } = this;
    const { adcode, year, viewType } = ctx.request.body;
    const addcodeName = await service.home.selectArea(adcode);
    const EnterpriseData = await service.home.getEnterpriseData({ districtRegAdd: [ addcodeName ], year, adcode });
    const districtsData = await service.home.getdDstrictsData({ adcode, year, viewType });

    const serviceOrgData = await service.home.getserviceOrgData({ adcode, districtRegAdd: [ addcodeName ], year });

    const data = {
      cityName: [ addcodeName ],
      adcode,
      ...EnterpriseData,
      districtsData,
      ...serviceOrgData,
    };
    console.log(data, '区县的辖区机构');
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }
  async searchCompany() {
    const { ctx, service } = this;
    const EnterpriseID = ctx.session.superUserInfo
      ? ctx.session.superUserInfo._id
      : '';
    const companyName = ctx.request.body.companyName;
    const districtRegAdd = await service.home.getJurisdiction(EnterpriseID);
    let EnterpriseData;
    if (companyName) {
      EnterpriseData = await service.home.searchCompany(
        companyName,
        districtRegAdd[districtRegAdd.length - 1]
      );
    } else {
      EnterpriseData = await service.home.getEnterpriseData({
        districtRegAdd,
      });
    }
    const data = {
      ...EnterpriseData,
    };
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }
  async loadMoreCompany() {
    const { ctx, service } = this;
    const {
      page,
      recordComplete,
      year,
      adcode,
      listType = 'record',
    } = ctx.request.body;

    const EnterpriseID = ctx.session.superUserInfo
      ? ctx.session.superUserInfo._id
      : '';
    let districtRegAdd;
    if (adcode) {
      districtRegAdd = await service.home.getAdCodeName(adcode);
    } else {
      districtRegAdd = await service.home.getJurisdiction(EnterpriseID);
    }
    const EnterpriseData = await service.home.loadMoreCompany({
      districtRegAdd,
      year,
      currentIndex: page,
      recordComplete,
      listType,
    });
    const data = {
      ...EnterpriseData,
    };
    // console.log(data);
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }
  // 得到 职业病体检企业数量（率） 表 所需的数据
  async getPhysicalExaminationChartData() {
    const { ctx, service } = this;
    // const { page, recordComplete, year, adcode } = ctx.request.body;
    const EnterpriseID = ctx.session.superUserInfo
      ? ctx.session.superUserInfo._id
      : '';

    const districtRegAdd = await service.home.getJurisdiction(EnterpriseID);

    const districtRegAdd1 =
      ctx.request.body.cityName.length === 0
        ? districtRegAdd
        : ctx.request.body.cityName;
    const PhysicalExaminationData =
      await service.home.getPhysicalExaminationChartData(
        districtRegAdd1,
        ctx.request.body.selectYear
      );
    const data = {
      ...PhysicalExaminationData,
    };
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  }
  async getServiceProject() {
    const { ctx, service } = this;
    const { organization, currentIndex, pageSize } = ctx.request.body;
    const res = await service.home.getServiceProject({
      organization,
      currentIndex,
      pageSize,
    });
    ctx.helper.renderSuccess(ctx, {
      data: res,
    });
  }
  // async refreshData() {
  //   const { ctx, service } = this;
  //   // 先删表
  //   await ctx.model.StatisticalTable.remove();
  //   // 再生成表
  //   const res = await ctx.model.Adminorg.find({});
  //   res.forEach(async item => {
  //     await service.home.setStatistical({
  //       EnterpriseID: item._id,
  //     });
  //   });
  //   ctx.helper.renderSuccess(ctx, {});
  // }

  // 首页获取未读通知
  async getUnreadMessages() {
    const { ctx, service } = this;
    try {
      const { pageSize, current, searchkey } = ctx.query;
      // console.log(1231, ctx.session.superUserInfo._id,ctx.session.superUserInfo.group);
      const noReadMessageList = await service.messageNotification.getMessage(
        ctx.session.superUserInfo._id,
        ctx.session.superUserInfo.group,
        0,
        pageSize || 10,
        current || 1,
        searchkey
      );

      ctx.helper.renderSuccess(ctx, {
        data: noReadMessageList,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 获取已读通知
  async getHasReadMessages() {
    const { ctx, service } = this;
    try {
      const { pageSize, current, searchkey } = ctx.query;
      const hasReadMessagesList = await service.messageNotification.getMessage(
        ctx.session.superUserInfo._id,
        ctx.session.superUserInfo.group,
        1,
        pageSize || 10,
        current || 1,
        searchkey
      );
      ctx.helper.renderSuccess(ctx, {
        data: hasReadMessagesList,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 获取当前单位的系统信息
  async mySystemMessage() {
    const { ctx, config } = this;
    const { isRead, pageInfo } = ctx.request.body;
    const _id = ctx.session.superUserInfo._id;
    if (!_id) {
      ctx.helper.renderFail(ctx, {
        message: '请先登录',
      });
      return;
    }
    const query = {
      state: 1,
      sendWay: { $in: [ 'systemMessage', 'both' ] },
    };
    if (isRead) {
      query.reader = { $elemMatch: { isRead: +isRead, readerID: _id } };
    } else {
      query.reader = { $elemMatch: { readerID: _id } };
    }
    const data = await ctx.model.MessageNotification.find(query, {
      reader: false,
      state: false,
    })
      .sort({ date: -1 })
      .skip((pageInfo.current - 1) * pageInfo.pageSize)
      .limit(pageInfo.pageSize)
      .populate('authorID', 'cname landline'); // 目前只有监管端发送消息
    pageInfo.total = await ctx.model.MessageNotification.count(query); // 总数
    // 处理返回的数据 文件 时间格式等
    const upload_http_path = config.upload_http_path;
    const list = data.map(ele => {
      const newEle = JSON.parse(JSON.stringify(ele));
      if (ele.files) {
        newEle.files = ele.files.map(fileName => ({
          url:
            fileName.startsWith('体检信息') && fileName.endsWith('.xlsx')
              ? `/static${upload_http_path}/messageNotification/${ele.authorID._id}/${fileName}`
              : `/static${upload_http_path}/messageNotification/${ele._id}/${fileName}`,
          name: fileName,
        }));
      }
      newEle.date = moment(ele.date).format('YYYY-MM-DD HH:mm');
      return newEle;
    });
    ctx.helper.renderSuccess(ctx, {
      data: {
        pageInfo,
        docs: list,
      },
      message: '数据获取成功',
    });
  }

  // 标记已读消息
  async readMessage() {
    const { ctx, service } = this;
    try {
      const {
        ID,
      } = ctx.request.body;
      ID.forEach(async function(item) {
        const message = await service.messageNotification.item(ctx, {
          query: {
            _id: item,
          },
        });
        const index = message.reader.findIndex(function(value) {
          return (
            value.readerID === ctx.session.superUserInfo._id &&
            value.readerGroup === ctx.session.superUserInfo.group &&
            value.isRead === 0
          );
        });
        if (index < 0) {
          ctx.helper.renderFail(ctx, {
            message: '数据库没有这条数据',
          });
          return;
        }
        message.reader[index].isRead = 1;
        const res = await service.messageNotification.update(
          ctx,
          item,
          message
        );
        if (!res) {
          ctx.helper.renderFail(ctx, {
            message: '数据库更新失败',
          });
        }
      });
      ctx.helper.renderSuccess(ctx, {
        data: 'OK',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async findSupervision() {
    const { ctx } = this;
    try {
      const regAdd = ctx.session.superUserInfo.regAdd;
      const superUserId = ctx.session.superUserInfo._id;
      // console.log('superUserId---------------', superUserId);
      const res = await ctx.model.Adminorg.aggregate([
        { $match: { 'workAddress.districts': { $all: regAdd } } },
        {
          $lookup: {
            from: 'supervision',
            localField: '_id',
            foreignField: 'companyId',
            as: 'supervision',
          },
        },
        { $match: { 'supervision.supervisionId': superUserId } },
      ]);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async warningEvent() {
    const { ctx, service } = this;
    try {
      const res = await service.home.warningEvent(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async harmTotal() {
    const { ctx } = this;
    const data = ctx.query;
    try {
      let regAdd = ctx.session.superUserInfo.regAdd;
      if (data && data.name) {
        regAdd = JSON.parse(data.name);
      }
      const res = await ctx.model.Adminorg.aggregate([
        { $match: { 'workAddress.districts': { $all: regAdd } } },
        { $project: { harmStatistics: 1 } },
      ]);
      let sum = 0;
      for (let i = 0; i < res.length; i++) {
        const every = res[i].harmStatistics || [];
        if (every.length > 0) {
          // for (let j = 0; j < every.length; j++) {
          //   sum += every[every.length - 1].count[0].value;
          // }
          sum += every[every.length - 1].count[0].value;
        }
      }
      // console.log(sum, 'sum----');
      ctx.helper.renderSuccess(ctx, {
        data: sum,
      });
    } catch (error) {
      ctx.auditLog('大屏接害人数', `${error}`, 'error');
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取杭州首页数据 xxn add
  async newDashboardData() {
    const { ctx } = this;
    let { adname, adcode, year } = ctx.request.body; // adcode:330100, adname: ["杭州市"]
    const { superUserInfo } = ctx.session;
    if (!adcode) {
      // 没有传adcode，就是默认当前监管区域
      adname = superUserInfo.regAdd;
      adcode = await ctx.service.home.getAdCode(superUserInfo._id);
    } else {
      // 根据adcode获取adname
      const ad = await ctx.service.dashboard.getCurArea(adcode);
      if (ad && ad.name) adname = [ ad.name ];
    }
    if (!adcode) {
      ctx.helper.renderFail(ctx, {
        message: 'adcode查不到',
      });
      return;
    }
    if (typeof adname !== 'object') {
      ctx.helper.renderFail(ctx, {
        message: 'adname必须为数组',
      });
      return;
    }
    if (!year) year = new Date().getFullYear();
    // 获取预警统计数据
    const warningStatistics = await ctx.service.warning.getStatistics(
      year,
      adname
    );
    // 查询statistics表中是否已经有数据了，有就直接返回
    const queryCode = adcode.length === 12 ? adcode.slice(0, 6) : adcode;
    const statisticsData = await ctx.model.Statistics.findOne(
      { 'query.adcode': queryCode, 'query.year': +year },
      { warningData: false }
    );
    if (statisticsData) {
      const updateTime = new Date(statisticsData.updateTime).getTime();
      const difTime = 10 * 60000; // 10min内
      if (
        new Date().getTime() - updateTime < difTime ||
        parseInt(year) !== new Date().getFullYear()
      ) {
        const resData = JSON.parse(JSON.stringify(statisticsData));
        resData.warningStatistics = warningStatistics;
        ctx.helper.renderSuccess(ctx, {
          data: resData,
          message: '统计数据获取成功',
        });
        return;
      }
      ctx.auditLog('杭州大屏首页数据', '统计数据已过期,updateTime:' + statisticsData.updateTime, 'error');
    }

    // // 1、获取检测项目数据
    // const jobHealthData = await ctx.service.dashboard.countJobHealthData(adcode, year);
    // // 2、获取体检数据和诊断数据
    // const healthCheckData = await ctx.service.dashboard.countHealthCheckData(adname, year);
    // // 3、获取预警数据(已解除和已撤销的不展示)，按workAddress来细分子区域下的预警可能不准，因为以前很多预警是没有workAddress字段的，也已经无法确定了
    // const warningData = await ctx.service.dashboard.countWarningData(adname, year);
    let jobHealthData = {},
      healthCheckData = {},
      warningData = {};
    const res = await Promise.all([
      ctx.service.dashboard.countJobHealthData(adcode, year),
      ctx.service.dashboard.countHealthCheckData(adname, year, 10),
      ctx.service.dashboard.countWarningData(adname, year) ]
    );
    if (res && res.length === 3) {
      for (let i = 0; i < 3; i++) {
        if (res[i].JobHealthList) {
          jobHealthData = res[i];
        } else if (res[i].HealthcheckList) {
          healthCheckData = res[i];
        } else {
          warningData = res[i];
        }
      }
    } else {
      ctx.helper.renderFail(ctx, {
        message: '数据获取失败',
        data: res,
      });
      return;
    }
    // 4、计算中间地图上的区域数据
    const districtsData = {}, // 区域数据
      jobHealthList = jobHealthData.JobHealthList,
      HealthcheckList = healthCheckData.HealthcheckList,
      odiseaseList = healthCheckData.odiseaseList,
      warningList = warningData.warningList;
    const curArea = await ctx.service.dashboard.getCurArea(adcode); // 当前区域
    if (curArea.level === '2' && superUserInfo.regAdd.includes('杭州市')) {
      // 杭州的数据到了区县级别就不获取子数据了
      const area_code = curArea.area_code;
      const warning1Num = warningList.filter(ele => ele.type === 1).length;
      districtsData[area_code] = [ curArea.lng, curArea.lat, curArea.name, jobHealthList.length, HealthcheckList.length, odiseaseList.length, warning1Num, warningList.length - warning1Num ]; // 后五个数分别对应检测数量，体检数量，诊断人次，检测预警，体检预警
    } else {
      // 计算中间地图上的子区域数据
      const subArea = await ctx.service.dashboard.getSubArea(adcode); // 所有的子区域
      subArea.forEach(ele => {
        const area_code = ele.area_code,
          area_name = ele.name;
        const jobHealthNum = jobHealthList.filter(ele =>
          ele.workPlaces.some(addr => addr.workAdd.includes(area_code))
        ).length;
        const healthCheckNum = HealthcheckList.filter(ele =>
          ele.workAddress.some(addr => addr.districts.includes(area_name))
        ).length;
        const odiseaseNum = odiseaseList.filter(ele => {
          const Enterprise = ele.adminOrgClient.length
            ? ele.adminOrgClient[0]
            : null;
          if (Enterprise) {
            // console.log(888888888, Enterprise.workAddress);
            return Enterprise.workAddress.some(addr =>
              addr.districts.includes(area_name)
            );
          }
          return false;
        }).length;
        const warning1Num = warningList.filter(ele => ele.type === 1 && ele.workAddress.some(addr => addr.includes(area_name))).length;
        const warning2Num = warningList.filter(ele => ele.type === 2 && ele.workAddress.some(addr => addr.includes(area_name))).length;
        districtsData[area_code] = [ ele.lng, ele.lat, area_name, jobHealthNum, healthCheckNum, odiseaseNum, warning1Num, warning2Num ]; // 后五个数分别对应检测数量，体检数量，诊断人次，检测预警，体检预警
      });
    }

    // 返回数据
    delete jobHealthData.JobHealthList;
    delete healthCheckData.HealthcheckList;
    delete healthCheckData.odiseaseList;
    adcode = adcode.length === 12 ? adcode.substring(0, 6) : adcode;
    const result = {
      query: { adname, adcode, year }, // 当前的查询条件
      jobHealthData,
      healthCheckData,
      // warningData,
      districtsData, // 子区域的统计数据
      warningStatistics,
    };
    // console.log(66666666, result);
    ctx.helper.renderSuccess(ctx, {
      data: result,
      message: '数据获取成功',
    });
    return result;
  }

  // 获取wkzwy大屏数据
  async getWkzwyDashboardData() {
    const { ctx } = this;
    let { adname, adcode, year } = ctx.request.body; // adcode:330300, adname: ["温州市"]
    const { superUserInfo } = ctx.session;

    if (!adcode) {
      // 没有传adcode，就是默认当前监管区域
      adname = superUserInfo.regAdd;
      adcode = await ctx.service.home.getAdCode(superUserInfo._id);
    } else {
      // 根据adcode获取adname
      const ad = await ctx.service.dashboard.getCurArea(adcode);
      if (ad && ad.name) adname = [ ad.name ];
    }
    if (!adcode) {
      ctx.helper.renderFail(ctx, {
        message: 'adcode查不到',
      });
      return;
    }
    if (typeof adname !== 'object') {
      ctx.helper.renderFail(ctx, {
        message: 'adname必须为数组',
      });
      return;
    }
    if (!year) year = new Date().getFullYear();

    const healthCheckData = await ctx.service.wkzwyDashboard.getHealthCheckData(
      adname,
      year
    );

    // 地图的区域数据
    const districtsData = {
      // 企业数 预约次数 体检批次 体检人次
    };
    const curArea = await ctx.service.dashboard.getCurArea(adcode); // 当前区域

    // TODO 当前地区所有的机构
    const PhysicalExamOrgList = await ctx.service.wkzwyDashboard.getPhysicalExamOrgList(adname);
    // 当年所有的预约
    const HealthCheckAppointmentList = await ctx.service.wkzwyDashboard.getHealthCheckAppointmentList(year);

    // console.log(55555, PhysicalExamOrgList);
    // console.log(66666, HealthCheckAppointmentList);
    // console.log(123456789, HealthCheckAppointmentList.filter(ele => PhysicalExamOrgList.map(item => item._id).includes(ele.physicalExamOrgId)));

    //    if (curArea.level === '2' && superUserInfo.regAdd.includes('福州市')) {
    if (curArea.level === '2') {
      const area_code = curArea.area_code;
      // 预约次数
      let ReservationNum = 0;
      if (PhysicalExamOrgList.length > 0) {
        // 筛选出当前区域的预约
        const ids = PhysicalExamOrgList.map(item => item._id);
        const areReservationData = HealthCheckAppointmentList.filter(ele => ids.includes(ele.physicalExamOrgId));
        ReservationNum = areReservationData.length;
      }

      districtsData[area_code] = [
        curArea.lng,
        curArea.lat,
        curArea.name,
        healthCheckData.enterpriseNum,
        ReservationNum,
        healthCheckData.HealthcheckList.length || 0,
        healthCheckData.statistics.actuallNum,
      ];
    } else {
      // 计算中间地图上的子区域数据
      const subArea = await ctx.service.dashboard.getSubArea(adcode); // 所有的子区域

      subArea.forEach(ele => {
        const area_code = ele.area_code;
        const area_name = ele.name;
        const areaHealthCheck = healthCheckData.HealthcheckList.filter(ele =>
          ele.workAddress.some(addr => addr.districts.includes(area_name))
        );
        const batchNum = areaHealthCheck.length;
        let enterpriseNum = 0;
        let actuallNum = 0;
        const enterpriseList = new Set();
        areaHealthCheck.forEach(item => {
          enterpriseList.add(item.EnterpriseID);
          actuallNum += item.actuallNum;
        });
        enterpriseNum = enterpriseList.size;

        // TODO
        // 预约次数
        let ReservationNum = 0;
        // 处于当前子区域的机构
        const areaphysicalExamOrg = PhysicalExamOrgList.filter(ele =>
          ele.regAddr.includes(area_name)
        );
        if (areaphysicalExamOrg.length > 0) {
          // 筛选出当前区域的预约
          const ids = areaphysicalExamOrg.map(item => item._id);
          const areReservationData = HealthCheckAppointmentList.filter(ele => ids.includes(ele.physicalExamOrgId));
          ReservationNum = areReservationData.length;
        }

        districtsData[area_code] = [
          ele.lng,
          ele.lat,
          area_name,
          enterpriseNum,
          ReservationNum,
          batchNum,
          actuallNum,
        ];
      });
    }

    // 返回数据
    adcode = adcode.length === 12 ? adcode.substring(0, 6) : adcode;
    const result = {
      query: { adname, adcode, year }, // 当前的查询条件
      districtsData,
      ...healthCheckData,
    };
    ctx.helper.renderSuccess(ctx, {
      data: result,
      message: '数据获取成功',
    });
    return result;
  }

  // 用人单位管理 wzq
  async jurisdictionList() {
    const { ctx } = this;
    let { adname, adcode, year } = ctx.request.body; // adcode:330100, adname: ["杭州市"]
    const { superUserInfo } = ctx.session;

    if (!adcode) {
      // 没有传adcode，就是默认当前监管区域
      adname = superUserInfo.regAdd;
      adcode = await ctx.service.home.getAdCode(superUserInfo._id);
    }
    if (!adcode) {
      ctx.helper.renderFail(ctx, {
        message: 'adcode查不到',
      });
      return;
    }
    if (typeof adname !== 'object') {
      ctx.helper.renderFail(ctx, {
        message: 'adname必须为数组',
      });
      return;
    }

    if (!year) year = new Date().getFullYear();
    console.log(**********, adname, adcode, year);
    // 1、获取检测项目数据
    const jobHealthData = await ctx.service.dashboard.countJobHealthData(
      adcode,
      year
    );
    console.log(99981231231, jobHealthData);
    const serviceOrgs = jobHealthData.serviceOrgs;
    const JobHealthList = jobHealthData.JobHealthList;
    // 2、获取体检数据和诊断数据
    const healthCheckData = await ctx.service.dashboard.countHealthCheckData(
      adname,
      year
    );
    // 3、获取预警数据(已解除和已撤销的不展示)，按workAddress来细分子区域下的预警可能不准，因为以前很多预警是没有workAddress字段的，也已经无法确定了
    // const warningData = await ctx.service.dashboard.countWarningData(adname, year);
    // 4、计算中间地图上的子区域数据
    const subArea = await ctx.service.dashboard.getSubArea(adcode); // 所有的子区域
    const districtsData = {}, // 子区域数据
      jobHealthList = jobHealthData.JobHealthList,
      HealthcheckList = healthCheckData.HealthcheckList,
      odiseaseList = healthCheckData.odiseaseList;
    // warningList = warningData.warningList;
    subArea.forEach(ele => {
      const area_code = ele.area_code,
        area_name = ele.name;
      const jobHealth = jobHealthList.filter(ele =>
        ele.workPlaces.some(addr => addr.workAdd.includes(area_code))
      ); // 该辖区检测项目
      const jobHealthNum = jobHealth.length; // 该辖区检测项目数
      const jobHealthAbnormalNum = jobHealth.filter(
        item => item.abnormal
      ).length; // 该辖区检测超标数
      const healthCheck = HealthcheckList.filter(ele =>
        ele.workAddress.some(addr => addr.districts.includes(area_name))
      ); // 该辖区体检项目
      const healthCheckNum = healthCheck.length;
      let forbid = 0; // 禁忌证
      let suspected = 0; // 疑似
      let healthCheckTotal = 0; // 疑似
      let re_examination = 0; // 复查
      healthCheck.forEach(item => {
        forbid += item.forbid;
        suspected += item.suspected;
        healthCheckTotal += item.actuallNum;
        re_examination += item.re_examination;
      });

      const odiseaseNum = odiseaseList.filter(ele => {
        const Enterprise = ele.adminOrgClient.length
          ? ele.adminOrgClient[0]
          : null;
        if (Enterprise) {
          return Enterprise.workAddress.some(addr =>
            addr.districts.includes(area_name)
          );
        }
        return false;
      }).length;
      // const warning1Num = warningList.filter(ele => ele.type === 1 && ele.workAddress.some(addr => addr.includes(area_name))).length;
      // const warning2Num = warningList.filter(ele => ele.type === 2 && ele.workAddress.some(addr => addr.includes(area_name))).length;

      districtsData[area_code] = [
        ele.lng,
        ele.lat,
        area_name, // 区域名称
        jobHealthNum, // 检测项目数
        jobHealthAbnormalNum, // 超标数
        healthCheckNum, // 体检数
        healthCheckTotal, // 体检总人数
        re_examination, // 复查人数
        suspected, // 疑似职业病
        odiseaseNum, // 职业病
        forbid, // 禁忌证
      ];
    });

    // 返回数据
    // delete jobHealthData.JobHealthList;
    delete healthCheckData.HealthcheckList;
    delete healthCheckData.odiseaseList;

    // 获取辖区所有机构
    const allServiceRes = await ctx.service.serviceOrg.getOrgs({
      regAddr: adname,
      limit: 1000,
    });
    const allService = allServiceRes.data.map(item => {
      let projectNum = 0;
      let abnormalCount = 0;
      let abnormalRate = 0;
      let abnormalCompany = 0;
      const filterRes = serviceOrgs.find(item2 => item2.name === item.name);
      if (filterRes) {
        projectNum = filterRes.projectNum ? filterRes.projectNum : 0;

        // 搜索超标项目数
        const abnormalList = JobHealthList.filter(
          list => list.name === item.name
        );
        abnormalCount = abnormalList.length;
        abnormalRate = isNaN((abnormalCount / projectNum) * 100)
          ? 0
          : (abnormalCount / projectNum) * 100;
        abnormalCompany = Array.from(
          new Set(abnormalList.map(list => list.EnterpriseID))
        ).length;
      }
      return {
        name: item.name,
        projectNum,
        abnormalCount,
        abnormalCompany,
        abnormalRate,
      };
    });

    // 获取体检机构
    const orgs1 = await ctx.service.physicalExaminationOrg.getOrgsByProject(
      adname
    );
    const orgs2 = await ctx.service.physicalExaminationOrg.getOrgsByRegAddr(
      adname,
      orgs1.map(ele => ele._id)
    );
    const allOrg = orgs1.concat(
      orgs2.map(ele => ({ _id: ele._id, count: 0 }))
    );
    const phyOrgRes = await ctx.service.physicalExaminationOrg.getDetailByIds(
      allOrg.map(ele => ele._id)
    );
    // console.log(*********, phyOrgRes)
    // 获取体检项目
    const phyProject = await ctx.service.home.getPhyProjects({
      regAddr: adname[adname.length - 1],
      year: year + '',
    });
    console.log(67676, phyProject);
    const PEOrgs = phyOrgRes.map(item => {
      let actuallNum = 0,
        suspected = 0,
        forbid = 0,
        re_examination = 0,
        otherDisease = 0,
        normal = 0,
        recheckProject = 0,
        recheckProjectActuallNum = 0,
        Enterprises = 0,
        projectCount = 0;
      const targteProject = phyProject.find(item2 => item2._id === item._id);
      if (targteProject) {
        actuallNum = targteProject.actuallNum;
        suspected = targteProject.suspected;
        forbid = targteProject.forbid;
        re_examination = targteProject.re_examination;
        otherDisease = targteProject.otherDisease;
        normal = targteProject.normal;
        recheckProject = targteProject.recheckProject;
        recheckProjectActuallNum = targteProject.recheckProjectActuallNum;
        Enterprises = targteProject.Enterprises;
        projectCount = targteProject.projectCount;
      }
      return {
        name: item.name,
        actuallNum,
        suspected,
        forbid,
        re_examination,
        otherDisease,
        normal,
        recheckProject,
        recheckProjectActuallNum,
        Enterprises,
        projectCount,
      };
    });

    const result = {
      healthCheckData,
      serviceOrgs: allService,
      PEOrgs,
      // warningData,
      districtsData, // 子区域的统计数据
      adcode,
      cityName: [ adname[adname.length - 1] ],
      adname,
      year,
    };
    ctx.helper.renderSuccess(ctx, {
      data: result,
      message: '数据获取成功',
    });
    return result;
  }

  async autoLogin(ctx) {
    const { EnterpriseID } = ctx.request.body;
    const origin = ctx.request.header.origin;
    const adminUser = await ctx.service.home.findUser(EnterpriseID);
    console.log(this.app.config.branch, 'this.app.config.branch');
    const newDomain = this.app.config.domainNames.enterprise;
    let domain = '127.0.0.1';
    if (this.app.config.branch === 'fz') {
      const data = await this.autoLoginFz(EnterpriseID);
      ctx.helper.renderSuccess(ctx, {
        data,
      });
      return;
    }
    if (origin.includes('.cn')) {
      if (this.app.config.branch === 'xhl') {
        domain = 'xhlzyws.cn';
      } else if (this.app.config.branch === 'hz') {
        domain = 'hzzyws.cn';
      } else if (this.app.config.branch === 'hf') {
        domain = 'hfzyws.cn';
      } else if (this.app.config.branch === 'yc') {
        domain = 'yc.zyws.cn';
      } else if (this.app.config.branch === 'ldq') {
        domain = 'ldqzyws.cn';
      } else {
        domain = 'zyws.cn';
      }
    } else if (origin.includes('.net')) {
      domain = 'zyws.net';
    }

    // console.log(123123, EnterpriseID, origin);
    const adminUserToken = jwt.sign(
      {
        _id: adminUser,
        EnterpriseID,
      },
      ctx.app.config.encrypt_key,
      {
        expiresIn: '30day',
      }
    );
    ctx.cookies.set(
      'admin_' + ctx.app.config.auth_cookie_zyjk_name,
      adminUserToken,
      {
        path: '/',
        maxAge: 1000 * 60 * 60 * 24 * 30,
        domain,
        signed: true,
        httpOnly: false,
      }
    ); // cookie 有效期30天
    ctx.helper.renderSuccess(ctx, {
      data: {
        EnterpriseID,
        header: ctx.request.header,
        origin,
        newDomain,
        token: adminUserToken,
        cookie: 'admin_' + ctx.app.config.auth_cookie_zyjk_name,
      },
    });
  }
  async autoLoginFz(EnterpriseID) {
    const { ctx, config } = this;
    const adminUser = await ctx.service.home.findUser(EnterpriseID);
    try {
      const { privateKey } = config.jgEcdhOptions;
      const { publicKey, sharedKey } = config.qyFzEcdhOptions;
      const _sharedKey =
        sharedKey || ecdh.generateSharedKey(privateKey, publicKey);
      const content = JSON.stringify({
        userId: adminUser,
        EnterpriseID,
        timestamp: Date.now(),
      });
      const token = ecdh.encrypt(content, _sharedKey);
      const enterprise = config.domainNames.enterprise;
      const encodedToken = encodeURIComponent(token);
      // ctx.redirect(`${enterprise}/api/acceptLogin?token=${encodedToken}`);
      return {
        EnterpriseID,
        token: encodedToken,
        newDomain: enterprise,
      };
    } catch (err) {
      ctx.auditLog(
        '从监管端自动跳转到企业端失败',
        `福州管理员upn: ${adminUser}`,
        'error'
      );
    }
  }
  async getPersonInfo(ctx) {
    const personInfo = ctx.session.superUserInfo;
    let res = '';
    let phoneNum = '';
    res = await ctx.model.SuperUser.findOne({
      userName: personInfo.userName,
    });
    if (res) {
      phoneNum = res.phoneNum;
    }
    if (!res) {
      res = await ctx.model.SuperUser.aggregate([
        {
          $match: { _id: personInfo._id },
        },
        {
          $unwind: '$members',
        },
        {
          $match: {
            'members.userName': personInfo.userName,
          },
        },
      ]);
      phoneNum = res[0].members.phoneNum;
    }
    ctx.helper.renderSuccess(ctx, {
      data: phoneNum,
      message: '数据获取成功',
    });
  }

  async getVideoReviewPage() {
    const { ctx } = this;
    try {
      // 获取传参
      const { meetingId } = ctx.params;
      if (!meetingId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少频道ID',
        });
        return;
      }
      const key = await ctx.service.warning.getReserveChannel(meetingId);
      await ctx.render('manage/videoPreview.html', {
        siteSeo: this.app.config.siteSeo,
        server_path: this.app.config.server_path,
        staticRootPath: this.app.config.static.prefix,
        data: JSON.stringify(key),
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getCloudSupervisionPage() {
    const { ctx } = this;
    try {
      // 获取传参
      const { meetingId } = ctx.params;
      if (!meetingId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少频道ID',
        });
        return;
      }
      const key = await ctx.service.helpOrg.getReserveChannel(meetingId);
      await ctx.render('manage/cloudSupervision.html', {
        siteSeo: this.app.config.siteSeo,
        server_path: this.app.config.server_path,
        staticRootPath: this.app.config.static.prefix,
        data: JSON.stringify(key),
      });
    } catch (error) {
      const { meetingId } = ctx.params;
      const res = await ctx.model.CloudSupervision.findOne({ meetingId });
      await ctx.render('manage/cloudNotFound.html', {
        siteSeo: this.app.config.siteSeo,
        server_path: this.app.config.server_path,
        staticRootPath: this.app.config.static.prefix,
        data: JSON.stringify(res),
      });
    }
  }
  // 点击问卷调查，获取和校验问卷系统的用户信息，并获取单点登录的token
  async checkWjUser() {
    const { ctx, config } = this;
    try {
      const adminUserInfo = ctx.session.superUserInfo;
      const org = await ctx.model.SuperUser.findOne({ _id: adminUserInfo._id });
      let adminUser = org.members.find(item => item.userName === adminUserInfo.userName);
      if (!adminUser) adminUser = org;
      const params = {
        id: adminUser._id,
        name: adminUser.name,
        phone: adminUser.phoneNum,
        gender: '',
        auth_account: adminUser.userName,
        org_id: org._id,
        org_name: org.cname,
        org_code: org.area_code,
        org_short_name: '',
      };
      const { data } = await ctx.curl(
        `${config.iService2Host}/wj`,
        {
          method: 'POST',
          dataType: 'json',
          data: params,
        }
      );
      if (data.code !== 200) {
        ctx.auditLog('问卷系统用户验证', `用户验证失败：${data.message}`, 'error');
        return ctx.helper.renderFail(ctx, {
          message: data.message || '用户验证失败',
          data: data.data,
        });
      }
      ctx.helper.renderSuccess(ctx, {
        data: data.data,
        message: data.message || '用户验证成功' });
    } catch (error) {
      ctx.auditLog('问卷系统用户验证错误', error.message, 'error');
      ctx.helper.renderFail(ctx, {
        message: error.message,
      });
    }
  }
}

module.exports = HomeController;
