const Controller = require('egg').Controller;
const axios = require('axios');
// const { decrypt } = require('../../utils/clientCrypto');
// const {
//   _list,
//   _item,
//   _count,
//   _create,
//   _update,
//   _removes,
//   _safeDelete,
// } = require('../../service/general');

class commonApiController extends Controller {
  // 解码加密字段
  async decryptField() {
    const { ctx } = this;
    const { combinedText, _id, field, modelName } = ctx.query;
    if (typeof combinedText !== 'string' || !combinedText.trim()) {
      ctx.helper.renderCustom(ctx, {
        status: 400,
        message: '缺少参数: combinedText',
      });
    }
    const model = ctx.model[modelName];
    const user = await model.findOne({ _id });
    // const user = await ctx.model.modelName.findOne({ _id });
    let decryptedData;
    const log = {
      modelName,
      _id,
      field,
      combinedText,
    };
    try {
      decryptedData = await user.decryptField(field);
      const data = {
        errCode: 0,
        message: '解密成功',
        input: combinedText,
        result: decryptedData,
      };
      log.status = '成功';
      await this.decryptDataLog(log);
      return ctx.helper.renderSuccess(ctx, {
        message: '解码成功',
        data,
      });
    } catch (error) {
      ctx.auditLog('解码失败', error, 'error');
      log.status = '失败';
      await this.decryptDataLog(log);
      return ctx.helper.renderCustom(ctx, {
        status: 500,
        message: error.message,
      });
    }
    // const res = decrypt(combinedText);
    // if (!res || res.errCode !== 0) {
    //   ctx.helper.renderCustom(ctx, {
    //     status: 500,
    //     message: res ? res.message : '解码失败',
    //   });
    // }
    // ctx.auditLog('解码加密字段', `${combinedText}:${res.result}`, 'info');
  }

  // 记录解密操作日志
  async decryptDataLog(log) {
    const { modelName, _id, field, combinedText, status } = log;
    const { ctx } = this;
    const user = ctx.session.superUserInfo;
    await ctx.service.operateLog.create(modelName, {
      optType: 'check',
      supplementaryNotes: `${user.name}查看了${_id}的${field}字段，加密数据为${combinedText}`,
    });
    await ctx.service.operateLog.create(modelName, {
      optType: 'decrypt',
      supplementaryNotes: `解码${combinedText}${status}`,
    });

  }

  // 测试接口
  async test() {
    const { ctx } = this;
    const { fzgmBaseUrl, fzgmKey } = ctx.app.config;
    // const { _id } = ctx.query;
    // const options = {
    //   returnOptions: {
    //     phoneNum: {
    //       returnPlaintext: true, // 返回明文密码
    //     },
    //     // IDcard: {
    //     //   returnPlaintext: true, // 返回明文密码
    //     // },
    //   },
    // };
    // const user = await ctx.model.AdminUser.findOne({ _id }).setOptions(options);
    // const user = await ctx.model.AdminUser.find({
    //   phoneNum: { $in: [ '8117', '1223' ] },
    // });
    // const user = await ctx.model.AdminUser.find({
    //   phoneNum: { $regex: '8117' },
    // }).setOptions(options);
    // const user = await ctx.model.AdminUser.findOne({
    //   phoneNum: /8117/,
    //   IDcard: { $regex: '1301' },
    // }).setOptions(options);
    // const user = await ctx.model.AdminUser.findOne({
    //   $or: [{ phoneNum: { $regex: '8117' } }, { IDcard: { $regex: '1301' } }],
    // }).setOptions(options);
    // const user = await ctx.model.AdminUser.aggregate([{
    //   $match: { IDcard: { $regex: '1301' }, phoneNum: { $in: [ '8117' ] } },
    // }]);
    // const res = await user.decryptField('phoneNum');
    // console.log(user[0].IDcardDecrypted, 999);
    // const params = {
    //   phoneNum: { $regex: '15577906903' },
    // };
    // const user = await _item(ctx, ctx.model.AdminUser, {
    //   files: {
    //     phoneNum: 1,
    //     IDcard: 1,
    //   }, query: params }, options);
    // ctx.body = user;

    try {
      // const response = await axios({
      //   method: 'get',
      //   url: `${fzgmBaseUrl}/apisix/plugin/jwt/sign`,
      //   params: {
      //     key: 'jq2i5nl95re1j2gn',
      //   },
      // }, {
      //   proxy: false,
      // });
      const response = await axios.get(
        `${fzgmBaseUrl}/apisix/plugin/jwt/sign?key=${fzgmKey}`
      );
      ctx.auditLog('福州请求token成功', response.data, 'info');
      const data = 'Fzcdc666.';
      const base64Data = Buffer.from(data).toString('base64');
      const encryptData = await axios({
        method: 'post',
        url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm3-hmac`,
        data: {
          data: base64Data,
          appid: fzgmKey,
          keyid: '',
        },
        headers: {
          Authorization: response.data,
        },
      });
      ctx.auditLog('福州hmac成功', encryptData, 'info');
      return ctx.helper.renderSuccess(ctx, {
        message: '请求成功',
        data: encryptData,
      });
    } catch (error) {
      ctx.auditLog('福州请求token失败', error, 'error');
      return ctx.helper.renderCustom(ctx, {
        status: 500,
        message: error,
      });
    }
  }
}

module.exports = commonApiController;
