const Controller = require('egg').Controller;
const {
  siteFunc,
} = require('@utils');
const fs = require('fs');
const path = require('path');
const _ = require('lodash');
const validator = require('validator');


class superUserController extends Controller {
  async logOutAction() {
    const ctx = this.ctx;
    ctx.auditLog('退出登录', '当前用户进行了退出登录操作。');
    const loginUserInfo = await this.ctx.helper.getSuperAdminInfo();

    const optUserId = loginUserInfo
      ? loginUserInfo._id
      : this.ctx.session.superUserInfo._id;
    ctx.service.operateLog.create('SuperUser', { optType: 'logout', supplementaryNotes: '用户退出登录', optUserId });
    ctx.session = null;
    ctx.cookies.set('admin_' + this.app.config.auth_cookie_name, null);
    ctx.cookies.set('admin_doracmsapi', null);
    ctx.helper.renderSuccess(ctx);
  }

  async getUserSession() {
    const {
      ctx,
      app,
      service,
    } = this;
    try {
      let noticeCounts = 0;
      if (!_.isEmpty(service.systemNotify)) {
        noticeCounts = await service.systemNotify.count({
          systemUser: ctx.session.superUserInfo._id,
          // 'systemUser': '4JiWCMhzg',
          isRead: false,
        });
      }

      const superUserInfo = await service.superUser.item(ctx, {
        query: {
          _id: ctx.session.superUserInfo._id,
          // _id: '4JiWCMhzg',
        },
        populate: [{
          path: 'group',
          select: 'power _id enable name',
        }],
        files: 'cname enable _id email userName logo name landline regAdd area_code phoneNum',
      });
      const curSuperUser = JSON.parse(JSON.stringify(superUserInfo));
      const LoginInfo = ctx.session.superUserInfo;
      let loginUserInfo = {};
      const _id = LoginInfo._id;
      const isManage = LoginInfo.isManage;
      if (isManage) {
        loginUserInfo = await ctx.model.SuperUser.findOne({ _id: LoginInfo._id });
      } else {
        loginUserInfo = await ctx.model.SuperUser.aggregate([
          { $match: { _id: LoginInfo._id } },
          {
            $unwind: '$members',
          },
          {
            $match: { 'members.userName': LoginInfo.userName },
          }]);
        if (loginUserInfo && loginUserInfo.length && loginUserInfo[0]._id === _id) {
          loginUserInfo = loginUserInfo[0].members;
        }
      }
      curSuperUser.isManage = ctx.session.superUserInfo.isManage;
      curSuperUser.name = ctx.session.superUserInfo.name;
      curSuperUser.userName = ctx.session.superUserInfo.userName;
      curSuperUser._name = loginUserInfo.name;
      curSuperUser.currentLoginId = loginUserInfo._id;
      curSuperUser._userName = loginUserInfo.userName;
      curSuperUser.phoneNum = curSuperUser.isManage ? superUserInfo.phoneNum : curSuperUser.userName;
      if (curSuperUser && !curSuperUser.logo.includes('defaultlogo') && curSuperUser.logo && !curSuperUser.logo.includes('static')) {
        curSuperUser.logo = '/static' + app.config.upload_http_path + curSuperUser.logo;
      }
      const redirected = ctx.session.redirected;
      const renderData = {
        redirected,
        noticeCounts,
        loginState: true,
        userInfo: curSuperUser,
        title: app.config.platformName || '职业健康数字化平台',
        branch: app.config.branch || 'master',
        logoShow: app.config.platformLogoShow,
      };
      ctx.helper.renderSuccess(ctx, {
        data: renderData,
      });

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
  }

  async dashboard() {
    const {
      ctx,
      service,
      config,
      app,
    } = this;
    if (ctx.query.token && ctx.request.header.referer) { // 用于第三方 单点登录 用户信息校验
      // 限制白名单中的第三方跳转过来的才行
      const requestOrigin = ctx.request.header.referer;
      const key = app.config.signInWhitelist.some(ele => requestOrigin.indexOf(ele) !== -1);
      ctx.auditLog('第三方跳转过来的', `ctx.request.header：${JSON.stringify(ctx.request.header)}/。`);
      if (!key) {
        ctx.redirect(`${app.config.admin_base_path}/login`);
        return;
      }
      // 解析token并存在cookies中
      ctx.cookies.set('admin_' + app.config.auth_cookie_name, ctx.query.token, {
        path: '/',
        maxAge: app.config.superUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      ctx.redirect(`${app.config.admin_base_path}/dashboard`);
      return;
    }
    const renderMap = [];
    const payload = {
      isPaging: '0',
    };
    const manageCates = await service.adminResource.find(payload, {
      files: 'api _id label enable routePath parentId type icon comments',
    });
    // 关于鉴权，此处查找登录用户的权限信息 权限为一对多时，可用这种稍加修改
    const adminPower = await ctx.helper.getAdminPower(ctx);
    // 得到登录用户最终拥有的导航栏数据
    // 检索代号：#0001  ctx.session.basePath 后台根路径
    const basePath = ctx.session.basePath;
    const currentCates = await siteFunc.renderNoPowerMenus(manageCates, adminPower, basePath, false);
    if (!_.isEmpty(currentCates)) {

      const powerPathMaps = [];
      // 过滤大类菜单的routePath
      for (const cateItem of currentCates) {
        if (cateItem.parentId !== 0 && cateItem.enable) {
          powerPathMaps.push(cateItem.routePath);
        }
      }
      // 追加navbar到第一位
      powerPathMaps.splice(0, 0, 'dashboard');
      powerPathMaps.splice(0, 0, 'navbar');

      let params = '';
      const isAutoUpdate = config.isAutoUpdate,
        isVersionUpdate = config.isVersionUpdate;
      for (const pathItem of powerPathMaps) {
        if (isVersionUpdate) {
          params = `?ver=CHINA${config.version}`;
        } else if (isAutoUpdate) {
          const date = new Date();
          params = `?ver=C${date.getSeconds()}HI${Math.random().toString(36).substr(2)}N${date.getMilliseconds()}A`;
        }
        // 注意：一定要将环境变量NODE_ENV设置为development，否则将无法渲染
        if (config.env === 'local') {
          // 读取本地文件获取调试端口号
          // 注意：backstage里的各Vue项目文件夹的名字一定要与数据库adminresources表的routePath一致，否则将无法获取该Vue项目
          // baseDir: E:\demo\temp\frame
          const admin_micro_path = path.join(config.baseDir, 'backstage');
          const modulePkgPath = `${admin_micro_path}/${pathItem}/package.json`;

          if (fs.existsSync(modulePkgPath)) {
            const modulePkg = require(modulePkgPath);
            const moduleDevInfo = modulePkg.scripts.serve;
            const modulePort = moduleDevInfo.split(' --port ')[1];

            //   /config/config.local.js/dev_modules
            if (config.dev_modules.indexOf(pathItem) >= 0) {
              // 要热开发模式下的Vue项目
              renderMap.push({
                name: pathItem,
                path: `${config.admin_root_path}:${modulePort}/app.js`,
              });
            } else {
              renderMap.push({
                name: pathItem,
                path: `${config.server_path + config.static.prefix}/${pathItem}/js/app.js${params}`,
              });
            }
          } else {
            // 警告：未读取到指定Vue项目文件夹，可能由于目标Vue项目文件夹名称与数据库adminresources表的routePath未保持一致，由于未知原因页面无法渲染
            console.log(`\r\n警告：未读取到指定Vue项目文件夹：${pathItem}，可能由于目标Vue项目文件夹名称与数据库adminresources表的routePath未保持一致`);
            renderMap.push({
              name: pathItem,
              // path: `${config.origin + '/cms/plugins' + config.static.prefix}/admin/${pathItem}/js/app.js`,
              path: `${config.origin + '/cms/plugins' + config.static.prefix}${basePath}/${pathItem}/js/app.js${params}`,
            });
          }

        } else {
          // 警告：环境变量NODE_ENV在非development模式下进入此
          // console.log('\r\n警告：环境变量NODE_ENV在非development模式下进入此，相关问题可看代码');
          // // 已安装的插件优先级最高，该框架暂时无用
          // let {
          //   plugins
          // } = this.app.getExtendApiList();

          // let pluginStr = `dora${pathItem.charAt(0).toUpperCase() + pathItem.slice(1)}`;

          // if (plugins.indexOf(pluginStr) >= 0 && config[pluginStr].adminUrl) {
          //   let adminUrlItem = config.admin_root_path + config[pluginStr].adminUrl;
          //   if (adminUrlItem instanceof Array) {
          //     for (const routerItem of adminUrlItem) {
          //       renderMap.push({
          //         name: routerItem.path,
          //         path: routerItem.url
          //       })
          //     }
          //   } else {
          //     renderMap.push({
          //       name: pathItem,
          //       path: adminUrlItem
          //     })
          //   }
          // } else {
          renderMap.push({
            name: pathItem,
            path: `${config.admin_root_path}/${pathItem}/js/app.js${params}`,
          });
          // }
        }
      }
    }
    ctx.auditLog('登录成功', '当前用户登录成功!', 'info');
    await ctx.render('manage/index.html', {
      renderMap,
      renderMapJson: JSON.stringify(renderMap),
      staticRootPath: config.static.prefix,
      siteSeo: this.app.config.siteSeo,
      adminBasePath: basePath,
      // appVersion: config.pkg.version,
      appVersion: config.version,
      appName: config.pkg.name,
    });
  }

  async getBasicSiteInfo() {
    const {
      ctx,
      service,
      // app,
    } = this;
    try {

      // const {
      //   plugins,
      // } = app.getExtendApiList();

      let superUserCount = 0,
        regUserCount = 0,
        contentCount = 0,
        messageCount = 0,
        messages = [],
        regUsers = [],
        loginLogs = [];

      superUserCount = await service.superUser.count({
        state: '1',
      });


      if (!_.isEmpty(ctx.service.user)) {
        regUserCount = await service.user.count({
          state: '1',
        });

        regUsers = await service.user.find({
          isPaging: '0',
          pageSize: 10,
        }, {
          files: {
            email: 0,
          },
        });
      }

      if (!_.isEmpty(service.content)) {
        contentCount = await service.content.count({
          state: '2',
        });
      }
      if (!_.isEmpty(service.message)) {
        messageCount = await service.message.count();
        messages = await service.message.find({
          isPaging: '0',
          pageSize: 8,
        }, {
          populate: [{
            path: 'contentId',
            select: 'stitle _id',
          }, {
            path: 'author',
            select: 'userName _id enable date logo',
          }, {
            path: 'replyAuthor',
            select: 'userName _id enable date logo',
          }, {
            path: 'adminAuthor',
            select: 'userName _id enable date logo',
          }, {
            path: 'adminReplyAuthor',
            select: 'userName _id enable date logo',
          }],
        });
      }

      const reKey = new RegExp(ctx.session.superUserInfo.userName, 'i');
      // console.log('sdsddsxxxxxxxxxxccccccccccc')
      // console.log(reKey)

      loginLogs = [];
      // TODO 作为插件需要优先判断是否存在
      if (!_.isEmpty(service.systemOptionLog)) {
        loginLogs = await service.systemOptionLog.find({
          isPaging: '0',
          pageSize: 1,
        }, {
          query: {
            type: 'login',
            logs: {
              $regex: reKey,
            },
          },
        });
      }


      // 权限标记  首页我的权限
      const fullResources = await service.adminResource.find({
        isPaging: '0',
      });
      const newResources = [];
      for (let i = 0; i < fullResources.length; i++) {
        const resourceObj = JSON.parse(JSON.stringify(fullResources[i]));
        if (resourceObj.type === '1' && !_.isEmpty(ctx.session.superUserInfo)) {
          const adminPower = await ctx.helper.getAdminPower(ctx);
          if (adminPower && adminPower.indexOf(resourceObj._id) > -1) {
            resourceObj.hasPower = true;
          } else {
            resourceObj.hasPower = false;
          }
          newResources.push(resourceObj);
        } else {
          newResources.push(resourceObj);
        }
      }

      const renderBasicInfo = {
        superUserCount,
        regUserCount,
        regUsers,
        contentCount,
        messageCount,
        messages,
        loginLogs,
        resources: newResources,
      };

      ctx.helper.renderSuccess(ctx, {
        data: renderBasicInfo,
      });
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
  }

  // 添加/编辑管理成员
  async addMember() {
    const { ctx } = this;
    const { phoneNum, countryCode, name, jobTitle, member_id, landline, roles } = ctx.request.body;
    let errMsg = '';
    const _id = ctx.session.superUserInfo._id;
    if (phoneNum) {
      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }
      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }
      // if (!messageCode || !validator.isNumeric((messageCode).toString()) || (messageCode).length !== 6) {
      //   errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
      // }
      const queryUserObj = {
        $or: [
          { phoneNum },
          { phoneNum: '0' + phoneNum },
          { members: { $elemMatch: {
            phoneNum,
          } } },
        ],
        countryCode,
      };

      if (member_id) {
        const oldUsers = await ctx.service.superUser.findAll(queryUserObj);
        if (oldUsers.length > 1 || (oldUsers.length === 1 && oldUsers[0]._id !== _id)) errMsg = '该手机号已被使用！';
      } else {
        const userCount = await ctx.service.superUser.count(queryUserObj);
        if (userCount > 0) errMsg = '该手机号已被使用！';
      }
      // const cacheKey = '_sendMessage_update_';
      // const params = countryCode + phoneNum;
      // const currentCode = ctx.helper.getCache(this.app.config.session_secret + cacheKey + params);
      // if (!currentCode || !validator.isNumeric((currentCode).toString()) || (currentCode).length !== 6) {
      //   errMsg = '对不起！手机号错误！';
      // }
      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }
      // if (currentCode !== messageCode) {
      //   ctx.helper.renderFail(ctx, {
      //     message: '对不起！验证码错误！看看是不是验证码写错了？',
      //   });
      //   return;
      // }
      if (member_id) {
        await ctx.service.superUser.editMember(_id, {
          _id: member_id,
          phoneNum,
          name,
          userName: phoneNum,
          jobTitle,
          landline,
          roles,
        });
      } else {
        await ctx.service.superUser.addMember(_id, {
          phoneNum,
          name,
          userName: phoneNum,
          // password: '123456',
          jobTitle,
          landline,
          roles,
        });
      }
      const res = await ctx.service.superUser.getMembers(_id);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res.members,
        message: '操作成功',
      });
    } else {
      errMsg = ctx.__('validate_error_params');
    }
  }
  // 获取管理成员们
  async getMembers() {
    const { ctx } = this;
    const _id = ctx.session.superUserInfo._id;
    const data = await ctx.service.superUser.getMembers(_id);
    const newData = JSON.parse(JSON.stringify(data));
    newData.isManage = ctx.session.superUserInfo.isManage;
    ctx.helper.renderSuccess(ctx, {
      status: 200,
      data: newData,
      message: '数据获取成功',
    });
  }
  // 删除管理成员
  async delMember() {
    const { ctx } = this;
    const _id = ctx.session.superUserInfo._id;
    const { member_id } = ctx.request.body;
    await ctx.service.superUser.delMember(_id, member_id);
    const data = await ctx.service.superUser.getMembers(_id);
    ctx.helper.renderSuccess(ctx, {
      status: 200,
      data: data.members,
      message: '数据删除成功',
    });
  }

  async update() {
    const { ctx } = this;
    const _id = ctx.session.superUserInfo._id;
    const query = ctx.request.body;
    await ctx.service.superUser.update(ctx, _id, query);
    const data = await ctx.service.superUser.findOne({ _id });
    ctx.helper.renderSuccess(ctx, {
      status: 200,
      data,
      message: '操作成功',
    });
  }

}

module.exports = superUserController;
