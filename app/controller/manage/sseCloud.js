const Controller = require('egg').Controller;
class SseCloudController extends Controller {
  async sse(ctx) {
    // let sseUrl = ctx.url.split('/');
    // const _id = sseUrl[sseUrl.length - 1];
    // const res = await ctx.model.CloudSupervision.findOne({_id});

    // let fileName = '';
    // if('instructions' in res) {
    //   fileName = res.instructions
    // }
    // Add console.log statement to check if SSE endpoint is being called
    ctx.res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      'Access-Control-Allow-Origin': '*',
    });

    ctx.respond = false;
    ctx.res.write('retry: 10000\n');
    ctx.res.write('event: connectTime\n');
    ctx.res.write(`id: ${+new Date()}\n`); // 消息 ID
    ctx.res.write('data: ' + new Date() + '\n\n');
    ctx.req.connection.addListener('close', function() {
      console.log('close ??????');
    });


    // console.log('file>>>>>>>>>>>>>>>>>>>>>>', fileName)
    // 监听错误
    // 1秒后向客户端发送消息 初始化成功
    setTimeout(() => {
      ctx.res.write('data: success\n\n');
    }, 1000);
    ctx.req.connection.addListener('error', function(error) {
      console.log('error ??????', error);
    });
    return;
  }

}

module.exports = SseCloudController;
