const Controller = require('egg').Controller;
// const multiparty = require('multiparty');
const path = require('path');
const fs = require('fs');
const sendToWormhole = require('stream-wormhole');
const shortid = require('shortid');
const {
  tools,
} = require('@utils');
const awaitWriteStream = require('await-stream-ready').write;
class CommentController extends Controller {
  // 批量获取课程
  async getCoursesList() {
    const {
      ctx,
    } = this;
    try {
      const current = ctx.query.current || 1;
      const pageSize = ctx.query.pageSize || 10;
      const searchkey = ctx.query.searchkey || '';
      const allowToOpenKey = ctx.query.allowToOpenKey;
      const completeKey = ctx.query.completeKey;
      const source = ctx.query.source || 'super';
      const query = { source };
      if (searchkey.length) {
        query.$or = [{
          name: {
            $regex: searchkey,
          },

        }];
      }
      if (allowToOpenKey.length) {
        query.allowToOpen = allowToOpenKey === 'true';
      }
      if (completeKey.length) {
        query.complete = completeKey === 'true';
      }
      const {
        list,
        count,
      } = await ctx.service.courses.getCoursesList(query, current, pageSize, 'manage');
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          count,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async getCourseOne() {
    const {
      ctx,
    } = this;
    try {
      const {
        _id,
      } = ctx.query;
      const course = await ctx.service.courses.getCourseOne(_id);
      const contentList = [];
      course.sort.sort(function(a, b) {
        return a.sequence - b.sequence;
      });
      for (let index = 0; index < course.sort.length; index++) {
        const element = course[course.sort[index].contentType].find(function(item) {
          return item._id === course.sort[index].ID;
        });
        let Video = void 0;
        switch (course.sort[index].contentType) {
          case 'videoInfos':
            if (element && element.VideoId) {
              Video = await ctx.helper.request_alivod('GetVideoInfo', {
                VideoId: element.VideoId,
              }, {});
            } else {
              // 有问题章节存在，捕捉错误
              ctx.logger.error(new Error(`捕捉到一个课程有错误章节\n${JSON.stringify(course)}\n${JSON.stringify(course.sort[index])}\n${JSON.stringify(course.videoInfos)}\n`));
            }
            contentList.push({
              contentType: course.sort[index].contentType,
              _id: course.sort[index]._id,
              ID: course.sort[index].ID,
              VideoId: element ? element.VideoId : '',
              classHours: element ? element.classHours : 1,
              author: element ? element.author : '',
              name: Video && Video.Video ? Video.Video.Title : '此视频出问题，请手动删除',
              cover: Video && Video.Video ? Video.Video.CoverURL : '',
              Description: Video && Video.Video ? Video.Video.Description : '',
              // classHours: element.classHours,
            });
            break;
          case 'documents':
            contentList.push({
              contentType: course.sort[index].contentType,
              _id: course.sort[index]._id,
              ID: course.sort[index].ID,
              htmlContent: element.htmlContent,
              name: element.name,
              cover: element.cover,
              Description: element.Description,
            });
            break;
          default:
            break;
        }
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          contentList,
          course,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async createCourse() {
    const {
      ctx,
      config,
    } = this;
    try {
      const parts = ctx.multipart({
        autoFields: true,
      });
      const stream = await parts();
      const suffixArray = stream.filename.split('.');
      const suffix = suffixArray[suffixArray.length - 1];
      const fileName = Math.random().toString(36).substr(2) + new Date().getTime() + '.' + suffix;
      const id = shortid.generate();
      const savePath = path.join(config.upload_courses_path, id);
      tools.makeEnterpriseDir(savePath);
      const writeStream = fs.createWriteStream(path.resolve(savePath, fileName));
      await awaitWriteStream(stream.pipe(writeStream));
      if (!Object.keys(parts.field).length) {
        await sendToWormhole(stream);
      }
      const newCourse = {
        _id: id,
        name: parts.field.name,
        explain: parts.field.explain,
        authorID: ctx.session.superUserInfo._id,
        superUserID: ctx.session.superUserInfo._id,
        createRange: ctx.session.superUserInfo.regAdd,
        credit: parts.field.credit,
        classHours: parts.field.classHours,
        classification: JSON.parse(parts.field.classification),
        labels: JSON.parse(parts.field.labels),
        allowToOpen: parts.field.allowToOpen === 'true',
        complete: parts.field.complete === 'true',
        cover: `/static${config.upload_courses_http_path}/${id}/${fileName}`,
        allowComment: parts.field.allowComment === 'true',
        questionBank: parts.field.questionBank,
        price: parts.field.price,
      };
      if (parts.field.complete === 'true') {
        newCourse.openTime = new Date();
      }
      const backData = await ctx.service.courses.createCourse(newCourse);
      ctx.helper.renderSuccess(ctx, {
        data: {
          backData,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async updateCourse() {
    const {
      ctx,
      config,
    } = this;
    try {
      const parts = ctx.multipart({
        autoFields: true,
      });
      const stream = await parts();
      if (!Object.keys(parts.field).length) {
        console.log(parts);
        return;
      }
      const $set = {
        name: parts.field.name,
        explain: parts.field.explain,
        classification: JSON.parse(parts.field.classification),
        labels: JSON.parse(parts.field.labels),
        allowToOpen: parts.field.allowToOpen === 'true',
        allowComment: parts.field.allowComment === 'true',
        updateTima: new Date(),
        credit: parts.field.credit,
        classHours: parts.field.classHours,
        questionBank: parts.field.questionBank,
        complete: parts.field.complete === 'true',
        price: parts.field.price,
        powerStatus: parts.field.powerStatus === 'true',
      };
      const course = await ctx.service.courses.getCourseOne(parts.field._id);
      // debugger;
      if (parts.field.complete === 'true' && course.complete === false) {
        $set.openTime = new Date();
      }

      if (stream && stream.filename) {
        try {
          fs.unlinkSync(process.cwd() + parts.field.cover.replace(/static/, 'app/public'));
        } catch (error) {
          ctx.logger.error(error);
          console.error(error);
        }
        const id = parts.field._id;
        // const oldFileName = parts.field.cover.split('/');
        const suffixArray = parts.field.cover.split('.');
        const suffix = suffixArray[suffixArray.length - 1];
        const fileName = Math.random().toString(36).substr(2) + new Date().getTime() + '.' + suffix;
        const savePath = path.join(config.upload_courses_path, id);
        tools.makeEnterpriseDir(savePath);
        const writeStream = fs.createWriteStream(path.resolve(savePath, fileName));
        await awaitWriteStream(stream.pipe(writeStream));
        $set.cover = `/static${config.upload_courses_http_path}/${id}/${fileName}`;
      }
      const backData = await ctx.service.courses.updateCourse(parts.field._id, {
        $set,
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          backData,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 删除
  async deleteCourse() {
    const {
      ctx,
    } = this;
    try {
      const {
        courseID,
      } = ctx.query;
      const adminPlanCount = await ctx.model.AdminTraining.countDocuments({
        coursesID: {
          $in: [ courseID ],
        },
      });
      const employeePlanCount = await ctx.model.EmployeesTrainingPlan.countDocuments({
        coursesID: {
          $in: [ courseID ],
        },
      });
      if (adminPlanCount + employeePlanCount) {
        ctx.helper.renderFail(ctx, {
          message: '该课程被引用，删除失败',
        });
        this.ctx.auditLog(`试图删除被引用的课程: ${courseID}`, `作者id${ctx.session.superUserInfo._id}`, 'info');
      } else {
        // 遍历章节内容，删除没有被引用的章节内容
        const course = await this.ctx.model.Courses.findOne({
          _id: courseID,
        });
        for (let index = 0; index < course.videoInfos.length; index++) {
          const coursesCount = await ctx.model.Courses.countDocuments({
            videoInfos: {
              $in: [ course.videoInfos[index] ],
            },
          });
          // console.log(11111111111111, coursesCount)
          if (coursesCount < 2) {
            const video = await ctx.model.VideoInfos.findOne({
              _id: course.videoInfos[index],
            });
            // await ctx.model.VideoInfos.deleteMany({
            //   _id: course.videoInfos[index],
            // });
            await ctx.service.db.deleteMany('VideoInfos', {
              _id: course.videoInfos[index],
            });
            if (video && video.VideoId) {
              await this.ctx.helper.request_alivod('DeleteVideo', {
                VideoIds: video.VideoId,
              }, {});
            }
          }
        }
        for (let index = 0; index < course.documents.length; index++) {
          const coursesCount = await ctx.model.Courses.countDocuments({
            documents: {
              $in: [ course.documents[index] ],
            },
          });
          if (coursesCount < 2) {
            // await ctx.model.TrainingDocument.deleteMany({
            //   _id: course.documents[index],
            // });
            await ctx.service.db.deleteMany('TrainingDocument', {
              _id: course.documents[index],
            });
          }
        }
        // 删除这个课程
        // await ctx.model.Courses.deleteMany({
        //   _id: courseID,
        // });
        await ctx.service.db.deleteMany('Courses', {
          _id: courseID,
        });
        ctx.helper.renderSuccess(ctx, {
          data: {
            message: 'OK',
          },
        });
      }

    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getAllVideos() {
    const {
      ctx,
    } = this;
    try {
      const list = await ctx.model.VideoInfos.find({ uploader: ctx.session.superUserInfo._id });
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }


  // 删除内容，先判断章节类型，再删除对应资源（视频/文档 and封面），最后删除数据库记录。
  async deleteContent() {
    const {
      ctx,
      // config,
    } = this;
    try {
      const {
        _id,
        id,
        ID,
        contentType,
      } = ctx.request.body;
      if (_id) {
        let course = await ctx.model.Courses.findOne({
          _id,
        });
        course = JSON.parse(JSON.stringify(course));
        let sortIndex = -1;
        for (let i = 0; i < course.sort.length; i++) {
          if (course.sort[i]._id === id) {
            sortIndex = i;
            break;
          }
        }
        if (sortIndex === -1) {
          ctx.helper.renderFail(ctx, {
            message: '该章节不存在',
          });
          return;
        }

        // // 该课程被培训引用的话就不给删
        // const adminPlanCount = await ctx.model.AdminTraining.countDocuments({
        //   coursesID: {
        //     $in: [_id],
        //   },
        // });
        // const employeePlanCount = await ctx.model.EmployeesTrainingPlan.countDocuments({
        //   coursesID: {
        //     $in: [_id],
        //   },
        // });
        // if (adminPlanCount + employeePlanCount) {
        //   ctx.helper.renderSuccess(ctx, {
        //     data: {
        //       message: 'not OK',
        //       count: adminPlanCount + employeePlanCount,
        //     },
        //   });
        //   return;
        // }

        const sort = course.sort[sortIndex];
        if (contentType !== sort.contentType) {
          ctx.helper.renderFail(ctx, {
            message: '章节类型不匹配',
          });
          return;
        }
        if (sort.contentType === 'videoInfos') {
          let index = -1;
          for (let i = 0; i < course.videoInfos.length; i++) {
            if (course.videoInfos[i] === ID) {
              index = i;
              break;
            }
          }
          if (index >= 0) {
            try {
              const video = await ctx.model.VideoInfos.findOne({
                _id: ID,
              });
              const coursesCount = await ctx.model.Courses.countDocuments({
                videoInfos: {
                  $in: [ ID ],
                },
              });
              if (coursesCount < 2) {
                // 没有被别的课程引用
                // await this.ctx.model.VideoInfos.deleteOne({
                //   _id: ID,
                // });
                await ctx.service.db.deleteOne('VideoInfos', {
                  _id: ID,
                });
                try {
                  await this.ctx.helper.request_alivod('DeleteVideo', {
                    VideoIds: video.VideoId,
                  }, {});
                } catch (error) {
                  this.ctx.auditLog('没有视频', '删除了错误的章节', 'info');
                  this.ctx.logger.error(error);
                  console.error(error);
                }
              }
              course.videoInfos.splice(index, 1);
              course.sort.splice(sortIndex, 1);
              // const backData = await ctx.model.Courses.updateOne({
              //   _id,
              // }, {
              //   $set: {
              //     sort: course.sort,
              //     videoInfos: course.videoInfos,
              //   },
              //   $inc: {
              //     classHours: (0 - Number(video.classHours)),
              //   },
              // });
              const backData = await ctx.service.db.updateOne('Courses', {
                _id,
              }, {
                $set: {
                  sort: course.sort,
                  videoInfos: course.videoInfos,
                },
                $inc: {
                  classHours: (0 - Number(video.classHours)),
                },
              });
              ctx.helper.renderSuccess(ctx, {
                data: {
                  // response,
                  backData,
                  message: 'OK',
                },
              });
            } catch (response) {
              console.error(response);
              ctx.helper.renderFail(ctx, {
                message: '视频删除失败',
              });
              return;
            }
          }
        } else if (sort.contentType === 'documents') {
          let index = -1;
          for (let i = 0; i < course.documents.length; i++) {
            if (course.documents[i] === ID) {
              index = i;
              break;
            }
          }
          if (index >= 0) {
            const doc = course.documents[index];
            try {
              // debugger;
              fs.unlinkSync(process.cwd() + doc.cover.replace(/static/, 'app/public'));
            } catch (error) {
              ctx.logger.error(error);
              console.error(error);
            }
            if (doc.documentsUrl && doc.documentsUrl.length) {
              for (let j = 0; j < doc.documentsUrl.length; j++) {
                try {
                  fs.unlinkSync(process.cwd() + doc.documentsUrl[j].replace(/static/, 'app/public'));
                } catch (error) {
                  ctx.logger.error(error);
                  console.error(error);
                }
              }
            }
            course.documents.splice(index, 1);
            course.sort.splice(sortIndex, 1);
            // const backData = await ctx.model.Courses.updateOne({
            //   _id,
            // }, {
            //   $set: {
            //     sort: course.sort,
            //     documents: course.documents,
            //   },
            // });
            const backData = await ctx.service.db.updateOne('Courses', {
              _id,
            }, {
              $set: {
                sort: course.sort,
                documents: course.documents,
              },
            });
            ctx.helper.renderSuccess(ctx, {
              data: {
                backData,
                message: 'OK',
              },
            });
          }
        } else {
          ctx.helper.renderFail(ctx, {
            message: '章节类型有误',
          });
          return;
        }
      }
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 在末尾插入内容
  async insertContent() {
    const {
      ctx,
    } = this;
    try {
      const {
        _id,
        type,
      } = ctx.request.body;
      let backData;
      if (type === 'videoInfos') {
        const {
          name,
          VideoId,
          Size,
          author,
          cover,
          video_id,
          classHours,
        } = ctx.request.body;
        ctx.auditLog('创建新的章节', `视频VideoId：${VideoId}\n课程id：${_id}\zn作者：${author}`, 'info');
        if (video_id && video_id.length) {
          const course = await ctx.model.Courses.findOne({
            _id,
          }, {
            sort: 1,
          });
          const video = await ctx.model.VideoInfos.findOne({
            _id: video_id,
          });
          // backData = await ctx.model.Courses.updateOne({
          //   _id,
          // }, {
          //   $push: {
          //     [type]: video_id,
          //     sort: {
          //       contentType: type,
          //       ID: video_id,
          //       sequence: course.sort.length + 1,
          //     },
          //   },
          //   $inc: {
          //     classHours: Number(video.classHours),
          //   },
          // });
          backData = await ctx.service.db.updateOne('Courses', {
            _id,
          }, {
            $push: {
              [type]: video_id,
              sort: {
                contentType: type,
                ID: video_id,
                sequence: course.sort.length + 1,
              },
            },
            $inc: {
              classHours: Number(video.classHours),
            },
          });
          console.log(33333, backData);
        } else {
          backData = await ctx.service.courses.insertContent(_id, type, {
            VideoId,
            Size,
            name,
            author,
            cover,
            classHours,
            uploader: ctx.session.superUserInfo._id,
          });
          ctx.auditLog('创建新的视频', `视频VideoId：${VideoId}\n课程id：${_id}\n`, 'info');
        }

      } else if (type === 'documents') {
        const {
          Description,
          htmlContent,
          name,
          cover,
          documentsUrl,
        } = ctx.request.body;
        backData = await ctx.service.courses.insertContent(_id, type, {
          Description,
          htmlContent,
          cover,
          name,
          documentsUrl,
          uploader: ctx.session.superUserInfo._id,
        });

      } else {
        ctx.helper.renderFail(ctx, {
          message: '类型不支持',
        });
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          backData,
          message: 'OK',
        },
      });
    } catch (error) {
      console.log(44444, error);
      ctx.auditLog('创建新章节失败', JSON.stringify(error), 'info');
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 重新排序内容
  async updateContent() {
    const {
      ctx,
    } = this;
    try {
      const {
        _id,
        id1,
        id2,
      } = ctx.request.body;
      const back = await ctx.service.courses.resortContent(_id, id1, id2);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          back,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async newSort() {
    const {
      ctx,
    } = this;
    try {
      const data = ctx.request.body;
      const back = await ctx.service.courses.newSort(data);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          back,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 更新章节内容
  async editContent() {
    const {
      ctx,
    } = this;
    try {
      const {
        contentType,
        name,
        Description,
        ID,
        author,
        classHours,
      } = ctx.request.body;
      if (contentType === 'videoInfos') {
        await ctx.helper.request_alivod('UpdateVideoInfo', {
          VideoId: ctx.request.body.VideoId,
          Title: name,
          Description,
        }, {});
        const video = await ctx.model.VideoInfos.findOne({
          VideoId: ctx.request.body.VideoId,
          _id: ID,
        });
        // await ctx.model.VideoInfos.updateOne({
        //   VideoId: ctx.request.body.VideoId,
        //   _id: ID,
        // }, {
        //   name,
        //   Description,
        //   author,
        //   classHours,
        // });
        await ctx.service.db.updateOne('VideoInfos', {
          VideoId: ctx.request.body.VideoId,
          _id: ID,
        }, {
          name,
          Description,
          author,
          classHours,
        });
        // console.log('qwqqwqwqwqwq',video)
        // console.log('111111111qwqwqwq', Number(classHours) - Number(video.classHours))

        // await ctx.model.Courses.update({
        //   videoInfos: {
        //     $in: [ ID ],
        //   },
        // }, {
        //   $inc: {
        //     classHours: Number(classHours) - Number(video.classHours),
        //   },
        // });
        await ctx.service.db.updateMany('Courses', {
          videoInfos: {
            $in: [ ID ],
          },
        }, {
          $inc: {
            classHours: Number(classHours) - Number(video.classHours),
          },
        });
      } else if (contentType === 'documents') {
        // await ctx.model.TrainingDocument.updateOne({
        //   _id: ID,
        // }, {
        //   name,
        //   Description,
        // });
        await ctx.service.db.updateOne('TrainingDocument', {
          _id: ID,
        }, {
          name,
          Description,
        });
      }
      // const back = await ctx.service.courses.resortContent(_id, id1, id2);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          // back,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 创建课程分类
  async createClassification() {
    const {
      ctx,
    } = this;
    try {
      const {
        name,
        explain,
        level,
        parentID,
      } = ctx.request.body;
      // console.log(name, explain, level, parentID);
      await ctx.service.courses.createClassification(name, explain, level, parentID, ctx.session.superUserInfo._id);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 删除课程分类
  async deleteClassification() {
    const {
      ctx,
      // config,
    } = this;
    try {
      const id = ctx.request.body._id;
      const result = ctx.service.courses.deleteClassficication(id);
      ctx.helper.renderSuccess(ctx, {
        data: {
          backData: result,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 更新课程分类
  async updateClassification() {
    const {
      ctx,
      // config,
    } = this;
    try {
      const {
        _id,
        name,
        explain,
      } = ctx.request.body;
      const result = ctx.service.courses.updateClassficication(_id, name, explain);
      ctx.helper.renderSuccess(ctx, {
        data: {
          backData: result,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 获取课程分类
  async getClassification() {
    const {
      ctx,
    } = this;
    try {
      const {
        level,
        parentID,
      } = ctx.query;
      const list = await ctx.service.courses.getClassification(level, parentID);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          list,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }


  // 前端获取阿里云账号ID，用于上传视频
  async getUserID() {
    this.ctx.helper.renderSuccess(this.ctx, {
      data: {
        message: 'OK',
        userID: this.config.aliVideo.userId,
      },
    });
  }
  // 获取阿里云视频分类
  async getVideoClassification() {
    const {
      ctx,
    } = this;
    try {
      const {
        CateId,
      } = ctx.query;
      console.log(CateId, 'CateId==========');
      const response = await ctx.helper.request_alivod('GetCategories', {
        CateId: CateId || -1,
        PageNo: 1,
        PageSize: 1000,
      }, {});
      this.ctx.body = {
        info: 200,
        response,
      };
    } catch (response) {
      console.error('ErrorCode = ' + response.code);
      console.error('ErrorMessage = ' + response.Message);
      console.error('RequestId = ' + response.RequestId);
      this.ctx.body = {
        info: 500,
      };
    }
  }
  // 刷新上传凭证
  async getUploadAuthWithID() {
    const {
      videoId,
    } = this.ctx.request.body;
    try {
      const response = await this.ctx.helper.request_alivod('RefreshUploadVideo', {
        videoId,
      }, {});
      // console.log(response);
      this.ctx.body = {
        info: 200,
        VideoId: response.VideoId,
        UploadAddress: response.UploadAddress,
        UploadAuth: response.UploadAuth,
      };
    } catch (response) {
      // console.log('responseresponse', response)
      console.error('ErrorCode = ' + response.code);
      console.error('ErrorMessage = ' + response.Message);
      console.error('RequestId = ' + response.RequestId);
      this.ctx.body = {
        info: 500,
      };
    }

  }
  // 获取上传凭证
  async getUploadAuthWithOutID() {
    const {
      Title,
      FileName,
      // region,
      CateId,
      Tags,
      Description,
      CoverURL,
    } = this.ctx.request.body;
    // console.log('Title, FileName, region, CateId, Tags', Title, FileName, region, CateId, Tags, Description);
    try {
      const params = {
        Title,
        FileName,
        // CoverURL,
        CateId,
        Tags,
        Description,
      };
      if (CoverURL && CoverURL.length) params.CoverURL = CoverURL;
      const response = await this.ctx.helper.request_alivod('CreateUploadVideo', params, {});
      // console.log(response)
      this.ctx.body = {
        info: 200,
        VideoId: response.VideoId,
        UploadAddress: response.UploadAddress,
        UploadAuth: response.UploadAuth,
        RequestId: response.RequestId,
      };
    } catch (response) {
      console.error('请求上传凭证出错啦');
      console.error('ErrorCode = ' + response.code);
      console.error('ErrorMessage = ' + response.Message);
      console.error('RequestId = ' + response.RequestId);
      this.ctx.body = {
        info: 500,
      };
    }
  }
  // 获取视频播放凭证
  async GetVideoPlayAuth() {
    const {
      VideoId,
    } = this.ctx.query;
    try {
      const response = await this.ctx.helper.request_alivod('GetVideoPlayAuth', {
        VideoId,
      }, {});
      console.log(121212, response);
      this.ctx.body = {
        info: 200,
        PlayAuth: response.PlayAuth,
      };
    } catch (response) {
      console.log('responseresponse', response);
      this.ctx.logger.error(response);
      console.error('ErrorCode = ' + response.code);
      console.error('ErrorMessage = ' + response.Message);
      console.error('RequestId = ' + response.RequestId);
      this.ctx.body = {
        info: 500,
        err: '错咯',
      };
    }
  }


  // 上传文件
  async uploadEditorImage() {
    const {
      ctx,
      config,
    } = this;
    try {
      const parts = ctx.multipart({
        autoFields: true,
      });
      const stream = await parts();
      const suffixArray = stream.filename.split('.');
      const suffix = suffixArray[suffixArray.length - 1];
      const fileName = Math.random().toString(36).substr(2) + new Date().getTime() + '.' + suffix;
      if (!Object.keys(parts.field).length) {
        console.log(parts);
        return;
      }
      const id = parts.field._id;
      const savePath = path.join(config.upload_courses_path, id);
      tools.makeEnterpriseDir(savePath);
      const writeStream = fs.createWriteStream(path.resolve(savePath, fileName));
      await awaitWriteStream(stream.pipe(writeStream));
      ctx.body = {
        // errno 即错误代码，0 表示没有错误。
        // 如果有错误，errno != 0，可通过下文中的监听函数 fail 拿到该错误码进行自定义处理
        errno: 0,
        data: [
          `/static${config.upload_courses_http_path}/${id}/${fileName}`,
        ],
        status: 200,
      };
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.body = {
        // errno 即错误代码，0 表示没有错误。
        // 如果有错误，errno != 0，可通过下文中的监听函数 fail 拿到该错误码进行自定义处理
        errno: -1,
        data: [],
        status: 200,
      };
    }
  }

  // 获取题库
  async getQuestionBank() {
    const {
      ctx,
    } = this;
    try {
      const query = {};
      const { regAdd } = this.ctx.session.superUserInfo;
      const queryArrForAdd = [];
      for (let i = 0; i < regAdd.length - 1; i++) {
        queryArrForAdd.push(regAdd.slice(0, i + 1));
      }
      query.$or = [
        { source: 'operate' },
        { source: 'super', authorID: ctx.session.superUserInfo._id },
        { source: 'super', createRange: { $in: queryArrForAdd } },
      ];

      const list = await ctx.model.QuestionBank.find(query);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          list,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 查询是否有被培训引用
  async findPlanQuote() {
    const {
      ctx,
    } = this;
    try {
      const {
        courseID,
      } = ctx.query;
      const adminPlanCount = await ctx.model.AdminTraining.countDocuments({
        coursesID: {
          $in: [ courseID ],
        },
      });

      const employeePlanCount = await ctx.model.EmployeesTrainingPlan.countDocuments({
        coursesID: {
          $in: [ courseID ],
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          count: employeePlanCount + adminPlanCount,
        },
      });
      return;
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }


  }

  // 获取所有企业、监管账户、体检、检测机构
  async getAllOrgs() {
    const { ctx } = this;
    const { courseId } = ctx.request.body;
    if (courseId) { // 获取课程权限
      const coursePower = await ctx.model.Courses.findOne({ _id: courseId }, { superUsers: 1, serviceOrgs: 1, physicalExamOrgs: 1, adminOrgs: 1, name: 1, powerStatus: 1 });
      ctx.helper.renderSuccess(ctx, {
        data: coursePower,
        message: '课程权限获取成功',
      });
      return;
    }
    const superUsers = await ctx.model.SuperUser.find({}, { cname: 1 });
    const serviceOrgs = await ctx.model.ServiceOrg.find({}, { name: 1 });
    const physicalExamOrgs = await ctx.model.PhysicalExamOrg.find({}, { name: 1 });
    const adminOrgs = await ctx.model.Adminorg.find({}, { cname: 1 });
    ctx.helper.renderSuccess(ctx, {
      data: {
        superUsers,
        serviceOrgs,
        physicalExamOrgs,
        adminOrgs,
      },
      message: '数据获取成功',
    });

  }
  // 保存某个课程的课程权限
  async saveCoursePower() {
    const { ctx } = this;
    const data = ctx.request.body;
    if (!data._id) {
      ctx.helper.renderSuccess(ctx, {
        message: '_id必须传',
      });
      return;
    }
    // const res = await ctx.model.Courses.updateOne({ _id: data._id }, data, { new: 1 });
    const res = await ctx.service.db.updateOne('Courses', { _id: data._id }, data, { new: 1 });
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '数据获取成功',
    });
  }

}
module.exports = CommentController;
