const Controller = require("egg").Controller;
const path = require("path");
const fs = require("fs");
const awaitWriteStream = require("await-stream-ready").write;
// 管道读入一个虫洞。
const sendToWormhole = require("stream-wormhole");
const mkdirp = require("mkdirp");
const moment = require("moment");
class HomeController extends Controller {
  async getAllGroup() {
    // console.log(this.config)
    this.ctx.helper.renderSuccess(this.ctx, {
      data: this.config.groupID,
    });
  }

  // 添加消息
  async addNew() {
    const { ctx, service } = this;
    try {
      const msg = ctx.request.body;
      // 目前就行政端发消息，首页企业没获取group，所以一旦没有group，那就判断是给企业的消息，这里补救一下
      // 以后优化后这一步一定删除，必须优化
      if (msg.reader[0].readerGroup === undefined) {
        for (let index = 0; index < msg.reader.length; index++) {
          const element = msg.reader[index];
          const enterprise = await service.adminorg.item(ctx, {
            query: { _id: element.readerID },
            populate: [
              {
                path: "adminUserId",
                select: "group",
              },
            ],
            files: "_id",
          });
          if (enterprise) {
            element.readerGroup = enterprise.adminUserId
              ? enterprise.adminUserId.group
              : this.config.groupID.superGroupID;
            element.isRead = 0;
          }
        }
      }

      // 处理传参
      const params = {
        title: msg.title,
        message: msg.message,
        reader: msg.reader || [],
        authorID: ctx.session.superUserInfo._id,
        authorGroup: ctx.session.superUserInfo.group,
        type: msg.type ? String(msg.type) : "",
        sendWay:
          msg.sendWay.length === 2
            ? "both"
            : msg.sendWay[0] === "站内信息"
            ? "systemMessage"
            : "sms",
        informer: ctx.session.superUserInfo.name || "", // 发通知的人名
        files: msg.files || [],
        templateCode: msg.templateCode || "",
        SignName: msg.SignName || "",
      };

      if (Array.isArray(ctx.session.superUserInfo.group)) {
        params.authorGroup = ctx.session.superUserInfo.group[0];
      }
      // 新建通知
      const res = await service.messageNotification.sendMessage(params);
      if (res) {
        // 发送短信
        if (params.sendWay === "both" || params.sendWay === "sms") {
          // 获取URLID
          const code = res._id;

          const group = this.config.groupID; // 1、获取config里的对应表关系
          // 2、按用户类型分组
          const readedItems = params.reader;
          const adminGroupIDs = readedItems
            .filter((ele) => ele.readerGroup === group.adminGroupID)
            .map((ele) => ele.readerID);
          const superGroupIDs = readedItems
            .filter((ele) => ele.readerGroup === group.superGroupID)
            .map((ele) => ele.readerID);
          const serviceGroupIDs = readedItems
            .filter((ele) => ele.readerGroup === group.serviceGroupID)
            .map((ele) => ele.readerID);
          const physicalExamGroupIDs = readedItems
            .filter((ele) => ele.readerGroup === group.physicalExamGroupID)
            .map((ele) => ele.readerID);
          // 3、获取接受者详情
          const adminGroup = await ctx.model.Adminorg.find(
            { _id: { $in: adminGroupIDs } },
            { cname: 1, phoneNum: 1 }
          ).populate("adminUserId", "phoneNum");
          const superGroup = await ctx.model.SuperUser.find(
            { _id: { $in: superGroupIDs } },
            { cname: 1, phoneNum: 1 }
          );
          const serviceGroup = await ctx.model.ServiceOrg.find(
            { _id: { $in: serviceGroupIDs } },
            { name: 1, managers: 1 }
          );
          const physicalExamGroup = await ctx.model.PhysicalExamOrg.find(
            { _id: { $in: physicalExamGroupIDs } },
            { name: 1, shortName: 1, phoneNum: 1 }
          );
          // 4、获取当前监管单位的信息
          const userDeatil = await this.getUserDeatil();
          const sendArr = adminGroup
            .concat(superGroup)
            .concat(physicalExamGroup);
          // 5、处理检测机构的数据，获取机构所有管理员的联系电话
          if (serviceGroup.length) {
            for (let i = 0; i < serviceGroup.length; i++) {
              const curService = serviceGroup[i];
              const serviceUser = await ctx.model.ServiceUser.find(
                { _id: { $in: curService.managers || [] } },
                { phoneNum: 1 }
              );
              const serviceUserArr = serviceUser.map((ele) => ({
                phoneNum: ele.phoneNum,
                name: curService.name,
              }));
              if (serviceUserArr.length) sendArr.push(...serviceUserArr);
            }
          }
          // 6、开始依次发送短信
          const len = sendArr.length;
          for (let j = 0; j < len; j++) {
            const item = sendArr[j];
            const sendPhoneNum = item.adminUserId
              ? item.adminUserId.phoneNum
              : item.phoneNum;
            if (!sendPhoneNum) continue;
            await ctx.service.messageNotification.sendSms(
              params.title || "",
              item.name || item.cname || "",
              userDeatil.cname,
              userDeatil.landline || userDeatil.phoneNum,
              sendPhoneNum,
              params.templateCode,
              params.SignName,
              code || ""
            );
          }
        }
        // 返回信息
        ctx.helper.renderSuccess(ctx, {
          data: res,
          message: "ok",
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 通知记录 列表查询
  async findMessageList() {
    const { ctx, service, config } = this;
    try {
      const params = ctx.request.body;
      // console.log(111111111, params);
      const query = {
        authorID: ctx.session.superUserInfo._id,
        state: 1,
      };
      // if (params.regAdd && params.regAdd.length > 0) {
      //   query['authorID.regAdd'] = { $all: JSON.parse(params.regAdd) };
      // }
      if (params.searchkey) {
        query.$or = [
          { message: { $regex: params.searchkey } },
          { title: { $regex: params.searchkey } },
        ];
      }
      if (params.dates && params.dates.length === 2) {
        const endDate = new Date(params.dates[1]);
        query.date = {
          $lt: new Date(endDate.setDate(endDate.getDate() + 1)),
          $gte: new Date(params.dates[0]),
        };
      }
      if (params.type) query.type = String(params.type);
      if (params.sendWay) query.sendWay = params.sendWay;
      const res = await service.messageNotification.getMyMessage(query, {
        pageSize: +params.pageSize || 10,
        current: +params.current || 1,
      });
      if (res) {
        const upload_http_path = config.upload_http_path;
        const docs = res.docs.map((ele) => {
          const newEle = JSON.parse(JSON.stringify(ele));
          if (ele.files) {
            newEle.files = ele.files.map((fileName) => ({
              url:
                fileName.startsWith("体检信息") && fileName.endsWith(".xlsx")
                  ? `/static${upload_http_path}/messageNotification/${ele.authorID[0]._id}/${fileName}`
                  : `/static${upload_http_path}/messageNotification/${ele._id}/${fileName}`,
              name: fileName,
            }));
          }
          newEle.date = moment(ele.date).format("YYYY-MM-DD HH:mm");
          return newEle;
        });
        ctx.helper.renderSuccess(ctx, {
          data: {
            docs,
            pageInfo: res.pageInfo,
          },
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取所有企业
  async getAllOrgList() {
    const { ctx, app } = this;
    try {
      const query = ctx.query.query ? JSON.parse(ctx.query.query) : {};
      console.log(query);
      const regAdd = ctx.session.superUserInfo.regAdd;
      const serchArr = [];
      if (regAdd[regAdd.length - 1] !== app.config.China.name) {
        // 不是搜索但是非全国用户的时候
        serchArr.push(...regAdd);
        query.districtRegAdd = {
          $all: serchArr,
        };
      }

      // const searchKeys = ['cname', 'regAdd', 'corp', 'adminUserId.phoneNum'];

      const adminorgList = await ctx.model.Adminorg.find(query, {
        cname: 1,
        _id: 1,
      }).limit(20);

      ctx.helper.renderSuccess(ctx, {
        data: adminorgList,
      });
    } catch (err) {
      // throw err;
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取阅读情况
  async getMsgOne() {
    const { ctx } = this;
    try {
      const { msgID } = ctx.query;
      const msg = await ctx.model.MessageNotification.findOne({
        _id: msgID,
      });
      const group = this.config.groupID; // 获取config里的对应表关系
      // 已读用户
      let readedItems = msg.reader.filter((ele) => ele.isRead === 1);
      let adminGroupIDs = readedItems
        .filter((ele) => ele.readerGroup === group.adminGroupID)
        .map((ele) => ele.readerID);
      let superGroupIDs = readedItems
        .filter((ele) => ele.readerGroup === group.superGroupID)
        .map((ele) => ele.readerID);
      let serviceGroupIDs = readedItems
        .filter((ele) => ele.readerGroup === group.serviceGroupID)
        .map((ele) => ele.readerID);
      let physicalExamGroupIDs = readedItems
        .filter((ele) => ele.readerGroup === group.physicalExamGroupID)
        .map((ele) => ele.readerID);
      readedItems = {
        // 按用户类型分组
        adminGroup: await ctx.model.Adminorg.find(
          { _id: { $in: adminGroupIDs } },
          { cname: 1 }
        ),
        superGroup: await ctx.model.SuperUser.find(
          { _id: { $in: superGroupIDs } },
          { cname: 1, phoneNum: 1 }
        ),
        serviceGroup: await ctx.model.ServiceOrg.find(
          { _id: { $in: serviceGroupIDs } },
          { name: 1 }
        ),
        physicalExamGroup: await ctx.model.PhysicalExamOrg.find(
          { _id: { $in: physicalExamGroupIDs } },
          { name: 1, shortName: 1 }
        ),
      };
      // 未读用户
      let unreadItems = msg.reader.filter((ele) => ele.isRead === 0);
      adminGroupIDs = unreadItems
        .filter((ele) => ele.readerGroup === group.adminGroupID)
        .map((ele) => ele.readerID);
      superGroupIDs = unreadItems
        .filter((ele) => ele.readerGroup === group.superGroupID)
        .map((ele) => ele.readerID);
      serviceGroupIDs = unreadItems
        .filter((ele) => ele.readerGroup === group.serviceGroupID)
        .map((ele) => ele.readerID);
      physicalExamGroupIDs = unreadItems
        .filter((ele) => ele.readerGroup === group.physicalExamGroupID)
        .map((ele) => ele.readerID);
      unreadItems = {
        adminGroup: await ctx.model.Adminorg.find(
          { _id: { $in: adminGroupIDs } },
          { cname: 1, phoneNum: 1 }
        ).populate("adminUserId", "phoneNum"),
        superGroup: await ctx.model.SuperUser.find(
          { _id: { $in: superGroupIDs } },
          { cname: 1, phoneNum: 1 }
        ),
        serviceGroup: await ctx.model.ServiceOrg.find(
          { _id: { $in: serviceGroupIDs } },
          { name: 1, managers: 1 }
        ),
        physicalExamGroup: await ctx.model.PhysicalExamOrg.find(
          { _id: { $in: physicalExamGroupIDs } },
          { name: 1, shortName: 1, phoneNum: 1 }
        ),
      };
      // 返回数据
      ctx.helper.renderSuccess(ctx, {
        data: {
          _id: msg._id,
          title: msg.title,
          templateCode: msg.templateCode || "",
          SignName: msg.SignName || "",
          readedItems,
          unreadItems,
        },
        message: "数据获取成功",
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 删除通知
  async changeState() {
    const { ctx } = this;
    const { _id, state } = ctx.request.body;
    // const res = await ctx.model.MessageNotification.updateOne({ _id }, { state: +state });
    const res = await ctx.service.db.updateOne(
      "MessageNotification",
      { _id },
      { state: +state }
    );
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: "删除成功",
    });
  }
  // 短信提醒
  async sendSms() {
    const { ctx } = this;
    const { title, unreadItems, templateCode, SignName } = ctx.request.body;
    const userDeatil = await this.getUserDeatil(); // 获取当前监管单位的信息
    const sendArr = unreadItems.adminGroup
      .concat(unreadItems.superGroup)
      .concat(unreadItems.physicalExamGroup);
    // 处理检测机构的数据，获取机构所有管理员的联系电话
    if (unreadItems.serviceGroup && unreadItems.serviceGroup.length) {
      for (let i = 0; i < unreadItems.serviceGroup.length; i++) {
        const curService = unreadItems.serviceGroup[i];
        const serviceUser = await ctx.model.ServiceUser.find(
          { _id: { $in: curService.managers || [] } },
          { phoneNum: 1 }
        );
        const serviceUserArr = serviceUser.map((ele) => ({
          phoneNum: ele.phoneNum,
          name: curService.name,
        }));
        if (serviceUserArr.length) sendArr.push(...serviceUserArr);
      }
    }
    // 开始依次发送短信
    const len = sendArr.length;
    for (let j = 0; j < len; j++) {
      const item = sendArr[j];
      const sendPhoneNum = item.adminUserId
        ? item.adminUserId.phoneNum
        : item.phoneNum;
      if (!sendPhoneNum) continue;
      await ctx.service.messageNotification.sendSms(
        title || "",
        item.name || item.cname || "",
        userDeatil.cname,
        userDeatil.landline || userDeatil.phoneNum,
        sendPhoneNum,
        templateCode,
        SignName
      );
    }
    ctx.helper.renderSuccess(ctx, {
      data: { unreadItems },
      message: "短信提醒发送成功",
    });
  }
  // 获取当前监管单位的信息
  async getUserDeatil() {
    const { ctx } = this;
    const _id = ctx.session.superUserInfo._id;
    return ctx.model.SuperUser.findOne(
      { _id },
      { cname: 1, landline: 1, phoneNum: 1 }
    );
  }
  // 监管端 - 上传通知文件
  async uploadFiles() {
    const { ctx, app } = this;
    const result = []; // 返回的结果
    let _id = ""; // 通知的id
    const parts = ctx.multipart({ autoFields: true });
    let part;
    while ((part = await parts()) != null) {
      if (!part.filename) {
        // 注意如果没有传入直接返回
        continue;
      }
      // console.log('file: ' + part.filename, part.encoding, part.mime);
      if (!_id) _id = parts.field._id;
      const uploadPath = `${app.config.upload_path}/messageNotification/${_id}`;
      const writePath = path.join(uploadPath, `/${part.filename}`);
      if (!fs.existsSync(uploadPath)) {
        await mkdirp(uploadPath); // 创建文件夹
      }
      // 生成一个文件写入 文件流
      const writeStream = fs.createWriteStream(writePath);
      try {
        // 异步把文件流 写入
        await awaitWriteStream(part.pipe(writeStream));
        result.push(part.filename);
      } catch (error) {
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        await sendToWormhole(part);
        writeStream.destroy();
        throw error;
      }
    }
    if (result.length) {
      ctx.body = {
        status: 200,
        msg: result.length ? "文件上传成功。" : "文件存储失败",
        data: result,
      };
    } else {
      ctx.body = { status: 200, msg: "文件无变动的情况下，请勿重新提交" };
    }
  }
  // 获取短信模板列表
  async getSmsTemplateList() {
    const { ctx } = this;
    try {
      const { PageSize = 10, CurrentPage = 1 } = ctx.request.query;
      const smsTemplateList =
        await ctx.service.messageNotification.getSmsTemplateList({
          PageSize,
          CurrentPage,
        });
      if (!smsTemplateList || !smsTemplateList.data) {
        ctx.helper.renderFail(ctx, {
          message: "iservice错误",
          data: smsTemplateList,
        });
        return;
      }
      ctx.helper.renderSuccess(ctx, {
        data: smsTemplateList.data,
        message: smsTemplateList.message,
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 新增短信模板
  async addSmsTemplate() {
    const { ctx } = this;
    try {
      const { TemplateName, TemplateContent, TemplateType, Remark } =
        ctx.request.body;
      const res = await ctx.service.messageNotification.addSmsTemplate({
        TemplateName,
        TemplateContent,
        TemplateType,
        Remark,
      });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "新增成功",
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 删除短信模板
  async deleteSmsTemplate() {
    const { ctx } = this;
    try {
      const { TemplateCode } = ctx.request.body;
      const res = await ctx.service.messageNotification.deleteSmsTemplate({
        TemplateCode,
      });
      if (res.status === 200) {
        ctx.helper.renderSuccess(ctx, {
          data: res,
          message: "删除成功",
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: res.message,
        });
      }
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 修改短信模板 ModifySmsTemplate
  async modifySmsTemplate() {
    const { ctx } = this;
    try {
      const {
        TemplateCode,
        TemplateContent,
        TemplateName,
        TemplateType,
        Remark,
      } = ctx.request.body;
      const res = await ctx.service.messageNotification.modifySmsTemplate({
        TemplateCode,
        TemplateContent,
        TemplateName,
        TemplateType,
        Remark,
      });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "修改成功",
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取短信签名列表
  async getSmsSignList() {
    const { ctx } = this;
    try {
      const { PageSize = 10, CurrentPage = 1 } = ctx.request.query;
      const smsSignList = await ctx.service.messageNotification.getSmsSignList({
        PageSize,
        CurrentPage,
      });
      if (!smsSignList || !smsSignList.data) {
        ctx.helper.renderFail(ctx, {
          message: "iservice错误",
          data: smsSignList,
        });
        return;
      }
      ctx.helper.renderSuccess(ctx, {
        data: smsSignList.data,
        message: smsSignList.message,
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
}

module.exports = HomeController;
