
const Controller = require('egg').Controller;
const mkdirp = require('mkdirp');
const path = require('path');
const fs = require('fs');
const awaitStreamReady = require('await-stream-ready').write;
const streamWormhole = require('stream-wormhole');

class ProjectGiveBackController extends Controller {
  async uploadImage(ctx) {
    console.log(213123123123);
    const superID = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';

    const stream = await ctx.getFileStream();
    // 文件名规则： giveBack_image_superuserID_时间戳_随机数.后缀
    const filename = 'giveBack_image_' + superID + '_' + new Date().getTime() + '_' + Math.random().toString(36).substr(8) + path.extname(stream.filename);
    const fullPath = path.join(ctx.app.config.upload_path, superID);
    console.log(12121, fullPath, filename);

    await mkdirp(fullPath).then(async () => {
      const target = path.join(fullPath, filename);
      console.log('文件路径', target); 1;
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitStreamReady(stream.pipe(writeStream));
      } catch (err) {
        await streamWormhole(stream);
      }
      ctx.body = {
        errno: 0,
        data: {
          url: '/static' + ctx.app.config.upload_http_path + '/' + superID + '/' + filename, // 图片 src ，必须
          alt: '',
        },
      };
    });
  }
  async giveBack(ctx) {
    const parmas = ctx.request.body;
    const origin = ctx.request.header.origin;
    console.log(123123123, parmas);
    // 校验参数
    if (!parmas.projectID) {
      return ctx.helper.renderFail(ctx, {
        message: '缺少项目编号，请刷新后重新尝试',
      });
    }
    if (!parmas.type || ![ 'serviceProject', 'physicalExaminationProjects', 'diagnostics', 'radiateProjects' ].includes(parmas.type)) {
      return ctx.helper.renderFail(ctx, {
        message: '参数错误，请刷新后重新尝试',
      });
    }

    try {
      // 修改状态
      await ctx.service.projectGiveBack.giveBack(parmas);

      // 发送短信通知
      let isTest = false; // 是否是测试环境
      if (!origin.includes('.cn')) {
        isTest = true;
      }
      console.log('走到了发送短信这里', parmas);
      await ctx.service.projectGiveBack.sendMessage(parmas, isTest);


      // 检查是否有需要删除的图片
      if (parmas.delImageList && parmas.delImageList.length > 0) {
        for (let i = 0; i < parmas.delImageList.length; i++) {
          const delImage = parmas.delImageList[i];
          await ctx.service.projectGiveBack.handleDelImage(delImage);
        }
      }

      return ctx.helper.renderSuccess(ctx, {
        message: 'suceesss',
      });

    } catch (err) {
      console.log(err);
      return ctx.helper.renderFail(ctx, {
        message: '退回失败，请重新尝试',
      });
    }
  }
  async giveBackDelImgs(ctx) {
    const parmas = ctx.request.body;
    if (parmas.delImageList) {
      for (let i = 0; i < parmas.delImageList.length; i++) {
        const delImage = parmas.delImageList[i];
        await ctx.service.projectGiveBack.handleDelImage(delImage);
      }
    }
    ctx.helper.renderSuccess(ctx, {
      message: 'suceesss',
    });
  }
  async giveBackRecord(ctx) {
    const parmas = ctx.request.body;
    console.log(11111111, parmas);
    const res = await ctx.service.projectGiveBack.giveBackRecord(parmas);
    if (res) {
      return ctx.helper.renderSuccess(ctx, {
        data: res,
        message: 'suceesss',
      });
    }
  }
}
module.exports = ProjectGiveBackController;
