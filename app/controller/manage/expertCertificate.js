// 职业健康专家证书管理
const path = require('path');
const fs = require('fs');
const expertCertificateController = {
  // 获取列表
  async list(ctx) {
    const { app } = this;
    try {
      const query = ctx.query;
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/certificate`,
        {
          method: 'GET',
          dataType: 'json',
          data: query,
        }
      );
      if (data.code === 200) {
        await ctx.helper.handleIserviceRes(ctx, data, '获取成功', '获取失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '数据获取失败',
          data: data.data || {},
        });
      }

    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '数据获取失败',
        data: err,
      });
    }
  },

  // 新增专家证书，采用form-data格式传输，支持文件上传
  async add(ctx) {
    const { app } = this;
    try {
      const stream = await ctx.getFileStream();
      const { name, user_id, number } = stream.fields;
      if (!name) throw new Error('证书名称name不能为空');
      if (!user_id) throw new Error('user_id不能为空');
      if (!number) throw new Error('证书编号number不能为空');
      const extname = path.extname(stream.filename);
      if (![ '.pdf', '.png', '.jpg', '.jpeg' ].includes(extname)) {
        throw new Error('文件格式不正确, 请上传pdf、png、jpg、jpeg格式的文件');
      }

      const target = path.resolve(app.config.certificate_path, number + extname);
      await ctx.helper.pipe({
        readableStream: stream,
        target,
      });
      const annex = `/static${app.config.certificate_http_path}/${number + extname}`;

      const { data } = await ctx.curl(
        `${app.config.iService2Host}/certificate`,
        {
          method: 'POST',
          dataType: 'json',
          data: {
            ...stream.fields,
            annex,
          },
        }
      );
      if (data.code === 200) {
        await ctx.helper.handleIserviceRes(ctx, data, '添加成功', '添加失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '操作失败',
          data: data.data || {},
        });
      }
    } catch (err) {
      console.log(44444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '新增失败',
        data: err,
      });
    }
  },

  // 删除
  async delete(ctx) {
    const { app } = this;
    const fields = ctx.request.body || {};
    try {
      const { id } = fields;
      if (!id) throw new Error('id不能为空');
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/certificate/${+id}`,
        {
          method: 'DELETE',
          dataType: 'json',
        }
      );
      if (data.code === 200) {
        // 删除证书文件
        const file = data.data.annex;
        if (file) {
          const filename = file.split('/').pop();
          const target = path.resolve(app.config.certificate_path, filename);
          if (fs.existsSync(target)) fs.unlinkSync(target);
        }
        // 返回结果
        await ctx.helper.handleIserviceRes(ctx, data, '删除成功', '删除失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '操作失败',
          data: data.data,
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '删除失败',
        data: err,
      });
    }
  },
};

module.exports = expertCertificateController;
