const Controller = require('egg').Controller;
class xjbtOBController extends Controller {
  // 获取经济类型
  async getQyEconomicTypes() {
    const { ctx, config } = this;
    const url = `${config.iService2Host}/hebei/qyEconomicTypes`;
    const { data } = await ctx.curl(url, {
      method: 'GET',
      dataType: 'json',
      data: ctx.query,
    });
    return ctx.helper.renderSuccess(ctx, {
      message: '获取经济类型数据成功',
      data: {
        ...data.data,
      },
    });
  }

  // 获取行业类别
  async getQyIndustryEmployerCategorys() {
    const { ctx, app } = this;
    try {
      const { data } = await ctx.curl(`${app.config.iService2Host}/hebei/qyIndustryCategorys`, {
        method: 'get',
        dataType: 'json',
      });

      let newData = [];
      if (data.data) {
        newData = data.data.map(e => {
          return {
            id: e.id,
            value: e.code,
            label: e.name,
            parent_id: e.parent_id,
            level: e.level,
          };
        });
      }

      // const docs = this.buildTree(newData);
      const docs = this.buildTree_pid(newData);

      ctx.helper.renderSuccess(ctx, {
        data: docs,
      });
    } catch (err) {
      ctx.logger.error(new Error(err));
      ctx.helper.renderFail(ctx, {
        message: '哎呀！网络开小差了！要不，稍后再试试？',
      });
    }
  }

  buildTree_pid(data) {
    const idMap = new Map();
    const tree = [];

    // 创建一个 id 映射，方便通过 id 找到对应的节点
    data.forEach(item => idMap.set(item.id, { ...item, children: [] }));

    data.forEach(item => {
      if (item.parent_id) {
        const parent = idMap.get(item.parent_id);
        if (parent) {
          parent.children.push(idMap.get(item.id));
        }
      } else {
        tree.push(idMap.get(item.id));
      }
    });

    // 移除空的 children 属性
    function removeEmptyChildren(nodes) {
      nodes && nodes.forEach(node => {
        if (node.children && node.children.length === 0) {
          delete node.children;
        } else {
          removeEmptyChildren(node.children);
        }
      });
    }

    removeEmptyChildren(tree);
    return tree;
  }

  buildTree(data) {
    const tree = [];
    const map = new Map();

    // Step 1: Create a map with level as key and nodes as value
    data.forEach(item => {
      map.set(item.level, { ...item, children: [] });
    });

    // Step 2: Build the tree structure
    data.forEach(item => {
      const levels = item.level.split('.');
      const currentNode = map.get(item.level);

      if (levels.length === 1) {
        // Root node, add directly to tree
        tree.push(currentNode);
      } else {
        // Find parent node (exclude the current level to get the parent level)
        const parentLevel = levels.slice(0, -1).join('.');
        const parentNode = map.get(parentLevel);

        // Ensure the parentNode exists before pushing the current node
        if (parentNode) {
          parentNode.children.push(currentNode);
        }
      }
    });

    // 移除空的 children 属性
    function removeEmptyChildren(nodes) {
      nodes.forEach(node => {
        if (node.children.length === 0) {
          delete node.children;
        } else {
          removeEmptyChildren(node.children);
        }
      });
    }

    removeEmptyChildren(tree);
    return tree;
  }

  // 申报查询 - 按申报审核统计
  async getDeclarationStatistics() {
    const { ctx, config } = this;
    try {
      // 参数校验
      const query = ctx.query;
      const { type, hazardousFactors } = query;
      if (!type) throw new Error('type参数不能为空');
      if (![ '1', '2', '3' ].includes(type)) throw new Error('type参数错误, 请传入1, 2, 3');
      const hazards = [ 'has_dust_hazard', 'has_chem_hazard', 'has_phys_hazard', 'has_radio_hazard', 'has_bio_hazard', 'has_other_hazard' ];
      if (hazardousFactors && !hazards.includes(hazardousFactors)) {
        throw new Error('hazardousFactors参数错误, 请传入has_dust_hazard,has_chem_hazard,has_phys_hazard,has_radio_hazard,has_bio_hazard,has_other_hazard');
      }
      const { area_code, regAdd } = ctx.session.superUserInfo;

      // 获取缓存数据
      if (area_code === '130000000000' && !query.districtCode) {
        const keys = Object.keys(query);
        if (keys.length === 1) {
          const cacheStatistics = await ctx.model.Statistics.findOne({ 'query.adcode': area_code, 'query.year': +type });
          if (cacheStatistics && cacheStatistics.districtsData && cacheStatistics.districtsData.message) {
            return ctx.helper.renderSuccess(ctx, cacheStatistics.districtsData);
          }
        }
      }
      // 区域权限校验
      if (query.districtCode) {
        const queryArea = await ctx.service.district.findParents(query.districtCode);
        const hasPermission = regAdd.length <= queryArea.length && regAdd.every((ele, index) => ele === queryArea[index]);
        if (!hasPermission) {
          return ctx.helper.renderCustom(ctx, {
            status: 403,
            message: `对不起，您没有权限查看${queryArea.join('/')}区域的数据。`,
            data: {
              regAdd,
              query,
            },
          });
        }
        query.districtId = query.districtCode;
      } else {
        query.districtId = area_code;
      }
      const districtId = query.districtId;
      // 请求数据
      const url = `${config.iService2Host}/hebei/declarationStatistics`;
      const { data } = await ctx.curl(url, {
        method: 'GET',
        dataType: 'json',
        headers: {
          'Content-Type': 'application/json',
        },
        data: query,
        timeout: 30000,
      });
      if (data.code !== 200) {
        return ctx.helper.renderFail(ctx, {
          message: '申报查询失败: ' + data.message,
          data: {
            query: ctx.query,
            err: data,
          },
        });
      }
      const list = data.data.declarationList; // 列表

      // 数据处理
      if (type === '1') {
        // 1、按申报审核统计
        let abnormalList = JSON.parse(JSON.stringify(list));
        const subArea = await ctx.service.district.list({ parent_code: districtId }, { area_code: 1, name: 1, level: 1 }, true);

        const result = subArea.map(curArea => {
          const curAreaList = list.filter(ele => ele.district_id.startsWith(curArea.area_code));
          abnormalList = abnormalList.filter(ele => !ele.district_id.startsWith(curArea.area_code));
          return {
            areaName: curArea.name,
            firstDeclare: {
              total: curAreaList.filter(ele => ele.declaration_type === 1).length,
              reject: curAreaList.filter(ele => ele.declaration_type === 1 && ele.audit_status === 2).length,
            },
            changeDeclare: {
              total: curAreaList.filter(ele => ele.declaration_type === 2).length,
              reject: curAreaList.filter(ele => ele.declaration_type === 2 && ele.audit_status === 2).length,
            },
            yearDeclare: {
              total: curAreaList.filter(ele => ele.declaration_type === 3).length,
              reject: curAreaList.filter(ele => ele.declaration_type === 3 && ele.audit_status === 2).length,
            },
          };
        });
        if (abnormalList.length) {
          const otherArea = {
            areaName: '其他',
            firstDeclare: {
              total: abnormalList.filter(ele => ele.declaration_type === 1).length,
              reject: abnormalList.filter(ele => ele.declaration_type === 1 && ele.audit_status === 2).length,
            },
            changeDeclare: {
              total: abnormalList.filter(ele => ele.declaration_type === 2).length,
              reject: abnormalList.filter(ele => ele.declaration_type === 2 && ele.audit_status === 2).length,
            },
            yearDeclare: {
              total: abnormalList.filter(ele => ele.declaration_type === 3).length,
              reject: abnormalList.filter(ele => ele.declaration_type === 3 && ele.audit_status === 2).length,
            },
          };
          result.push(otherArea);
        }
        return ctx.helper.renderSuccess(ctx, {
          message: '申报查询 - 按申报审核统计',
          data: {
            reviewResults: data.data.reviewResults, // 申报结果统计
            list: result,
            query: ctx.query,
            abnormalDistrictCodes: Array.from(new Set(abnormalList.map(ele => ele.district_id))),
          },
        });
      } else if (type === '2') {
        // 2、按行业分类统计
        return ctx.helper.renderSuccess(ctx, {
          message: '申报查询 - 按行业分类统计',
          data: {
            list,
            abnormalIndustry: data.data.abnormalIndustry,
            query: ctx.query,
          },
        });
      }

      // 3、按行政区域统计
      const subArea = await ctx.service.district.list({ parent_code: districtId }, { area_code: 1, name: 1, level: 1 }, true);
      let abnormalList3 = JSON.parse(JSON.stringify(list));
      const result = subArea.map(curArea => {
        const curAreaList = list.filter(ele => ele.district_id.startsWith(curArea.area_code));
        abnormalList3 = abnormalList3.filter(ele => !ele.district_id.startsWith(curArea.area_code));
        return {
          areaName: curArea.name,
          employer_num: curAreaList.length,
          employee_num: curAreaList.reduce((acc, ele) => acc + ele.employee_num, 0),
          outsourced_num: curAreaList.reduce(
            (acc, ele) => acc + ele.outsourced_num,
            0
          ),
          exposed_num: curAreaList.reduce((acc, ele) => acc + ele.exposed_num, 0),
          patient_num: curAreaList.reduce((acc, ele) => acc + ele.patient_num, 0),
        };
      });
      let abnormalArea = null;
      if (abnormalList3.length) {
        const otherArea = {
          areaName: '其他',
          employer_num: abnormalList3.length,
          employee_num: abnormalList3.reduce((acc, ele) => acc + ele.employee_num, 0),
          outsourced_num: abnormalList3.reduce((acc, ele) => acc + ele.outsourced_num, 0),
          exposed_num: abnormalList3.reduce((acc, ele) => acc + ele.exposed_num, 0),
          patient_num: abnormalList3.reduce((acc, ele) => acc + ele.patient_num, 0),
        };
        result.push(otherArea);
        abnormalArea = Array.from(new Set(abnormalList3.map(ele => ele.district_id)));
      }

      return ctx.helper.renderSuccess(ctx, {
        message: '申报查询 - 按行政区域统计',
        data: {
          list: result,
          abnormalArea,
          query: ctx.query,
        },
      });

    } catch (err) {
      console.log(44444, err);
      ctx.helper.renderFail(ctx, {
        message: '申报查询失败: ' + err.message,
        data: {
          err,
        },
      });
    }
  }
}
module.exports = xjbtOBController;
