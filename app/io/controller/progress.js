let onlineMonitoringTimer = null;
module.exports = app => {
  class Controller extends app.Controller {
    // 在线监测 xxn add
    async onlineMonitoring(ctx) {
      const nsp = ctx.socket.nsp;
      const id = ctx.socket.id; // 获取到连接到此命名空间的客户机ID
      try {
        // 获取企业的所有设备
        const deviceList = await ctx.service.devices.list();
        nsp.sockets[id].emit('deviceList', deviceList);
        if (deviceList.length === 0) return;
        // 获取设备的最新数据
        const deviceIDs = deviceList.map(ele => ele.deviceID);
        const client = nsp.sockets[id];

        const newData = await ctx.service.devicesData.newData(deviceIDs);
        client.emit('newData', newData);

        if (onlineMonitoringTimer) clearInterval(onlineMonitoringTimer);
        onlineMonitoringTimer = setInterval(async () => {
          const newData = await ctx.service.devicesData.newData(deviceIDs);
          client.emit('newData', newData);
        }, 2000);

      } catch (error) {
        if (onlineMonitoringTimer) clearInterval(onlineMonitoringTimer);
        ctx.auditLog('在线监测错误', JSON.stringify(error), 'error');
        nsp.sockets[id].emit('err', { errMsg: '服务器出错' + JSON.stringify(error) });
      }
    }
    // 关闭在线监测定时器
    async closeOnlineMonitoring() {
      if (onlineMonitoringTimer) clearInterval(onlineMonitoringTimer);
    }

  }
  return Controller;
};
