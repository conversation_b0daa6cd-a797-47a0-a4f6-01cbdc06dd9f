const amqp = require('amqplib');
module.exports = app => {
  return class extends app.Controller {
    async watchSMSnotification(ctx) {
    //   const message = ctx.args[0];
    //   console.log(88888, message);
      const nsp = ctx.socket.nsp;
      const id = ctx.socket.id;
      const client = nsp.sockets[id];

      const quene = 'warning_sms_count';
      const rabbitmq = app.config.rabbitmq;
      const connection = await amqp.connect(rabbitmq.url);
      const channel = await connection.createChannel();
      channel.assertQueue(quene, { durable: true });
      channel.consume(quene, async msg => {
        const content = msg.content.toString();
        try {
          const data = JSON.parse(content);
          const { userId, count } = data;
          if (userId === ctx.session.superUserInfo._id && count === 0) {
            client.emit('watchSMSnotificationEnd', 'end');
            channel.ack(msg);
            channel.close();
            connection.close();
          }
        } catch (error) {
          ctx.logger.error('监听预警短信发送结果出错', JSON.stringify(error), 'error');
          channel.ack(msg);
        }
      });
    }
    async fromVue(ctx) {
      // const nsp = ctx.socket.nsp;
      // const id = ctx.socket.id;
      // const client = nsp.sockets[id];
      // client.emit('sendWarningData', 'ffffffffffff');

      // client.on('sendWarningData', (message) => {
      //   // io.emit('receive-broadcast', message);
      //   client.emit('reviveData', 'xxxxxxxxxxxx');
      // });
      // ctx.socket.on('connection', (socket) => {
      //   console.log('a user connected');

      //   ctx.socket.on('fromVue', (data) => {
      //     console.log('Message from Vue:', data);
      //     // 将消息推送给所有连接的客户端，包括HTML页面
      //     ctx.socket.emit('toHtml', data);
      //   });

      //   socket.on('disconnect', () => {
      //     console.log('user disconnected');
      //   });
      // });
      const io = ctx.socket.io;
      io.on('connection', socket => {
        console.log('a user connected', socket);

        ctx.socket.on('fromVue', data => {
          console.log('Message from Vue:', data);
          // 将消息推送给所有连接的客户端，包括HTML页面
          io.emit('toHtml', { data: 'dddddddddd' });
        });

        ctx.socket.on('disconnect', () => {
          console.log('user disconnected');
        });
      });
    }

  };
};
