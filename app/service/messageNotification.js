const Service = require('egg').Service;
// const { siteFunc } = require('@utils');


// const _ = require('lodash');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
  _unionQuery,
} = require('./general');


class MessageNotificationService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.MessageNotification, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }

  async count(params = {}) {
    return _count(this.ctx.model.MessageNotification, params);
  }

  async removes(res, values, key = '_id') {
    // return _removes(res, this.ctx.model.MessageNotification, values, key);
    return _removes(res, 'MessageNotification', values, key);
  }

  async safeDelete(res, values) {
    // return _safeDelete(res, this.ctx.model.MessageNotification, values);
    return _safeDelete(res, 'MessageNotification', values);
  }

  async update(res, _id, payload) {
    // return _update(res, this.ctx.model.MessageNotification, _id, payload);
    return _update(res, this.ctx.model.MessageNotification, 'MessageNotification', _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.MessageNotification, params);
  }

  // /** 发送消息
  //  * @param {*} authorID String
  //  * 作者的_id，就是发消息者的_id
  //  * @param {*} authorGroup String
  //  * 发消息者的group
  //  * @param {*} title String
  //  * @param {*} message String
  //  * @param {*} reader Array
  //  * 这是最关键的参数
  //  * [{
  //  * readerID:'', 这是对应每个接收消息用户的_id，在对应用户表的_id，
  //  * readerGroup: '', 这是该用户对应adminGroup表的_id，用处是监管端管理，需要从不同表去查，比较复杂
  //  * }]
  //  * 所有的group一定要对应正确，否则就会让这条数据变成冗余的死数据
  //  */
  async sendMessage(payload) {
    // return _create(this.ctx.model.MessageNotification, payload);
    return _create('MessageNotification', payload, this.ctx);
  }

  /** 获取接收的消息
   * 这是读者获取自己的消息，就是别人发送给你的消息
   * @param {*} id 你的id，查询者的id
   * @param {*} group 查询者的group
   * @param {*} isread 是否已读，已读为1，未读为0
   * @param {*} pageSize pageSize
   * @param {*} current current
   * @param {*} searchkey searchKey
   */
  async getMessage(id, group, isread,
    pageSize = 10,
    current = 1,
    searchkey
  ) {
    const searchKeys = [ 'title', 'message' ];
    const reader = [{
      readerID: id,
      readerGroup: group,
      isRead: isread ? 1 : 0,
    }];
    const playLoad = {
      pageSize: pageSize || 10,
      current: current || 1,
    };
    if (searchkey) playLoad.searchkey = searchkey;
    const argv = {
      collections: [],
      unwindArray: [],
      query: {
        reader,
      },
      searchKeys,
      files: {
        date: 1,
        title: 1,
        message: 1,
        authorID: 1,
        authorGroup: 1,
        reader: 1,
      },
      sort: {
        date: -1,
      },
      statisticsFiles: [],
    };
    const listdata = await _unionQuery(this.ctx.model.MessageNotification, playLoad, argv);
    // console.log('listdata', listdata);
    if (listdata.docs.length) {
      for (let index = 0; index < listdata.docs.length; index++) {
        const element = listdata.docs[index];
        const group = this.config.groupID;
        switch (element.authorGroup) {
          case group.superGroupID: {
            const user = await this.ctx.model.SuperUser.find({
              _id: element.authorID,
            }, {
              cname: 1,
              email: 1,
            });
            console.log('user', user);
            if (!user.length) {
              element.user = {
                cname: '该政府用户没了',
                email: '',
              };
              break;
            }
            element.user = user[0];
            break;
          }
          case group.serviceGroupID: {
            const user = await this.ctx.model.ServiceOrg.find({
              _id: element.authorID,
            }, {
              name: 1,
            });
            if (!user) {
              element.user = {
                cname: '该用户没了',
                email: '',
              };
              break;
            }
            // 前端使用的是cname
            user.cname = user[0].name;
            element.user = user;
            break;
          }
          default:
            break;
        }
      }
      return listdata;
    }
    return listdata;
  }


  // 通知记录 列表数据
  async getMyMessage(query, page) {
    try {
      // console.log(2222222222, query, page);
      // const userInfo = ctx.session.superUserInfo;
      const listData = await this.ctx.model.MessageNotification.aggregate([
        { $match: query },
        // { $match: { 'authorID.regAdd': { $all: userInfo.regAdd } } },
        { $sort: { date: -1 } },
        { $skip: (page.current - 1) * page.pageSize },
        { $limit: page.pageSize },
        {
          $lookup: {
            from: 'superusers',
            localField: 'authorID',
            foreignField: '_id',
            as: 'authorID',
          },
        },
      ]);
      const totalLength = await this.ctx.model.MessageNotification.count(query);

      return {
        docs: listData,
        pageInfo: { ...page, totalItems: totalLength },
      };
    } catch (error) {
      console.log(error);
    }
  }
  // 短信提醒
  async sendSms(title, employer, supervision, phone, PhoneNumbers, templateCode = '', SignName = '', code = '') {
    // 尊敬的${employer}，您好。现发送关于${title}通知，请登录合肥市职业健康智慧管家平台及时查看处理。${supervision}，联系电话：${phone}
    try {
      const TemplateParam = {
        title, // 通知标题
        employer, // '用人单位',
        supervision, // '行政部门',
        phone, // '行政部门联系电话',
        code, // 通知id
      };
      const { data } = await this.ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'messageNotification',
          TemplateParam: JSON.stringify(TemplateParam),
          PhoneNumbers,
          templateCode,
          SignName,
          code,
        },
      });
      if (data.Message === 'OK') return true;
      this.ctx.auditLog(`短信发送失败 - ${PhoneNumbers}`, JSON.stringify(data), 'error');
    } catch (err) {
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }
  }
  // 获取短信模板列表
  async getSmsTemplateList(params) {
    try {
      const res = await this.ctx.curl(`${this.config.iServiceHost}/api/QuerySmsTemplateList`, {
        method: 'GET',
        dataType: 'json', // 返回的数据类型
        data: params,
      });
      if (res.status === 200) return res.data;
      throw {
        message: res.data || '获取短信模板列表失败',
      };
    } catch (err) {
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }
  }
  // 增加短信模板
  async addSmsTemplate(params) {
    try {
      const res = await this.ctx.curl(`${this.config.iServiceHost}/api/AddSmsTemplate`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: params,
      });
      if (res.status === 200) return res.data;
      throw {
        message: res.data || '增加短信模板失败',
      };
    } catch (err) {
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }
  }
  // 删除短信模板
  async deleteSmsTemplate(params) {
    try {
      const res = await this.ctx.curl(`${this.config.iServiceHost}/api/DeleteSmsTemplate`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          TemplateCode: params.TemplateCode,
        },
      });
      console.log('res', res);
      if (res.status === 200) {
        return {
          status: 200,
          data: res.data,
        };
      }
      throw {
        message: res.data.message || '删除短信模板失败',
      };
    } catch (err) {
      throw new Error(`删除短信模板失败:${err.message}`);
    }
  }
  // 修改短信模板 ModifySmsTemplate
  async modifySmsTemplate(params) {
    try {
      const res = await this.ctx.curl(`${this.config.iServiceHost}/api/ModifySmsTemplate`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: params,
      });
      if (res.status === 200) return res.data;
      throw {
        message: res.data.message || '修改短信模板失败',
      };
    } catch (err) {
      throw new Error(`${err.message}`);
    }
  }
  // 获取短信签名列表
  async getSmsSignList(params) {
    try {
      const res = await this.ctx.curl(`${this.config.iServiceHost}/api/QuerySmsSignList`, {
        method: 'GET',
        dataType: 'json', // 返回的数据类型
        data: params,
      });
      if (res.status === 200) return res.data;
      throw {
        message: res.data || '获取短信签名列表失败',
      };
    } catch (err) {
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }
  }

}

module.exports = MessageNotificationService;
