const { Service } = require('egg');

/**
 * Service for handling Server-Sent Events (SSE).
 */
class SseService extends Service {
  sendToClient(clientId, message) {
    console.log('🚀 ~ sendToClient ~ clientId, message', clientId, message);
    const client = this.app.sseClients.get(clientId);
    if (client) {
      client.write(`id: ${JSON.stringify(message)}\n`);
      client.write(`data: ${JSON.stringify(message)}\n\n`);
    }
  }
}

module.exports = SseService;
