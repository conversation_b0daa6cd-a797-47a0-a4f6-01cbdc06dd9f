/*
 * @Author: htt
 * @Date: 2022-06-22
 * @description 数据库操作以及统一处理操作日志
 * 具体操作可参考文档
 *      mongoose：https://mongoosejs.com/docs/api/model.html
 *      mongodb: https://www.mongodb.com/docs/manual/reference/method/js-collection/
 */
const Service = require('egg').Service;

class DaoService extends Service {
  /**
   * <AUTHOR>
   * @description 查询多条数据
   * @param {String} Model model名称 注意首字母大写
   * @param {Object} query 查询条件
   * @param {Object} projection Optional. Specifies the fields to return in the documents that match the query filter. To return all fields in the matching documents, omit this parameter.
   * @param {Object} options 其他选项
   * @return {Array} 返回数组
   */
  async find(Model, query, projection, options) {
    return await this.ctx.model[Model].find(query, projection, options);
  }

  /**
   * <AUTHOR>
   * @description 查询一条数据
   * @param {String} Model model名称 注意首字母大写
   * @param {Object} query 查询条件
   * @param {Object} projection Optional. Specifies the fields to return in the documents that match the query filter. To return all fields in the matching documents, omit this parameter.
   * @param {Object} options 其他选项
   * @return {Object|null} 返回
   */
  async findOne(Model, query, projection, options) {
    return await this.ctx.model[Model].findOne(query, projection, options);
  }

  /**
   * <AUTHOR>
   * @description 计算集合或视图中数据的聚合值。
   * @param {String} Model model名称 注意首字母大写
   * @param {Array} pipeline 管道操作
//    * @param {document} options 可选的。aggregate()传递给aggregate命令的附加选项。
   * @return {Array} 返回
   */
  async aggregate(Model, pipeline) {
    return await this.ctx.model[Model].aggregate(pipeline);
  }

  /**
   * <AUTHOR>
   * @description find()返回与集合或视图的查询匹配的文档计数
   * @param {String} Model model名称 注意首字母大写
   * @param {Object} query 查询条件
//    * @param {document} options 可选的。用于修改计数的额外选项。
   * @return {Number} 返回数量
   */
  async count(Model, query) {
    return await this.ctx.model[Model].find(query).count();
  }

  /**
   * <AUTHOR>
   * @description 删除一个文档
   * @param {String} Model model名称 注意首字母大写
   * @param {Object} filter 使用查询运算符指定删除条件。
   * @param {String} supplementaryNotes 补充说明
   * @return {Object} 返回 { n: Number, ok: Number, deletedCount: Number }
   */
  async deleteOne(Model, filter, supplementaryNotes) {
    const deleteData = await this.findOne(Model, filter);
    const res = await this.ctx.model[Model].deleteOne(filter);
    if (res.deletedCount === 1) { // 添加操作记录
      this.ctx.service.operateLog.create(Model, {
        optType: '删除',
        optIds: [ deleteData._id ],
        filterJson: JSON.stringify(filter),
        deleteData: [ deleteData ],
        supplementaryNotes,
      });
    }
    return res;
  }

  /**
   * <AUTHOR>
   * @description 删除多条文档
   * @param {String} Model model名称 注意首字母大写
   * @param {Object} filter 使用查询运算符指定删除条件。
   * @param {String} supplementaryNotes 补充说明
   * @return {Object} 返回 { n: Number, ok: Number, deletedCount: Number }
   */
  async deleteMany(Model, filter, supplementaryNotes) {
    const deleteData = await this.find(Model, filter);
    const res = await this.ctx.model[Model].deleteMany(filter);
    if (res.deletedCount > 0) { // 添加操作记录
      this.ctx.service.operateLog.create(Model, {
        optType: '删除',
        optIds: deleteData.map(item => item._id),
        filterJson: JSON.stringify(filter),
        deleteData,
        supplementaryNotes,
      });
    }
    return res;
  }

  /**
   * <AUTHOR>
   * @description 查找单个文档并更新
   * @param {String} Model model名称 注意首字母大写
   * @param {Object} filter 更新的选择标准。可以使用与方法中相同的查询选择器find()。
   * @param {Object} update 更新文档或从 MongoDB 4.2 开始的 聚合管道。
   * @param {Object} options 选项
   * @param {String} supplementaryNotes 补充说明
   * @return {Object} 返回文档
   */
  async findOneAndUpdate(Model, filter, update, options, supplementaryNotes) {
    const res = await this.ctx.model[Model].findOneAndUpdate(filter, update, options);
    const updateData = await this.findOne(Model, filter);
    this.ctx.service.operateLog.create(Model, {
      optType: '更新',
      optIds: [ updateData._id ],
      filterJson: JSON.stringify(filter),
      supplementaryNotes,
      updateInfoJson: JSON.stringify(update),
    });
    return res;
  }

  /**
   * <AUTHOR>
   * @description 根据过滤器更新集合中的单个文档。
   * @param {String} Model model名称 注意首字母大写
   * @param {Object} filter 更新的选择标准。可以使用与方法中相同的查询选择器find()。
   * @param {Object} update 更新文档或从 MongoDB 4.2 开始的 聚合管道。
   * @param {Object} options 选项
   * @param {String} supplementaryNotes 补充说明
   * @return {Object} 返回 {n:Number,nModified:Number,ok:Number}
   */
  async updateOne(Model, filter, update, options, supplementaryNotes) {
    const res = await this.ctx.model[Model].updateOne(filter, update, options);
    if (res.nModified === 1) {
      const updateData = await this.findOne(Model, filter);
      this.ctx.service.operateLog.create(Model, {
        optType: '更新',
        optIds: [ updateData._id ],
        filterJson: JSON.stringify(filter),
        supplementaryNotes,
        updateInfoJson: JSON.stringify(update),
      });
    }
    return res;
  }

  /**
   * <AUTHOR>
   * @description 根据过滤器更新集合中的多个文档。
   * @param {String} Model model名称 注意首字母大写
   * @param {Object} filter 更新的选择标准。可以使用与方法中相同的查询选择器find()。
   * @param {Object} update 更新文档或从 MongoDB 4.2 开始的 聚合管道。
   * @param {Object} options 选项
   * @param {String} supplementaryNotes 补充说明
   * @return {Object} 返回 {n:Number,nModified:Number,ok:Number}
   */
  async updateMany(Model, filter, update, options, supplementaryNotes) {
    const res = await this.ctx.model[Model].updateMany(filter, update, options);
    if (res.nModified > 0) {
      const updateData = await this.find(Model, filter);
      this.ctx.service.operateLog.create(Model, {
        optType: '更新',
        optIds: updateData.map(item => item._id),
        filterJson: JSON.stringify(filter),
        supplementaryNotes,
        updateInfoJson: JSON.stringify(update),
      });
    }
    return res;
  }

  /**
   * <AUTHOR>
   * @description 将一个或多个文档保存到数据库
   * @param {String} Model model名称 注意首字母大写
   * @param {Array|Object} docs 要插入的文档
   * @param {String} supplementaryNotes 补充说明
   * @return {Array|Object} 返回创建的文档
   */
  async create(Model, docs, supplementaryNotes) {
    const res = await this.ctx.model[Model].create(docs);
    if (Model === 'OperateLog') return;
    this.ctx.service.operateLog.create(Model, {
      optType: '创建',
      optIds: res instanceof Array ? res.map(item => item._id) : [ res._id ],
      supplementaryNotes,
    });
    return res;
  }
}

module.exports = DaoService;
