const Service = require('egg').Service;
const moment = require('moment');

class CertificateService extends Service {
  async getCertificateDetails(certificateNumber) {
    // 根据证书编号查询证书的详细信息
    const { ctx } = this;
    const certificate = await ctx.model.Certificate.aggregate([
      { $match: { number: certificateNumber } },
      { $project: { _id: 0, 'winner.name': 1, trainingType: 1, number: 1, 'winner.companyName': 1, unit: 1, issuanceTime: 1, img: 1 } },
    ]);
    // console.log('证书', certificate);
    if (certificate.length === 0) {
      // 如果找不到对应的证书，可以抛出自定义异常或返回特定的错误信息
      throw new Error('Certificate not found');
    }
    if (certificate[0].trainingType === 1) {
      certificate[0].trainingType = '管理员培训';
    } else {
      certificate[0].trainingType = '自主培训';
    }
    if (certificate[0].issuanceTime) {
      certificate[0].issuanceTime = moment(certificate[0].issuanceTime).format('YYYY-MM-DD');
    }
    // 返回证书的详细信息
    return certificate;
  }
}

module.exports = CertificateService;
