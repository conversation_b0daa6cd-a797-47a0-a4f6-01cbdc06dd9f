const Service = require('egg').Service;

class UserService extends Service {

  // 给adminUser绑定userId
  async bindUserToAdminUser(_adminUserId) {
    if (!_adminUserId) return;
    const { ctx } = this;
    // 1、获取adminUser详情
    const adminUserInfo = await this.findAdminUserDetail(_adminUserId);
    const { phoneNum, userId, adminUserId } = adminUserInfo;
    // 2. 判断adminUser是否已经绑定或者没有手机号
    if (!phoneNum || userId) return;
    // 3、根据手机号查询user表
    const usersList = await ctx.model.User.find({ phoneNum }, { id: 1 });
    let user;
    if (usersList.length === 0) {
      // 4、没有user就创建一个
      // user = await ctx.model.User.create({
      //   phoneNum,
      //   userName: phoneNum,
      //   idNo: adminUserInfo.IDcard || '',
      // });
      user = await ctx.service.db.create('User', {
        phoneNum,
        userName: phoneNum,
        idNo: adminUserInfo.IDcard || '',
      });
      if (user && user._id) {
        this.ctx.auditLog('创建user用户', `用户${adminUserId}创建user用户${userId}成功。`);
      } else {
        this.ctx.auditLog('创建user用户失败', `用户${adminUserId}创建user用户${userId}失败。`, 'error');
      }

    } else if (usersList.length === 1) {
      user = usersList[0];

    } else {
      this.ctx.auditLog('创建user用户失败', `手机号${phoneNum}在users表中匹配到了多条数据。`, 'error');
    }

    // 5、满足条件，开始给adminUser绑定userId
    if (user && user._id) {
      // await ctx.model.AdminUser.updateOne({ _id: adminUserId }, { userId: user._id }).
      await ctx.service.db.updateOne('AdminUser', { _id: adminUserId }, { userId: user._id }).
        then(res => {
          console.log('给adminUser绑定userId: ', res);
          if (res.ok === 1) {
            ctx.auditLog('给adminUser绑定userId', `adminUser用户${adminUserId}绑定user用户id为${user._id}成功。`);
            return 'ok';
          }
        });
    }
  }

  async findAdminUserDetail(_adminUserId) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';
    const adminUserId = _adminUserId || '';
    const adminUser = await ctx.model.AdminUser.findOne({
      _id: adminUserId,
    }, {
      employees: 1,
      userId: 1,
      phoneNum: 1,
      IDcard: 1,
    });
    const employees = adminUser.employees || [];
    let employeeId = '';
    let employeeDeatil = null;
    if (employees.length) {
      const data = await ctx.model.Employee.find({
        _id: { $in: employees },
        EnterpriseID,
        enable: true,
      });
      if (data.length === 1 && data[0]._id) {
        employeeDeatil = data[0];
        employeeId = employeeDeatil._id;
      }
    }
    return {
      adminUserId,
      employeesId: employeeId || '',
      userId: adminUser.userId || '',
      phoneNum: adminUser.phoneNum || '',
      IDcard: adminUser.IDcard || '',
      _id: adminUserId,
    };
  }

}

module.exports = UserService;
