
const Service = require('egg').Service;


// const _ = require('lodash');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require('./general');


class DistrictService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.District, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.District, params);
  }

  async create(payload) {
    // return _create(this.ctx.model.District, payload);
    return _create('District', payload, this.ctx);
  }

  async removes(res, values, key = '_id') {
    // return _removes(res, this.ctx.model.District, values, key);
    return _removes(res, 'District', values, key);
  }

  async safeDelete(res, values) {
    // return _safeDelete(res, this.ctx.model.District, values);
    return _safeDelete(res, 'District', values);
  }

  async update(res, _id, payload) {
    // return _update(res, this.ctx.model.District, _id, payload);
    return _update(res, this.ctx.model.District, 'District', _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.District, params);
  }

  async findList(query) {
    return await this.ctx.model.District.find(query, { name: 1, area_code: 1, _id: 0 });
  }


  async list(query = {}, select, removeTrailingZeros = false) {
    const list = await this.ctx.model.District.find(query, select);
    if (!list || list.length < 1) {
      return [];
    }
    if (removeTrailingZeros) {
      const level = list[0].level;
      if (!level) {
        throw new Error('level is required');
      }
      const len = +level === 3 ? 9 : (+level + 1) * 2;
      return list.map(item => {
        item.area_code = item.area_code.slice(0, len);
        return item;
      });
    }
    return list;
  }


  // 递归查询area_code的所有父级name
  async findParents(area_code, res = []) {
    const cur = await this.ctx.model.District.findOne({ area_code }, { name: 1, parent_code: 1 });
    if (cur && cur.name) {
      res.unshift(cur.name);
      if (cur.parent_code) {
        return this.findParents(cur.parent_code, res);
      }
    }
    return res; // 返回结果数组， eg: ['河北省', '石家庄市', '长安区']
  }


}

module.exports = DistrictService;
