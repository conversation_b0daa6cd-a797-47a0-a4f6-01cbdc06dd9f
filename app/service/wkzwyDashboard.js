const Service = require('egg').Service;
class WkzwyDashboardService extends Service {

  async getHealthCheckData(adname, year) {
    const { ctx } = this;
    const query = {
      reportStatus: true,
      checkDate: {
        $lt: new Date(`${+year + 1}-1-1`),
        $gte: new Date(`${year}-1-1`),
      },
      workAddress: {
        $elemMatch: { districts: { $all: adname } },
      },
    };

    const HealthcheckList = await ctx.model.Healthcheck.find(query, {
      _id: 1,
      // 体检机构
      physicalExaminationOrgID: 1,
      organization: 1,
      // 体检企业
      EnterpriseID: 1,
      enterpriseName: 1,
      // 体检情况统计
      actuallNum: 1,
      suspected: 1,
      forbid: 1,
      re_examination: 1,
      otherDisease: 1,
      normal: 1,
      // 体检日期（用于月度统计）
      checkDate: 1,
      // 用于区域数据统计
      workAddress: 1,
    });

    // 体检情况统计
    const statistics = {
      actuallNum: 0,
      suspected: 0,
      forbid: 0,
      re_examination: 0,
      otherDisease: 0,
      normal: 0,

      ordinary: 0, // 普通体检
    };

    // 企业体检数量 （企业的 体检项目数）
    const enterpriseExamNum = {
      // '企业id' : { name:企业名称, id:企业id, num:体检项目数 }
    };

    // 体检机构数据
    const examOrgData = {
      // '机构id' : {
      // _id:机构id, name:机构名称,
      // enterpriseList: 企业id列表，set结构存储当前机构体检的企业数 用于计算体检企业数量排行
      // projectNum: 体检项目数, 用于计算体检批次排行
      // personNum: 体检人次, 用于计算体检人次排行
      // }
    };

    // 体检企业列表
    const enterpriseList = new Set();

    // 月份体检次数
    const monthlyTimes = new Array(12).fill(0);

    for (let i = 0; i < HealthcheckList.length; i++) {
      const item = HealthcheckList[i];
      // 统计体检情况
      statistics.actuallNum += +item.actuallNum;
      statistics.suspected += +item.suspected;
      statistics.forbid += +item.forbid;
      statistics.re_examination += +item.re_examination;
      statistics.otherDisease += +item.otherDisease;
      statistics.normal += +item.normal;

      // 机构ID
      const orgID = item.physicalExaminationOrgID || item.organization;
      // 企业id
      const enterpriseID = item.EnterpriseID;

      // 体检机构数据
      if (orgID) {
        if (!examOrgData[orgID]) {
          // 获取体检机构简称
          const org = await ctx.model.PhysicalExamOrg.findOne({ _id: item.physicalExaminationOrgID }, { name: 1, shortName: 1 });
          const name = org && org.name ? (org.shortName || org.name) : (item.organization || '');
          examOrgData[orgID] = {
            _id: item.physicalExaminationOrgID,
            name,
            enterpriseList: new Set(),
            projectNum: 0,
            personNum: 0,
          };
        }
        examOrgData[orgID].enterpriseList.add(enterpriseID);
        examOrgData[orgID].projectNum += 1;
        examOrgData[orgID].personNum += item.actuallNum;
      }

      // 企业体检数量
      if (enterpriseID) {
        if (!enterpriseExamNum[enterpriseID]) {
          const name = item.enterpriseName;
          enterpriseExamNum[enterpriseID] = { name, num: 0, _id: enterpriseID };
        }
        enterpriseExamNum[enterpriseID].num += 1;
        enterpriseList.add(enterpriseID);
      }

      // 月份体检次数
      const index = new Date(item.checkDate).getMonth();
      monthlyTimes[index] += 1;
    }

    // 体检批次排行
    const examBatchListTemp = Object.values(examOrgData).map(item => { return { _id: item._id, name: item.name, num: item.projectNum }; });
    const examBatchList = examBatchListTemp.sort((a, b) => b.num - a.num);
    // 体检企业数量排行
    const examEnterprisesListTemp = Object.values(examOrgData).map(item => { return { _id: item._id, name: item.name, num: item.enterpriseList.size }; });
    const examEnterprisesList = examEnterprisesListTemp.sort((a, b) => b.num - a.num);
    // 体检人次排行
    const examPersonTimesListTemp = Object.values(examOrgData).map(item => { return { _id: item._id, name: item.name, num: item.personNum }; });
    const examPersonTimesList = examPersonTimesListTemp.sort((a, b) => b.num - a.num);

    // 企业体检排行
    const enterpriseExamList = Object.values(enterpriseExamNum).sort((a, b) => b.num - a.num);

    // 体检机构数 （机构数）
    const orgNum = Object.keys(examOrgData).length;
    // 体检企业数 （企业数）
    const enterpriseNum = enterpriseList.size;
    // 企业体检数 （企业 体检项目数）
    const enterpriseCheckNum = HealthcheckList.length;

    // 月份体检人数
    const monthlyPersons = new Array(12).fill(0);
    const SuspectQuery = {
      checkDate: {
        $lt: new Date(`${+year + 1}-1-1`),
        $gte: new Date(`${year}-1-1`),
      },
    };
    const ids = HealthcheckList.map(item => item._id);
    SuspectQuery.batch = { $in: ids };
    const SuspectList = await ctx.model.Suspect.find(SuspectQuery, {
      name: 1,
      IDCard: 1,
      // employeeId: 1,
      checkDate: 1,
    });
    console.log(999999, SuspectList);
    SuspectList.forEach(ele => {
      if (ele.checkDate) {
        const index = new Date(ele.checkDate).getMonth();
        if (!monthlyPersons[index]) {
          monthlyPersons[index] = new Set();
        }
        const msgStr = `${ele.name}(${ele.IDCard})`;
        monthlyPersons[index].add(msgStr);
      }
    });
    const monthlyPersonNum = monthlyPersons.map(item => {
      return item.size ? item.size : item;
    });

    return {
      statistics, // 体检情况统计
      monthlyTimes, // 月份体检次数（批次）
      monthlyPersonNum, // 月份体检人数

      examBatchList, // 体检批次排行
      examEnterprisesList, // 体检企业数量排行
      examPersonTimesList, // 体检人次排行

      enterpriseExamList, // 企业体检排行

      orgNum, // 体检机构数
      enterpriseNum, // 体检企业数
      enterpriseCheckNum, // 企业体检数

      HealthcheckList, // 体检报告列表
    };
  }

  async getReservationData(adname, year) {
    const { ctx } = this;
    const orgQuery = {
      regAddr: { $all: adname },
    };

    const physicalExamOrgList = await ctx.model.PhysicalExamOrg.find(orgQuery).select('_id');

    const appointmentQuery = {
      startTime: {
        $lt: new Date(`${+year + 1}-1-1`),
        $gte: new Date(`${year}-1-1`),
      },
      physicalExamOrgId: { $in: physicalExamOrgList.map(item => item._id) },
    };
    const appointmentList = await ctx.model.HealthCheckAppointment.find(appointmentQuery);
    return appointmentList;
  }

  // 体检机构列表
  async getPhysicalExamOrgList(adname) {
    const { ctx } = this;
    const query = {
      regAddr: { $all: adname },
    };
    const physicalExamOrgList = await ctx.model.PhysicalExamOrg.find(query, { _id: 1, regAddr: 1 });
    return physicalExamOrgList;
  }

  // 体检预约列表
  async getHealthCheckAppointmentList(year) {
    const { ctx } = this;
    const query = {
      startTime: {
        $lt: new Date(`${+year + 1}-1-1`),
        $gte: new Date(`${year}-1-1`),
      },
    };
    const appointmentList = await ctx.model.HealthCheckAppointment.find(query, { _id: 1, physicalExamOrgId: 1 });
    return appointmentList;
  }
}

module.exports = WkzwyDashboardService;
