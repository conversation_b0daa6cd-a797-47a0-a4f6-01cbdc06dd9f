
const Service = require('egg').Service;


// const _ = require('lodash');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require('./general');


class AdminGroupService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.AdminGroup, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.AdminGroup, params);
  }

  async create(payload) {
    // return _create(this.ctx.model.AdminGroup, payload);
    return _create('AdminGroup', payload, this.ctx);
  }

  async removes(res, values, key = '_id') {
    // return _removes(res, this.ctx.model.AdminGroup, values, key);
    return _removes(res, 'AdminGroup', values, key);
  }

  async safeDelete(res, values) {
    // return _safeDelete(res, this.ctx.model.AdminGroup, values);
    return _safeDelete(res, 'AdminGroup', values);
  }

  async update(res, _id, payload) {
    // return _update(res, this.ctx.model.AdminGroup, _id, payload);
    return _update(res, this.ctx.model.AdminGroup, 'AdminGroup', _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.AdminGroup, params);
  }
  // 获取页脚信息
  async getEnvConfig() {
    const { ctx, config } = this;
    try {
      const envConfig = await ctx.curl(`${config.iServiceHost}/api/envConfig`, {
        dataType: 'json',
        data: { host: ctx.host },
      });
      return {
        recordNumber: envConfig.data.data.recordNumber || '',
        footerContent: envConfig.data.data.footerContent || [],
      };
    } catch (err) {
      return {
        recordNumber: '',
        footerContent: [],
      };
    }
  }
  // 获取加密公钥
  async getPublicKey() {
    const { ctx } = this;
    const publicKeyRes = await ctx.curl(`${this.config.iServiceHost}/crypto/getPublicKey`, {
      method: 'GET',
      dataType: 'json', // 返回的数据类型
      data: {
      },
    });
    if (publicKeyRes.status !== 200) {
      throw new Error('获取公钥失败');
    }
    return { publicKey: publicKeyRes.data.data.publicKey };
  }

}

module.exports = AdminGroupService;
