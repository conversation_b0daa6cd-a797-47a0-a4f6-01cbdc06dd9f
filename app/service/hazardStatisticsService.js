/* eslint-disable no-dupe-keys */

const axios = require('axios');
const Service = require('egg').Service;
const moment = require('moment');

class HazardStatisticsService extends Service {
  /**
   * 计算并更新统计数据
   * @param {Object} options - 计算选项
   * @param {String} options.timeFrame - 时间维度 (daily, weekly, monthly, quarterly, yearly, all)
   * @param {Object} options.filter - 筛选条件
   * @return {Object} - 更新后的统计数据
   */
  async calculateAndUpdateStatistics(options = {}) {
    const { timeFrame = 'all', filter = {} } = options;
    const { ctx } = this;

    // 1. 设置查询时间点
    const timePoint = this.getTimePoint(timeFrame);

    // 2. 构建基础查询条件
    const baseQuery = this.buildBaseQuery(filter);

    try {
      // 3. 计算各项统计数据
      const [
        enterpriseCount,
        hazardFactorsStats,
        industryStats,
        regionStats,
        mapData,
      ] = await Promise.all([
        this.calculateEnterpriseCount(baseQuery),
        this.calculateHazardFactorsStats(baseQuery),
        this.calculateIndustryStats(baseQuery),
        this.calculateRegionStats(baseQuery),
        this.generateMapData(baseQuery),
      ]);

      // 4. 保存或更新统计结果
      const statsDoc = await ctx.model.HazardStatistics.findOneAndUpdate(
        {
          timeFrame,
          timePoint,
          'filterConditions.regionCode': filter.regionCode || null,
          'filterConditions.industryCode': filter.industryCode || null,
        },
        {
          enterpriseCount,
          hazardFactorsStats,
          industryStats,
          regionStats,
          mapData,
          filterConditions: {
            hazardFactors: filter.hazardFactors || [],
            regionCode: filter.regionCode || null,
            industryCode: filter.industryCode || null,
            dateRange: {
              start: filter.startDate || null,
              end: filter.endDate || null,
            },
          },
          updatedAt: new Date(),
          lastCalculationTime: new Date(),
        },
        {
          new: true,
          upsert: true, // 如果不存在则创建
          setDefaultsOnInsert: true,
        }
      );

      return statsDoc;
    } catch (error) {
      this.ctx.logger.error('计算统计数据失败:', error);
      throw error;
    }
  }

  /**
   * 根据时间维度获取时间点
   * @param {String} timeFrame - 时间维度 (daily, weekly, monthly, quarterly, yearly, all)
   * @return {Date} - 时间点
   */
  getTimePoint(timeFrame) {
    const now = moment();

    switch (timeFrame) {
      case 'daily':
        return now.startOf('day').toDate();
      case 'weekly':
        return now.startOf('week').toDate();
      case 'monthly':
        return now.startOf('month').toDate();
      case 'quarterly':
        return now.startOf('quarter').toDate();
      case 'yearly':
        return now.startOf('year').toDate();
      case 'all':
      default:
        return new Date(2000, 0, 1); // 固定参考点
    }
  }

  /**
   * 构建基础查询条件
   * @param {Object} filter - 筛选条件
   * @return {Object} - 查询条件
   */
  buildBaseQuery(filter) {
    const query = {
      isDelete: false, // 非删除状态
      productionStatus: '2', // 正常生产的企业
    };

    // 添加时间范围条件
    if (filter.startDate && filter.endDate) {
      query.reportTime = {
        $gte: new Date(filter.startDate),
        $lte: new Date(filter.endDate),
      };
    }

    // 添加区域筛选
    if (filter.regionCode) {
      query['workAddress.districts'] = { $elemMatch: { area_code: filter.regionCode } };
    }

    // 添加行业筛选
    if (filter.industryCode) {
      // 根据行业编码构建查询条件
      query.industryCategory = { $elemMatch: { $elemMatch: { code: filter.industryCode } } };
    }

    return query;
  }

  /**
   * 计算企业数量统计
   * @param {Object} baseQuery - 基础查询条件
   * @return {Object} - 企业数量统计结果
   */
  async calculateEnterpriseCount(baseQuery) {
    const { ctx } = this;

    // 总企业数
    const total = await ctx.model.Adminorg.countDocuments(baseQuery);

    // 已检查企业数(有报告时间的企业)
    const checked = await ctx.model.Adminorg.countDocuments({
      ...baseQuery,
      reportTime: { $exists: true, $ne: null },
    });

    // 超标企业数(任一检测结果超标)
    const exceedingQuery = {
      ...baseQuery,
      $or: [
        { 'checkResult.dust.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.chemical.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.noise.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.radiation.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.biological.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.heat.exceed': { $exists: true, $ne: '0' } },
      ],
    };

    const exceeding = await ctx.model.Adminorg.countDocuments(exceedingQuery);

    return { total, checked, exceeding };
  }

  /**
   * 计算危害因素统计
   * @param {Object} baseQuery - 基础查询条件
   * @return {Array} - 危害因素统计结果
   */
  async calculateHazardFactorsStats(baseQuery) {
    const { ctx } = this;
    const stats = [];

    // 获取所有危害因素类型
    const hazardTypes = [
      { type: 'dust', name: '粉尘' },
      { type: 'chemical', name: '化学物质' },
      { type: 'noise', name: '噪声' },
      { type: 'radiation', name: '辐射' },
      { type: 'biological', name: '生物因素' },
      { type: 'heat', name: '高温' },
    ];

    // 逐一计算各类型统计
    for (const hazard of hazardTypes) {
      // 查询具有该危害因素的企业
      const hazardQuery = {
        ...baseQuery,
        [`checkResult.${hazard.type}.point`]: { $exists: true, $ne: null },
      };

      const enterprises = await ctx.model.Adminorg.find(
        hazardQuery,
        `_id checkResult.${hazard.type}`
      );

      if (enterprises.length === 0) {
        continue; // 跳过没有数据的危害因素
      }

      // 计算统计数据
      let total = 0;
      let exceed = 0;

      enterprises.forEach(enterprise => {
        const checkResult = enterprise.checkResult[hazard.type];
        if (checkResult) {
          total += parseInt(checkResult.point || 0, 10);
          exceed += parseInt(checkResult.exceed || 0, 10);
        }
      });

      // 计算超标率
      const exceedRate = total > 0 ? (exceed / total) * 100 : 0;

      stats.push({
        type: hazard.type,
        name: hazard.name,
        total,
        exceed,
        exceedRate: parseFloat(exceedRate.toFixed(2)),
        affectedEnterprises: enterprises.length,
        unit: '',
      });
    }

    return stats;
  }

  /**
   * 计算行业分布统计
   * @param {Object} baseQuery - 基础查询条件
   * @return {Array} - 行业分布统计结果
   */
  async calculateIndustryStats(baseQuery) {
    const { ctx } = this;

    // 获取行业分布聚合
    const industryAggregation = await ctx.model.Adminorg.aggregate([
      { $match: baseQuery },
      { $unwind: '$industryCategory' },
      {
        $project: {
          industryCategory: 1,
        },
      },
      {
        $group: {
          _id: {
            $arrayElemAt: [ '$industryCategory', 0 ],
          },
          count: { $sum: 1 },
          enterprises: { $push: '$_id' },
        },
      },
      { $sort: { count: -1 } },
      {
        $lookup: {
          from: 'industryCategory',
          localField: '_id',
          foreignField: 'value',
          as: 'result',
        },
      },
      {
        $addFields: {
          label: { $ifNull: [{ $arrayElemAt: [ '$result.label', 0 ] }, '其他' ] },
        },
      },
      {
        $project: {
          _id: 0,
          name: '$label',
          code: '$_id',
          count: 1,
          enterprises: 1,
        },
      },
    ]);
    // 转换为所需格式
    const stats = [];
    let totalEnterprises = 0;

    // 计算企业总数
    for (const industry of industryAggregation) {
      totalEnterprises += industry.count;
    }

    // 生成行业统计
    for (let i = 0; i < industryAggregation.length; i++) {
      const industry = industryAggregation[i];

      // 计算超标企业数
      const exceedQuery = {
        ...baseQuery,
        _id: { $in: industry.enterprises },
        $or: [
          { 'checkResult.dust.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.chemical.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.noise.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.radiation.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.biological.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.heat.exceed': { $exists: true, $ne: '0' } },
        ],
      };

      const exceedCount = await ctx.model.Adminorg.countDocuments(exceedQuery);

      stats.push({
        code: industry.code,
        name: industry.name,
        level: 1, // 默认为一级，可根据编码规则调整
        parentCode: '', // 根据实际层级填充
        count: industry.count,
        exceedCount,
        percentage: totalEnterprises > 0 ?
          parseFloat(((industry.count / totalEnterprises) * 100).toFixed(2)) : 0,
        ranking: i + 1,
      });
    }

    return stats;
  }

  /**
   * 计算区域分布统计
   * @param {Object} baseQuery - 基础查询条件
   * @return {Array} - 区域分布统计结果
   */
  async calculateRegionStats(baseQuery) {
    const { ctx } = this;

    // 获取区域分布聚合
    const regionAggregation = await ctx.model.Adminorg.aggregate([
      { $match: baseQuery },
      { $match: { districtRegAdd: { $exists: true, $ne: null } } },
      {
        $project: {
          regionCode: {
            $cond: {
              if: { $gte: [{ $size: '$districtRegAdd' }, 2 ] },
              then: { $arrayElemAt: [ '$districtRegAdd', 1 ] },
              else: '其他',
            },
          },
          _id: 1,
        },
      },
      {
        $group: {
          _id: '$regionCode',
          count: { $sum: 1 },
          enterprises: { $push: '$_id' },
        },
      },
      { $sort: { count: -1 } },
      {
        $project: {
          _id: 0,
          name: '$_id',
          count: 1,
          enterprises: 1,
        },
      },
    ]);

    // 转换为所需格式
    const stats = [];
    let totalEnterprises = 0;

    // 计算企业总数
    for (const region of regionAggregation) {
      totalEnterprises += region.count;
    }

    // 生成区域统计
    for (let i = 0; i < regionAggregation.length; i++) {
      const region = regionAggregation[i];

      // 计算超标企业数
      const exceedQuery = {
        ...baseQuery,
        _id: { $in: region.enterprises },
        $or: [
          { 'checkResult.dust.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.chemical.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.noise.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.radiation.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.biological.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.heat.exceed': { $exists: true, $ne: '0' } },
        ],
      };

      const exceedCount = await ctx.model.Adminorg.countDocuments(exceedQuery);

      stats.push({
        code: region.code,
        name: region.name,
        level: this.getRegionLevel(region.code), // 省/市/区县级别
        parentCode: this.getRegionParentCode(region.code), // 父级区域编码
        count: region.count,
        exceedCount,
        percentage: totalEnterprises > 0 ?
          parseFloat(((region.count / totalEnterprises) * 100).toFixed(2)) : 0,
        ranking: i + 1,
      });
    }

    return stats;
  }

  /**
   * 根据区域代码判断区域级别
   * @param {String} areaCode - 区域编码
   * @return {Number} - 区域级别(1:省级, 2:市级, 3:区县级)
   */
  getRegionLevel(areaCode) {
    if (!areaCode) return 0;

    const codeStr = areaCode.toString();

    if (codeStr.endsWith('0000')) return 1; // 省级
    if (codeStr.endsWith('00')) return 2; // 市级
    return 3; // 区县级
  }

  /**
   * 获取区域父级编码
   * @param {String} areaCode - 区域编码
   * @return {String} - 父级区域编码
   */
  getRegionParentCode(areaCode) {
    if (!areaCode) return '';

    const codeStr = areaCode.toString();
    const level = this.getRegionLevel(areaCode);

    if (level === 1) return ''; // 省级无父级

    if (level === 2) { // 市级，父级为省级
      return codeStr.substring(0, 2) + '0000';
    }

    // 区县级，父级为市级
    return codeStr.substring(0, 4) + '00';
  }

  /**
   * 生成地图数据
   * @param {Object} baseQuery - 基础查询条件
   * @return {Array} - 地图标记点数据
   */
  async generateMapData(baseQuery) {
    const { ctx } = this;

    // 查询所有满足条件的企业
    const enterprises = await ctx.model.Adminorg.find(
      {
        ...baseQuery,
        'workAddress.point': { $exists: true, $ne: [] },
      },
      'cname industryCategory workAddress checkResult'
    );// 限制数量，避免数据过大

    const mapData = [];

    for (const enterprise of enterprises) {
      // 处理每个工作地址
      for (const workAddr of enterprise.workAddress || []) {
        if (!workAddr.point || workAddr.point.length < 2) continue;

        // 提取危害因素
        const hazardFactors = [];
        const checkResult = enterprise.checkResult || {};

        for (const key of Object.keys(checkResult)) {
          if (checkResult[key] && checkResult[key].point && checkResult[key].point !== '0') {
            hazardFactors.push({
              name: checkResult[key].name || key,
              value: parseInt(checkResult[key].point || 0, 10),
              unit: '',
              isExceed: checkResult[key].exceed && checkResult[key].exceed !== '0',
            });
          }
        }

        // 获取行业名称
        const industryName = [];
        if (enterprise.industryCategory && enterprise.industryCategory.length > 0 && enterprise.industryCategory[0][0]) {
          for (const industry of enterprise.industryCategory) {
            const industryRes = await ctx.model.IndustryCategory.findOne({ value: industry[0] });
            industryName.push(industryRes.label);
          }
        }
        if (!workAddr.point[0]) {
          const url = `${this.config.tiandimap.url}/geocoder?ds=${encodeURIComponent(JSON.stringify({ keyWord: enterprise.cname }))}&tk=${this.config.tiandimap.key}`;

          const response = await axios.get(url);
          const data = response.data;
          workAddr.point = [ data.location.lon, data.location.lat ];
          await ctx.model.Adminorg.findByIdAndUpdate(enterprise._id, { $set: { 'workAddress.0.point': workAddr.point } });
        }
        mapData.push({
          enterpriseId: enterprise._id,
          name: enterprise.cname,
          industry: industryName,
          address: workAddr.address || '',
          location: {
            longitude: workAddr.point[0],
            latitude: workAddr.point[1],
          },
          hazardFactors,
          employeeCount: 0, // 员工数暂不可用
        });
      }
    }

    return mapData;
  }

  /**
   * 根据筛选条件获取最近的统计数据
   * @param {Object} filter - 筛选条件
   * @return {Object} - 统计数据
   */
  async getStatistics(filter = {}) {
    const { ctx } = this;

    // 构建查询条件
    const query = {
      timeFrame: 'all',
    };

    // 区域筛选
    if (filter.region) {
      query['filterConditions.regionCode'] = filter.region;
    }

    // 行业筛选
    if (filter.industry) {
      query['filterConditions.industryCode'] = filter.industry;
    }

    // 查询最新的统计数据
    let stats = await ctx.model.HazardStatistics.findOne(query)
      .sort({ updatedAt: -1 });

    // 判断数据是否过期(超过1天)
    const isStale = !stats ||
      (new Date() - new Date(stats.updatedAt)) > 24 * 60 * 60 * 1000;

    // 如果数据不存在或已过期，重新计算
    if (isStale) {
      stats = await this.calculateAndUpdateStatistics({
        timeFrame: 'all',
        filter,
      });
    }

    // 根据筛选条件过滤危害因素
    if (stats && filter.hazardFactors && filter.hazardFactors.length > 0) {
      stats.hazardFactorsStats = stats.hazardFactorsStats.filter(
        h => filter.hazardFactors.includes(h.type)
      );
    }

    return stats;
  }
}

module.exports = HazardStatisticsService;
