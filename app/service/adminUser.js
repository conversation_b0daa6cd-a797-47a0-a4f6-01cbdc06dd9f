
const Service = require('egg').Service;
const moment = require('moment');
const jwt = require('jsonwebtoken');
const {
  superUserRule,
} = require('@validate');
const _ = require('lodash');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require('./general');
const shortId = require('shortid');


class AdminUserService extends Service {

  async loginAction(fields) {
    const {
      ctx,
      config,
    } = this;
    console.log(fields, 'fieldssssssssssss');
    // const fields = ctx.request.body || {};
    const systemConfigs = await ctx.service.systemConfig.find({
      isPaging: '0',
    });
    const {
      showImgCode,
    } = systemConfigs[0];
    let errMsg = '';
    if (showImgCode && (!fields.imageCode || !(ctx.session.imageCode >= fields.imageCode - 5 && ctx.session.imageCode <= fields.imageCode + 5))) {
      errMsg = ctx.__('图片滑动验证失败');
      // if (showImgCode && (!fields.imageCode || fields.imageCode !== ctx.session.imageCode)) {
      // errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
    }
    console.log(errMsg, 'errMsg==========');

    if (errMsg) {
      throw new Error(errMsg);
    }
    console.log(234);
    const userName = fields.userName.trim();
    const formObj = {
      userName,
    };
    // box解密密码
    let password = '';
    const decryptRes = await ctx.curl(`${this.config.iServiceHost}/crypto/decrypt`, {
      method: 'POST',
      dataType: 'json', // 返回的数据类型
      data: {
        ciphertext: fields.password,
        fontPublicKey: fields.publicKey,
      },
    });
    if (decryptRes.status !== 200) {
      throw new Error('密码解密失败');
    }
    password = decryptRes.data.data.password;
    ctx.validate(superUserRule.login(ctx), Object.assign({}, formObj, {
      password,
    }));

    const user = await ctx.service.superUser.item(ctx, {
      query: {
        $or: [
          formObj, // 管理员
          { members: { $elemMatch: formObj } }, // 成员
        ],
      },
      populate: [{
        path: 'group',
        select: 'power _id enable name',
      }],
      files: 'enable password _id userName members loginAttempts loginAttemptsTimestamp passwordExpiresAt passwordEncryptionAlgorithm',
    });
      // console.log(121212, user);
    if (!_.isEmpty(user)) {
      // 判断是否锁定 为锁定,否则根据配置是否可以自动解锁
      const limitLoginAttemps = config.limitLoginAttemps; // 最大尝试登录次数
      const loginAttemptsTimeRange = config.loginAttemptsTimeRange; // 登录尝试时间范围
      const lockedLoginTime = config.lockedLoginTime; // 登录锁定时间
      const ifAutoUnlock = config.ifAutoUnlock; // 登录失败后是否自动解锁
      let loginAttempts = Number(user.loginAttempts || 0);
      let loginAttemptsTimestamp = user.loginAttemptsTimestamp || [];
      // 次数超过限制并且在锁定时间范围内
      if (loginAttempts >= limitLoginAttemps && loginAttemptsTimestamp.length > 0 && new Date(loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1 ]).getTime() + lockedLoginTime > new Date().getTime()) {
        // 将剩余解锁时间转换为x分钟x秒格式
        const lockedTime = new Date(loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1 ]).getTime() + lockedLoginTime - new Date().getTime();
        const lockedMinutes = Math.floor(lockedTime / 1000 / 60);
        const lockedSeconds = Math.floor(lockedTime / 1000 % 60);
        throw new Error(`账号已锁定，请${lockedMinutes}分钟${lockedSeconds}秒后再试，或手机验证码登录解锁`);
      } else if (loginAttempts > limitLoginAttemps && ifAutoUnlock) {
        // 已经锁过了,并且支持自动解锁，现在解锁 需要将登录次数和登录时间清空
        loginAttempts = 0;
        loginAttemptsTimestamp = [];
        await ctx.service.superUser.update(ctx, user._id, {
          loginAttempts,
          loginAttemptsTimestamp,
        });

      } else if (loginAttempts > limitLoginAttemps && !ifAutoUnlock) {
        throw new Error('账号已锁定，请联系管理员解锁');
      }
      const originPwd = password;
      let hashPassword = password;
      // if (password.length < 20) {
      //   console.log('进来了猫猫们');
      //   hashPassword = ctx.helper.hashSha256(password, config.salt_sha2_key);
      // }
      let userPsd = user.password; // 管理员的密码
      let passwordEncryptionAlgorithm = user.passwordEncryptionAlgorithm || '';
      let loginId = user._id;
      if (userName !== user.userName) { // 普通成员
        userPsd = user.members.filter(ele => ele.userName === userName)[0].password;
        loginId = user.members.filter(ele => ele.userName === userName)[0]._id;
        if (!userPsd) {
          userPsd = user.password;
        }
        passwordEncryptionAlgorithm = user.members.filter(ele => ele.userName === userName)[0].passwordEncryptionAlgorithm || '';
      }
      if (passwordEncryptionAlgorithm && password.length < 20) {
        const status = await ctx.helper.verifySm3(
          originPwd,
          passwordEncryptionAlgorithm,
          userPsd
        );
        if (status) {
          hashPassword = userPsd;
          await this.ctx.service.operateLog.create('SuperUser', {
            optType: 'hsm',
            supplementaryNotes: `HMAC校验成功${userPsd}`,
            optUserId: loginId,
          });
          this.ctx.auditLog(
            `HMAC校验成功${passwordEncryptionAlgorithm}:${status}:${hashPassword}:${userPsd}`,
            'HMAC校验成功',
            'info'
          );
        }
      } else {
        console.log('进来了猫猫们');
        hashPassword = ctx.helper.hashSha256(password, config.salt_sha2_key);
      }
      // hashPassword = ctx.helper.hashSha256(password, config.salt_sha2_key);
      if (userPsd !== hashPassword) {
        loginAttempts++;
        loginAttemptsTimestamp.push(new Date().getTime());
        await ctx.service.superUser.update(ctx, user._id, {
          loginAttempts,
          loginAttemptsTimestamp,
        });
        await this.ctx.service.operateLog.create('SuperUser', {
          optType: 'hsm',
          supplementaryNotes: `HMAC校验失败${passwordEncryptionAlgorithm}，密码不正确`,
          optUserId: loginId,
        });
        this.ctx.auditLog('HMAC校验失败', `HMAC校验失败${passwordEncryptionAlgorithm}，密码不正确`, 'error');
        // 从loginAttemptsTimestamp中筛选出在配置的连续登录时间范围内的登录时间次数
        const loginAttemptsTimestampInRange = loginAttemptsTimestamp.filter(
          item => {
            return (
              item >
              loginAttemptsTimestamp[loginAttemptsTimestamp.length - 1] -
                loginAttemptsTimeRange
            );
          }
        );
        // 在配置的连续登录时间范围内连续登录是否超过限制次数
        if (loginAttemptsTimestampInRange.length <= limitLoginAttemps) {
          throw new Error(
            `密码错误，您还有${
              limitLoginAttemps - loginAttemptsTimestampInRange.length
            }次机会,${Math.floor(
              loginAttemptsTimeRange / 1000 / 60
            )}分钟内连续登录错误${limitLoginAttemps}次账号将被锁定${Math.floor(
              lockedLoginTime / 1000 / 60
            )}分钟`
          );
        }
        throw new Error(ctx.__('validate_login_notSuccess_1'));
      }
      // 判断密码是否过期-如果没有设置
      let passwordExpiresAt = user.passwordExpiresAt;
      if (!passwordExpiresAt) {
        passwordExpiresAt = new Date();
        // 更新字段进行有效期设置
        await ctx.service.superUser.update(ctx, user._id, {
          passwordExpiresAt,
        });
      }
      // if ((passwordExpiresAt.getTime() + config.passwordExpiresIn) < new Date().getTime()) {
      //   throw new Error('密码已过期，请联系管理员重置密码');
      // }
      if (!user.enable) {
        throw new Error(ctx.__('validate_user_forbiden'));
      }
      try {
        const superUserToken = await this.createToken(fields, user, loginId);
        // if (!passwordEncryptionAlgorithm && hmacSignAlgorithm) {
        //   // 重置密码sm3
        //   const pwd = await ctx.helper.hashSm3(originPwd, hmacSignAlgorithm);
        //   await this.ctx.service.operateLog.create('SuperUser', {
        //     optType: 'hsm',
        //     supplementaryNotes: `HMAC计算成功${pwd}`,
        //     optUserId: user._id,
        //   });
        //   this.ctx.auditLog('HMAC计算成功', `HMAC计算成功${pwd}`, 'info');
        //   await this.updatePasswordToSm3(
        //     user,
        //     userName,
        //     pwd,
        //     hmacSignAlgorithm
        //   );
        // }
        return superUserToken;
      } catch (error) {
        ctx.auditLog(
          '登录操作',
          `未知用户正在通过用户名 ${user.userName.trim()} 执行登录。${JSON.stringify(error)}`
        );
      }

    }
    console.log(ctx.__('validate_login_notSuccess'), 11111111112);
    return { code: -1, message: ctx.__('validate_login_notSuccess') };

    // ctx.helper.renderFail(ctx, {
    //   message: ctx.__('validate_login_notSuccess'),
    // });

  }

  // 更新用户password为sm3
  async updatePasswordToSm3(user, userName, pwd, passwordEncryptionAlgorithm) {
    const { ctx } = this;
    if (userName === user.userName) {
      await ctx.service.superUser.update(ctx, user._id, {
        password: pwd,
        passwordEncryptionAlgorithm,
      });
    } else {
      const members = user.members.map(ele => {
        if (ele.userName === userName) {
          ele.password = pwd;
          ele.passwordEncryptionAlgorithm = passwordEncryptionAlgorithm;
        }
        return ele;
      });
      await ctx.service.superUser.update(ctx, user._id, {
        members,
      });
    }
  }
  // 提取生成token方法
  async createToken(fields, user, loginId) {
    const { ctx } = this;
    const superUserToken = jwt.sign(
      {
        userName: fields.userName.trim(),
        _id: user._id,
        clientId: user._id + shortId.generate(),
      },
      this.app.config.encrypt_key,
      {
        expiresIn: '30day',
      }
    );
    ctx.cookies.set(
      'admin_' + this.app.config.auth_cookie_name,
      superUserToken,
      {
        path: '/',
        maxAge: this.app.config.superUserMaxAge,
        signed: true,
        httpOnly: false,
      }
    ); // cookie 有效期30天

    // 记录登录日志
    const clientIp =
          ctx.header['x-forwarded-for'] ||
          ctx.header['x-real-ip'] ||
          ctx.request.ip;
    const loginLog = {
      type: 'login',
      // logs: user.userName + ' login，ip:' + clientIp,
      logs: fields.userName.trim() + ' login，ip:' + clientIp,
    };
    // ctx.session.

    if (!_.isEmpty(ctx.service.systemOptionLog)) {
      await ctx.service.systemOptionLog.create(loginLog);
    }
    ctx.auditLog(
      '登录操作',
      `未知用户正在通过用户名 ${user.userName.trim()} 执行登录。`
    );
    ctx.service.operateLog.create('SuperUser', {
      optType: 'login',
      supplementaryNotes: '用户登录',
      optUserId: loginId,
    });
    // ctx.helper.renderSuccess(ctx, {
    //   data: {
    //     token: superUserToken,
    //   },
    // });
    return superUserToken;
  }
  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.AdminUser, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.AdminUser, params);
  }

  async create(payload) {
    // return _create(this.ctx.model.AdminUser, payload);
    return _create('AdminGroup', payload, this.ctx);
  }

  async removes(res, values, key = '_id') {
    // return _removes(res, this.ctx.model.AdminUser, values, key);
    return _removes(res, 'AdminUser', values, key);
  }

  async safeDelete(res, values) {
    // return _safeDelete(res, this.ctx.model.AdminUser, values);
    return _safeDelete(res, 'AdminUser', values);
  }

  async update(res, _id, payload) {
    // return _update(res, this.ctx.model.AdminUser, _id, payload);
    return _update(res, this.ctx.model.AdminUser, 'AdminUser', _id, payload);
  }

  async item(res, {
    query = {},
    populate = [],
    files = null,
  } = {}) {
    return _item(res, this.ctx.model.AdminUser, {
      files: files ? files : {
        password: 0,
        email: 0,
      },
      query,
      populate: !_.isEmpty(populate) ? populate : [{
        path: 'group',
        select: 'power _id enable name',
      }],
    });
  }

  async getChartData({ EnterpriseID }) {
    const { ctx } = this;
    const listdata = await ctx.model.Propagate.find({ year: ctx.request.body.NowYear, EnterpriseID });
    const listdataLength = await ctx.model.Propagate.find({ year: ctx.request.body.NowYear, EnterpriseID }).count();// 培训总次数


    const allType = [ '负责人培训', '管理人员培训', '劳动者培训' ];
    // let allType=["负责人、管理人员培训","劳动者培训","其他培训"];
    const PersonTraining = [];// 负责人培训
    const ManagementTraining = [];// 管理人员培训
    const LaborTraining = [];// 劳动者培训

    // let allHours = 0;// 总学时 包括其他培训暂时不用？
    let PersonHours = 0;// 负责人培训 学时
    let ManagementHours = 0;// 管理人员培训 学时
    let LaborHours = 0;// 劳动者培训  学时

    let PersonNumber = 0;// 负责人培训 次数
    let ManagementNumber = 0;// 管理人员培训 次数
    let LaborNumber = 0;// 劳动者培训  次数


    listdata.forEach(item => {
      // allHours += Number(item.hours);

      if (item.type === allType[0]) {
        PersonNumber++;
        PersonHours += Number(item.hours);
        // 负责人培训
        item.personnel.forEach(item1 => {
          if (PersonTraining.indexOf(item1[item1.length - 1]) === -1) {
            PersonTraining.push(item1[item1.length - 1]);
          }
        });
      } else if (item.type === allType[1]) {
        ManagementNumber++;
        ManagementHours += Number(item.hours);
        // 管理人员培训
        item.personnel.forEach(item1 => {
          if (ManagementTraining.indexOf(item1[item1.length - 1]) === -1) {
            ManagementTraining.push(item1[item1.length - 1]);
          }
        });
      } else if (item.type === allType[2]) {
        LaborNumber++;
        LaborHours += Number(item.hours);
        // 劳动者培训
        item.personnel.forEach(item1 => {
          if (LaborTraining.indexOf(item1[item1.length - 1]) === -1) {
            LaborTraining.push(item1[item1.length - 1]);
          }
        });
      }

    });

    // // console.log("??????",ctx.request.body);
    return {
      PersonNumber,
      ManagementNumber,
      LaborNumber,
      allHours: PersonHours + ManagementHours + LaborHours,
      PersonHours,
      ManagementHours,
      LaborHours,
      listdataLength,
      allType,
      PersonTraining: PersonTraining.length,
      ManagementTraining: ManagementTraining.length,
      LaborTraining: LaborTraining.length,
    };
  }
  async getSexCensus({ EnterpriseID }) {
    const { ctx } = this;
    const doc = await ctx.model.Employee.aggregate([
      {
        $match: { EnterpriseID, status: 1 },
      },
      {
        $group: { _id: '$gender', count: { $sum: 1 } },
      },
      {
        $sort: { _id: -1 },
      },
    ]);
    const res = doc.map(item => {
      return {
        name: item._id === '0' ? '男' : '女',
        value: item.count,
      };
    });

    return res;
  }
  async setStatistical({ EnterpriseID }) {
    const { ctx } = this;
    const doc = await ctx.model.StatisticalTable.findOne({ EnterpriseID });
    if (!doc) {
      const res = await ctx.model.Adminorg.findOne({ _id: EnterpriseID });
      const newDAta = {
        EnterpriseID,
        regAdd: res.districtRegAdd,
        regTime: res.createTime,
      };
      // const setRes = await new ctx.model.StatisticalTable(newDAta).save();
      const setRes = await ctx.service.db.create('StatisticalTable', newDAta);
      if (setRes) {
        return true;
      }
    } else {
      return true;
    }
  }
  async getData({ collection, EnterpriseID = '', year = '', type }) {
    const { ctx } = this;
    switch (type) {
      case 'basicData':
        return ctx.model[collection].find({ EnterpriseID }).count();
      case 'Facility':
        return ctx.model[collection].find({ EnterpriseID, 'formData.time': { $gte: year, $lt: 1 + parseInt(year) } }).count();
      case 'Defendproducts':
        return ctx.model[collection].find({ EnterpriseID, 'formData.date': { $gte: year, $lt: 1 + parseInt(year) } }).count();
      case 'DiseasesOverhaul':
        return ctx.model[collection].find({ EnterpriseID, checkTime: { $gte: moment(year), $lt: moment(('' + (1 + parseInt(year)))) } }).count();
      case 'SanitaryInspection':
        return ctx.model[collection].find({ EnterpriseID, checkTime: { $gte: year, $lt: '' + (1 + parseInt(year)) } }).count();
      case 'OnlineDeclaration':
        return ctx.model.OnlineDeclarationFiles.find({ EnterpriseID, year: { $gte: year, $lt: '' + (1 + parseInt(year)) } }).count();
      default:
        break;
    }
    if (type === 'basicData') {
      return ctx.model[collection].find({ EnterpriseID }).count();
    }
    return ctx.model[collection].find({ EnterpriseID, year }).count();
  }
  async getPersonNum({ EnterpriseID }) {
    const { ctx } = this;
    const doc1 = await ctx.model.MillConstruction.aggregate([
      {
        $match: {
          EnterpriseID,
          category: 'workspaces',
        },
      },
      {
        $unwind: '$children',
      },
      {
        $match: { 'children.children.0': { $exists: true } },
      },
      {
        $project: { 'children.children': 1 },
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'children.children.employees',
          foreignField: '_id',
          as: 'employees',
        },
      },
    ]);
    const doc2 = await ctx.model.MillConstruction.aggregate([
      {
        $match: {
          EnterpriseID,
          category: 'mill',
        },
      },
      {
        $unwind: '$children',
      },
      {
        $unwind: '$children.children',
      },
      {
        $match: { 'children.children.children.0': { $exists: true } },
      },
      {
        $project: { 'children.children.children': 1 },
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'children.children.children.employees',
          foreignField: '_id',
          as: 'employees',
        },
      },
    ]);
    // console.log(doc2[0].children.children[0].children);
    let toatlNum = 0;
    const doc = [ ...doc1, ...doc2 ];
    doc.forEach(item => {
      toatlNum += item.employees.length;
    });
    return toatlNum;
  }
  async getHealthData({ EnterpriseID }) {
    const { ctx } = this;
    const year = new Date().getFullYear();
    const years = [ year - 3, year - 2, year - 1, year ];
    const doc = await ctx.model.Healthcheck.find({ EnterpriseID, year: { $in: years }, checkType: 1 });
    // console.log(doc);
    const res = {
      shouldCheckNum: [ 0, 0, 0, 0 ],
      actuallNum: [ 0, 0, 0, 0 ],
      re_examination: [ 0, 0, 0, 0 ],
      normal: [ 0, 0, 0, 0 ],
      suspected: [ 0, 0, 0, 0 ],
      forbid: [ 0, 0, 0, 0 ],
      otherDisease: [ 0, 0, 0, 0 ],
    };
    doc.forEach(item => {
      const index = years.indexOf(parseInt(item.year));
      Object.keys(res).forEach(key => {
        res[key][index] += parseInt(item[key]);
      });
    });
    res.years = years.map(item => item + '年');
    // console.log(res)
    return res;
  }
  async getDiseaseNum({ EnterpriseID }) {
    const { ctx } = this;
    return await ctx.model.Odisease.find({ EnterpriseID }).count();
  }
  async getHarmCheck({ EnterpriseID }) {
    const { ctx } = this;
    const res = await ctx.model.Adminorg.findOne({ _id: EnterpriseID }, { dust: 1, chemical: 1, physical: 1, radiation: 1, biological: 1 });
    const defaultData = { point: '0', exceed: '0' };
    const data = {
      dust: res.dust ? res.dust : defaultData,
      chemical: res.dust ? res.chemical : defaultData,
      physical: res.dust ? res.physical : defaultData,
      radiation: res.dust ? res.radiation : defaultData,
      biological: res.dust ? res.biological : defaultData,
    };
    const factors = [
      [ 'product', '总点数', '超标点数' ],
    ];
    const compute = function(num1, num2) {
      if (num1) {
        return 0;
      }
      return (parseInt(num2) / parseInt(num1) * 100).toFixed(2);
    };
    const mainClass = function(type, item) {
      return [ type, data[item].point, data[item].exceed, compute(data[item].point, data[item].exceed) ];
    };
    Object.keys(data).forEach(item => {
      switch (item) {
        case 'dust':
          factors.push(mainClass('粉尘', item));
          break;
        case 'chemical':
          factors.push(mainClass('化学物质', item));
          break;
        case 'physical':
          factors.push(mainClass('物理因素', item));
          break;
        case 'radiation':
          factors.push(mainClass('放射因素', item));
          break;
        case 'biological':
          factors.push(mainClass('其他因素', item));
          break;
        default:
          break;
      }
    });
    return factors;
  }
  async getHealthHeader({ EnterpriseID }) {
    const { ctx } = this;
    // console.log('EnterpriseID',EnterpriseID,ctx.model)
    const doc = await ctx.model.Roles.findOne({ EnterpriseID }).populate('formData.userId');
    if (!doc) {
      return [];
    }
    // console.log(doc)
    const data = doc.formData.map(item => {
      item.names = [];
      item.userId.forEach(item2 => {
        item.names.push(item2.name);
      });
      return [ item.role, item.userId.length, item.names ];
    });
    // console.log(data)
    return data;
  }

  // async hazardDeclaration({ EnterpriseID, year }) {

  // }

}

module.exports = AdminUserService;
