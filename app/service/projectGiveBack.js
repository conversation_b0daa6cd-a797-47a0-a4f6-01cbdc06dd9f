const Service = require('egg').Service;
const path = require('path');
const fs = require('fs');

class ProjectGiveBackService extends Service {
  async giveBack(parmas) {
    console.log('走到了退回这里', parmas);
    const superID = this.ctx.session.superUserInfo ? this.ctx.session.superUserInfo._id : '';

    const data = {
      giveBackStatus: true,
      giveBackTime: new Date(),
      giveBackRemark: parmas.remark,
    };

    let modelName = '';
    switch (parmas.type) {
      case 'serviceProject':
        modelName = 'JobHealth';
        data.status = -1;
        break;
      case 'physicalExaminationProjects':
        modelName = 'Healthcheck';
        data.reportStatus = false;
        break;
      case 'diagnostics':
        modelName = 'Odisease';
        data.applyStatus = -1;
        break;
      case 'radiateProjects':
        modelName = 'RadiateProjects';
        data.status = -1;
        break;
      default:
        break;
    }

    const filterParams = { _id: parmas.projectID };
    const updateParmas = { $set: data };

    // 更新项目状态
    await this.ctx.model[modelName].updateOne(filterParams, updateParmas);

    // 记录日志
    let orignDoc = [];
    if (parmas.type === 'serviceProject') {
      orignDoc = await this.ctx.model[modelName].aggregate([
        { $match: filterParams },
        {
          $lookup: {
            from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'personInCharge', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'personInCharge', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $lookup: {
            from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'personsOfProject', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'personsOfProject', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $lookup: {
            from: 'riskAssessmentReport',
            localField: '_id',
            foreignField: 'jobHealthId',
            as: 'riskAssessmentReport',
          },
        },
      ]);
    } else if (parmas.type === 'physicalExaminationProjects') {
      orignDoc = await this.ctx.model[modelName].aggregate([
        { $match: filterParams },
        {
          $lookup: {
            from: 'adminorgs',
            localField: 'EnterpriseID',
            foreignField: '_id',
            as: 'EnterpriseID',
          },
        },
        {
          $lookup: {
            from: 'physicalExamOrgs',
            localField: 'physicalExaminationOrgID',
            foreignField: '_id',
            as: 'physicalExaminationOrgID',
          },
        },
        {
          $lookup: {
            from: 'physicalExamDetectionMechanism',
            localField: 'physicalExaminationOrgID.qualifies',
            foreignField: '_id',
            as: 'physicalExamDetectionMechanism',
          },
        },
      ]);
    } else if (parmas.type === 'diagnostics') {
      orignDoc = await this.ctx.model[modelName].aggregate([
        { $match: filterParams },
        {
          $lookup: {
            from: 'adminorgs',
            localField: 'EnterpriseID',
            foreignField: '_id',
            as: 'adminOrgClient',
          },
        },
        {
          $lookup: {
            from: 'physicalExamOrgs',
            localField: 'physicalExaminationOrgID',
            foreignField: '_id',
            as: 'physicalExaminationOrg',
          },
        },
      ]);
    } else if (parmas.type === 'radiateProjects') {
      orignDoc = await this.ctx.model[modelName].aggregate([
        { $match: filterParams },
        {
          $lookup: {
            from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'personInCharge', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'personInCharge', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $lookup: {
            from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'personsOfProject', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'personsOfProject', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $lookup: {
            from: 'serviceOrg', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'serviceID', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'serviceOrg', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $addFields: {
            name: '$serviceOrg.name',
          },
        },
        {
          $project: {
            serviceOrg: 0,
          },
        },
      ]);
    }
    const logData = {
      optType: '更新',
      optIds: parmas.projectID,
      optUserId: superID,
      modelName,
      updateInfoJson: JSON.stringify(updateParmas),
      filterJson: JSON.stringify(filterParams),
      supplementaryNotes: '退回',
      deleteData: orignDoc,
    };
    await new this.ctx.model.OperateLog(logData).save();
    // await this.ctx.service.db.create('SuperOperateLog', logData);
    //  删除预警 假删
    if (parmas.warningId) {
      // await this.ctx.model.Warning.updateOne({ _id: parmas.warningId }, { delete: true });
      await this.ctx.service.db.updateOne('Warning', { _id: parmas.warningId }, { delete: true });
    } else {
      // await this.ctx.model.Warning.updateMany({ jobHealthId: parmas.projectID }, { delete: true });
      await this.ctx.service.db.updateMany('Warning', { jobHealthId: parmas.projectID }, { delete: true });
    }

  }
  async sendMessage(parmas, isTest) {
    const { ctx } = this;
    let modelName = '';
    switch (parmas.type) {
      case 'serviceProject':
        modelName = 'JobHealth';
        break;
      case 'physicalExaminationProjects':
        modelName = 'Healthcheck';
        break;
      case 'diagnostics':
        modelName = 'Odisease';
        break;
      case 'radiateProjects':
        modelName = 'RadiateProjects';
        break;
      default:
        break;
    }
    let phoneNum = '';
    let userName = '';
    let projectName = '';
    // todo 短信提醒
    const doc = await this.ctx.model[modelName].findOne({ _id: parmas.projectID });

    if (parmas.type === 'physicalExaminationProjects') {
      const org = await this.ctx.model.PhysicalExamOrg.findOne({ _id: doc.physicalExaminationOrgID }, { phoneNum: 1, name: 1 });
      phoneNum = org.phoneNum;
      userName = org.name;
      projectName = doc.projectNumber;
    } if (parmas.type === 'diagnostics') {
      const org = await this.ctx.model.PhysicalExamOrg.findOne({ _id: doc.physicalExaminationOrgID }, { phoneNum: 1, name: 1 });
      phoneNum = org.phoneNum;
      userName = org.name;
      projectName = '职业病诊断';
    } else if (parmas.type === 'serviceProject') {
      const org = await this.ctx.model.ServiceUser.findOne({ org_id: doc.serviceID }, { phoneNum: 1 });
      phoneNum = org ? org.phoneNum : '';
      userName = doc.name;
      projectName = doc.projectNumber;
    }
    if (isTest) {
      phoneNum = '13116841683';
    }
    // console.log(222222222222, phoneNum, userName, projectName);
    if (phoneNum) {
      // 发送短消息;
      const { data } = await ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'giveBack',
          TemplateParam: JSON.stringify({
            name: userName,
            projectName,
          }),
          PhoneNumbers: phoneNum,
        },
      });
      if (data.Message !== 'OK') {
        ctx.auditLog(`短信发送失败 - ${phoneNum}`, JSON.stringify(data), 'error');
      }
    }
  }
  async handleDelImage(delImage) {
    const { ctx } = this;
    const delImageArray = delImage.split('/static/upload/images');
    if (delImageArray[1]) {
      const delPath = path.join(ctx.app.config.upload_path, delImageArray[1]);
      try {
        if (fs.existsSync(delPath)) fs.unlinkSync(delPath);
      } catch (err) {
        console.log(err);
      }
    }
  }
  async giveBackRecord(params) {
    const { ctx } = this;
    const superID = this.ctx.session.superUserInfo ? this.ctx.session.superUserInfo._id : '';

    console.log('走到了退回记录这里', params);
    const filterParmas = {
      source: 'super',
      optType: '更新',
      supplementaryNotes: '退回',
      optUserId: superID,
    };
    if (params.source === 'service') {
      filterParmas.modelName = 'JobHealth';

      if (params.keyword) {
        filterParmas.$or = [
          { 'deleteData.companyName': { $regex: params.keyword } },
          { 'deleteData.projectNumber': { $regex: params.keyword } },
        ];
      }
    } else if (params.source === 'physical') {
      filterParmas.modelName = 'Healthcheck';

      if (params.keyword) {
        filterParmas.$or = [
          { 'deleteData.enterpriseName': { $regex: params.keyword } },
          { 'deleteData.name': { $regex: params.keyword } },
        ];
      }
    } else if (params.source === 'diagnostics') {
      filterParmas.modelName = 'Odisease';

      if (params.keyword) {
        filterParmas.$or = [
          { 'deleteData.cname': { $regex: params.keyword } },
          { 'deleteData.name': { $regex: params.keyword } },
          { 'deleteData.hospital': { $regex: params.keyword } },
        ];
      }
    } else if (params.source === 'radiate') {
      filterParmas.modelName = 'RadiateProjects';
      if (params.keyword) {
        filterParmas.$or = [
          { 'deleteData.0.companyName': { $regex: params.keyword } },
          { 'deleteData.0.name.0': { $regex: params.keyword } },
          { 'deleteData.0.companyContact': { $regex: params.keyword } },
          { 'deleteData.0.projectNumber': { $regex: params.keyword } },
        ];
      }
    }

    const sortParmas = { createdAt: -1, _id: -1 };

    const doc = await ctx.model.OperateLog.find(filterParmas).sort(sortParmas);

    console.log('退回记录拿到的数据', doc);
    let filterDoc;
    if (params.source === 'radiate') {
      filterDoc = doc
        .slice((params.curPage - 1) * params.pageSize, (params.curPage - 1) * params.pageSize + params.pageSize).filter(item => item !== null).map(item => ({ ...item.deleteData[0], ...JSON.parse(item.updateInfoJson) }));
    } else {
      filterDoc = doc
        .slice((params.curPage - 1) * params.pageSize, (params.curPage - 1) * params.pageSize + params.pageSize)
        .map(item => item.deleteData[0]);
    }
    return {
      doc: filterDoc,
      allSize: doc.length,
    };

  }
}

module.exports = ProjectGiveBackService;
