
const Service = require('egg').Service;


// const _ = require('lodash');
const {
  _unionQuery,
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require('./general');


class SuperUserService extends Service {

  async unionQuery(payload, {
    collections = [],
    unwindArray = [],
    query = {},
    searchKeys = [],
    files = null,
    sort = {},
    statisticsFiles = [],
  } = {}) {

    const listdata = _unionQuery(this.ctx.model.SuperUser, payload, {
      collections,
      unwindArray,
      query,
      searchKeys,
      files,
      sort,
      statisticsFiles,
    });
    return listdata;
  }

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.SuperUser, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.SuperUser, params);
  }

  async create(payload) {
    // return _create(this.ctx.model.SuperUser, payload);
    return _create('SuperUser', payload, this.ctx);
  }

  async removes(res, values, key = '_id') {
    // return _removes(res, this.ctx.model.SuperUser, values, key);
    return _removes(res, 'SuperUser', values, key);
  }

  async safeDelete(res, values) {
    // return _safeDelete(res, this.ctx.model.SuperUser, values);
    return _safeDelete(res, 'SuperUser', values);
  }

  async update(res, _id, payload) {
    // return _update(res, this.ctx.model.SuperUser, _id, payload);
    return _update(res, this.ctx.model.SuperUser, 'SuperUser', _id, payload);
  }

  async item(res, params = {}, options = {}) {
    return _item(res, this.ctx.model.SuperUser, params, options);
  }

  async addMember(_id, params = {}) {
    // return await this.ctx.model.SuperUser.updateOne(
    //   { _id },
    //   { $push: { members: params } },
    //   { new: true }
    // );
    return await this.ctx.service.db.updateOne('SuperUser', { _id }, { $push: { members: params } }, { new: true });
  }
  async addMember2(query = {}, params = {}) {
    return await this.ctx.service.db.updateOne('SuperUser', query, { $push: { members: params } }, { new: true });
  }

  async getMembers(_id) {
    return await this.ctx.model.SuperUser.findOne(
      { _id, enable: true },
      { password: 0, 'members.password': 0 }
    );
  }

  async delMember(_id, member_id) {
    // return await this.ctx.model.SuperUser.updateOne(
    //   { _id, enable: true },
    //   { $pull: { members: { _id: member_id } } }
    // );
    return await this.ctx.service.db.updateOne('SuperUser', { _id, enable: true }, { $pull: { members: { _id: member_id } } });
  }

  async editMember(_id, params = {}) {
    // return await this.ctx.model.SuperUser.update(
    //   { _id, 'members._id': params._id },
    //   { $set: {
    //     'members.$.name': params.name,
    //     'members.$.phoneNum': params.phoneNum,
    //     'members.$.userName': params.phoneNum,
    //     'members.$.jobTitle': params.jobTitle,
    //     'members.$.landline': params.landline,
    //     'members.$.roles': params.roles,
    //   } }
    // );
    return await this.ctx.service.db.updateOne('SuperUser', { _id, 'members._id': params._id },
      { $set: {
        'members.$.name': params.name,
        'members.$.phoneNum': params.phoneNum,
        'members.$.userName': params.phoneNum,
        'members.$.jobTitle': params.jobTitle,
        'members.$.landline': params.landline,
        'members.$.roles': params.roles,
      } });
  }

  async findAll(query) {
    return await this.ctx.model.SuperUser.find(
      query,
      { password: 0, 'members.password': 0 }
    );
  }

  async findOne(query) {
    return await this.ctx.model.SuperUser.findOne(
      query,
      { password: 0, 'members.password': 0 }
    );
  }

}

module.exports = SuperUserService;
