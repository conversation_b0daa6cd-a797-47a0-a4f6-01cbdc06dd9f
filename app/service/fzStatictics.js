const Service = require('egg').Service;
const moment = require('moment');

class FzStatisticService extends Service {
  async findEnterpriseIds(districtName = '福州市') {
    return await this.ctx.model.Adminorg.aggregate([
      {
        $match: {
          isactive: { $ne: '2' },
          $or: [
            { isDelete: false },
            { isDelete: null },
          ],
          'workAddress.districts': districtName,
          productionStatus: { $ne: '0' },
        },
      },
      {
        $project: { _id: 1 },
      },
    ]);
  }

  async harmfactorStatistical(adcode) {
    try {
      const { ctx } = this;
      const year = new Date().getFullYear();
      const districtsData = await ctx.model.District.find({
        parent_code: adcode,
      }, { area_code: 1, name: 1 });
      const res = [];
      for (const district of districtsData) {
        const districtName = district.name;
        const EnterpriseList = await this.findEnterpriseIds(districtName);
        // 按年份统计项目数量
        for (let curYear = year; curYear >= year - 2; curYear--) {
          // 查询本年度，本地区下的所有企业
          const allCompanies = await ctx.model.Adminorg.aggregate([
            {
              $match: {
                _id: { $in: EnterpriseList.map(item => item._id) },
              },
            },
            {
              $project: {
                harmStatistics: 1,
              },
            },
          ]);
          const counts = [];
          for (let i = 0; i < allCompanies.length; i++) {
            const harmStatistics = allCompanies[i].harmStatistics;
            for (let j = 0; j < harmStatistics.length; j++) {
              const count = harmStatistics[j].count;
              for (let k = 0; k < count.length; k++) {
                counts.push(count[k]);
              }
            }
          }
          let glfc = 0;
          let glhxdu = 0;
          let glwlys = 0;
          let qtwlys = 0;
          let allPerson = 0;
          let total = 0;
          for (let i = 0; i < counts.length; i++) {
            if (counts[i].label === '化学有害因素') {
              glhxdu += counts[i].value;
            }
            if (counts[i].label === '粉尘') {
              glfc += counts[i].value;
            }
            if (counts[i].label === '噪声') {
              glwlys += counts[i].value;
            }
            if (counts[i].label === '其他物理因素') {
              qtwlys += counts[i].value;
            }
          }
          allPerson = glfc + glhxdu + glwlys + qtwlys;
          total += allPerson;
          res.push({
            DQ: districtName, // 地区
            NF: curYear, // 年份
            GLFC: glfc, // 各类粉尘
            GLHXDW: glhxdu, // 各类化学毒物
            GLWLYS: glwlys + qtwlys, // 各类物理因素
            NDJCZRS: allPerson, // 年度接触总人数
            ZB: Number((allPerson / total * 100).toFixed(0)) ? Number((allPerson / total * 100).toFixed(0)) : 0, // 占比
          });
        }
      }
      await ctx.model.DpStatistical.updateOne({ table: 'DP_ZYWSDP_LDZZYBWHYSJCTJ', adcode }, { data: res }, { upsert: true });
    } catch (e) {
      console.error(e);
    }
  }

  // 用人单位职业危害因素申报统计
  async declarationStatistical(adcode) {
    try {
      const { ctx } = this;
      const year = new Date().getFullYear();
      const nowTime = new Date(); // 当前时间
      const tenTime = new Date(moment().subtract(10, 'months').format('YYYY-MM-DD'));
      const districtsData = await ctx.model.District.find({
        parent_code: adcode,
      }, { area_code: 1, name: 1 });
      const res = []; // 职业病危害因素申报统计
      for (const district of districtsData) {
        const districtName = district.name;
        const EnterpriseList = await this.findEnterpriseIds(districtName);
        // 按年份统计项目数量
        for (let curYear = year; curYear >= year - 2; curYear--) {
          const onlineDeclaraCompetedNum = await ctx.model.Adminorg.find({
            _id: { $in: EnterpriseList.map(item => item._id) },
            'onlineDeclarationFiles.monthD': {
              $gte: tenTime,
              $lt: nowTime,
            },
          }).count();
          res.push({
            DQ: districtName, // 地区
            ND: curYear, // 年份
            YRDWS: EnterpriseList.length, // 用人单位数量
            YSBDYRDWS: onlineDeclaraCompetedNum, // 已申报的用人单位数量
            SBL: EnterpriseList.length === 0 ? 0 : Number((onlineDeclaraCompetedNum / EnterpriseList.length * 100).toFixed(0)), // 申报率
          });
        }
      }
      await ctx.model.DpStatistical.updateOne({ table: 'DP_ZYWSDP_YRDWZYWHYSSBTJ', adcode }, { data: res }, { upsert: true });
    } catch (e) {
      console.error(e);
    }
  }
  async getIndustry(adcode) {
    try {
      const res = [];
      const res2 = [];
      const big = {
        YRDWGM: '大',
        YRDWS: 0,
      };
      const middle = {
        YRDWGM: '中',
        YRDWS: 0,
      };
      const small = {
        YRDWGM: '小',
        YRDWS: 0,
      };
      const mircle = {
        YRDWGM: '微',
        YRDWS: 0,
      };
      const unknown = {
        YRDWGM: '未知',
        YRDWS: 0,
      };
      const industryArray = await this.ctx.service.industryCategory.find(
        {
          isPaging: '0',
        },
        {
          sort: {
            sort: 1,
          },
        }
      );
      industryArray.forEach(item => {
        res2.push({
          YRDWHY: item.label,
          YRDWS: 0,
          CODE: item.value,
        });
      });
      const companiesList = await this.ctx.model.Adminorg.aggregate([
        {
          $match: {
            isactive: { $ne: '2' },
            $or: [
              { isDelete: false },
              { isDelete: null },
            ],
            'workAddress.districts': '福州市',
            productionStatus: { $ne: '0' },
          },
        },
        {
          $project: { _id: 1, companyScale: 1, industryCategory: 1 },
        },
      ]);

      companiesList.forEach(item => {
        if (item.companyScale === '大' || item.companyScale === '大型') {
          big.YRDWS += 1;
        } else if (item.companyScale === '中' || item.companyScale === '中型') {
          middle.YRDWS += 1;
        } else if (item.companyScale === '小' || item.companyScale === '小型') {
          small.YRDWS += 1;
        } else if (item.companyScale === '微' || item.companyScale === '微型') {
          mircle.YRDWS += 1;
        } else {
          unknown.YRDWS += 1;
        }

        res2.forEach(item2 => {
          if (item.industryCategory.length > 0) {
            if (item.industryCategory[item.industryCategory.length - 1] !== null && item.industryCategory[item.industryCategory.length - 1][0] === item2.CODE) {
              item2.YRDWS += 1;
            }
          }
        });

      });
      res.push(big, middle, small, mircle, unknown);
      res2.forEach(item => {
        delete item.CODE;
      });

      await this.ctx.model.DpStatistical.updateOne({ table: 'DP_ZYWSDP_JCYRDWHYTJ', adcode }, { data: res2 }, { upsert: true });
      await this.ctx.model.DpStatistical.updateOne({ table: 'DP_ZYWSDP_JCYRDWGMTJ', adcode }, { data: res }, { upsert: true });
    } catch (err) {
      console.error(err);
    }
  }

}

module.exports = FzStatisticService;
