/**
 * @description RabbitMQ Service for use
 * @class RabbitMQService
 * @extends {Service}
 * @example
 *  await service.rabbitmq.produce('myQueue', message);
 *  await service.rabbitmq.consume('myQueue', message => {
 *   console.log(message);
 *  });
 */
const amqp = require('amqplib');
const { Service } = require('egg');

class RabbitMQService extends Service {
  async produce(queue, message) {
    const { ctx, config } = this;
    const connection = await amqp.connect(config.rabbitmq.url);
    const channel = await connection.createChannel();

    await channel.assertQueue(queue, { durable: false });

    channel.sendToQueue(queue, Buffer.from(JSON.stringify(message)));

    ctx.logger.info(`Message sent to ${queue}: ${message}`);

    setTimeout(() => {
      connection.close();
    }, 500);
  }

  async consume(queue, callback) {
    console.log('rmq消费者start');
    const { config } = this;
    const connection = await amqp.connect(config.rabbitmq.url);
    const channel = await connection.createChannel();

    await channel.assertQueue(queue, { durable: false });

    channel.consume(queue, msg => {
      if (msg !== null) {
        callback(msg.content.toString());
        channel.ack(msg);
      }
    });
  }
}

module.exports = RabbitMQService;
