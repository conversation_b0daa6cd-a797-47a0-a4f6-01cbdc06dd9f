const Service = require('egg').Service;

class HzReservationService extends Service {

  // 查询预约列表（支持分页、关键字、状态筛选）
  async getReservationList({ page = 1, pageSize = 10, keyword, status }) {
    const { ctx } = this;
    const query = {};
    
    // 添加查询条件
    if (keyword) {
      // 使用$or操作符在多个字段中查询关键字
      query.$or = [
        { name: new RegExp(keyword, 'i') },        
        { phoneNum: new RegExp(keyword, 'i') },    
        { company: new RegExp(keyword, 'i') },     
        { description: new RegExp(keyword, 'i') } 
      ];
    }
    
    if (status) {
      query.status = status;                        // 精确匹配状态
    }
    
    const total = await ctx.model.HzReservation.countDocuments(query);
    const list = await ctx.model.HzReservation.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean();
    return { total, list };
  }

  // 更新预约状态
  async updateOne(id, status, acceptDescription) {
    const { ctx } = this;
    // 验证状态值是否有效
    const validStatus = [ '0', '1', '2', '3' ];
    if (!validStatus.includes(status)) {
      throw new Error('无效的状态值');
    }
    
    const record = await ctx.model.HzReservation.findById(id);
    if (!record) {
      throw new Error('预约记录不存在');
    }
    
    // 如果当前已是取消状态，且尝试更改为其他状态，不允许
    if (record.status === '3' && status !== '3') {
      throw new Error('已取消的预约不能更改状态');
    }
    
    record.status = status;
    record.acceptDescription = acceptDescription;
    await record.save();
    return record;
  }
}

module.exports = HzReservationService;
