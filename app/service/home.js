const Service = require('egg').Service;
// const path = require('path');
const moment = require('moment');
const {
  parseInt,
} = require('lodash');
// const axios = require('axios');

class HomeService extends Service {

  async preventAssessFindAll(params) {
    const {
      ctx,
    } = this;
    try {
      const searchParams = params.year ? {
        year: params.year + '',
      } : {};
      // const EnterpriseID = ctx.session.superUserInfo ? ctx.session.superUserInfo.EnterpriseID : '';
      // console.log('5555555555555555');
      console.log(params.EnterpriseID);
      // console.log('5555555555555555');
      const data = await ctx.model.PreventAssess.find({
        EnterpriseID: params.EnterpriseID,
        ...searchParams,
      }).skip((params.pageNum - 1) * 10).limit(parseInt(params.pageSize));
      const totalLength = await ctx.model.PreventAssess.find({
        EnterpriseID: params.EnterpriseID,
        ...searchParams,
      }).countDocuments();
      return {
        data,
        totalLength,
      };
    } catch (error) {
      console.log(error);
      return 500;
    }
  }
  // 获取企业注册地址
  async getJurisdiction(EnterpriseID) {
    const {
      ctx,
    } = this;
    const { crossRegionManage } = ctx.app.config;
    if (crossRegionManage) {
      const res = await ctx.model.SuperUser.findOne({
        _id: EnterpriseID,
      }).lean();
      return res.crossRegionAdd;
    }
    const res = await ctx.model.SuperUser.findOne({
      _id: EnterpriseID,
    }).lean();
    return res.regAdd;
  }
  // 获取所在辖区的行政区划代码
  async getJurisdictionCode(EnterpriseID) {
    const {
      ctx,
    } = this;
    const res = await ctx.model.SuperUser.findOne({
      _id: EnterpriseID,
    }).lean();
    return res.area_code;
  }
  // 获取辖区企业数据 (包括)
  async getEnterpriseData({
    districtRegAdd,
    year = new Date(),
    adcode,
    jurisdictionList,
  }) {
    const {
      ctx,
    } = this;
    const fullYear = moment('' + year).year();

    year = new Date((1 + parseInt(fullYear)) + ''); // 保存选择的年度
    const { crossRegionManage } = ctx.app.config;
    let jurisdiction;
    if (crossRegionManage) {
      jurisdiction = districtRegAdd[districtRegAdd.length - 1] === '中国' ? {
        $exists: true,
      } : districtRegAdd; // 获取管辖区域
    } else {
      jurisdiction = districtRegAdd[districtRegAdd.length - 1] === '中国' ? {
        $exists: true,
      } : districtRegAdd[districtRegAdd.length - 1]; // 获取管辖区域
    }

    // 所在辖区XXXX年以及之前注册的企业数
    // const EnterpriseCount = await ctx.model.Adminorg.find({
    //   'workAddress.0.districts': jurisdiction,
    // }).count();
    let EnterpriseList = [];
    // let EnterpriseIDs = [];

    if (jurisdictionList) {
      const query = {
        status: 1, // 已上报的
        projectStop: { $ne: true },
        completeTime: {
          $lt: new Date(parseInt(fullYear) + 1 + ''),
          $gte: new Date(parseInt(fullYear) + ''),
        },
        workPlaces: {
          $elemMatch: { workAdd: (adcode.length === 6) ? adcode + '000000' : adcode },
        },
      };
      EnterpriseList = await ctx.model.JobHealth.aggregate([
        {
          $match: query,
        },
        {
          $project: {
            EnterpriseID: 1,
          },
        },
        {
          $lookup: {
            from: 'adminorgs', // 检测表数据
            localField: 'EnterpriseID',
            foreignField: '_id',
            as: 'adminorg',
          },
        },
        {
          $unwind: '$adminorg',
        },
        {
          $match: {
            'adminorg.isactive': { $ne: '2' },
            $or: [
              { 'adminorg.isDelete': false },
              { 'adminorg.isDelete': null },
            ],
            'adminorg.workAddress.districts': jurisdiction,
            'adminorg.productionStatus': { $ne: '0' },
          },
        },
        {
          $project: { workAddress: '$adminorg.workAddress', cname: '$adminorg.cname', level: '$adminorg.level', EnterpriseID: '$adminorg._id' },
        },
      ]);
      // EnterpriseIDs = EnterpriseList.map(ele => ele.EnterpriseID);
    } else {
      // xxn change
      EnterpriseList = await ctx.model.Adminorg.aggregate([
        {
          $match: {
            isactive: { $ne: '2' },
            $or: [
              { isDelete: false },
              { isDelete: null },
            ],
            'workAddress.districts': jurisdiction,
            productionStatus: { $ne: '0' },
          },
        },
        // {
        //   $lookup: {
        //     from: 'jobhealths', // 检测表数据
        //     localField: '_id',
        //     foreignField: 'EnterpriseID',
        //     as: 'jobHealth',
        //   },
        // },
        {
          $project: { workAddress: 1, cname: 1, level: 1,
            // 'jobHealth.reportTime': 1, 'jobHealth._id': 1,
          },
        },
      ]);
      // EnterpriseIDs = EnterpriseList.map(ele => ele._id);
    }

    const EnterpriseCount = EnterpriseList.length;
    // 查询体检数据  physicalExaminationProjects/getProjects
    const query = {
      reportStatus: true,
      checkDate: { $exists: true, $ne: null },
      // checkDate: {
      //   $lt: new Date(`${+fullYear + 1}-1-1`),
      //   $gte: new Date(`${fullYear}-1-1`),
      // },
      workAddress: {
        $elemMatch: { districts: { $all: districtRegAdd } },
      },
    };
    const suspectsData = await ctx.model.Healthcheck.aggregate([
      { $match: query },
      { $group: {
        checkDate: { $push: '$checkDate' },
        _id: { EnterpriseID: '$EnterpriseID' } },
      },
    ]);
    // 查询检测数据 xxn add
    const query2 = {
      status: 1, // 已上报的
      projectStop: { $ne: true },
      // completeTime: {
      //   $lt: new Date(`${+fullYear + 1}-1-1`),
      //   $gte: new Date(`${fullYear}-1-1`),
      // },
      completeTime: { $exists: true, $ne: null },
      workPlaces: {
        $elemMatch: { workAdd: adcode.length === 6 ? adcode + '000000' : adcode },
      },
    };
    if (crossRegionManage) {
      // adcode是个数组
      query2.workPlaces = {
        $elemMatch: { workAdd: { $in: adcode } },
      };
    }

    const jobHealthData = await ctx.model.JobHealth.aggregate([
      { $match: query2 },
      { $group: {
        reportTime: { $push: '$completeTime' },
        _id: { EnterpriseID: '$EnterpriseID' } },
      },
    ]);

    // const jurisdictionData = await this.getJurisdictionData({
    //   districtRegAdd,
    //   year: fullYear,
    //   EnterpriseCount,
    // });
    const jurisdictionData = await this.getJurisdictionData2({
      districtRegAdd,
      year: fullYear,
      EnterpriseCount,
      suspectsData,
      jobHealthData,
    });
    return {
      EnterpriseCount,
      jurisdictionData,
      EnterpriseList,
      suspectsData, // 体检数据
      jobHealthData, // 检测数据
    };
  }

  // 获取辖区机构数据
  async getserviceOrgData({
    adcode,
  }) {
    const {
      ctx,
    } = this;

    const query = [{
      $match: {
        workPlaces: {
          $elemMatch: {
            workAdd: adcode.length === 12 ? adcode : adcode + '000000',
          },
        },
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        serviceOrgID: 1,
        companyName: 1,
        companyID: 1,
        status: 1,
        completeStatus: 1,
        completeUpdate: 1,
      },
    },
    {
      $group: {
        _id: '$serviceOrgID',
        count: {
          $sum: 1,
        },
        status: {
          $sum: '$status',
        },
        completeStatus: {
          $sum: '$completeStatus',
        },
        completeUpdate: {
          $sum: '$completeUpdate',
        },
      },
    },
    {
      $match: {
        '_id.name': { $ne: '' },
      },
    },
    ];
    const serviceOrgs = await ctx.model.JobHealth.aggregate(query);
    // console.log(2222222, serviceOrgs);
    const jurisdictionRes = await ctx.model.District.findOne({ area_code: (adcode + '').slice(0, 3) + '*********' }, { name: 1 });

    let jurisdiction = '';
    if (jurisdictionRes) {
      jurisdiction = jurisdictionRes.name;
    }

    const jurisdictionOrgs = await ctx.model.ServiceOrg.aggregate([
      {
        $match: {
          status: 3,
        },
      },
      {
        $lookup: {
          from: 'detectionMechanism',
          foreignField: '_id',
          localField: 'qualifies',
          as: 'detectionMechanism',
        },
      },
      {
        $project: {
          organization: 1,
          name: 1,
          regAddr: 1,
          detectionMechanism: 1,
        },
      },
      {
        $match: {
          $or: [
            { regAddr: jurisdiction },
            { 'detectionMechanism.level': '甲级' },
          ],
          organization: { $exists: true },
          name: { $exists: true },
        },
      },
    ]);
    // console.log(**********99, jurisdictionOrgs);
    const serviceOrgsList = [];
    jurisdictionOrgs.forEach(item => {
      const target = serviceOrgs.find(item2 => item2._id === item.organization);
      const data = {
        _id: { serviceOrgID: item.organization, name: item.name },
        count: target ? target.count : 0,
        status: target ? target.status : 0,
        completeStatus: target ? target.completeStatus : 0,
        completeUpdate: target ? target.completeUpdate : 0,
      };
      serviceOrgsList.push(data);
    });

    return {
      serviceOrgs: serviceOrgsList,
    };
  }

  async getJurisdictionData2({
    districtRegAdd,
    year = new Date().getFullYear(),
    EnterpriseCount = 0,
    suspectsData,
    jobHealthData,
  }) {
    const allHealth = { [year]: [], [year - 1]: [], [year - 2]: [] },
      allJob = { [year]: [], [year - 1]: [], [year - 2]: [] };
    suspectsData.forEach(ele => {
      // 去掉ele.checkDate里面的null
      ele.checkDate = ele.checkDate.filter(item => item !== null);
      const years = ele.checkDate.map(item => item.getFullYear());
      if (years.includes(year)) allHealth[year].push(ele._id.EnterpriseID);
      if (years.includes(year - 1)) { allHealth[year - 1].push(ele._id.EnterpriseID); }
      if (years.includes(year - 2)) { allHealth[year - 2].push(ele._id.EnterpriseID); }
    });
    jobHealthData.forEach(ele => {
      ele.reportTime = ele.reportTime.filter(item => item !== null);
      // const years = ele.reportTime.map(item => item.getFullYear());
      // if (years.includes(year)) allJob[year].push(ele._id.EnterpriseID);
      // if (years.includes(year - 1)) allJob[year - 1].push(ele._id.EnterpriseID);
      // if (years.includes(year - 2)) allJob[year - 2].push(ele._id.EnterpriseID);
    });
    const threeYear = [ year - 2, year - 1, year ];
    const res = [];
    const jurisdiction = districtRegAdd[districtRegAdd.length - 1] === '中国' ? {
      $exists: true,
    } : districtRegAdd[districtRegAdd.length - 1]; // 管辖区域

    for (let i = 0; i < 3; i++) {
      const curYear = threeYear[i];
      const jobHealthNum = allJob[curYear].length;
      const examinationNum = allHealth[curYear].length;
      // 档案完成率
      const recordField = 'recordList.' + curYear;
      const record = await this.ctx.model.Adminorg.count({
        $and: [{
          'workAddress.0.districts': jurisdiction,
        }, {
          $or: [{
            [recordField]: {
              $size: 4,
            },
          }, {
            [recordField]: {
              $size: 5,
            },
          }],
        }],
      });

      const completeField2 = 'completeList.' + curYear;
      const doc = await this.ctx.model.Adminorg.aggregate([{
        $match: {
          'workAddress.0.districts': jurisdiction,
        },
      },
      {
        $project: {
          [completeField2]: {
            $cond: {
              if: { $isArray: '$' + completeField2 },
              then: '$' + completeField2,
              else: [],
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          OnlineDeclaration: {
            $sum: {
              $cond: [{
                $in: [ 'OnlineDeclaration', '$' + completeField2 ],
              },
              1,
              0,
              ],
            },
          },
          propagate: {
            $sum: {
              $cond: [{
                $in: [ 'Propagate', '$' + completeField2 ],
              },
              1,
              0,
              ],
            },
          },
        },
      },
      ]);
      res.push({
        name: +curYear,
        OnlineDeclaration: this.calculationrate(doc[0] && doc[0].OnlineDeclaration ? doc[0].OnlineDeclaration : 0, EnterpriseCount),
        record: this.calculationrate(record, EnterpriseCount),
        jobHealth: this.calculationrate(jobHealthNum, EnterpriseCount),
        propagate: this.calculationrate(doc[0] && doc[0].propagate ? doc[0].propagate : 0, EnterpriseCount),
        examination: this.calculationrate(examinationNum, EnterpriseCount),
      });
    }
    return res;
  }

  calculationrate(val, enterpriseCount) {
    if (enterpriseCount) {
      return (parseInt(val) / parseInt(enterpriseCount) * 100).toFixed(2);
    }
    return 0;
  }

  async getJurisdictionData({
    districtRegAdd,
    year = new Date(),
    EnterpriseCount = 0,
  }) {
    // console.log(11111, districtRegAdd, year, EnterpriseCount)
    const jurisdiction = districtRegAdd[districtRegAdd.length - 1] === '中国' ? {
      $exists: true,
    } : districtRegAdd[districtRegAdd.length - 1]; // 获取管辖区域
    const {
      ctx,
    } = this;
    year = parseInt(moment('' + year).format('YYYY'));
    const threeYear = [ year - 2, year - 1, year ];
    year = new Date((1 + year) + '');
    const calculationrate = function(val1, enterpriseCount) {
      if (enterpriseCount) {
        return (parseInt(val1) / parseInt(enterpriseCount) * 100).toFixed(2);
      }
      return 0;
    };
    const declareRate = [];

    for (let i = 0; i < threeYear.length; i++) {
      const item = threeYear[i];
      const completeField2 = 'completeList.' + item;
      const recordField = 'recordList.' + item;

      const doc = await ctx.model.Adminorg.aggregate([{
        $match: {
          'workAddress.0.districts': jurisdiction,
        },
      },
      {
        $project: {
          [completeField2]: {
            $ifNull: [ '$' + completeField2, []],
          },
        },
      },
      {
        $group: {
          _id: null,
          OnlineDeclaration: {
            $sum: {
              $cond: [{
                $in: [ 'OnlineDeclaration', '$' + completeField2 ],
              },
              1,
              0,
              ],
            },
          },
          jobHealth: {
            $sum: {
              $cond: [{
                $in: [ 'jobHealth', '$' + completeField2 ],
              },
              1,
              0,
              ],
            },
          },
          propagate: {
            $sum: {
              $cond: [{
                $in: [ 'Propagate', '$' + completeField2 ],
              },
              1,
              0,
              ],
            },
          },
          examination: {
            $sum: {
              $cond: [{
                $in: [ 'Suspect', '$' + completeField2 ],
              },
              1,
              0,
              ],
            },
          },
        },
      },
      ]);

      const EnterpriseC = await ctx.model.Adminorg.find({
        'workAddress.0.districts': jurisdiction,
      }).count();
      const record = await ctx.model.Adminorg.find({
        $and: [{
          'workAddress.0.districts': jurisdiction,
        }, {
          $or: [{
            [recordField]: {
              $size: 4,
            },
          }, {
            [recordField]: {
              $size: 5,
            },
          }],
        }],
      }).count(); // 完成档案
      // console.log(123456, doc[0]);
      if (!doc[0]) {
        declareRate.push({
          name: item,
          OnlineDeclaration: 0,
          record: 0,
          jobHealth: 0,
          propagate: 0,
          examination: 0,
        });
      } else {
        declareRate.push({
          name: item,
          OnlineDeclaration: calculationrate(doc[0].OnlineDeclaration ? doc[0].OnlineDeclaration : 0, EnterpriseC),
          record: calculationrate(record, EnterpriseC),
          jobHealth: calculationrate(doc[0].jobHealth ? doc[0].jobHealth : 0, EnterpriseC),
          propagate: calculationrate(doc[0].propagate ? doc[0].propagate : 0, EnterpriseC),
          examination: calculationrate(doc[0].examination ? doc[0].examination : 0, EnterpriseC),
        });
      }
    }
    const nowTime = new Date(); // 当前时间
    const twelveTime = new Date(moment().subtract(1, 'years').format('YYYY-MM-DD')); // 前十二个月

    const EnterpriseInfo = await ctx.model.Adminorg.aggregate([{
      $match: {
        'workAddress.0.districts': jurisdiction,
      },
    },
    {
      $project: {
        aaa: 0,
        wdist: 0,
      },
    },
    {
      $group: {
        _id: null,
        count: {
          $sum: 1,
        },
        onlineDeclaraCompeted: {
          $sum: {
            $cond: [{
              $and: [{
                $lt: [ '$onlineDeclarationFiles.monthD', nowTime ],
              }, {
                $gte: [ '$onlineDeclarationFiles.monthD', twelveTime ],
              }],
            },
            1,
            0,
            ],
          },
        },
        jobCompeted: {
          $sum: {
            $cond: [{
              $and: [{
                $lt: [ '$reportTime', nowTime ],
              }, {
                $gte: [ '$reportTime', twelveTime ],
              }],
            },
            1,
            0,
            ],
          },
        },
        healthCompeted: {
          $sum: {
            $cond: [{
              $and: [{
                $lt: [ '$healcheckInfo.recentDay', nowTime ],
              }, {
                $gte: [ '$healcheckInfo.recentDay', twelveTime ],
              }],
            },
            1,
            0,
            ],
          },
        },
      },
    },
    ]);
    if (EnterpriseInfo.length === 0) {
      declareRate[declareRate.length - 1].OnlineDeclaration = calculationrate(0, EnterpriseCount);
      declareRate[declareRate.length - 1].jobHealth = calculationrate(0, EnterpriseCount);
      declareRate[declareRate.length - 1].examination = calculationrate(0, EnterpriseCount);
    } else {
      declareRate[declareRate.length - 1].OnlineDeclaration = calculationrate(EnterpriseInfo[0].onlineDeclaraCompeted ? EnterpriseInfo[0].onlineDeclaraCompeted : 0, EnterpriseInfo[0].count);
      declareRate[declareRate.length - 1].jobHealth = calculationrate(EnterpriseInfo[0].jobCompeted ? EnterpriseInfo[0].jobCompeted : 0, EnterpriseInfo[0].count);
      declareRate[declareRate.length - 1].examination = calculationrate(EnterpriseInfo[0].healthCompeted ? EnterpriseInfo[0].healthCompeted : 0, EnterpriseInfo[0].count);
    }
    return declareRate;
  }
  async getdDstrictsData({
    adcode,
    // year,
  }) {
    const {
      ctx,
    } = this;
    let fullAdcode;

    if (adcode === '100000') {
      fullAdcode = '0';
    } else {
      fullAdcode = adcode;
    }
    let dstrictsDoc = await ctx.model.District.find({
      parent_code: Array.isArray(fullAdcode) ? { $in: fullAdcode } : fullAdcode,
    });
    if (dstrictsDoc.length === 0) {
      return;
    }

    let parentDstricts = await ctx.model.District.find({
      area_code: Array.isArray(fullAdcode) ? { $in: fullAdcode } : fullAdcode,
    });
    if (dstrictsDoc[0].name === '直辖区') {
      dstrictsDoc = await ctx.model.District.find({
        parent_code: dstrictsDoc[0].area_code,
      });
    }
    if (!parentDstricts || parentDstricts.length === 0) {
      parentDstricts = [{
        level: 0,
        name: '中国',
        lng: '',
        lat: '',
        parent_code: '1*********00',
        area_code: '1*********00',
      }];
    }

    // console.log('你来时携风带雨', fullAdcode);

    const nowTime = new Date(); // 当前时间
    const tenTime = new Date(moment().subtract(10, 'months').format('YYYY-MM-DD')); // 前十个月
    const twelveTime = new Date(moment().subtract(1, 'years').format('YYYY-MM-DD')); // 前十二个月
    const twelveTime2 = new Date(moment().subtract(2, 'years').format('YYYY-MM-DD')); // 前十二个月
    const cityDeep = parentDstricts[0].name === '中国' ? 1 : parseInt(parentDstricts[0].level) + 2;
    const matchDistricts = parentDstricts.map(district => (district.name === '中国' ? '' : district.name));
    const queryParams = [
      {
        $unwind: '$workAddress',
      },
    ];
    if (matchDistricts.length > 0) {
      queryParams.push({
        $match: {
          'workAddress.districts': { $in: matchDistricts },
          productionStatus: { $ne: '0' },
          isactive: { $ne: '2' },
          $or: [
            { isDelete: false },
            { isDelete: null },
          ],
        },
      });
    }
    const EnterpriseInfo = await ctx.model.Adminorg.aggregate([
      ...queryParams,
      {
        $addFields: {
          wdist: '$workAddress.districts',
        },
      },
      {
        $addFields: {
          wdist2: {
            $slice: [ '$wdist', 0, cityDeep ],
          },
        },
      },
      {
        $project: {
          wdist: 0,
        },
      },
      {
        $group: {
          _id: '$wdist2',
          count: {
            $sum: 1,
          },
          levelCount: {
            $sum: {
              $cond: [{
                $eq: [ '$level', '严重' ],
              }, 1, 0 ],
            },
          },
          suspectedCount: {
            $sum: {
              // $toDouble: '$healcheckInfo.suspected',
              $cond: [{ $eq: [{ $toDouble: '$healcheckInfo.suspected' }, NaN ] }, 0, { $toDouble: '$healcheckInfo.suspected' }],
            },
          },
          odiseasedCount: {
            $sum: {
              // $toDouble: '$healcheckInfo.occupational',
              $cond: [{ $eq: [{ $toDouble: '$healcheckInfo.occupational' }, NaN ] }, 0, { $toDouble: '$healcheckInfo.occupational' }],
            },
          },
          forbidCount: {
            $sum: {
              // $toDouble: '$healcheckInfo.forbid',
              $cond: [{ $eq: [{ $toDouble: '$healcheckInfo.forbid' }, NaN ] }, 0, { $toDouble: '$healcheckInfo.forbid' }],
            },
          },
          onlineDeclaraExpire: {
            $sum: {
              $cond: [{
                $and: [{
                  $lt: [ '$onlineDeclarationFiles.monthD', twelveTime ],
                }, {
                  $gte: [ '$onlineDeclarationFiles.monthD', twelveTime2 ],
                }],
              },
              1,
              0,
              ],
            },
          },
          onlineDeclaraWilloExpire: {
            $sum: {
              $cond: [{
                $and: [{
                  $lt: [ '$onlineDeclarationFiles.monthD', tenTime ],
                }, {
                  $gte: [ '$onlineDeclarationFiles.monthD', twelveTime ],
                }],
              },
              1,
              0,
              ],
            },
          },
          onlineDeclaraCompeted: {
            $sum: {
              $cond: [{
                $and: [{
                  $lt: [ '$onlineDeclarationFiles.monthD', nowTime ],
                }, {
                  $gte: [ '$onlineDeclarationFiles.monthD', tenTime ],
                }],
              },
              1,
              0,
              ],
            },
          },
          jobExpire: {
            $sum: {
              $cond: [{
                $and: [{
                  $lt: [ '$reportTime', twelveTime ],
                }, {
                  $gte: [ '$reportTime', twelveTime2 ],
                }],
              },
              // { $lt: [ '$reportTime', twelveTime ] },
              1,
              0,
              ],
            },
          },
          jobWilloExpire: {
            $sum: {
              $cond: [{
                $and: [{
                  $lt: [ '$reportTime', tenTime ],
                }, {
                  $gte: [ '$reportTime', twelveTime ],
                }],
              },
              1,
              0,
              ],
            },
          },
          jobCompeted: {
            $sum: {
              $cond: {
                if: {
                  $and: [{
                    $lt: [ '$reportTime', nowTime ],
                  }, {
                    $gte: [ '$reportTime', tenTime ],
                  }],
                },
                then: 1,
                else: 0,
              },
            },
          },
          healthExpire: {
            $sum: {
              $cond: [{
                $and: [{
                  $lt: [ '$healcheckInfo.recentDay', twelveTime ],
                }, {
                  $gte: [ '$healcheckInfo.recentDay', twelveTime2 ],
                }],
              },
              1,
              0,
              ],
            },
          },
          healthWilloExpire: {
            $sum: {
              $cond: [{
                $and: [{
                  $lt: [ '$healcheckInfo.recentDay', tenTime ],
                }, {
                  $gte: [ '$healcheckInfo.recentDay', twelveTime ],
                }],
              },
              1,
              0,
              ],
            },
          },
          healthCompeted: {
            $sum: {
              $cond: [{
                $and: [{
                  $lt: [ '$healcheckInfo.recentDay', nowTime ],
                }, {
                  $gte: [ '$healcheckInfo.recentDay', tenTime ],
                }],
              },
              1,
              0,
              ],
            },
          },
          EIDArr: { $addToSet: '$_id' },
        },
      },
      {
        $addFields: { count: { $size: '$EIDArr' } },
      },
      {
        $project: { EIDArr: 0 },
      },
    ]);

    const exceedPoint = await ctx.model.Adminorg.aggregate([
      ...queryParams,
      {
        $addFields: {
          wdist: '$workAddress.districts',
        },
      },
      {
        $addFields: {
          wdist2: {
            $slice: [ '$wdist', 0, cityDeep ],
          },
        },
      },
      {
        $project: {
          checkResult: 1,
          wdist2: 1,
        },
      },
      {
        $match: {
          'checkResult.dust': { $exists: true },
        },
      },
      {
        $addFields: {
          dust: { $cond: [{ $toDouble: '$checkResult.dust.exceed' }, { $toDouble: '$checkResult.dust.exceed' }, 0 ] },
          heat: { $cond: [{ $toDouble: '$checkResult.heat.exceed' }, { $toDouble: '$checkResult.heat.exceed' }, 0 ] },
          chemical: { $cond: [{ $toDouble: '$checkResult.chemical.exceed' }, { $toDouble: '$checkResult.chemical.exceed' }, 0 ] },
          radiation: { $cond: [{ $toDouble: '$checkResult.radiation.exceed' }, { $toDouble: '$checkResult.radiation.exceed' }, 0 ] },
          biological: { $cond: [{ $toDouble: '$checkResult.biological.exceed' }, { $toDouble: '$checkResult.biological.exceed' }, 0 ] },
          noise: { $cond: [{ $toDouble: '$checkResult.noise.exceed' }, { $toDouble: '$checkResult.noise.exceed' }, 0 ] },
          powerFrequencyElectric: { $cond: [{ $toDouble: '$checkResult.powerFrequencyElectric.exceed' }, { $toDouble: '$checkResult.powerFrequencyElectric.exceed' }, 0 ] },
          ultraHighRadiation: { $cond: [{ $toDouble: '$checkResult.ultraHighRadiation.exceed' }, { $toDouble: '$checkResult.ultraHighRadiation.exceed' }, 0 ] },
          handBorneVibration: { $cond: [{ $toDouble: '$checkResult.handBorneVibration.exceed' }, { $toDouble: '$checkResult.handBorneVibration.exceed' }, 0 ] },
          highFrequencyEle: { $cond: [{ $toDouble: '$checkResult.highFrequencyEle.exceed' }, { $toDouble: '$checkResult.highFrequencyEle.exceed' }, 0 ] },
          laser: { $cond: [{ $toDouble: '$checkResult.laser.exceed' }, { $toDouble: '$checkResult.laser.exceed' }, 0 ] },
          microwave: { $cond: [{ $toDouble: '$checkResult.microwave.exceed' }, { $toDouble: '$checkResult.microwave.exceed' }, 0 ] },
          ultraviolet: { $cond: [{ $toDouble: '$checkResult.ultraviolet.exceed' }, { $toDouble: '$checkResult.ultraviolet.exceed' }, 0 ] },
          ionizatioRadial: { $cond: [{ $toDouble: '$checkResult.ionizatioRadial.exceed' }, { $toDouble: '$checkResult.ionizatioRadial.exceed' }, 0 ] },
          ionizatioSource: { $cond: [{ $toDouble: '$checkResult.ionizatioSource.exceed' }, { $toDouble: '$checkResult.ionizatioSource.exceed' }, 0 ] },
        },
      },
      {
        $project: {
          wdist2: 1,
          // totalExceed: { $sum: [ '$dust', '$heat', '$chemical', '$radiation', '$biological', '$noise', '$powerFrequencyElectric', '$ultraHighRadiation', '$handBorneVibration', '$highFrequencyEle', '$laser', '$microwave', '$ultraviolet', '$ionizatioRadial', '$ionizatioSource' ] },
          totalExceed: { $sum: [
            { $cond: [{ $eq: [ '$dust', NaN ] }, 0, '$dust' ] },
            { $cond: [{ $eq: [ '$heat', NaN ] }, 0, '$heat' ] },
            { $cond: [{ $eq: [ '$chemical', NaN ] }, 0, '$chemical' ] },
            { $cond: [{ $eq: [ '$radiation', NaN ] }, 0, '$radiation' ] },
            { $cond: [{ $eq: [ '$biological', NaN ] }, 0, '$biological' ] },
            { $cond: [{ $eq: [ '$noise', NaN ] }, 0, '$noise' ] },
            { $cond: [{ $eq: [ '$powerFrequencyElectric', NaN ] }, 0, '$powerFrequencyElectric' ] },
            { $cond: [{ $eq: [ '$ultraHighRadiation', NaN ] }, 0, '$ultraHighRadiation' ] },
            { $cond: [{ $eq: [ '$handBorneVibration', NaN ] }, 0, '$handBorneVibration' ] },
            { $cond: [{ $eq: [ '$highFrequencyEle', NaN ] }, 0, '$highFrequencyEle' ] },
            { $cond: [{ $eq: [ '$laser', NaN ] }, 0, '$laser' ] },
            { $cond: [{ $eq: [ '$microwave', NaN ] }, 0, '$microwave' ] },
            { $cond: [{ $eq: [ '$ultraviolet', NaN ] }, 0, '$ultraviolet' ] },
            { $cond: [{ $eq: [ '$ionizatioRadial', NaN ] }, 0, '$ionizatioRadial' ] },
            { $cond: [{ $eq: [ '$ionizatioSource', NaN ] }, 0, '$ionizatioSource' ] },
          ] },
        },
      },
      {
        $group: {
          _id: '$wdist2',
          totalExceed: { $sum: '$totalExceed' },
        },
      },
    ]);

    const subFun = function(count, ...val) {
      const res = val.reduce((prev, current) => {
        return parseInt(prev) + parseInt(current);
      });
      return parseInt(count) - res;
    };
    // console.log(9999, EnterpriseInfo);
    const newData = {};

    if (dstrictsDoc.length > 0) {
      if (parentDstricts[0].level === '2') {
        dstrictsDoc.push({
          level: '3',
          name: '',
          lng: '0',
          lat: '0',
          area_code: parentDstricts[0].area_code,
          parent_code: parentDstricts[0].area_code,
        });
      }
      for (let i = 0; i < dstrictsDoc.length; i++) {
        if (dstrictsDoc[i].name === '市辖区') {
          continue;
        }
        const data = [];
        if (dstrictsDoc[i].name) {
          data.push(...[ dstrictsDoc[i].lng, dstrictsDoc[i].lat, dstrictsDoc[i].name ? dstrictsDoc[i].name : '未知地区' ]);
        } else {
          console.log(444444444, 'getdDstrictsData获取到未知区域！！！');
          continue;
        }

        const doc = EnterpriseInfo.filter(item => {
          return item._id && item._id.includes(dstrictsDoc[i].name);
        });
        const exceedPointDoc = exceedPoint.filter(item => {
          return item._id && item._id.includes(dstrictsDoc[i].name);
        });
        // console.log(333333, exceedPointDoc);
        if (doc.length > 0) {
          data.push(...[
            doc[0].count,
            [ doc[0].count, doc[0].onlineDeclaraCompeted, doc[0].onlineDeclaraWilloExpire, doc[0].onlineDeclaraExpire,
              subFun(doc[0].count, doc[0].onlineDeclaraCompeted, doc[0].onlineDeclaraWilloExpire, doc[0].onlineDeclaraExpire),
            ],
            [ doc[0].count, doc[0].jobCompeted, doc[0].jobWilloExpire, doc[0].jobExpire,
              subFun(doc[0].count, doc[0].jobCompeted, doc[0].jobWilloExpire, doc[0].jobExpire),
            ],
            [ doc[0].count, doc[0].healthCompeted, doc[0].healthWilloExpire, doc[0].healthExpire,
              subFun(doc[0].count, doc[0].healthCompeted, doc[0].healthWilloExpire, doc[0].healthExpire),
            ],
            doc[0].suspectedCount,
            doc[0].forbidCount,
            doc[0].odiseasedCount,
            doc[0].levelCount,
          ]);
        } else {
          data.push(...[
            0,
            [ 0, 0, 0, 0, 0 ],
            [ 0, 0, 0, 0, 0 ],
            [ 0, 0, 0, 0 ],
            0,
            0,
            0,
            0,
          ]);
        }
        if (exceedPointDoc.length > 0) {
          data.push(exceedPointDoc[0].totalExceed);
        } else {
          data.push(0);
        }
        if (dstrictsDoc[i].level === '3') {
          // 直接12位吧
          newData[dstrictsDoc[i].area_code] = data;
        } else {
          newData[dstrictsDoc[i].area_code.slice(0, 6)] = data;
        }
      }
    }
    return newData;
  }
  async getVisualizationData({ adcode, year }) {
    const { ctx } = this;
    const superUserInfo = ctx.session.superUserInfo ? ctx.session.superUserInfo : '';
    if (superUserInfo.regAdd && superUserInfo.regAdd.length === 4) {
      const districtRes = await ctx.model.District.findOne({ name: superUserInfo.regAdd[superUserInfo.regAdd.length - 1] });
      districtRes && (adcode = districtRes.area_code);
    }

    let fullAdcode;
    if (adcode === '100000') {
      fullAdcode = '0';
    } else {
      const startIndex = (adcode + '').length;
      fullAdcode = adcode;
      for (let i = 0; i < (12 - startIndex); i++) {
        fullAdcode += '0';
      }
    }
    let dstrictsDoc = await ctx.model.District.findOne({
      area_code: fullAdcode,
    });
    // 预警相关
    const query = {
      status: { $ne: 5 },
      regAddr: [ dstrictsDoc ],
      limit: 9999,
      year: year || new Date().getFullYear(),
    };
    const warningList = await ctx.service.warning.find(query);
    if (!dstrictsDoc) {
      dstrictsDoc = {
        level: 0,
        name: '中国',
        lng: '',
        lat: '',
        parent_code: '1*********00',
        area_code: '1*********00',
      };
    }

    // console.log(111111111, fullAdcode, dstrictsDoc);

    const nowTime = new Date(); // 当前时间
    const tenTime = new Date(moment().subtract(10, 'months').format('YYYY-MM-DD')); // 前十个月
    const twelveTime = new Date(moment().subtract(1, 'years').format('YYYY-MM-DD')); // 前十二个月
    const twelveTime2 = new Date(moment().subtract(2, 'years').format('YYYY-MM-DD')); // 前二四个月
    const matchDistricts = dstrictsDoc.name === '中国' ? '' : dstrictsDoc.name;
    const cityDeep = dstrictsDoc.name === '中国' ? 1 : parseInt(dstrictsDoc.level) + 2;
    // console.time('facet');
    const queryParams = [
    ];
    if (matchDistricts) {
      queryParams.push({
        $match: {
          'workAddress.districts': matchDistricts,
          productionStatus: { $ne: '0' },
          isactive: { $ne: '2' },
          isDelete: { $ne: true },
        },
      });
    }
    console.log(11111111, ...queryParams);
    const resss = await ctx.model.Adminorg.aggregate([
      ...queryParams,
      {
        $facet: {
          baseInfo: [{
            $group: {
              _id: null,
              count: {
                $sum: 1,
              },
              suspectedCount: {
                $sum: {
                  // $toDouble: '$healcheckInfo.suspected',
                  $cond: [{ $eq: [{ $toDouble: '$healcheckInfo.suspected' }, NaN ] }, 0, { $toDouble: '$healcheckInfo.suspected' }],
                },
              },
              odiseasedCount: {
                $sum: {
                  // $toDouble: '$healcheckInfo.occupational',
                  $cond: [{ $eq: [{ $toDouble: '$healcheckInfo.occupational' }, NaN ] }, 0, { $toDouble: '$healcheckInfo.occupational' }],
                },
              },
              forbidCount: {
                $sum: {
                  // $toDouble: '$healcheckInfo.forbid',
                  $cond: [{ $eq: [{ $toDouble: '$healcheckInfo.forbid' }, NaN ] }, 0, { $toDouble: '$healcheckInfo.forbid' }],
                },
              },
              onlineDeclaraExpire: {
                $sum: {
                  $cond: [{
                    $and: [{
                      $lt: [ '$onlineDeclarationFiles.monthD', twelveTime ],
                    }, {
                      $gte: [ '$onlineDeclarationFiles.monthD', twelveTime2 ],
                    }],
                  },
                  1,
                  0,
                  ],
                },
              },
              onlineDeclaraWilloExpire: {
                $sum: {
                  $cond: [{
                    $and: [{
                      $lt: [ '$onlineDeclarationFiles.monthD', tenTime ],
                    }, {
                      $gte: [ '$onlineDeclarationFiles.monthD', twelveTime ],
                    }],
                  },
                  1,
                  0,
                  ],
                },
              },
              onlineDeclaraCompeted: {
                $sum: {
                  $cond: [{
                    $and: [{
                      $lt: [ '$onlineDeclarationFiles.monthD', nowTime ],
                    }, {
                      $gte: [ '$onlineDeclarationFiles.monthD', tenTime ],
                    }],
                  },
                  1,
                  0,
                  ],
                },
              },
              jobExpire: {
                $sum: {
                  $cond: [{
                    $and: [{
                      $lt: [ '$reportTime', twelveTime ],
                    },
                    {
                      $ifNull: [ '$reportTime', false ],
                    }],
                  },
                  1,
                  0,
                  ],
                },
              },
              jobWilloExpire: {
                $sum: {
                  $cond: [{
                    $and: [{
                      $lt: [ '$reportTime', tenTime ],
                    }, {
                      $gte: [ '$reportTime', twelveTime ],
                    }],
                  },
                  1,
                  0,
                  ],
                },
              },
              jobCompeted: {
                $sum: {
                  $cond: {
                    if: {
                      $and: [{
                        $lt: [ '$reportTime', nowTime ],
                      }, {
                        $gte: [ '$reportTime', tenTime ],
                      }],
                    },
                    then: 1,
                    else: 0,
                  },
                },
              },
              healthExpire: {
                $sum: {
                  $cond: [{
                    $and: [{
                      $lt: [ '$healcheckInfo.recentDay', twelveTime ],
                    }, {
                      $gte: [ '$healcheckInfo.recentDay', twelveTime2 ],
                    }],
                  },
                  1,
                  0,
                  ],
                },
              },
              healthWilloExpire: {
                $sum: {
                  $cond: [{
                    $and: [{
                      $lt: [ '$healcheckInfo.recentDay', tenTime ],
                    }, {
                      $gte: [ '$healcheckInfo.recentDay', twelveTime ],
                    }],
                  },
                  1,
                  0,
                  ],
                },
              },
              healthCompeted: {
                $sum: {
                  $cond: [{
                    $and: [{
                      $lt: [ '$healcheckInfo.recentDay', nowTime ],
                    }, {
                      $gte: [ '$healcheckInfo.recentDay', tenTime ],
                    }],
                  },
                  1,
                  0,
                  ],
                },
              },
            },
          }],
          // 1为丙类，2为乙类，3为甲类 风险等级
          riskSortLevel: [{
            $group: {
              _id: '$riskSortLevel',
              count: {
                $sum: 1,
              },
            },
          }],
          // 0为低，1为中，2为高 暴露风险等级
          ExposeRiskLevel: [{
            $group: {
              _id: '$exposeRiskLevel',
              count: {
                $sum: 1,
              },
            },
          }],
          // 职业病风险等级
          zybLevel: [{
            $group: {
              _id: '$level',
              count: {
                $sum: 1,
              },
            },
          }],
          // 行业分类
          industryCategory: [
            {
              $match: {
                industryCategory: {
                  $exists: true,
                  $ne: null,
                },
              },
            },
            {
              $unwind: '$industryCategory',
            },
            {
              $match: {
                'industryCategory.1': {
                  $exists: true,
                },
              },
            },
            { $addFields: { industryCategoryFirst: { $arrayElemAt: [ '$industryCategory', 0 ] } } },
            { $group: { _id: '$industryCategoryFirst', count: { $sum: 1 } } },
            {
              $sort: {
                count: -1,
              },
            },
            {
              $limit: 8,
            },
            {
              $lookup: {
                from: 'industryCategory',
                foreignField: 'value',
                localField: '_id',
                as: 'industryCategory',
              },
            },
            {
              $unwind: '$industryCategory',
            },
            {
              $project: {
                count: '$count',
                name: '$industryCategory.label',
              },
            },
          ],
          // 企业类型
          regType: [
            {
              $addFields: {
                regType2: {
                  $convert: {
                    input: '$regType',
                    to: 'double',
                    onError: '',
                    onNull: '',
                  },
                },
              },

            },
            {
              $match: {
                regType2: {
                  $exists: true,
                  $ne: '',
                },
              },
            },
            {
              $group: {
                _id: '$regType',
                count: {
                  $sum: 1,
                },
              },
            },
            {
              $lookup: {
                from: 'economicTypes',
                localField: '_id',
                foreignField: 'code',
                as: 'type',
              },
            },
            {
              $unwind: {
                path: '$type',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $addFields: {
                typeName: {
                  $arrayElemAt: [ '$type.content', 0 ],
                },
              },
            },
            {
              $addFields: {
                typeName: {
                  $ifNull: [ '$typeName', '$_id' ],
                },
              },
            },
            {
              $project: {
                typeName: 1,
                count: 1,
              },
            },
            {
              $sort: {
                count: -1,
              },
            },
          ],
          // 企业规模
          companyScale: [{
            $match: {
              companyScale: {
                $exists: true,
                $ne: '',
              },
            },
          },
          {
            $group: {
              _id: '$companyScale',
              count: {
                $sum: 1,
              },
            },
          },
          {
            $sort: {
              count: -1,
            },
          },
          ],
          proPagate: [{
            $lookup: {
              from: 'propagate',
              foreignField: 'EnterpriseID',
              localField: '_id',
              as: 'propagates',
            },
          }, {
            $match: {
              propagates: { $not: { $size: 0 } },
            },
          }, {
            $project: {
              _id: 1,
            },
          }, {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          }],
          // 各辖区情况
          jurisdiction: [
            {
              $unwind: '$workAddress',
            },
            {
              $addFields: {
                wdist: '$workAddress.districts',
              },
            },
            {
              $addFields: {
                wdist2: {
                  $slice: [ '$wdist', 0, cityDeep ],
                },
              },
            },
            {
              $project: {
                wdist: 0,
              },
            },
            {
              $match: {
                wdist2: {
                  $ne: '',
                },
              },
            },
            {
              $match: {
                wdist2: {
                  $ne: [],
                },
              },
            },
            {
              $group: {
                _id: '$wdist2',
                count: {
                  $sum: 1,
                },
                onlineDeclaraCompeted: {
                  $sum: {
                    $cond: [{
                      $and: [{
                        $lt: [ '$onlineDeclarationFiles.monthD', nowTime ],
                      }, {
                        $gte: [ '$onlineDeclarationFiles.monthD', twelveTime ],
                      }],
                    },
                    1,
                    0,
                    ],
                  },
                },
                jobCompeted: {
                  $sum: {
                    $cond: {
                      if: {
                        $and: [{
                          $lt: [ '$reportTime', nowTime ],
                        }, {
                          $gte: [ '$reportTime', twelveTime ],
                        }],
                      },
                      then: 1,
                      else: 0,
                    },
                  },
                },
                healthCompeted: {
                  $sum: {
                    $cond: [{
                      $and: [{
                        $lt: [ '$healcheckInfo.recentDay', nowTime ],
                      }, {
                        $gte: [ '$healcheckInfo.recentDay', twelveTime ],
                      }],
                    },
                    1,
                    0,
                    ],
                  },
                },
              },
            }],
          // 激活状态
          activeCount: [
            {
              $lookup: {
                from: 'adminusers',
                foreignField: '_id',
                localField: 'adminUserId',
                as: 'adminusers',
              },
            },
            {
              $unwind: '$adminusers',
            },
            {
              $project: {
                adminusers: 1,
              },
            },
            {
              $addFields: { phoneNum: '$adminusers.phoneNum' },
            },
            {
              $match: {
                phoneNum: { $ne: '' },
              },
            },
            {
              $count: 'activeCount',
            },
          ],
        },
      },
    ]);

    // console.log(222222222222, resss[0]);


    // 三同时项目
    const projectInfo = await ctx.model.Project.aggregate([{
      $lookup: {
        from: 'adminorgs',
        foreignField: '_id',
        localField: 'EnterpriseID',
        as: 'adminorg',
      },
    },
    {
      $addFields: {
        adminorg: { $arrayElemAt: [ '$adminorg', 0 ] },
      },
    },
    {
      $addFields: {
        workAddress: '$adminorg.workAddress',
      },
    },
    ...queryParams,
    ]);

    // 三同时结果
    const projectRes = {
      allCount: projectInfo.length,
      preComment: 0,
      facilityDesign: 0,
      ImpactAssessment: 0,
    };
    projectRes.allCount = projectInfo.length;
    projectInfo.forEach(item => {
      item.evaluated.forEach(item2 => {
        if (item2.status) {
          projectRes[item2.category]++;
        }
      });
    });
    // console.log('三同时', projectRes);

    // 辖区企业综合风险等级
    const riskAssessment = await ctx.model.RiskAssessmentReport.aggregate([{
      $lookup: {
        from: 'adminorgs',
        foreignField: '_id',
        localField: 'EnterpriseID',
        as: 'adminorg',
      },
    },
    {
      $addFields: {
        adminorg: { $arrayElemAt: [ '$adminorg', 0 ] },
      },
    },
    {
      $addFields: {
        workAddress: '$adminorg.workAddress',
      },
    },
    {
      $match: {
        year: {
          $regex: String(year),
        },
      },
    },
    ...queryParams,
    {
      $project: {
        assessmentResult: 1,
        year: 1,
      },
    },
    {
      $group: {
        _id: '$assessmentResult',
        count: {
          $sum: 1,
        },
      },
    },
    ]);
    // 辖区健康管理状况
    const date = String(year) + '-01-01';
    const preventAssess = await ctx.model.PreventAssess.aggregate([{
      $lookup: {
        from: 'adminorgs',
        foreignField: '_id',
        localField: 'EnterpriseID',
        as: 'adminorg',
      },
    },
    {
      $addFields: {
        adminorg: { $arrayElemAt: [ '$adminorg', 0 ] },
      },
    },
    {
      $addFields: {
        workAddress: '$adminorg.workAddress',
      },
    },
    ...queryParams,
    {
      $match: {
        year: new Date(date),
      },
    },
    // {
    //   $match: {
    //     'adminorg.0.workAddress.0.districts': matchDistricts,
    //   },
    // },
    {
      $project: {
        level: 1,
      },
    },
    {
      $group: {
        _id: '$level',
        count: {
          $sum: 1,
        },
      },
    },
    ]);
    const dataRes = resss[0];

    const districtList = await ctx.model.District.find({ parent_code: fullAdcode }, { area_code: 1, name: 1 });

    return {
      districtList,
      ...dataRes.baseInfo[0],
      riskSortLevel: dataRes.riskSortLevel,
      ExposeRiskLevel: dataRes.ExposeRiskLevel,
      zybLevel: dataRes.zybLevel,
      industryCategory: dataRes.industryCategory,
      regType: dataRes.regType,
      companyScale: dataRes.companyScale,
      proPagate: dataRes.proPagate,
      riskAssessment,
      projectRes,
      jurisdiction: dataRes.jurisdiction,
      preventAssess,
      activeCount: dataRes.activeCount[0] ? dataRes.activeCount[0].activeCount : 0,
      warningList, // 预警列表
    };
  }
  async getAdCode(EnterpriseID) {
    const {
      ctx,
    } = this;
    const { crossRegionManage } = ctx.app.config;
    if (crossRegionManage) {
      const res = await ctx.model.SuperUser.findOne({
        _id: EnterpriseID,
      }).lean();
      return res.crossRegionArea;
    }
    const res = await ctx.model.SuperUser.findOne({
      _id: EnterpriseID,
    }).lean();
    return res.area_code;
  }
  async getAdCodeName(adcode) {
    const res = await this.ctx.model.District.findOne({
      area_code: adcode.length === 12 ? adcode : adcode + '000000',
    });
    if (res) {
      return [ res.name ];
    }
    return [ '中国' ];
  }
  async selectArea(adcode) {
    const {
      ctx,
    } = this;
    const city = await ctx.model.District.findOne({
      area_code: adcode.length === 12 ? adcode : adcode + '000000',
    });
    if (city) {
      return city.name;
    }
    return '中国';
  }
  async searchCompany(companyName, jurisdiction) {
    const {
      ctx,
    } = this;
    let Enterprises;
    jurisdiction = jurisdiction === '中国' ? {
      $exists: true,
    } : jurisdiction;
    if (companyName) {
      Enterprises = await ctx.model.StatisticalTable.aggregate([{
        $match: {
          regAdd: jurisdiction,
        },
      },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'EnterpriseID',
          foreignField: '_id',
          as: 'Enterprise',
        },
      },
      {
        $match: {
          'Enterprise.cname': {
            $regex: companyName,
          },
        },
      },
      ]);
    }

    return {
      Enterprises,
    };
  }
  async loadMoreCompany({
    districtRegAdd,
    year = new Date(),
    currentIndex = 1,
    recordComplete,
    listType,
  }) {
    const {
      ctx,
    } = this;
    const pageSize = 20;
    const fullYear = moment('' + year).year();
    // const recordField = 'recordList.' + fullYear; // 要筛选的档案年度
    let listField;
    switch (listType) {
      case 'record':
        listField = 'recordList.' + fullYear; // 要筛选的档案年度
        break;
      case 'examination':
        listField = 'healthList.' + fullYear;
        break;
      case 'OnlineDeclaration':
      case 'jobHealth':
      case 'propagate':
        listField = 'completeList.' + fullYear;
        break;
      default:
        break;
    }
    year = new Date((1 + parseInt(year)) + ''); // 保存选择的年度
    // const jurisdiction = districtRegAdd[districtRegAdd.length - 1];
    const jurisdiction = districtRegAdd[districtRegAdd.length - 1] === '中国' ? {
      $exists: true,
    } : districtRegAdd[districtRegAdd.length - 1]; // 获取管辖区域

    let pipe;
    // 已完成
    if (parseInt(recordComplete) === 1) {
      if (listType === 'record') {
        pipe = {
          $match: {
            regAdd: jurisdiction,
            regTime: {
              $lte: year,
            },
            [listField]: {
              $size: 4,
            },
          },
        };
      } else {
        pipe = {
          $match: {
            regAdd: jurisdiction,
            regTime: {
              $lte: year,
            },
            [listField]: listType,
          },
        };
      }
    } else if (parseInt(recordComplete) === -1) {
      if (listType === 'record') {
        pipe = {
          $match: {
            regAdd: jurisdiction,
            regTime: {
              $lte: year,
            },
            [listField]: {
              $not: {
                $size: 4,
              },
            },
          },
        };
      } else {
        pipe = {
          $match: {
            regAdd: jurisdiction,
            regTime: {
              $lte: year,
            },
            [listField]: {
              $not: {
                $eq: listType,
              },
            },
          },
        };
      }
    } else {
      // 全部
      pipe = {
        $match: {
          regAdd: jurisdiction,
          regTime: {
            $lte: year,
          },
        },
      };
    }
    const Enterprises = await ctx.model.StatisticalTable.aggregate([
      pipe,
      {
        $skip: (currentIndex - 1) * pageSize,
      },
      {
        $limit: pageSize,
      },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'EnterpriseID',
          foreignField: '_id',
          as: 'Enterprise',
        },
      },
      {
        $project: {
          'Enterprise.area': 0,
          'Enterprise.message': 0,
          'Enterprise.money': 0,
          'Enterprise.level': 0,
          'Enterprise.PID': 0,
          'Enterprise.adminUserId': 0,
          'Enterprise.type': 0,
        },
      },
    ]);
    return {
      Enterprises,
    };
  }
  async getPhysicalExaminationChartData(districtRegAdd, selectYear) {
    const {
      ctx,
    } = this;
    const year = selectYear;
    const yearArr = [ year - 3, year - 2, year - 1, year ]; // 近4年
    const yearArrNum = [ 0, 0, 0, 0 ]; // 健康检查过的人数
    let yearArrRate = [ 0, 0, 0, 0 ]; // 健康检查率

    const res = await ctx.model.StatisticalTable.aggregate([{
      $match: {
        regAdd: districtRegAdd[0],
      },
    },
    {
      $lookup: {
        from: 'adminorgs',
        localField: 'EnterpriseID',
        foreignField: '_id',
        as: 'Enterprise',
      },
    },
    {
      $project: {
        'Enterprise.area': 0,
        'Enterprise.message': 0,
        'Enterprise.money': 0,
        'Enterprise.level': 0,
        'Enterprise.PID': 0,
        'Enterprise.adminUserId': 0,
        'Enterprise.type': 0,
      },
    },
    ]);

    res.forEach(item => {
      item.healthList && Object.keys(item.healthList).forEach(item2 => {
        if (item.healthList[item2].indexOf('examination') !== '-1') {
          if (item2 === yearArr[0].toString()) {
            yearArrNum[0]++;
          } else if (item2 === yearArr[1].toString()) {
            yearArrNum[1]++;
          } else if (item2 === yearArr[2].toString()) {
            yearArrNum[2]++;
          } else if (item2 === yearArr[3].toString()) {
            yearArrNum[3]++;
          }
        }

      });


    });

    if (res.length === 0) {
      yearArrRate = [ 0, 0, 0, 0 ];
    } else {
      yearArrRate[0] = yearArrNum[0] / res.length * 100;
      yearArrRate[1] = yearArrNum[1] / res.length * 100;
      yearArrRate[2] = yearArrNum[2] / res.length * 100;
      yearArrRate[3] = yearArrNum[3] / res.length * 100;
    }

    // console.log("????////",yearArrNum);
    return {
      // PhysicalExaminationNumber:res.length,//总体检企业数量
      // yearArrNum,//近四年的已经 体检过的 企业数量
      yearArr, // 近四年 年份
      yearArrRate, // 健康检查率
    };
  }
  async getServiceProject({
    organization,
    currentIndex,
    pageSize,
  }) {
    const {
      ctx,
    } = this;
    const docSize = await ctx.model.JobHealth.find({
      serviceOrgID: organization,
    }).count();
    const doc = await ctx.model.JobHealth.find({
      serviceOrgID: organization,
    }).sort({
      expectStartTime: 1,
    }).skip((currentIndex - 1) * pageSize)
      .limit(pageSize);
    return {
      doc,
      docSize,
    };
  }
  async warningEvent(query) {
    const { ctx } = this;
    // query.regAddr = [ '江山市' ];
    query.limit = 9999;
    query.year = query.year || new Date().getFullYear();
    const res = await ctx.service.warning.find(query);
    // const res = await ctx.model.Warning.aggregate(
    //   [
    //     { $match: { supervisionId: superUserId } },
    //     { $sort: { ctime: -1 } },
    //     { $lookup: { from: 'adminorgs', localField: 'companyId', foreignField: '_id', as: 'new' } },
    //     // { $limit: 3 },
    //     { $project: { 'new.cname': 1 } },
    //   ]
    // );
    return res;
  }
  async getPhyProjects(params) {
    const query = {
      reportStatus: true,
      EnterpriseID: { $exists: true },
      'workAddress.districts': params.regAddr,
      year: params.year,
    };

    // 获取所有项目
    const healthchecks = await this.ctx.model.Healthcheck.aggregate([
      { $match: query },
      {
        $project: {
          actuallNum: 1,
          suspected: 1,
          forbid: 1,
          re_examination: 1,
          otherDisease: 1,
          normal: 1,
          physicalExaminationOrgID: 1,
          EnterpriseID: 1,
          recheck: 1,
        },
      },
      // {
      //   $group: {
      //     _id: '$physicalExaminationOrgID',
      //     actuallNum: { $sum: '$actuallNum' },
      //     suspected: { $sum: '$suspected' },
      //     forbid: { $sum: '$forbid' },
      //     re_examination: { $sum: '$re_examination' },
      //     otherDisease: { $sum: '$otherDisease' },
      //     normal: { $sum: '$normal' },
      //   }
      // }
    ]);

    const heackObj = {};
    for (let i = 0; i < healthchecks.length; i++) {
      const healthcheck = healthchecks[i];
      if (!heackObj[healthcheck.physicalExaminationOrgID]) {
        heackObj[healthcheck.physicalExaminationOrgID] = {
          projectCount: 0,
          actuallNum: 0, // 实检人数
          suspected: 0, // 疑似职业病
          forbid: 0, // 禁忌证
          re_examination: 0, // 复查人数
          otherDisease: 0, // 其他疾病
          normal: 0, // 未见异常
          recheckProject: 0, // 复查项目数
          recheckProjectActuallNum: 0, // 实际复查数
          Enterprises: [], // 企业
        };
      }
      heackObj[healthcheck.physicalExaminationOrgID].actuallNum += healthcheck.actuallNum;
      heackObj[healthcheck.physicalExaminationOrgID].suspected += healthcheck.suspected;
      heackObj[healthcheck.physicalExaminationOrgID].forbid += healthcheck.forbid;
      heackObj[healthcheck.physicalExaminationOrgID].re_examination += healthcheck.re_examination;
      heackObj[healthcheck.physicalExaminationOrgID].otherDisease += healthcheck.otherDisease;
      heackObj[healthcheck.physicalExaminationOrgID].normal += healthcheck.normal;
      heackObj[healthcheck.physicalExaminationOrgID].Enterprises.push(healthcheck.EnterpriseID);
      heackObj[healthcheck.physicalExaminationOrgID].projectCount += 1;

      if (healthcheck.recheck) {
        heackObj[healthcheck.physicalExaminationOrgID].recheckProject += 1;
        heackObj[healthcheck.physicalExaminationOrgID].recheckProjectActuallNum += healthcheck.actuallNum;
      }
    }
    const healthcheckArr = [];
    Object.keys(heackObj).forEach(key => {
      const item = heackObj[key];
      item.Enterprises = Array.from(new Set(item.Enterprises)).length;
      item._id = key;
      healthcheckArr.push(item);
    });
    // console.log(**********, healthcheckArr)

    return healthcheckArr;
  }
  async findUser(EnterpriseID) {
    const { ctx } = this;
    const superUserID = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';

    const serviceOrgUser = await ctx.model.SuperUser.findOne({ _id: superUserID });
    const adminorg = await ctx.model.Adminorg.findOne({ _id: EnterpriseID }, { cname: 1 });


    const adminuser = await ctx.model.AdminUser.findOne({ userName: adminorg.cname + '托管账号' });

    if (adminuser) {
      return adminuser._id;
    }
    const powerGroup = await ctx.model.AdminGroup.findOne({ name: '企业端档案托管用户' });

    // 为机构创建临时的企业用户
    const user = {
      userName: adminorg.cname + '托管账号',
      name: serviceOrgUser.cname,
      password: '123456',
      group: powerGroup._id,
      // phoneNum: serviceOrgUser ? serviceOrgUser.phoneNum : '暂无',
      IDcard: '暂无',
    };

    // const res = await new ctx.model.AdminUser(user).save();
    // const res = await ctx.service.db.create('AdminUser', user);
    const res = await ctx.model.AdminUser.create(user);
    // await ctx.model.Adminorg.update({ _id: EnterpriseID }, { $push: { adminArray: res._id } });
    await ctx.service.db.updateOne('Adminorg', { _id: EnterpriseID }, { $push: { adminArray: res._id } });
    return res._id;

  }

}

module.exports = HomeService;
