const Service = require('egg').Service;
// 福州-大屏统计 create by xjy
class StatisticFzService extends Service {
  // 监测用人单位职业健康培训统计
  async getStatisticalFz_JCYRDWZYJKPXTJ(adcode) {
    const { ctx } = this;
    const districtsData = await ctx.service.home.getdDstrictsData({ adcode });
    const districtNames = Object.values(districtsData).map(entry => entry[2]);
    // const res = [];
    // console.log('districtNames', districtNames);
    const currentYear = new Date().getFullYear();
    // const threeYearsAgo = currentYear - 3;
    const table = 'DP_ZYWSDP_JCYRDWZYJKPXTJ';
    const result = await this.ctx.model.Adminorg.aggregate([
      {
        $unwind: {
          path: '$workAddress',
        },
      },
      {
        $match: {
          'workAddress.districts': {
            $elemMatch: {
              $in: districtNames,
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          year: 1,
          workAddress: 1,
          moneny: 1,
        },
      },
      {
        $lookup:
        {
          from: 'propagate',
          localField: '_id',
          foreignField: 'EnterpriseID',
          as: 'propagate_info',
        },
      },
      {
        $lookup:
        {
          from: 'employees',
          localField: '_id',
          foreignField: 'EnterpriseID',
          as: 'employees_info',
        },
      },
      {
        $unwind: {
          path: '$workAddress.districts',
          includeArrayIndex: 'arrayIndex',
        },
      },
      {
        $match: {
          arrayIndex: 2,
        },
      },
      {
        $group: {
          _id: '$workAddress.districts',
          documents: {
            $push: '$$ROOT',
          },
        },
      },
      {
        $sort: {
          _id: 1,
        },
      },
    ]);
    // console.log('JCYRDWZYJKPXTJresult', JSON.stringify(result));
    const JCYRDWZYJKPXTJ = {
      DQ: '',
      YRDWS: 0,
      FZRPXQYS: 0,
      GLRYPXQYS: 0,
      LDZZRS: 0,
      LDZPXRS: 0,
      LDZPXL: 0,
      HYLX: '制造业',
      QYGM: '100万',
      ND: '',
    };
    JCYRDWZYJKPXTJ.ND = currentYear;
    for (let i = 0; i < result.length; i++) {
      const item = result[i];
      JCYRDWZYJKPXTJ.DQ = item._id;
      JCYRDWZYJKPXTJ.YRDWS = item.documents.length;
      for (let j = 0; j < item.documents.length; j++) {
        const doc = item.documents[j];
        // if (doc.year < threeYearsAgo) {
        //   continue;
        // }
        JCYRDWZYJKPXTJ.LDZZRS += doc.employees_info.length;
        if (doc.propagate_info && doc.propagate_info.length !== 0) {
          for (let k = 0; k < doc.propagate_info.length; k++) {
            const entry = doc.propagate_info[k];
            if (entry.type === '负责人培训') {
              JCYRDWZYJKPXTJ.FZRPXQYS++;
            } else if (entry.type === '管理人员培训') {
              JCYRDWZYJKPXTJ.GLRYPXQYS++;
            } else if (entry.type === '劳动者培训') {
              JCYRDWZYJKPXTJ.LDZPXRS++;
            }
          }
        }
        JCYRDWZYJKPXTJ.LDZPXL = JCYRDWZYJKPXTJ.LDZPXRS === 0 ? 0 : (JCYRDWZYJKPXTJ.LDZPXRS / JCYRDWZYJKPXTJ.LDZZRS * 100).toFixed(0);
      }
      // console.log('JCYRDWZYJKPXTJ', JCYRDWZYJKPXTJ);
      const res = await this.ctx.model.DpStatistical.findOne({ table, adcode });
      if (res) {
        await ctx.model.DpStatistical.updateOne({
          table, adcode,
        }, {
          $push: {
            data: JCYRDWZYJKPXTJ,
          },
        },
        { upsert: true });
      } else {
        await this.ctx.model.DpStatistical.create({
          table,
          adcode,
          data: [ JCYRDWZYJKPXTJ ],
        });
      }
    }
  }

  // 职业健康检查统计数据——Healthcheck
  async getStatisticalFz_ZYJKJCTJ(adcode) {
    const { ctx } = this;
    const districtsData = await ctx.service.home.getdDstrictsData({ adcode });
    const districtNames = Object.values(districtsData).map(entry => entry[2]);
    const currentYear = new Date().getFullYear();
    // const threeYearsAgo = currentYear - 3;
    const table = 'DP_ZYWSDP_ZYJKJCTJ';
    const result = await this.ctx.model.Healthcheck.aggregate([
      {
        $unwind: {
          path: '$workAddress',
        },
      },
      {
        $match: {
          'workAddress.districts': {
            $elemMatch: {
              $in: districtNames,
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          year: 1,
          workAddress: 1,
          shouldCheckNum: 1, // 应检人数
          actuallNum: 1, // 实际人数
          re_examination: 1, // 复查人数
          //   suspected: 1, // 疑似职业病
          //   forbid: 1, // 禁忌证
          //   otherDisease: 1, // 其他疾病
          //   recheck: 1, // 是否复查
          normal: 1, // 未见异常
        },
      },
      {
        $unwind: {
          path: '$workAddress.districts',
          includeArrayIndex: 'arrayIndex',
        },
      },
      {
        $match: {
          arrayIndex: 2,
        },
      },
      {
        $group: {
          _id: '$workAddress.districts',
          documents: {
            $push: '$$ROOT',
          },
        },
      },
      {
        $sort: {
          _id: 1,
        },
      },
    ]);
    const ZYJKJCTJ = {
      DQ: '',
      NF: '',
      YJRS: 0,
      TJL: 0,
      KFCRS: 0,
      FCWCL: 0,
      TJYCL: 0,
    };
    // 遍历 result 数组
    for (let i = 0; i < result.length; i++) {
      // 获取当前元素
      const item = result[i];
      ZYJKJCTJ.DQ = item._id; // 地区
      let totalYJRS = 0;
      let totalTJL = 0;
      let totalKFCRS = 0;
      let totalTJYCL = 0;
      for (let j = 0; j < item.documents.length; j++) {
        // 获取当前文档
        const doc = item.documents[j];
        // if (doc.year < threeYearsAgo) {
        //   continue;
        // }
        // 更新 ZYJKJCTJ 对象的数据
        totalYJRS += doc.shouldCheckNum;
        totalTJL += doc.actuallNum;
        totalKFCRS += doc.re_examination;
        totalTJYCL += doc.normal;
      }
      ZYJKJCTJ.NF = currentYear;
      ZYJKJCTJ.YJRS = totalYJRS;
      ZYJKJCTJ.TJL = (totalTJL / totalYJRS * 100).toFixed(0);
      ZYJKJCTJ.KFCRS = totalKFCRS;
      ZYJKJCTJ.FCWCL = totalKFCRS / totalTJL;
      ZYJKJCTJ.FCWCL = (ZYJKJCTJ.FCWCL * 100).toFixed(0);
      ZYJKJCTJ.TJYCL = (totalTJL - totalTJYCL) / totalTJL;
      ZYJKJCTJ.TJYCL = (ZYJKJCTJ.TJYCL * 100).toFixed(0);
      // 打印 ZYJKJCTJ 对象
      // console.log('ZYJKJCTJ', JSON.stringify(ZYJKJCTJ));
      // 将 ZYJKJCTJ 对象保存到StatisticalFz表中
      const res = await this.ctx.model.DpStatistical.findOne({ table, adcode });
      if (res) {
        await ctx.model.DpStatistical.updateOne({
          table, adcode,
        }, {
          $push: {
            data: ZYJKJCTJ,
          },
        },
        { upsert: true });
      } else {
        await this.ctx.model.DpStatistical.create({
          table,
          adcode,
          data: [ ZYJKJCTJ ],
        });
      }
    }
  }

  // 重点危害因素监测统计——occupationalExposureLimits
  async getStatisticalFz_ZDWHYSJCTJ(adcode) {
    const { ctx } = this;
    const districtsData = await ctx.service.home.getdDstrictsData({ adcode });
    const districtNames = Object.values(districtsData).map(entry => entry[2]);
    const res = [];
    // console.log('districtNames', districtNames);
    // const currentYear = new Date().getFullYear();
    // const threeYearsAgo = currentYear - 3;
    const table = 'DP_ZYWSDP_ZDWHYSJCTJ';
    const result = await this.ctx.model.Adminorg.aggregate([
      {
        $unwind: {
          path: '$workAddress',
        },
      },
      {
        $match: {
          'workAddress.districts': {
            $elemMatch: {
              $in: districtNames,
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          year: 1,
          workAddress: 1,
          harmStatistics: 1, // 工作场所接害人数统计
          'checkResult.dust': 1, // 检测结果下的dust字段
          'checkResult.chemical': 1, // 检测结果下的chemical字段
          'checkResult.noise': 1, // 检测结果下的noise字段
        },
      },
    ]);
    // console.log('ZDWHYSJCTJresult', JSON.stringify(result));
    const dustZDWHYSJCTJ = {
      LX: '粉尘',
      YRDWS: 0,
      GWGZCBL: 0,
      JCGZCSCBL: 0,
    };
    const chemicalZDWHYSJCTJ = {
      LX: '化学毒物',
      YRDWS: 0,
      GWGZCBL: 0,
      JCGZCSCBL: 0,
    };
    const noiseZDWHYSJCTJ = {
      LX: '噪声',
      YRDWS: 0,
      GWGZCBL: 0,
      JCGZCSCBL: 0,
    };
    for (let i = 0; i < result.length; i++) {
      const item = result[i];
      dustZDWHYSJCTJ.YRDWS = result.length;
      chemicalZDWHYSJCTJ.YRDWS = result.length;
      noiseZDWHYSJCTJ.YRDWS = result.length;
      if (item.checkResult.checkResult) {
        if (item.checkResult.dust.exceed && item.checkResult.dust.point) {
          const exceed = item.checkResult.dust.exceed;
          const point = item.checkResult.dust.point;
          dustZDWHYSJCTJ.GWGZCBL += Number(exceed);
          dustZDWHYSJCTJ.JCGZCSCBL += Number(point);
        }
        if (item.checkResult.chemical.exceed && item.checkResult.chemical.point) {
          const exceed = item.checkResult.chemical.exceed;
          const point = item.checkResult.chemical.point;
          chemicalZDWHYSJCTJ.GWGZCBL += Number(exceed);
          chemicalZDWHYSJCTJ.JCGZCSCBL += Number(point);
        }
        if (item.checkResult.noise.exceed && item.checkResult.noise.point) {
          const exceed = item.checkResult.noise.exceed;
          const point = item.checkResult.noise.point;
          noiseZDWHYSJCTJ.GWGZCBL += Number(exceed);
          noiseZDWHYSJCTJ.JCGZCSCBL += Number(point);
        }
      }
    }
    dustZDWHYSJCTJ.GWGZCBL = dustZDWHYSJCTJ.GWGZCBL / dustZDWHYSJCTJ.YRDWS;
    dustZDWHYSJCTJ.JCGZCSCBL = dustZDWHYSJCTJ.JCGZCSCBL / dustZDWHYSJCTJ.YRDWS;
    chemicalZDWHYSJCTJ.GWGZCBL = chemicalZDWHYSJCTJ.GWGZCBL / chemicalZDWHYSJCTJ.YRDWS;
    chemicalZDWHYSJCTJ.JCGZCSCBL = chemicalZDWHYSJCTJ.JCGZCSCBL / chemicalZDWHYSJCTJ.YRDWS;
    noiseZDWHYSJCTJ.GWGZCBL = noiseZDWHYSJCTJ.GWGZCBL / noiseZDWHYSJCTJ.YRDWS;
    noiseZDWHYSJCTJ.JCGZCSCBL = noiseZDWHYSJCTJ.JCGZCSCBL / noiseZDWHYSJCTJ.YRDWS;
    // console.log('dustZDWHYSJCTJ', dustZDWHYSJCTJ);
    // console.log('chemicalZDWHYSJCTJ', chemicalZDWHYSJCTJ);
    // console.log('noiseZDWHYSJCTJ', noiseZDWHYSJCTJ);
    res.push(dustZDWHYSJCTJ);
    res.push(chemicalZDWHYSJCTJ);
    res.push(noiseZDWHYSJCTJ);
    await this.ctx.model.DpStatistical.updateOne({ table, adcode }, { data: res }, { upsert: true });
  }
  // 用人单位开展三同时统计
  async getStatisticalFz_YRDWKZSTSTJ(adcode) {
    const { ctx } = this;
    const districtsData = await ctx.service.home.getdDstrictsData({ adcode });
    const districtNames = Object.values(districtsData).map(entry => entry[2]);
    const res = [];
    // const currentYear = new Date().getFullYear();
    const table = 'DP_ZYWSDP_YRDWKZSTSTJ';
    const result = await this.ctx.model.Adminorg.aggregate([
      {
        $unwind: {
          path: '$workAddress',
        },
      },
      {
        $match: {
          'workAddress.districts': {
            $elemMatch: {
              $in: districtNames,
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          workAddress: 1,
        },
      },
      {
        $lookup:
        {
          from: 'project',
          localField: '_id',
          foreignField: 'EnterpriseID',
          as: 'project_info',
        },
      },
      {
        $project: {
          evaluated: '$project_info.evaluated',
        },
      },
    ]);
    // console.log('YRDWKZSTSTJresult', JSON.stringify(result));
    const YRDWKZSTSTJ = {
      LX: '',
      YRDWS: 0,
      QBKZS: 0,
      BFKZS: 0,
      WKZS: 0,
    };
    const preCommentYRDWKZSTSTJ = { // 预评价
      LX: '预评价',
      YRDWS: 0,
      QBKZS: 0,
      BFKZS: 0,
      WKZS: 0,
    };
    const facilityDesignYRDWKZSTSTJ = { // 防护设施设计
      LX: '防护设施设计',
      YRDWS: 0,
      QBKZS: 0,
      BFKZS: 0,
      WKZS: 0,
    };
    const ImpactAssessmentYRDWKZSTSTJ = { // 控制效果评价
      LX: '控制效果评价',
      YRDWS: 0,
      QBKZS: 0,
      BFKZS: 0,
      WKZS: 0,
    };
    YRDWKZSTSTJ.YRDWS = result.length;
    preCommentYRDWKZSTSTJ.YRDWS = result.length;
    facilityDesignYRDWKZSTSTJ.YRDWS = result.length;
    ImpactAssessmentYRDWKZSTSTJ.YRDWS = result.length;
    for (let i = 0; i < result.length; i++) {
      const item = result[i];
      if (item.evaluated && item.evaluated.length !== 0) {
        for (let j = 0; j < item.evaluated.length; j++) {
          const entry = item.evaluated[j];
          if (entry.category === 'preComment') {
            if (entry.status) {
              preCommentYRDWKZSTSTJ.QBKZS++;
            } else {
              preCommentYRDWKZSTSTJ.BFKZS++;
            }
          }
          if (entry.category === 'facilityDesign') {
            if (entry.status) {
              facilityDesignYRDWKZSTSTJ.QBKZS++;
            } else {
              facilityDesignYRDWKZSTSTJ.BFKZS++;
            }
          }
          if (entry.category === 'ImpactAssessment') {
            if (entry.status) {
              ImpactAssessmentYRDWKZSTSTJ.QBKZS++;
            } else {
              ImpactAssessmentYRDWKZSTSTJ.BFKZS++;
            }
          }
        }
      }
    }
    preCommentYRDWKZSTSTJ.WKZS = preCommentYRDWKZSTSTJ.YRDWS - preCommentYRDWKZSTSTJ.QBKZS - preCommentYRDWKZSTSTJ.BFKZS;
    facilityDesignYRDWKZSTSTJ.WKZS = facilityDesignYRDWKZSTSTJ.YRDWS - facilityDesignYRDWKZSTSTJ.QBKZS - facilityDesignYRDWKZSTSTJ.BFKZS;
    ImpactAssessmentYRDWKZSTSTJ.WKZS = ImpactAssessmentYRDWKZSTSTJ.YRDWS - ImpactAssessmentYRDWKZSTSTJ.QBKZS - ImpactAssessmentYRDWKZSTSTJ.BFKZS;
    res.push(preCommentYRDWKZSTSTJ);
    res.push(facilityDesignYRDWKZSTSTJ);
    res.push(ImpactAssessmentYRDWKZSTSTJ);
    // console.log(res, 'res');
    await this.ctx.model.DpStatistical.updateOne({ table, adcode }, { data: res }, { upsert: true });
  }
  // 职业病防护用品配备及发放统计
}

module.exports = StatisticFzService;
