const Service = require('egg').Service;

// const _ = require('lodash')

const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require('./general');
const moment = require('moment');

class SystemConfigService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.SystemConfig, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.SystemConfig, params);
  }

  async create(payload) {
    // return _create(this.ctx.model.SystemConfig, payload);
    return _create('SystemConfig', payload, this.ctx);
  }

  async removes(res, values, key = '_id') {
    // return _removes(res, this.ctx.model.SystemConfig, values, key);
    return _removes(res, 'SystemConfig', values, key);
  }

  async safeDelete(res, values) {
    // return _safeDelete(res, this.ctx.model.SystemConfig, values);
    return _safeDelete(res, 'SystemConfig', values);
  }

  async update(res, _id, payload) {
    // return _update(res, this.ctx.model.SystemConfig, _id, payload);
    return _update(res, this.ctx.model.SystemConfig, 'SystemConfig', _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.SystemConfig, params);
  }

  async authAdminEnabel() {
    const { ctx } = this;
    // 判断当前系统是否限制运行期间
    const nowDate = new Date(new Date().setHours(0, 0, 0, 0));
    const limitationLoginDoc = await ctx.model.OperationPlan.find({ platform: 'jg', 'datePeriod.0': { $lte: nowDate }, 'datePeriod.1': { $gte: nowDate } });

    const isLimitOperation = limitationLoginDoc.some(item => {
      const runTime = item.runTime.map((e, i) => {
        const [ h, m ] = e.split(':');
        const s = i ? 59 : 0;
        return moment({ hour: h, minute: m, second: s });
      });
      if (moment().isBefore(runTime[0]) || moment().isAfter(runTime[1])) {
        return true;
      }
      return false;
    });
    const limitOperationDoc = limitationLoginDoc.map(e => {
      return e.notice;
    });
    console.log('限制登陆了?', isLimitOperation, moment().format('HH:mm:ss'));
    return {
      isLimitOperation,
      limitOperationDoc,
    };
  }
}

module.exports = SystemConfigService;
