
const Service = require('egg').Service;


class UserService extends Service {

  async update(_id, status, newComment, replyMsg) {
    newComment && (newComment.role = 0);
    // return await this.ctx.model.Complaints.update(
    //   { _id, status: { $ne: 4 } },
    //   {
    //     $push: { comments: newComment },
    //     status,
    //     replyMsg,
    //   },
    //   { new: true }
    // );
    return await this.ctx.service.db.updateOne('Complaints', { _id, status: { $ne: 4 } }, {
      $push: { comments: newComment },
      status,
      replyMsg,
    },
    { new: true });
  }
  async getBySuperId(supervisionId = '', params) {
    const reg = new RegExp(params.keyWords, 'i'); // 不区分大小写
    const query = {
      supervisionId,
      $or: [ // 多条件，数组
        { userName: { $regex: reg } },
        { companyName: { $regex: reg } },
      ],
    };
    if (params.status && params.status.length) {
      query.status = { $in: params.status };
    }
    if (params.date) { // 这里是按年份
      const year = new Date(params.date).getFullYear();
      const startTime = new Date('1/1/' + year);
      const endTime = new Date('1/1/' + (year + 1));
      query.time = {
        $gte: startTime,
        $lt: endTime,
      };
    }
    if (params.adminUserId) {
      query.adminUserId = params.adminUserId;
    }
    const skipNum = (params.curPage ? params.curPage - 1 : 0) * params.limit;
    return await this.ctx.model.Complaints
      .find(query)
      .populate('userId', 'name phoneNum')
      .sort({ time: -1 })
      .skip(skipNum)
      .limit(params.limit || 10);
  }
  // 数量统计
  async count(supervisionId = '', params) {
    const query = {
      supervisionId,
      status: params.status,
    };
    if (params.date) { // 这里是按年份
      const year = new Date(params.date).getFullYear();
      const startTime = new Date('1/1/' + year);
      const endTime = new Date('1/1/' + (year + 1));
      query.time = {
        $gte: startTime,
        $lt: endTime,
      };
    }
    if (params.adminUserId) {
      query.adminUserId = params.adminUserId;
    }
    return await this.ctx.model.Complaints.count(query);
  }

}

module.exports = UserService;
