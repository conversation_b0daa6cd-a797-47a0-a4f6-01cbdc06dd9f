const Service = require('egg').Service;
// 福州-主屏（数据总线） create by xxn

class StatisticFz2Service extends Service {
  // 按区域查找企业id
  async findEnterpriseIds(districtName = '福州市') {
    return await this.ctx.model.Adminorg.aggregate([
      {
        $match: {
          isactive: { $ne: '2' },
          $or: [
            { isDelete: false },
            { isDelete: null },
          ],
          'workAddress.districts': districtName,
          productionStatus: { $ne: '0' },
        },
      },
      {
        $project: { _id: 1 },
      },
    ]);
  }
  // 各地区职业卫生覆盖统计-列表 + 职业卫生大屏-监测用人单位数量统计
  async coverageStatistics(adcode) {
    const { ctx } = this;
    const year = new Date().getFullYear();
    const districtsData = await await ctx.model.District.find({
      parent_code: adcode,
    }, { area_code: 1, name: 1 });
    const res = []; // 各地区职业卫生覆盖统计-列表
    const res2 = []; // 职业卫生大屏-监测用人单位数量统计
    for (const district of districtsData) {
      const districtName = district.name,
        districtCode = district.area_code;
      const EnterpriseList = await this.findEnterpriseIds(districtName);
      // 按年份统计项目数量
      for (let curYear = year; curYear >= year - 2; curYear--) {
        const projectNum = await ctx.model.JobHealth.count({
          status: 1,
          projectStop: { $ne: true },
          EnterpriseID: { $in: EnterpriseList.map(item => item._id) },
          workPlaces: {
            $elemMatch: { workAdd: districtCode },
          },
          completeTime: {
            $exists: true,
            $lt: new Date(`${curYear + 1}-1-1`),
            $gte: new Date(`${curYear}-1-1`),
          },
        });
        res.push({
          DQ: districtName, // 地区
          NF: curYear, // 年份
          KZSL: EnterpriseList.length, // 开展数量
          WCJCRWS: projectNum, // 完成监测任务数
          WCJCRWZB: EnterpriseList.length === 0 ? 0 : (projectNum / EnterpriseList.length * 100).toFixed(0), // 完成监测任务占比（%）
        });
        res2.push({
          DQ: districtName, // 地区
          NF: curYear, // 年份
          YKZSL: EnterpriseList.length, // 应开展数量
          SJKZSL: projectNum, // 实际开展数量
          KZC: EnterpriseList.length === 0 ? 0 : (projectNum / EnterpriseList.length * 100).toFixed(0), // 开展率
        });
      }
    }
    await ctx.model.DpStatistical.updateOne({ table: 'DP_ZYWSDP_GDQZYWSFGTJ_LB', adcode }, { data: res }, { upsert: true });
    await ctx.model.DpStatistical.updateOne({ table: 'DP_ZYWSDP_JCYRDWSLTJ', adcode }, { data: res2 }, { upsert: true });
  }

  // 职业卫生大屏-职业卫生科室工作量-列表
  async departmentalWorkload(adcode) {
    const { ctx } = this;
    const year = new Date().getFullYear();
    const districtsData = await ctx.model.District.find({
      parent_code: adcode,
    }, { area_code: 1, name: 1 });
    const industry = [ '化工', '粉尘', '噪声' ];
    const res = [];
    for (const district of districtsData) {
      const districtName = district.name;
      for (let m = 1; m <= 12; m++) {
        // for (let i = 0; i < industry.length; i++) {
        // const curIndustry = industry[i];
        const curIndustry = industry[Math.floor(Math.random() * industry.length)];
        const EnterpriseCounut = (await this.findEnterpriseIds(districtName)).length;
        const BYJCWCS = parseInt(EnterpriseCounut * (Math.floor(Math.random() * (100 - 10 + 1)) + 10) / 100);
        res.push({
          SJ: `${year}-${m}`, // 时间
          JCDWZS: EnterpriseCounut, // 监测单位总数
          JCDWFBQY: districtName, // 监测单位分布区域
          HYLB: curIndustry, // 行业类别
          JCDWGM: Math.floor(Math.random() * (200 - 10 + 1)) + 10, // 监测单位规模
          BYJCWCS, // 本月监测完成数
          DJCWCS: EnterpriseCounut - BYJCWCS, // 待监测完成数
        });
        // }
      }
    }
    await ctx.model.DpStatistical.updateOne({ table: 'DP_ZYWSDP_ZYWSKSGZL_LB', adcode }, { data: res }, { upsert: true });
  }


}

module.exports = StatisticFz2Service;
