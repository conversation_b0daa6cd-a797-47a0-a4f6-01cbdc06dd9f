const Service = require('egg').Service;
const _ = require('lodash');
class DashboardService extends Service {

  // 获取首页检测项目数据 xxn add
  async countJobHealthData(adcode, year) { // adcode:330100, adname: ["杭州市"]
    // console.log('年份：', year);
    const { ctx } = this;
    // 1、获取检测项目机构和数据 按照项目地址来
    if (adcode.length === 6) adcode = adcode + '000000';
    const query = {
      status: 1, // 已上报的
      projectStop: { $ne: true },
      completeTime: {
        $lt: new Date(`${+year + 1}-1-1`),
        $gte: new Date(`${year}-1-1`),
      },
      workPlaces: {
        $elemMatch: { workAdd: adcode },
      },
    };
    let JobHealthList = await ctx.model.JobHealth.find(query, { workPlaces: 1, name: 1, serviceOrgID: 1, completeTime: 1, EnterpriseID: 1 });
    let JobHealthQyList;
    if (JobHealthList && JobHealthList.length) {
      JobHealthQyList = _.uniqBy(JobHealthList, 'EnterpriseID');
    } else {
      JobHealthQyList = [];
    }
    // ctx.auditLog('企业数据：', JobHealthQyList && JobHealthQyList.length ? JSON.stringify(JobHealthQyList) : JobHealthQyList);
    const JobHealthQyNumber = JobHealthQyList.length;
    JobHealthList = JSON.parse(JSON.stringify(JobHealthList));
    const JobHealthCount = JobHealthList.length; // 检测项目总次数
    const JobHealthIds = JobHealthList.map(ele => ele._id);
    const checkDataList = await ctx.model.CheckAssessment.find( // 检测数据
      { jobHealthId: { $in: JobHealthIds } }
    );
    // 分类分级
    const riskAssessmentList = await ctx.model.RiskAssessmentReport.find(
      { jobHealthId: { $in: JobHealthIds } },
      { assessManageLevel: 1, assessExposeLevel: 1, assessmentResult: 1, EnterpriseID: 1 }
    );
    const riskAssessmentStatistics = {
      assessExposeLevel: [ 0, 0, 0 ], // 危害暴露风险等级 0低、1中、2高
      assessManageLevel: [ 0, 0, 0 ], // 职业健康管理状况等级，0A、1B、2C
      assessmentResult: [ 0, 0, 0 ], // 危害综合风险等级，0甲类、1乙类、2丙类
      projectCount: riskAssessmentList.length, // 有分类分级的项目/企业总数量
    };
    const manageLevelIndex = { A: 0, B: 1, C: 2 };
    riskAssessmentList.forEach(ele => {
      riskAssessmentStatistics.assessExposeLevel[ele.assessExposeLevel] += 1;
      riskAssessmentStatistics.assessmentResult[ele.assessmentResult] += 1;
      riskAssessmentStatistics.assessManageLevel[manageLevelIndex[ele.assessManageLevel]] += 1;
    });
    // 不合格的项目
    const abnormalCheckDataList = checkDataList.filter(ele => JSON.stringify(ele).includes('不符合') || JSON.stringify(ele).includes('不合格'));
    // 检测合格率
    const abnormalPercent = JobHealthCount ? parseInt((JobHealthCount - abnormalCheckDataList.length) * 100 / JobHealthCount) : 0;
    // 检测机构排行榜 对项目列表中的serviceOrgID做去重
    const serviceOrgsTemp = {};
    const monthTrend = new Array(12).fill(0); // 按月统计次数，默认都是0
    JobHealthList.forEach(ele => {
      const serviceOrgID = ele.serviceOrgID || ele.name;
      if (!serviceOrgsTemp[serviceOrgID]) serviceOrgsTemp[serviceOrgID] = { name: ele.name, projectNum: 0 };
      serviceOrgsTemp[serviceOrgID].projectNum += 1;

      const index = new Date(ele.completeTime).getMonth();
      monthTrend[index] += 1;

      // wzq 查询项目是否不合格，若不合格则给该项目加上字段 abnormal: true =======>
      const targetRes = abnormalCheckDataList.some(item => item.jobHealthId === ele._id);
      if (targetRes) {
        ele.abnormal = true;
      }
      // <======= wzq
    });
    const serviceOrgs = Object.values(serviceOrgsTemp).sort((a, b) => b.projectNum - a.projectNum).slice(0, 10);
    return {
      serviceOrgs, // 检测机构项目数量排行榜，如果没有项目，就是[]
      abnormalPercent: abnormalPercent / 100, // 检测合格率，没有项目的时候是 0
      JobHealthCount, // 检测项目次数
      JobHealthQyNumber, // 检测企业家数
      JobHealthList, // 检测项目列表
      monthTrend, // 按月统计检测次数
      riskAssessmentStatistics, // 分类分级
    };
  }

  // 获取首页体检数据 xxn add
  async countHealthCheckData(adname, year, maxLen) { //  adname: ["杭州市"]
    const { ctx } = this;
    // 1、 获取体检总次数
    const query = {
      reportStatus: true,
      checkDate: {
        $lt: new Date(`${+year + 1}-1-1`),
        $gte: new Date(`${year}-1-1`),
      },
      workAddress: {
        $elemMatch: { districts: { $all: adname } },
      },
    };
    const HealthcheckList = await ctx.model.Healthcheck.find(query, { workAddress: 1, physicalExaminationOrgID: 1, organization: 1, actuallNum: 1, re_examination: 1, suspected: 1, forbid: 1, checkDate: 1, recheck: 1, EnterpriseID: 1 });
    const peopleCounting = { // 体检人数统计
      totle: 0,
      re_examination: 0,
      suspected: 0,
      forbid: 0,
    };
    const serviceOrgsTemp = {};
    const monthTrend = new Array(12).fill(0); // 按月统计次数，默认都是0
    const len = HealthcheckList.length;
    for (let i = 0; i < len; i++) {
      const ele = HealthcheckList[i];
      if (!ele.recheck) { peopleCounting.totle += ele.actuallNum; } // 体检总人数中不包含职业病的和复查的项目
      peopleCounting.re_examination += ele.re_examination;
      peopleCounting.suspected += ele.suspected;
      peopleCounting.forbid += ele.forbid;

      const serviceOrgID = ele.physicalExaminationOrgID || ele.organization; // 有的项目没有机构ID
      if (!serviceOrgID) continue;
      if (!serviceOrgsTemp[serviceOrgID]) {
        // 获取体检机构简称
        const org = await ctx.model.PhysicalExamOrg.findOne({ _id: ele.physicalExaminationOrgID }, { name: 1, shortName: 1 });
        const name = org && org.name ? (org.shortName || org.name) : (ele.organization || '');
        serviceOrgsTemp[serviceOrgID] = { name, projectNum: 0, _id: ele.physicalExaminationOrgID };
      }
      serviceOrgsTemp[serviceOrgID].projectNum += 1;

      const index = new Date(ele.checkDate).getMonth();
      monthTrend[index] += 1;
    }
    // 体检机构的项目数量统计
    let physicalExamOrgs = Object.values(serviceOrgsTemp).sort((a, b) => b.projectNum - a.projectNum);
    if (maxLen) {
      physicalExamOrgs = physicalExamOrgs.slice(0, maxLen);
    }
    // 职业病人数统计
    const odiseaseList = await ctx.service.diagnosticsService.findList({
      regAddr: adname,
      // TimeQuery: [ year + '', +year + 1 + '' ],
      year,
    });
    const odiseaseOrgList = _.uniqBy(odiseaseList, 'EnterpriseID');// 在原有的诊断人次上根据企业id进行去重，得到的是诊断家数
    peopleCounting.odisease = odiseaseList.length; // 诊断人次
    peopleCounting.odiseaseQyNumber = odiseaseOrgList.length; // 诊断人次改为诊断家数
    // console.log('诊断人次：', odiseaseList);
    peopleCounting.diagnosedOdisease = odiseaseList.filter(ele => (ele.diseaseName[0].trim() ? ele.diseaseName[0].trim()[0] !== '无' : false)).length; // 确诊为职业病的人数
    let HealthLQyNumber;
    if (HealthcheckList && HealthcheckList.length) {
      // ctx.auditLog('体检数据：', HealthcheckList);
      HealthLQyNumber = _.uniqBy(HealthcheckList, 'EnterpriseID');// 将体检上报次数根据企业机构id进行去重得到体检的企业家数
    } else {
      HealthLQyNumber = [];
    }
    // ctx.auditLog('体检企业家数：', HealthLQyNumber);
    return {
      peopleCounting, // 体检情况统计（数量统计）
      HealthcheckList, // 体检列表
      healthcheckCount: HealthcheckList.length, // 体检上报次数
      healthcheckQyCount: HealthLQyNumber.length, // 体检机构上报家数
      odiseaseList, // 职业病列表
      physicalExamOrgs, // 体检机构项目数量排行榜，如果没有项目，就是[]
      monthTrend, // 按月统计体检次数
    };
  }

  // 获取首页预警数据 xxn add
  async countWarningData(adname, year, schedule = false) { // adname: ["杭州市"]
    const { ctx } = this;
    let query;
    if (schedule) {
      query = {
        delete: false,
        lever: { $in: [ 1, 2, 3 ] },
        status: { $in: [ 0, 1, 2, 4 ] },
        workAddress: { $elemMatch: { $elemMatch: { $all: [ '杭州市' ] } } },
        ctime: {
          $lte: new Date(+year + 1, 0, 1),
          $gt: new Date(year, 0, 1),
        },
      };
    } else {
      query = await ctx.service.warning.addSearchAddrAddYear({ delete: false, lever: { $in: [ 1, 2, 3 ] }, status: { $in: [ 0, 1, 2, 4 ] } }, adname, year);
    }
    // console.log(88888888888, query);
    const warningList = await ctx.model.Warning.find(query, { status: 1, type: 1, companyName: 1, workAddress: 1, lever: 1, ctime: 1 });
    const count1 = warningList.filter(ele => ele.type === 1).length;
    return {
      warningList,
      count1,
      count2: warningList.length - count1,
      totalCount: warningList.length,
    };
  }
  // 获取子区域code和name xxn add
  async getSubArea(adcode) {
    const { ctx, app } = this;
    if (adcode + '' === app.config.China.area_code) {
      return await ctx.model.District.find(
        { level: '0', parent_code: '0' },
        { name: 1, area_code: 1, lng: 1, lat: 1, level: 1 }
      );
    }
    if (adcode.length === 6) adcode = adcode + '000000';
    return await ctx.model.District.find(
      { parent_code: adcode },
      { name: 1, area_code: 1, lng: 1, lat: 1, level: 1 }
    );
  }
  // 获取当前区域
  async getCurArea(adcode) {
    const { ctx } = this;
    if (adcode.length === 6) adcode = adcode + '000000';
    return await ctx.model.District.findOne(
      { area_code: adcode },
      { name: 1, area_code: 1, lng: 1, lat: 1, level: 1 }
    );
  }
}

module.exports = DashboardService;
