<!DOCTYPE html>
<html class="adminlogin">

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta name="viewport"
        content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no" />
    <meta property="og:type" content="website" />
    <meta name="description" content="{{siteSeo.description}}">
    <meta name="keywords" content="{{siteSeo.keywords}}">
    <meta name="author" content="{{siteSeo.author}}">
    <title>{{siteSeo.title}}</title>

    <!-- 引入组件库 -->
    <script src="{{staticRootPath}}/plugins/axios/0.19.0-beta.1/axios.min.js"></script>
    <script src="{{staticRootPath}}/plugins/vue/2.6.10/vue.min.js"></script>
    <script src="{{staticRootPath}}/plugins/crypto-js/3.1.9/crypto-js.min.js"></script>
    <script src="{{staticRootPath}}/plugins/jquery/1.10.2/jquery.min.js" type="text/javascript"></script>
    <link href="{{staticRootPath}}/plugins/twitter-bootstrap/3.3.5/css/bootstrap.min.css" rel="stylesheet">
    <script src="{{staticRootPath}}/plugins/twitter-bootstrap/3.3.5/js/bootstrap.min.js" type="text/javascript">
    </script>
    <link href="{{staticRootPath}}/plugins/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- <script src="{{staticRootPath}}/plugins/avalon.js/2.2.7/avalon.min.js"></script> -->
    <script src="{{staticRootPath}}/plugins/layer/layer.js"></script>
    <link rel="stylesheet" href="{{staticRootPath}}/plugins/layer/theme/default/layer.css">
    <!-- 引入用户跟踪和客服 -->
    <script src="{{staticRootPath}}/plugins/customerService.js" type="text/javascript"></script>
    <script src="{{staticRootPath}}/plugins/tweetnacl.js/nacl.min.js"></script>
    <script src="{{staticRootPath}}/plugins/tweetnacl.js/nacl-util.min.js"></script>
    <script src="{{staticRootPath}}/plugins/tweetnacl.js/zyws-nacl.min.js"></script>
    <!--default theme-->

    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
      <script src="{{staticRootPath}}/plugins/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="{{staticRootPath}}/plugins/respond.js/1.4.2/respond.min.js"></script>
      <script src="{{staticRootPath}}/plugins/json3/3.3.2/json3.min.js"></script>
      <script src="{{staticRootPath}}/plugins/es6-promise/4.0.5/es6-promise.min.js"></script>
    <![endif]-->
    <input type="hidden" value="{{lsk}}" id="sysKeys">
    <style>
        .container {
            position: relative;
        }

        .container .logo {
            margin-top: 212px;
            width: 90%;
        }

        .adminlogin {
            height: 100%;
            background-repeat: no-repeat;
            background: url('/static/images/loginBg.png');
            background-size: cover;
        }

        .adminlogin body {
            background: none;
        }

        .adminlogin .login-container {
            background-clip: padding-box;
            background: transparent;
        }

        .adminlogin .login-container .loginForm {

            background: #fff;
            border-radius: 5px;

        }

        .adminlogin .login-container .loginForm .form-title {
            border-bottom: 1px solid #e8eaec;
            padding: 14px 16px;
            line-height: 1;
            margin-bottom: 10px;
        }

        .adminlogin .login-container .loginForm .form-content {
            padding: 15px;
        }

        .adminlogin .login-container .loginForm .form-title p {
            display: inline-block;
            width: 100%;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            color: #17233d;
            font-weight: 700;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 0;
        }

        .adminlogin .login-container .loginForm .input-group {

            margin-bottom: 25px;
        }

        .adminlogin .login-container .admin-logo-title h3 {
            color: #99a9bf;
            font-size: 35px;
            text-align: center;
            font-weight: normal;
            margin: 40px 0;
        }

        .adminlogin .login-container .input-group .imageCode-img {
            width: 115px;
            height: 94%;
            position: absolute;
            right: 4px;
            top: 1px;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
        }

        .adminlogin .login-container .btn-primary {
            width: 100%;
        }


        .regButton {
            margin-top: 10px;
            width: 100%;
        }

        .regStyle {
            margin-bottom: 5px;
            text-align: right;
            padding-right: 5px;
        }

        .nav {
            margin-bottom: 5px !important;
        }

        .nav-tabs {
            border-bottom: 0 !important;
        }

        .nav-tabs>li>a {
            color: black !important;
            border: 0 !important;
        }

        .nav-tabs>li.active>a,
        .nav-tabs>li.active>a:hover,
        .nav-tabs>li.active>a:focus {
            background-color: #fff !important;
            border-bottom: 3px solid #337ab7 !important;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            position: absolute;
            bottom: 10px;
            font-size: 12px;
            color: aliceblue;
            width: 100%;
            z-index: -1;

        }

        .limitOperation {
            width: 100%;
            padding: 40px;
            font-size: 16px;
            line-height: 30px;
            color: #E6A23C;
            height: 260px;
            overflow: scroll;
            background: white;
        }

        ::-webkit-scrollbar {
            background-color: transparent;
            width: 6px;
            height: 6px;
        }

        /* 滑动验证 */
        .slideVerification {
            position: relative;
            width: 300px;
            margin: 0 auto;

            border: 2px solid #eee;
            box-sizing: content-box;
        }

        .slideVerification .slide-wrapper {
            position: relative;
            width: 300px;
            height: 35px;

            border-top: 2px solid #eee;
            box-sizing: content-box;
        }

        .slideVerification .bg-start {
            background: #FFF;
        }

        .slideVerification .bg {
            position: absolute;
            height: 40px;
            /* background: #0063E0; */
            border: 1px solid #0063E0;
            border-right: 0;
            background: rgba(0, 99, 224, 0.4);
            transition:
                background .2s linear,
                border .2s linear;
        }

        .slideVerification .bg-fail {

            border: 1px solid #F56C6C;
            border-right: 0;
            background: rgba(245, 108, 108, 0.4);

        }

        .slideVerification .text {
            position: absolute;
            width: 100%;
            height: 40px;
            text-align: center;
            line-height: 40px;
            margin: 0;
            color: #0063E0;
        }

        .slideVerification .text-success {
            color: #67C23A;
        }

        .slideVerification .text-fail {
            color: #F56C6C;
        }

        .slideVerification .btn {
            position: absolute;
            width: 40px;
            height: 40px;
            z-index: 1;
            border-radius: 5px;
            border: 1px solid #999;
            background: rgb(143, 145, 148);
            text-align: center;
            font-size: 24px;
            color: white;
            /* box-shadow: 0 0 1px 1px #fff; */
            background: #fff no-repeat center url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTEyNTVEMURGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTEyNTVEMUNGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2MTc5NzNmZS02OTQxLTQyOTYtYTIwNi02NDI2YTNkOWU5YmUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+YiRG4AAAALFJREFUeNpi/P//PwMlgImBQkA9A+bOnfsIiBOxKcInh+yCaCDuByoswaIOpxwjciACFegBqZ1AvBSIS5OTk/8TkmNEjwWgQiUgtQuIjwAxUF3yX3xyGIEIFLwHpKyAWB+I1xGSwxULIGf9A7mQkBwTlhBXAFLHgPgqEAcTkmNCU6AL9d8WII4HOvk3ITkWJAXWUMlOoGQHmsE45ViQ2KuBuASoYC4Wf+OUYxz6mQkgwAAN9mIrUReCXgAAAABJRU5ErkJggg==");

        }

        .slideVerification .btn-active {
            border: 2px solid #0063E0;
        }

        .slideVerification .btn-success {
            border: 2px solid #67C23A;
            background: #fff no-repeat center url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDlBRDI3NjVGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDlBRDI3NjRGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDphNWEzMWNhMC1hYmViLTQxNWEtYTEwZS04Y2U5NzRlN2Q4YTEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+k+sHwwAAASZJREFUeNpi/P//PwMyKD8uZw+kUoDYEYgloMIvgHg/EM/ptHx0EFk9I8wAoEZ+IDUPiIMY8IN1QJwENOgj3ACo5gNAbMBAHLgAxA4gQ5igAnNJ0MwAVTsX7IKyY7L2UNuJAf+AmAmJ78AEDTBiwGYg5gbifCSxFCZoaBMCy4A4GOjnH0D6DpK4IxNSVIHAfSDOAeLraJrjgJp/AwPbHMhejiQnwYRmUzNQ4VQgDQqXK0ia/0I17wJiPmQNTNBEAgMlQIWiQA2vgWw7QppBekGxsAjIiEUSBNnsBDWEAY9mEFgMMgBk00E0iZtA7AHEctDQ58MRuA6wlLgGFMoMpIG1QFeGwAIxGZo8GUhIysmwQGSAZgwHaEZhICIzOaBkJkqyM0CAAQDGx279Jf50AAAAAABJRU5ErkJggg==");
        }

        .slideVerification .btn-fail {
            border: 2px solid #F56C6C;
        }

        .slideRefresh {
            cursor: pointer;
            width: 20px;
            height: 20px;
            position: relative;
            z-index: 1;
            right: 10px;
            opacity: .6;
            /* background: url('../assets/ref.jpg') no-repeat; */
            /* background-size: cover; */
            border-radius: 3px;
        }

        /* 滑块对话框 */
        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .dialog {
            background: white;
            border-radius: 5px;
            padding: 15px;
            min-width: 300px;
        }

        .dialog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0;
        }

        .dialog-close-button {
            border: none;
            background: none;
            cursor: pointer;
            font-size: 20px;
        }

        /* loading */
        .loading {
            position: absolute;
            width: 300px;
            height: 150px;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 99;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 消息提示框 */
        .message {
            padding: 10px 20px;
            border-radius: 4px;
            color: #fff;
            position: fixed;
            right: calc(50% - 111px);
            top: 20%;
            z-index: 1000;
            font-size: 14px;
            line-height: 1.5;
        }

        .message-success {
            background-color: #67c23a;
        }

        .message-warning {
            background-color: #e6a23c;
        }

        .message-info {
            background-color: #909399;
        }

        .message-error {
            background-color: #f56c6c;
        }

        .fade-enter-active,
        .fade-leave-active {
            transition: opacity 0.5s;
        }

        .fade-enter,
        .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>

<body>


    <div class="container" id="app" style="height: 100%;">

        <!-- 页面内容 -->
        <div class="col-md-12">
          <!-- logo -->
          <div class="col-xs-12 col-sm-12 col-md-8 col-lg-6  min-hight">
            <img class="logo" src="/static/images/zaw/jg_title.png">
          </div>
          <div class="col-md-4" style="height: 220px;">&nbsp;</div>
          <!-- content -->
          <div class="col-xs-12 col-sm-4 col-md-4 col-lg-5  min-hight" style=" background:#fff;border-radius: 5px;">
            <!-- Limit -->
            <div v-if="isLimitOperation" class="limitOperation">
              <div v-for="item in limitOperationDoc" style="padding: 10px;"><span v-text="item"></span></div>
            </div>
            <!--用户登录模块-->
            <div v-else>
              <!--tab点击切换-->
              <ul id="manywaylogin" class="nav nav-tabs">
                <li id="email" role="presentation" :class="{'active':active==1}" @click="active=1">
                  <a href="#">密码登录</a>
                </li>
                <li id="phone" role="presentation" :class="{'active':active==2}" @click="active=2">
                  <a href="#">手机登录</a>
                </li>
              </ul>
              <!--登录方式-->
              <div>
                <!-- 密码登录 -->
                <div id="emailForm" class="login-container" v-show="active==1">
                  <form name="loginForm" class="loginForm">
                    <div class="form-content">
                      <div class="input-group">
                        <span class="input-group-addon" id="basic-addon1">
                          <i class="fa fa-user"></i>
                        </span>
                        <input type="text" class="form-control" name="userName" placeholder="请输入用户名"
                        ref="account" v-model.trim="account" @input="adminLoginErrorMessage=''">
                      </div>
    
                      <div class="input-group">
                        <span class="input-group-addon" id="basic-addon1">
                          <i class="fa fa-lock"></i>
                        </span>
                        <input type="password" class="form-control" id='password' name="password" placeholder="请输入密码" 
                        ref="password" v-model.trim="password" @input="adminLoginErrorMessage=''">
                      </div>
    
                      <div id="ul-s5" style="position: relative;">
                        <p style="width: 100%; height: 12px;font-size: 12px;line-height: 12px;color: red;position: absolute;top: -18.5px;"
                          v-html="adminLoginErrorMessage">
                        </p>
                        <button class="btn btn-primary" @click.prevent="accountLogin">登录</button>&nbsp;
                      </div>
    
                    </div>
                  </form>
                </div>
                <!-- 短信登录 -->
                <div id="phoneForm" class="login-container" v-show="active==2">
                  <form name="loginForm" class="loginForm">
                    <div class="form-content">
                      <div class="input-group">
                        <span class="input-group-addon" id="basic-addon1">
                          <i class="fa fa-user"></i>
                        </span>
                        <input type="text" class="form-control" id="phoneNum" name="phoneNum" placeholder="{{__('lc_basic_pl_tel')}}" 
                          ref="phoneNum" v-model.trim="phoneNum" @input="phoneLoginErrorMessage=''">
                      </div>
    
                      <div class="input-group">
                        <input type="text" class="form-control" id="messageCode" name="messageCode" placeholder="{{__('label_user_imageCode')}}" 
                          ref="messageCode" v-model.trim="messageCode" @input="phoneLoginErrorMessage=''">
                        <span class="input-group-btn">
                          <button id="get-v-code" class="btn btn-default vCodeBtn" @click.prevent="sendCode"
                            v-text="codeMsg"></button>
                        </span>
                      </div>
                      <div class="sub-btns" id="ul-s5" style="position: relative;">
                        <p style="width: 100%; height: 12px;font-size: 12px;line-height: 12px;color: red;position: absolute;top: -18.5px;"
                          v-html="phoneLoginErrorMessage">
                        </p>
                        <div>
                          <button class="btn btn-primary" @click.prevent="phoneLogin">
                            {{__('lc_confirm_login')}}
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
    
              </div>
    
            </div>
          </div>
    
        </div>
    
        <!-- slide 滑动验证对话框 -->
        <transition name="fade">
          <!-- 遮罩层 -->
          <div class="dialog-overlay" style="z-index: 999;" v-show="showDialog">
            <!-- 对话框 -->
            <div class="dialog">
              <!-- 标题按钮栏 -->
              <div class="dialog-header">
                <span name="header">请完成下列验证后继续</span>
                <div>
                  <!-- 刷新按钮 -->
                  <!-- <button class="slideRefresh" @click="slideRefresh"></button> -->
                  <button class="dialog-close-button" style="position: relative;top: -1px;"
                    @click="slideRefresh">&#x21BB;</button>
                  <!-- 关闭 -->
                  <button class="dialog-close-button" @click="closeDialog">&times;</button>
                </div>
              </div>
              <div class="dialog-body">
                <!-- 滑动验证 -->
                <div class="slideVerification" ref="slideVerification">
                  <!-- loading -->
                  <div class="loading" v-if="isLoading">
                    <div class="loading-spinner"></div>
                  </div>
                  <!-- 拼图在js中创建 -->
                  <!-- 拼图背景部分 -->
                  <canvas ref="slideVerify"></canvas>
                  <!-- 下面滑块部分 -->
                  <div class="slide-wrapper bg-start" style="position: relative;top: -5px;">
                    <!-- 滑块 -->
                    <div class="btn" ref="slideBtn"></div>
                    <p class="text" ref="slideText">&emsp;&emsp;&nbsp;按住左边按钮拖动完成上方拼图</p>
                    <div class="bg" ref="slideBg"></div>
                  </div>
                </div>
              </div>
              <!-- <div class="dialog-footer">
                    <button @click="closeDialog">关闭</button>
                </div> -->
            </div>
          </div>
        </transition>
    
        <!-- message 消息提示框 -->
        <transition name="fade">
          <div v-show="showMessage" :class="['message', messageType]" @click="closeMessage">
            <p ref="messageContent" style="margin: 0;padding: 0;"></p>
          </div>
        </transition>
      </div>

      <div class="footer">
        <span id="footerContent0"></span>
        <a href="http://beian.miit.gov.cn/" target="_blank">{{recordNumber}}</a><br>
        <span id="footerContent1"></span>
      </div>
</body>
<script>
  layer.config({
    extend: 'blue/layer.css', //加载您的扩展样式,它自动从theme目录下加载这个文件
    skin: 'layui-layer-blue' //layui-layer-orange这个就是上面我们定义css 的class
  });
  var msgTime = 2000;
  const pop = (message) => {
    layer.msg(message, {
      icon: 2,
      shade: [0.001, '#000'],
      time: msgTime
    });
  }

  const vm = new Vue({
    el: "#app",
    data: {
      active: "1", //1是账号密码登录，2是手机号
      account: "", //用户名/账号
      password: "", //密码
      code: "", // 统一社会信用代码
      codePassword: "", // 统一社会信用代码登录密码
      popShow: false, //弹框是否显示
      popContent: "", //弹框提示内容
      codeMsg: "获取验证码", //验证码按钮提示信息
      messageCode: "", //验证码
      phoneNum: "", //手机号
      userInfo: {}, //用户信息
      orgInfo: null, // 机构信息
      imageCodeValue: {
        account: '',
        phone: '',
        code: ''
      },
      imgCodeUrl: {
        account: '',
        phone: '',
        code: ''
      },
      phoneImgFlag: true, // 验证码展示标识
      isLimitOperation: false,
      limitOperationDoc: [],
      // 错误提示信息
      phoneLoginErrorMessage: '',
      adminLoginErrorMessage: '',
      codeLoginErrorMessage: '',

      // 滑动验证码
      imgNode: null,
      imgIndex: 1,
      slideImgUrl: 'https://picsum.photos/300/150?random=1',
      blockCanvas: null,  // canvas
      slideText: '按住左边按钮拖动完成上方拼图',  // 提示文字
      isDown: false, // 鼠标是否按下
      btnX: 0, // 滑块的位置
      imgX: 0, // 空缺拼图位置
      leftX: 0, // x偏移量（由于发送请求
      slideSuccess: false, // 滑动成功
      // dialog
      showDialog: false,
      // message
      showMessage: false,
      messageType: '',
      messageContent: '',
      // loading
      isLoading:false
    },
    mounted() {
      if ({{ showImgCode }}){
        // 滑动验证码初始化
        document.addEventListener('mousemove', this.mouseMove)
        this.imgNode = new Image();
        this.imgNode.src = '/static/images/loginBg.png';
        this.imgNode.decode().then(()=>{
          this.imageCanvas()
          console.log(this.imgNode);
        })
      }
    },
    created() {
      axios.get("/api/getAuthLogin", {
        params:{
          platform: 'jg',
        }
      })
        .then((res) => {
          const data = res.data.data
          if (data) {
            this.isLimitOperation = data.isLimitOperation
            this.limitOperationDoc = data.limitOperationDoc;
          } else {
            this.isLimitOperation = false
            this.limitOperationDoc = []
          }
        })
    },
    methods: {
      // 生成随机数字
      randomNumber(min, max) {
        return Math.floor(Math.random() * (max - min) + min)
      },
      // 鼠标按下时
      mouseDown(e) {
        this.isDown = true
        this.btnX = e.clientX - this.$refs.slideBtn.offsetLeft
        this.$refs.slideBtn.classList.add('btn-active')
      },
      // 鼠标滑动时
      mouseMove(e) {
        // 滑块左端向右边移动的距离
        let moveX = e.clientX - this.btnX
        if (this.isDown) {
          // 滑块滑动时不能超过的距离
          if (this.$refs.slideBtn.offsetLeft <= 259 && this.$refs.slideBtn.offsetLeft >= 0) {
            this.$refs.slideBtn.style.left = `${moveX}px`
            this.blockCanvas.style.left = `${moveX - this.imgX}px`
            this.$refs.slideBg.style.width = `${moveX + 10}px`
          }
          // 超过
          else {
            this.$refs.slideBtn.style.left = 0
            this.blockCanvas.style.left = `-${this.imgX}px`
            this.$refs.slideBg.style.width = 0
            this.isDown = false
          }
        }
      },
      // 滑动中松开
      mouseUp() {
        this.$refs.slideBtn.classList.remove('btn-active')
        let leftX = this.$refs.slideBtn.offsetLeft
        this.leftX = leftX
        // 方块的位置和缺失的位置重合允许存在的误差
        if (this.imgX >= leftX - 5 && this.imgX <= leftX + 5) {
          // 滑动成功时的逻辑
          this.slideSuccess = true
          this.$refs.slideBtn.classList.add('btn-success')
          this.isDown = false
          // 延迟关闭
          setTimeout(() => {
            this.closeDialog()
          }, 250)
          // 根据active值判断是哪种登录方式 1喵喵登录，2手机登录
          if (this.active == 1) {
            this.accountLoginRequest()
          } else if (this.active == 2) {
            this.sendCodeHandle(this.phoneNum)
          }
        }
        //  如果滑动失败
        if (this.isDown) {
          // 添加失败样式
          this.$refs.slideBtn.classList.add('btn-fail')
          this.$refs.slideBg.classList.add('bg-fail')
          this.slideText = '&emsp;&nbsp;验证失败，请重试'
          this.$refs.slideText.innerHTML = this.slideText
          this.$refs.slideText.classList.add('text-fail')
          // 禁用滑块点击事件
          this.$refs.slideBtn.onmousedown = null
          this.$refs.slideBtn.onmouseup = null
          
          // 延迟一秒后重置
          setTimeout(() => {
            this.$refs.slideBtn.classList.remove('btn-fail')
            this.$refs.slideBg.classList.remove('bg-fail')
            this.slideText = '&emsp;&emsp;&nbsp;按住左边按钮拖动完成上方拼图'
            this.$refs.slideText.innerHTML = this.slideText
            this.$refs.slideText.classList.remove('text-fail')
            // 启用滑块点击事件
            this.$refs.slideBtn.onmousedown = this.mouseDown
            this.$refs.slideBtn.onmouseup = this.mouseUp

            this.$refs.slideBtn.style.left = 0
            this.blockCanvas.style.left = `-${this.imgX}px`
            this.$refs.slideBg.style.width = 0

            this.slideRefresh()
          }, 1000)

        }
        this.isDown = false
      },
      // 清空canvas
      cleanCanvas() {
        // 清空背景
        let cxt = this.$refs.slideVerify.getContext('2d')
        cxt.clearRect(0, 0, 300, 150)
      },
      // 新建canvas
      createCanvas(width, height) {
        const canvas = document.createElement('canvas')
        canvas.width = width
        canvas.height = height
        canvas.style.position = 'absolute'
        return canvas
      },
      // 画图
      async imageCanvas() {
        this.isLoading = true
        this.slideText = '&emsp;图片加载中，请稍后...'
        this.$refs.slideText.innerHTML = this.slideText
        // 拼图空缺的位置（x轴的偏移量）
        const res = await axios.get("/api/getSlideCode")
        let x = Number(this.decryptImageCode(res.data.data))
        // 创建拼图 实际上是复制一份图片，然后截出来滑块那一部分
        this.blockCanvas = this.blockCanvas ? this.blockCanvas.remove() : null
        this.blockCanvas = this.createCanvas(300, 150)
        this.$refs.slideVerification.insertBefore(this.blockCanvas, this.$refs.slideVerify)
        // let x = this.randomNumber(60, 200)
        let y = this.randomNumber(30, 60)
        this.imgX = x
        let content = this.$refs.slideVerify
        let bg = content.getContext('2d')
        let bk = this.blockCanvas.getContext('2d')
        // 画出拼图的背景
        this.drawCanvas(bg, x, y, 'fill')
        // 画出拼图
        this.drawCanvas(bk, x, y, 'clip')

        // 在两块画布上都画上相同的图片
        // let img = this.createImg()
        // 偏移量
        const cropX = Math.floor(Math.random() * (this.imgNode.width - 300));
        const cropY = Math.floor(Math.random() * (this.imgNode.height - 150));
        // 等到图片加载完毕
        // img.onload = () => {
          // bg.drawImage(img, 0, 0)
          // bk.drawImage(img, 0, 0)
          bg.drawImage(this.imgNode, cropX, cropY, 300, 150, 0, 0, 300, 150)
          bk.drawImage(this.imgNode, cropX, cropY, 300, 150, 0, 0, 300, 150)
          this.isLoading = false
          this.slideText = '&emsp;&emsp;&nbsp;按住左边按钮拖动完成上方拼图'
          this.$refs.slideText.innerHTML = this.slideText
          // 启用滑块点击事件
          this.$refs.slideBtn.onmousedown = this.mouseDown
          this.$refs.slideBtn.onmouseup = this.mouseUp
          this.$refs.slideBtn.removeAttribute('disabled');
        // }
      },
      // 画抠出来的拼图/背景
      drawCanvas(ctx, x, y, type) {
        ctx.beginPath()
        ctx.moveTo(x, y)
        ctx.arc(x + 42 / 2, y - 9 + 2, 9, 0.72 * Math.PI, 2.26 * Math.PI)
        ctx.lineTo(x + 42, y)
        ctx.arc(x + 42 + 9 - 2, y + 42 / 2, 9, 1.21 * Math.PI, 2.78 * Math.PI)
        ctx.lineTo(x + 42, y + 42)
        ctx.lineTo(x, y + 42)
        ctx.arc(x + 9 - 2, y + 42 / 2, 9 + 0.4, 2.76 * Math.PI, 1.24 * Math.PI, true)
        ctx.lineTo(x, y)
        ctx.lineWidth = 2
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)'
        ctx.stroke()
        ctx[type]()
        ctx.globalCompositeOperation = 'destination-over'
        // 解决进入页面时不自动扣拼图样式的麻烦(有时需要鼠标点击后才会出现裁剪后的拼图)
        this.blockCanvas.style.left = `-${x}px`
      },
      // 创建图片元素
      createImg() {
        // 创建DOM
        const img = document.createElement("img");
        // 允许跨源
        img.crossOrigin = "Anonymous";
        img.onerror = () => {
          img.src = this.slideImgUrl;
        };
        img.src = this.slideImgUrl;
        return img;
      },
      // 刷新滑动验证码图片
      resetSlideImgUrl() {
        // this.imgIndex += 1
        // this.slideImgUrl = 'https://picsum.photos/300/150?random=' + this.imgIndex
        // this.slideImgUrl = '/static/images/loginBg.png'
        // 阻止图片加载期间的滑动
        this.$refs.slideBtn.onmousedown = null
        this.$refs.slideBtn.onmouseup = null
        this.$refs.slideBtn.setAttribute('disabled', '');
      },
      // 拼图刷新
      slideRefresh() {
        this.resetSlideImgUrl()
        this.cleanCanvas()
        this.slideSuccess = false
        this.$refs.slideBtn.style.left = 0
        this.$refs.slideBg.style.width = 0
        this.$refs.slideBtn.classList.remove('btn-success')
        this.isDown = false // 鼠标是否按下
        this.btnX = 0 // 鼠标点击的水平位置与滑块移动水平位置的差
        this.imgX = 0
        this.imageCanvas()
      },

      // 关闭滑块对话框
      closeDialog() {
        this.showDialog = false
        // 如果验证成功，关闭后立即刷新
        if (this.slideSuccess) {
          this.slideRefresh()
        }
      },
      // 消息提示框 type: success、warning、info、error
      openMessage(type = 'success', content, duration = 2000) {
        this.showMessage = true
        this.$refs.messageContent.innerHTML = content
        this.messageType = `message-${type}`
        setTimeout(() => {
          this.showMessage = false
        }, duration)
      },
      closeMessage() {
        this.showMessage = false
      },

      //手机验证码登录
      phoneLogin() {
        if (!this.phoneNum) {
            this.phoneLoginErrorMessage = '请输入手机号';
            this.$refs.phoneNum.focus();
            return;
        }
        if (!/^1\d{10}$/.test(this.phoneNum)) {
          this.phoneLoginErrorMessage = '手机号填写错误，请重新确认';
          this.$refs.phoneNum.removeAttribute("readonly");
          this.$refs.phoneNum.focus();
          return;
        }
        if (!this.messageCode) {
          this.phoneLoginErrorMessage = '请输入验证码';
          this.$refs.messageCode.focus();
          return;
        }
        if (this.messageCode.length != 6) {
          this.phoneLoginErrorMessage = '请输入正确的验证码';
          this.$refs.messageCode.focus();
          return;
        }
        axios
          .post("/api/admin/SmdoLogin", {
            loginType: '1',
            phoneNum: this.phoneNum,
            messageCode: this.messageCode,
            countryCode: "86",
          })
          .then((res) => res.data)
          .then((res) => {
            // 成功
            if (res.status == 200) {
              let url = window.location.pathname;
              if (url.indexOf('/admin') !== -1 || url.indexOf('/jk-admin') !== -1 || url === '/') {
                window.location.href = "/admin/dashboard";
              } else if (url.indexOf('/qy') !== -1) {
                window.location.href = "/qy/dashboard";
              } else if (url.indexOf('/user') !== -1) {
                window.location.href = "/user/dashboard";
              }
            } else {
              pop(res.message);
            }
          });
      },

      //点击获取验证码按钮
      sendCode() {
        if (this.codeMsg != "获取验证码") {
          this.phoneLoginErrorMessage = '验证码已发送，请勿重复点击';
          return;
        }
        if (!this.phoneNum) {
            this.phoneLoginErrorMessage = '请输入手机号';
            this.$refs.phoneNum.focus();
            return;
        }
        if (/^1\d{10}$/.test(this.phoneNum)) {
          //手机号匹配成功
          if ({{ showImgCode }}){
            this.showDialog = true
          } else {
            this.sendCodeHandle(this.phoneNum);//获取验证码
          }
        } else {
          this.phoneLoginErrorMessage = '手机号填写错误，请重新确认';
          this.$refs.phoneNum.focus();
        }
      },

      // 获取短信验证码事件
      sendCodeHandle(phone) {
        let smsParams = {
          messageType: "1", //0是注册  1 是登录
          phoneNum: phone,
          countryCode: "86",
          // imageCode: this.imageCodeValue.phone || '',
          imageCode: this.leftX,
        };
        axios
          .post("/api/user/sendVerificationCode", smsParams)
          .then((res) => res.data)
          .then((res) => {
            if (res.status == 200) {
              this.openMessage('success', '短信验证码已发送', 2000)
              this.$refs.phoneNum.setAttribute("readonly", "readonly"); //设置手机号码为readonly状态
              this.phoneImgFlag = false;
              //验证码有效期倒计时
              this.codeMsg = 120;
              let timer = setInterval(() => {
                this.codeMsg--;
                if (this.codeMsg == 0) {
                  clearInterval(timer);
                  this.codeMsg = "获取验证码";
                  this.phoneImgFlag = true;
                  // 清除只读
                  this.$refs.phoneNum.removeAttribute("readonly");
                  // this.reSetImgCode('phone')
                  // this.imageCodeValue.phone = '';
                }
              }, 1000);
            } else {
              pop(res.message || "验证码发送失败，请联系管理员");
            }
          })
          .catch((err) => {
            pop(err.response.data.message);
          });
      },
      //密码登录
      accountLogin() {
        let regUserName = /\w{2,35}$/;
        let regPassword = /(?!^\d+$)(?!^[a-zA-Z]+$)(?!^[_#@]+$).{6,12}/;
        if (!this.account) {
          this.adminLoginErrorMessage = '请输入用户名！';
          this.$refs.account.focus()
          return;
        } else if (!regUserName.test(this.account)) {
          this.adminLoginErrorMessage = '请输入正确的用户名！'
          this.$refs.account.focus()
          return
        }
        if (!this.password) {
          this.adminLoginErrorMessage = '请输入密码！';
          this.$refs.password.focus()
          return;
        } else if (!regPassword.test(this.password)) {
          this.adminLoginErrorMessage = '密码需要输入6-12个字符！'
          this.$refs.password.focus()
          return
        }
      
        if ({{ showImgCode }}) {
          this.showDialog = true
        } else {
          this.accountLoginRequest()
        }
      },

      accountLoginRequest(){
        let handlePwd = encrypt(this.password, "{{publicKey}}");
        axios
          .post("/api/admin/doLogin", {
            //账户密码验证
            userName: this.account,
            password: handlePwd.ciphertext,
            publicKey: handlePwd.publicKey,
            // imageCode: this.imageCodeValue.account || '',
            imageCode: this.leftX,
          })
          .then((res) => res.data)
          .then((res) => {
            if (res.status == 200) {
              //登录成功
              let url = window.location.pathname;
              if (url.indexOf('/admin') !== -1 || url.indexOf('/jk-admin') !== -1 || url === '/') {
                window.location.href = "/admin/dashboard";
              } else if (url.indexOf('/qy') !== -1) {
                window.location.href = "/qy/dashboard";
              } else if (url.indexOf('/user') !== -1) {
                window.location.href = "/user/dashboard";
              }
            } else {
              pop(res.message);
            }
          })
          .catch(function (error) {
            console.log(error);
          });
      },

      // 使用共享密钥进行解密
      decryptImageCode(encryptedData) {
        // 共享密钥
        let key = "2U8//VqSBjze1nja0N5jDX2arBceo5qJWjpgrwGpWx0=";
        // 将 base64 格式的数据转换为 WordArray 格式
        let encryptedDataWordArray = CryptoJS.enc.Base64.parse(encryptedData);
        let keyWordArray = CryptoJS.enc.Base64.parse(key);
        // 创建一个长度为 16 的数组，并用 0 填充
        let iv = new Array(16).fill(0);
        // 将数组转换为 WordArray 格式
        let ivWordArray = CryptoJS.lib.WordArray.create(iv);
        // 解密数据
        let decrypted = CryptoJS.AES.decrypt({ ciphertext: encryptedDataWordArray }, keyWordArray, {
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7,
          iv: ivWordArray
        });
        // 将解密后的数据转换为 UTF8 字符串
        let decryptedData = decrypted.toString(CryptoJS.enc.Utf8);
        return decryptedData
      },

      // 获取验证码图片
      reSetImgCode(item) {
        this.imgCodeUrl[item] = "/api/getImgCode?" + Math.random();
      },
    },
  });

  document.onkeydown = function (event) {
    var e = event || window.event;
    if (e && e.keyCode == 13) {
      if (vm.active == 1) {
        const aa = document.getElementById("phoneLogin");
        aa.click();
      } else if (vm.active == 2) {
        const aa = document.getElementById("accountLogin");
        aa.click();
      }
    }
  };
  $('#footerContent0').html($('#footerContent0').html("{{footerContent[0]}}").text());
  $('#footerContent1').html($('#footerContent1').html("{{footerContent[1]}}").text());
</script>

</html>