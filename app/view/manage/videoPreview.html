<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>示范企业审核</title>
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
            html {
              font-size: 12px;
            }
            .videoLeft {
              /* width: 160rem;
              /* height: 17.6rem; */ */
            }
            .reviewVideo {
              /* padding: 20px; */
              position: relative;
              /* height: 77.55vh; */
            }
            .topStyle {
              /* width: 64.53vw;
              height: 3.05vh; */
              background-color: #000000;
              opacity: 0.6026;
            }
            .localVideoStyle {
              /* width: 64.53vw;
              height: 64.5vh; */
              /* background-color: red; */
            }
            .noJoin {
              /* width: 64.53vw; */
              height: 24.54vh;
              background-color: #000000;
              opacity: 0.5;
            }
            .remoteVideoStyle {
              /* background-color: blue; */
            }
            .bottomBtn {
              position: absolute;
              /* left: 20px; */
              width: 100%;
              /* height: 3vh; */
              opacity: 0.6026;
              background: #000000;
              font-size: 1rem;
            }
            .el-button {
              background-color: transparent;
              border: 0;
              color: #C0C4CC;
            }
            .el-button+.el-button, .el-checkbox.is-bordered+.el-checkbox.is-bordered {
              margin-left: 0;
            }
            .el-button.is-disabled,
            .el-button.is-disabled:focus,
            .el-button.is-disabled:hover {
              background-color: transparent;
              border: 0;
            }
            .el-button:hover {
              background-color: transparent;
              border: 0;
            }
            .buttons-container {
        display: flex;
        flex-direction: row;
        justify-content: space-around; /* 根据需要调整，space-around 在项目之间提供了均等的空间 */
        flex-wrap: wrap; /* 允许在必要时换行 */
      }

      /* 针对 iPhone 12 的媒体查询 */
      @media (max-width: 390px) {
        .buttons-container {
          flex-wrap: nowrap; /* 确保在 iPhone 12 屏幕宽度下不换行 */
        }
      }

      .warning {
        margin-top: 50px;
        display: flex;
        flex-direction: column;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="main-container">
        <div class="videoLeft">
          <div class="reviewVideo">
            <div class="topStyle">xxxx</div>
            <div
              class="noJoin"
              id="noJoin"
              v-show="Object.keys(remoteUsers).length === 0"
            ></div>
            <div class="videoStyle">
              <div v-if="joined" class="localVideoStyle">
                <agora-video-player
                  :is-local="true"
                  :video-track="videoTrack"
                  :audio-track="audioTrack"
                  :width="'8.896rem'"
                  :height="'9.88625rem'"
                  :style="{
                    position: 'absolute',
                    'z-index':'1',
                    right: '0',
                    top: '1.37rem'
                  }"
                  ref="localVideoPlayer"
                ></agora-video-player>
              </div>
              <div
                v-if="Object.keys(remoteUsers).length"
                class="remoteVideoStyle"
              >
                <agora-video-player
                  v-for="item in remoteUsers"
                  :key="item.uid"
                  :video-track="item.videoTrack"
                  :audio-track="item.audioTrack"
                ></agora-video-player>
              </div>
            </div>
            <div class="bottomBtn buttons-container">
              <el-button
                class="el-button"
                :disabled="joined"
                @click="joinVideo"
                size="mini"
                type="text"
              >
                加入
              </el-button>
              <el-button
                class="el-button"
                :disabled="!joined"
                @click="leave"
                size="mini"
                type="text"
              >
                离开
              </el-button>
              <el-button
                class="el-button"
                :disabled="!joined"
                @click="changeCamera"
                size="mini"
                icon="el-icon-refresh"
                type="text"
              >
                切换摄像头
              </el-button>
              <!-- <el-button
                class="el-button"
                :disabled="!joined"
                @click="takeScreenshot"
                size="mini"
                icon="el-icon-camera"
                round
              >
                截屏
              </el-button>
              <el-button
                class="el-button"
                :disabled="!joined"
                @click="startRecording"
                size="mini"
                icon="el-icon-video-camera"
              >
                开始录屏
              </el-button>
              <el-button
                class="el-button"
                :disabled="!joined"
                @click="stopRecording"
                size="mini"
                icon="el-icon-video-camera"
              >
                结束录屏
              </el-button> -->
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="warning">
          <div>
            <span>预警内容：</span>
            <span style="color: #8D8D8D;line-height: 20px;">发士大夫石帆胜丰是否的沙发沙发沙发沙发024年06月11日的在岗职业健康检查结果中发现禁忌证1名，请及时安排调岗</span>
          </div>
          <div style="margin-top: 10px;">
            <span>预警等级:</span>
            <span style="color: #FF5219;">的那几</span>
          </div>
          <div style="margin-top: 10px;">
            <span>预警类型:</span>
            <span style="color: #8D8D8D;">
              体检
            </span>
          </div>
          
      </div> -->

      <join-form ref="formRef" v-show="false"></join-form>
      
    </div>

    <script src="{{staticRootPath}}/plugins/vue/2.6.10/vue.min.js"></script>
    <script src="{{staticRootPath}}/plugins/Agora_Web_SDK_FULL/AgoraRTC_N-4.21.0.js"></script>
    <script src="{{staticRootPath}}/plugins/html2canvas/html2canvas.min.js"></script>
    <script src="{{staticRootPath}}/plugins/socket/socket.io.js"></script>
    <!-- <script src="https://cdn.jsdelivr.net/npm/agora-rtc-sdk-ng"></script> -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/html2canvas"></script> -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- <script src="https://cdn.socket.io/2.0.0/socket.io.min.js"></script> -->

    <script>
      // const socket = io('http://127.0.0.1:7003', {
      //   transports: ['websocket']
      // });
      // socket.on('toHtml', function(data) {
      //     console.log('Received》》》》》》》》》》》》》》》》》》》', data);
      // });
      
      

   
      // document.addEventListener('DOMContentLoaded', (event) => {
      //       console.log('ccccccccccccccccccccccc')
      //       const socket = io('http://127.0.0.1:7003');
            
      //           socket.on("reviveData", (data) => {
      //               alert("预警短信提醒发送完成");
               
      //           });
           
      //   });
    


      Vue.component('join-form', {
        template: `
                <el-row :gutter="16">
                    <el-col :span="6">
                        <div class="title">APP ID</div>
                        <el-input type="text" placeholder="Enter the appid" v-model="appId"></el-input>
                        <div class="footer">You find your APP ID in the <a href="https://console.agora.io/projects">Agora Console</a></div>
                    </el-col>
                    <el-col :span="6">
                        <div class="title">Token(optional)</div>
                        <el-input type="text" placeholder="Enter the app token" v-model="token"></el-input>
                        <div class="footer">To create a temporary token, <a href="https://console.agora.io/projects">edit your project</a> in Agora Console.</div>
                    </el-col>
                    <el-col :span="6">
                        <div class="title">Channel Name</div>
                        <el-input type="text" placeholder="Enter the channel name" v-model="channel"></el-input>
                        <div class="footer">You create a channel when you create a temporary token. You guessed it, in <a href="https://console.agora.io/projects">Agora Console</a></div>
                    </el-col>
                    <el-col :span="6">
                        <div class="title">User ID(optional)</div>
                        <el-input type="text" placeholder="Enter the user ID (number)" v-model="uid" :formatter="(value) => \`\${value}\`" :parser="(value) => value.replace(/[^0-9]/g, '')"></el-input>
                    </el-col>
                </el-row>
            `,
        data() {
          return {
            appId: '',
            token: '',
            channel: '',
            uid: '',
          };
        },
        methods: {
          getValue() {
            return {
              appId: this.appId,
              token: this.token,
              channel: this.channel,
              uid: this.uid ? Number(this.uid) : null,
            };
          },
          setValue(data) {
            if (data.appId) {
              this.appId = data.appId;
            }
            if (data.token) {
              this.token = data.token;
            }
            if (data.channel) {
              this.channel = data.channel;
            }
            if (data.uid) {
              this.uid = data.uid;
            }
          },
        },
      });

      Vue.component('agora-video-player', {
        template: `
                <div :style="style">
                    <div ref="videoRef" :style="{ width, height }"></div>
                </div>
            `,
        props: {
          videoTrack: {
            type: Object,
            default: null,
          },
          audioTrack: {
            type: Object,
            default: null,
          },
          config: {
            type: Object,
            default: () => ({
              mirror: false,
            })
          },
          isLocal: {
            type: Boolean,
            default: false,
          },
          text: {
            type: [String, Number],
            default: '',
          },
          width: {
            type: String,
            default: '100%',
          },
          height: {
            type: String,
            default: '26rem',
          },
          style: {
            type: Object,
            default: () => ({}),
          },
        },
        data() {
          return {
            videoRef: null,
          };
        },
        mounted() {
          this.videoRef = this.$refs.videoRef;
          if (this.videoTrack) {
            this.videoTrack.play(this.videoRef, this.config);
          }
          if (!this.isLocal && this.audioTrack) {
            this.audioTrack.play();
          }

          this.$watch('videoTrack', (track) => {
            if (track && this.videoRef) {
              track.play(this.videoRef);
            }
          });

          this.$watch('audioTrack', (track) => {
            if (!this.isLocal && track) {
              track.play();
            }
          });
        },
        beforeDestroy() {
          if (this.videoTrack) {
            this.videoTrack.close();
          }
          if (this.audioTrack) {
            this.audioTrack.close();
          }
        },
        methods: {
          handleClick() {
            this.$emit('click');
          },
        },
      });

      new Vue({
        el: '#app',
        data: {
          client: AgoraRTC.createClient({
            mode: 'rtc',
            codec: 'vp8',
          }),
          joined: false,
          remoteUsers: {},
          videoTrack: null,
          audioTrack: null,
          recorder: null,
          chunks: [],
          appId: '',
          channel: '',
          token: '',
          uid: null,
          facingMode: 'user',
        },
        mounted() {
          //   this.initEvent();
          const data = JSON.parse('{{data | safe}}');
          const { appId, channel, token, uid } = data;
          this.appId = appId;
          this.channel = channel;
          this.token = token;
          this.uid = uid;
          this.$refs.formRef.setValue(data);
          window.addEventListener('beforeunload', this.handleBeforeUnload);
        },
        beforeDestroy() {
          window.removeEventListener('beforeunload', this.handleBeforeUnload);
          if (this.joined) {
            this.$message.warning('请先离开频道!');
            this.leave();
          }
        },
        methods: {
          handleBeforeUnload(event) {
            if (this.joined) {
              const message = '你确定要离开吗？请确保已经离开频道。';
              event.returnValue = message;
              return message;
            }
          },
          changeCamera() {
            if (this.facingMode === 'user') {
              this.facingMode = 'environment';
            } else {
              this.facingMode = 'user';
            }
            this.videoTrack.setDevice({
              facingMode: this.facingMode,
            });
          },
          async initTracks() {
            if (this.audioTrack && this.videoTrack) {
              return;
            }
            const tracks = await Promise.all([
              AgoraRTC.createMicrophoneAudioTrack(),
              AgoraRTC.createCameraVideoTrack({
                encoderConfig: '1080p_2',
              }),
            ]);
            this.audioTrack = tracks[0];
            this.videoTrack = tracks[1];
          },
          async handleUserPublished(user, mediaType) {
            await this.client.subscribe(user, mediaType);
            // if (mediaType === 'video') {
            //   user.videoTrack.setEncoderConfiguration({
            //     width: 640,
            //     height: 480,
            //     frameRate: 15,
            //     bitrateMax: 500,
            //   });
            // }
            this.$set(this.remoteUsers, user.uid, user);
          },
          handleUserUnpublished(user, mediaType) {
            if (mediaType === 'video') {
              this.$delete(this.remoteUsers, user.uid);
            }
          },
          async initEvent() {
            // const params = new URLSearchParams(window.location.search);
            // const query = { channel: params.get('company_id') };
            // const res = await axios.get('/api/manage/getWarningVideoToken', {
            //   params: query,
            // });
            // this.join(appId, channel, token, uid);
          },
          joinVideo() {
            this.join(this.appId, this.channel, this.token, this.uid);
          },
          async join(appId, channel, token, uid) {
            this.client.on('user-published', this.handleUserPublished);
            this.client.on('user-unpublished', this.handleUserUnpublished);

            const options = this.$refs.formRef.getValue();
            // Join a channel
            options.uid = await this.client.join(
              options.appId,
              options.channel,
              options.token || null,
              options.uid || null
            );
            await this.initTracks();
            await this.client.publish([this.videoTrack, this.audioTrack]);
            this.joined = true;
          },
          async leave() {
            if (this.videoTrack) {
              this.videoTrack.close();
              this.videoTrack = null;
            }
            if (this.audioTrack) {
              this.audioTrack.close();
              this.audioTrack = null;
            }
            this.remoteUsers = {};
            await this.client.leave();
            this.joined = false;
            this.$message.success('已离开频道!');
          },
          takeScreenshot() {
            const imageData = this.videoTrack.getCurrentFrameData();
            const canvas = document.createElement('canvas');
            canvas.width = imageData.width;
            canvas.height = imageData.height;
            const ctx = canvas.getContext('2d');
            if (!ctx) return;
            ctx.putImageData(imageData, 0, 0);
            const dataURL = canvas.toDataURL();
            const link = document.createElement('a');
            link.download = 'capture.png';
            link.href = dataURL;
            document.body.appendChild(link);
            link.click();
            link.remove();
            canvas.remove();
          },
          startRecording() {
            const videoElement =
              this.$refs.localVideoPlayer.$refs.videoRef.querySelector('video');
            if (!videoElement) {
              console.error('Video element not found');
              return;
            }
            const stream = videoElement.srcObject;
            if (!stream) {
              console.error('No stream found in video element');
              return;
            }
            this.chunks = [];
            this.recorder = new MediaRecorder(stream);
            this.$message.success('录屏开始');
            this.recorder.ondataavailable = (e) => this.chunks.push(e.data);
            this.recorder.onstop = () => {
              const blob = new Blob(this.chunks, { type: 'video/mp4' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'recording.mp4';
              a.click();
            };
            this.recorder.start();
          },
          stopRecording() {
            this.$message.success('录屏结束');
            this.recorder.stop();
          },
        },
      });
    </script>
  </body>
</html>
