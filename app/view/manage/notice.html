<!DOCTYPE html>
<html class="adminlogin">

<head>
  <meta charset="utf-8">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no" />
  <meta property="og:type" content="website" />
  <meta name="descriptison" content="{{siteSeo.description}}">
  <meta name="keywords" content="{{siteSeo.keywords}}">
  <meta name="author" content="{{siteSeo.author}}">
  <title>{{siteSeo.title}}</title>
  <script src="{{staticRootPath}}/plugins/axios/0.19.0-beta.1/axios.min.js"></script>
  <script src="{{staticRootPath}}/plugins/vue/2.6.10/vue.min.js"></script>
  <!-- 引入样式 -->
  <link rel="stylesheet" href="{{staticRootPath}}/plugins/element-ui/2.11.1/theme-chalk/index.css">
  <!-- 引入组件库 -->
  <script src="{{staticRootPath}}/plugins/element-ui/2.11.1/index.js"></script>
  <!-- 引入用户跟踪和客服 -->
  <script src="{{staticRootPath}}/plugins/jquery/1.10.2/jquery.min.js" type="text/javascript"></script>
  <script src="{{staticRootPath}}/plugins/customerService.js" type="text/javascript"></script>

  <!-- <link rel="stylesheet" href="{{staticRootPath}}/plugins/bootstrap/css/bootstrap.css"> -->

  <link href="{{staticRootPath}}/plugins/twitter-bootstrap/3.3.5/css/bootstrap.min.css" rel="stylesheet">
  <script src="{{staticRootPath}}/plugins/twitter-bootstrap/3.3.5/js/bootstrap.min.js" type="text/javascript"></script>


  <style>
    .projectPublicityTitle {
      text-align: center;
      font-size: 28px;
      font-weight: bold;
      color: #000000;
      line-height: 32px;
      margin: 70px 30px;
    }
    .table_container {
      width: 100%;
      margin: 30px 0 60px;
    }

    .table_container_main {
      width: 80%;
      margin: 0 auto;
    }

    .pagination {
      margin-top: 50px;
      text-align: center;
    }

    /* 底部 */
    footer {
      /* height: 200px; */
      position: fixed;
      width: 100%;
      bottom: 0;
      background: #0063E0;
      color: #fff;
    }

    footer ul {
      display: flex;
      width: 100%;
      padding-top: 40px;
      box-sizing: border-box;
      list-style: none;
    }

    footer ul li {
      flex: 1;
    }

    footer ul li .title4 {
      font-size: 18px;
      font-weight: 600;
      color: #BFD4FF;
      margin-bottom: 26px;
    }

    footer ul li a {
      color: #fff;
      font-size: 14px;
      display: block;
      font-weight: normal;
      line-height: 29px;
      cursor: pointer;
      opacity: .9;
    }

    footer ul li a:hover {
      opacity: 1;
      text-decoration: underline;
      color: #fff;
    }

    footer ul li:last-child a {
      cursor: auto;
    }

    footer ul li a.phone {
      text-indent: 3.25em;
    }

    footer .recordInfo {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      width: 100%;
      font-size: 12px;
      font-weight: normal;
      opacity: .9;
      text-align: center;
    }

    .QRCode_dv {
      display: flex;
    }

    .QRCode_dv img {
      width: 90px;
      height: 90px;
    }

    .QRCode_item {
      text-align: center;
      margin-right: 40px;
    }

    .QRCode_item p {
      margin-top: 10px;
    }

    .nav {
      width: 100%;
      height: 68px;
      background: #FFFFFF;
      box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.08);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40px;
      z-index: 9999;
    }

    .nav_left {
      width: 60%;
      flex: 1;
    }

    .navList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 200px;
    }

    .navItem {
      line-height: 16px;
      margin-right: 20px;
    }

    .navItem a {
      font-size: 16px;
      color: #0063E0;
      text-decoration: none;
    }

    .navItem a:active {
      font-weight: bold;
    }

    .logo {
      width: 100%;
      max-width: 500px;
    }

    @media (max-width: 767px) {
      footer {
        position: relative;
        margin-top: 50px;
      }

      footer ul {
        display: block !important;
      }

      footer ul li {
        margin-top: 20px;
      }

      .table_container_main {
        width: 100%;
      }
      .wtable {
        width: 90%;
      }
    }
    .activeNavItem {
            font-weight: bold;
        }

    .projectName_a {
      cursor: pointer;
    }
    .wtable {
    margin-top: 30px;
    margin: 0 auto;
    width: 80%;
    border-collapse: collapse;
    table-layout: fixed;
  }
  .wtable td {
    padding: 10px;
    border: 1px solid #E4E7ED;
    height: 40px;
  }
  .wtable td div {
      white-space: pre-wrap;
    }
  .wtable .info {
      background: #fafafa;
      text-align: center;
    }
    .file {
      margin-bottom: 10px;
    }
    .fileInfo {
      margin-left: 10px;
      color: #409EFF;
    }
  </style>
</head>

<body>
  <div id="projectPublicity">
    <div class="nav">
      <div class="nav_left">
        <!-- <img class="logo" src="/static/images/home/<USER>" /> -->
      </div>
      <div class="navList">
        <div class="navItem"><a href="/">首页</a></div>
        <!-- <div class="navItem" :class="activeRouter === '/admin/projectPublicity' ? 'activeNavItem' : ''"><a href="/admin/projectPublicity">项目公示</a></div> -->
        <!-- <div class="navItem"><a href="/">帮助</a></div> -->
      </div>
    </div>

    <div class="projectPublicityTitle">通知公告</div>
    
    <div>
        <table class="wtable">
            <tr>
              <td class="info" colspan="4">标题</td>
              <td colspan="20">
                <div v-text="notice.title"></div>
              </td>
            </tr>
            <tr>
              <td class="info" colspan="24">附件</td>
            </tr>
            <tr>
              <td  colspan="24">
                <div v-if="!notice.files || notice.files.length === 0">无</div>
                <div v-for="item in notice.files" class="file">
                  <el-link :underline="false" :href="item.url" target="_blank">
                    <span>
                      <i class="el-icon-tickets"></i>
                      <span v-text="item.name"></span>
                      <span class="fileInfo">[下载]</span>
                    </span>
                  </el-link>
                  
                </div>
              </td>
            </tr>
          </table>
    </div>


    <!-- 底部区域 -->
    <footer>
      <div class="container-fluid">
        <ul>
          <li>
            <p class="title4">解决方案</p>
            <a href="https://zyws.cn/" target="_blank">用人单位职业健康数字化平台</a>
            <a href="https://jg.zyws.cn/" target="_blank">职业健康智慧监管数字化大脑</a>
            <a href="https://jcqlc.zyws.cn/" target="_blank">职业卫生技术服务机构全流程平台</a>
          </li>
          <li>
            <p class="title4">友情链接</p>
            <a href="http://www.nhc.gov.cn/zyjks/new_index.shtml" target="_blank">职业健康司</a>
            <a href="http://www.chinacdc.cn" target="_blank">中国疾病预防控制中心（中国CDC）</a>
            <a href="http://www.chinasafety.ac.cn/" target="_blank">中国安全生产科学研究院（中国安科院）</a>
          </li>
          <li>
            <p class="title4">行业资讯</p>
            <a href="https://mp.weixin.qq.com/s/R4AOowpqGKVjfPbm8IpoOA" target="_blank">浙江省政务网职业病危害申报指南</a>
            <a href="https://news.zyws.cn/details/AWVYrFGX.html" target="_blank">使用有毒物品作业场所劳动保护条例</a>
            <a href="http://www.nhc.gov.cn/zyjks/s7786/202104/c41d2dcfb8b64610a173d0357ef22a88.shtml"
              target="_blank">职业病诊断与鉴定管理方法</a>
          </li>
        </ul>

        <div class="recordInfo">
          <div>   {{footerContent[0]}} <a href="http://beian.miit.gov.cn/"
            target="_blank" style="color:#fff;">{{recordNumber}}</a></div>
          <p>{{footerContent[1]}}</p>
        </div>
      </div>
    </footer>

  </div>
</body>


<script>
  const vm = new Vue({
    el: '#projectPublicity',
    data: {
      notice: {},
      activeRouter: '',
    },
    async created() {
      this.activeRouter = window.location.pathname
      await this.getData()
    },
    mounted() {
    },
    methods: {
      async getData() {
        console.log(window.location)
        const res = await axios.post(this.activeRouter, {})
        console.log(res)
        if (res.data && res.data.data) {
          this.notice = res.data.data
        }
      },
      
      goDetail(item) {
        sessionStorage.setItem('projectPublicityPageInfo', JSON.stringify({
          ...this.pageInfo,
          keyword: this.keyword,
        }))
        window.location.href = `/admin/projectPublicityDetail?id=${item._id}`
      },
    },

  })

</script>

</html>