<!-- FILEPATH: d:\studyplace\jg\app\view\manage\certificatePreview.html -->

<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>职业健康培训证书</title>

        <!-- 引入PDF.js库 -->
        <script src="/static/plugins/pdfjs/3.10.111/build/pdf.js"></script>
        <script>
            // 从URL中获取证书编号
            var urlParams = new URLSearchParams(window.location.search);
            var certificateNumber = urlParams.get('number');
            let certificateDetails = null;
            let pdfUrl = '';

            // 将证书编号传给后端，后端返回证书信息
            // 发送AJAX请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/certificatePreview/getCertificate'); // 根据后端接口的路径进行调整
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === XMLHttpRequest.DONE) {
                    if (xhr.status === 200) {
                        certificateDetails = JSON.parse(xhr.responseText);
                        pdfUrl = '/static/certificate/' + certificateDetails[0].img;
                        // console.log(pdfUrl, 'pdfUrl');
                        // console.log(pdfjsLib, 'pdfjsLib');
                        pdfjsLib.GlobalWorkerOptions.workerSrc = '/static/plugins/pdfjs/3.10.111/build/pdf.worker.js';
                        // // 解决跨域问题
                        // pdfjsLib.getDocument({
                        //     url: pdfUrl,
                        //     withCredentials: true,
                        //     httpHeaders: {
                        //         'Access-Control-Allow-Origin': '*'
                        //     }
                        // }).promise.then(function(pdf) {
                        //     // 加载和渲染PDF文件
                        // });
                        // 获取父容器元素
                        var container = document.querySelector('.pdf');

                        // 获取 canvas 元素
                        var canvas = document.getElementById('pdfCanvas');

                        // 使用 PDF.js 加载和渲染 PDF 文件
                        pdfjsLib.getDocument(pdfUrl).promise.then(function(pdf) {
                            var totalPages = pdf.numPages;
                            var renderPromises = [];

                            for (var i = 1; i <= totalPages; i++) {
                                var canvas = document.createElement('canvas');
                                container.appendChild(canvas);
                                renderPromises.push(renderPage(i, canvas));
                            }

                            function renderPage(pageNumber, canvas) {
                                return pdf.getPage(pageNumber).then(function(page) {
                                    var viewport = page.getViewport({ scale: 1 });
                                    
                                    // 更新 canvas 的大小为父容器的大小
                                    canvas.width = container.clientWidth;
                                    canvas.height = container.clientHeight;

                                    // 计算缩放比例，使 PDF 页面填满 canvas
                                    var scale = Math.min(canvas.width / viewport.width, canvas.height / viewport.height);
                                    scale = Math.max(scale, 1); // 确保至少为1，避免过度缩小
                                    // 根据缩放比例重新计算 viewport
                                    viewport = page.getViewport({ scale });

                                    // 如果缩放后的页面宽度小于canvas的宽度，则重新计算缩放比例，使页面宽度与canvas的宽度匹配
                                    if (viewport.width < canvas.width) {
                                        scale = canvas.width / viewport.width;
                                        viewport = page.getViewport({ scale });
                                    }

                                    var context = canvas.getContext('2d');
                                    canvas.height = viewport.height;
                                    canvas.width = viewport.width;

                                    var renderContext = {
                                        canvasContext: context,
                                        viewport: viewport
                                    };

                                    return page.render(renderContext).promise.then(function() {
                                        // 将canvas元素转换为图像
                                        var imgData = canvas.toDataURL('image/png');
                                        // console.log(imgData);
                                    });
                                    // return page.render(renderContext).promise;
                                });
                            }

                            Promise.all(renderPromises)
                                .then(function() {
                                    console.log("All pages rendered");
                                })
                                .catch(function(error) {
                                    console.error("Error rendering pages:", error);
                                });
                        });
                        // console.log(container);
                        // iframe.src = `${blobUrl}#scrollbars=0&toolbar=0&statusbar=0`;
                        updateCertificateFields(certificateDetails[0]);
                    } else {
                        // 处理请求错误的情况
                        console.error('请求错误:', xhr.status);
                    }
                }
            };
            xhr.send(JSON.stringify({ certificateNumber: certificateNumber }));

            function updateCertificateFields(details) {
                // 更新页面中的证书详细信息
                document.querySelector('.name-value').textContent = details.winner.name;
                // document.querySelector('.certificates-type-value').textContent = details.trainingType;
                document.querySelector('.certificates-number-value').textContent = details.number;
                document.querySelector('.company-name-value').textContent = details.winner.companyName;
                document.querySelector('.unit-value').textContent = details.unit;
                document.querySelector('.date-value').textContent = details.issuanceTime;
            }

            // window.download = function() {
            //     window.open(pdfUrl);
            // }

            window.download = function() {
                var link = document.createElement('a');
                link.href = pdfUrl;
                link.download = certificateDetails[0].number + '-' + certificateDetails[0].winner.name + '.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        </script>
        <style>
            /* Add any custom styles for the certificate preview here */
            html, body {
                margin: 0;
                padding: 0;
            }
            .certificate-preview {
                width: 100%;
                height: 100%;
                /* border: 1px solid #ff0000; */
                background-color: #F6F6F6;
            }
            .certificate-preview .title {
                margin-top: -30px;
                width: 100%;
                height: 150px;
                background: linear-gradient(to top right, #F6F6F6, #336BFE);
            }
            .certificate-preview .title h1 {
                color: #ffffff;
                text-align: center;
                font-size: 40px;
                padding-top: 50px;
            }
            .certificate-preview .information {
                font-size: 30px;
                padding: 20px;
                background-color: #ffffff;
                /* border: 1px solid #ff0000; */
                color: #999999;
                border-radius: 5px;
                margin: 15px 15px;
            }
            .certificate-preview .information .name {
                margin-bottom: 20px;
            }
            .certificate-preview .information .name .name-value {
                color: #000000;
            }
            .certificate-preview .information .certificates-type {
                margin-bottom: 20px;
            }
            .certificate-preview .information .certificates-type .certificates-type-value {
                color: #000000;
            }
            .certificate-preview .information .certificates-number {
                margin-bottom: 20px;
            }
            .certificate-preview .information .certificates-number .certificates-number-value {
                color: #000000;
            }
            .certificate-preview .information .company-name {
                margin-bottom: 20px;
            }
            .certificate-preview .information .company-name .company-name-value {
                color: #000000;
            }
            .certificate-preview .information .unit {
                margin-bottom: 20px;
            }
            .certificate-preview .information .unit .unit-value {
                color: #000000;
            }
            .certificate-preview .information .date {
                margin-bottom: 0px;
            }
            .certificate-preview .information .date .date-value {
                color: #000000;
            }
            .certificate-preview .pdf {
                position: relative;
                background-color: #ffffff;
                width: 97%;
                height: auto;
                margin: 15px 15px;
                border-radius: 5px;
                /* border: 1px solid #ff0000 */
            }
            #pdfCanvas {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: none;
            }
            .certificate-preview .download {
                margin: 30px 15px;
                text-align: center;
            }
            .certificate-preview .download .btn {
                background-color: #3E73FE;
                width: 300px;
                height: 100px;
                color: white;
                padding: 10px 20px;
                text-align: center;
                font-size: 40px;
                border-radius: 10px;
            }
        </style>
    </head>
    <body>
        <div class="certificate-preview">
            <div class="title">
                <h1>职业健康培训证书验证</h1>
            </div>
            <div class="information">
                <div class="name">
                    姓&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp名: <span class="name-value">John Doe</span>
                </div>
                <!-- <div class="certificates-type">
                    证书类型: <span class="certificates-type-value">Occupational Health and Safety</span>
                </div> -->
                <div class="certificates-number">
                    证书编号: <span class="certificates-number-value">**********</span>
                </div>
                <div class="company-name">
                    单位名称: <span class="company-name-value">ABC Company</span>
                </div>
                <div class="unit">
                    发证单位: <span class="unit-value">ABC Company</span>
                </div>
                <div class="date">
                    发证日期: <span class="date-value">2018-01-01</span>
                </div>
                <img src="/static/images/certificateSecurity.png" alt="Certificate Security" style="position: absolute; top: 400px; right: 130px; width: 300px; height: 300px; z-index: 1000;">
            </div>
            <div class="pdf">
                <canvas id="pdfCanvas"></canvas>
            </div>
            <div class="download">
                <button class="btn" onclick="window.download()">下载证书</button>
            </div>
        </div>
    </body>
</html>
