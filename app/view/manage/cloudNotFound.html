<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>云监督</title>
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
            html {
              font-size: 12px;
            }
          
            .reviewVideo {
              /* padding: 20px; */
              position: relative;
              /* height: 77.55vh; */
            }
            .topStyle {
        
              background-color: #000000;
              opacity: 0.6026;
            }
       
            .noJoin {
              /* width: 64.53vw; */
              height: 24.54vh;
              background-color: #000000;
              opacity: 0.5;
            }
          
            .bottomBtn {
              position: absolute;
              /* left: 20px; */
              width: 100%;
              /* height: 3vh; */
              opacity: 0.6026;
              background: #000000;
              font-size: 1rem;
            }
            .el-button {
              background-color: transparent;
              border: 0;
              color: #C0C4CC;
            }
            .el-button+.el-button, .el-checkbox.is-bordered+.el-checkbox.is-bordered {
              margin-left: 0;
            }
            .el-button.is-disabled,
            .el-button.is-disabled:focus,
            .el-button.is-disabled:hover {
              background-color: transparent;
              border: 0;
            }
            .el-button:hover {
              background-color: transparent;
              border: 0;
            }
            .buttons-container {
              display: flex;
              flex-direction: row;
              justify-content: space-around; /* 根据需要调整，space-around 在项目之间提供了均等的空间 */
              flex-wrap: wrap; /* 允许在必要时换行 */
            }

      /* 针对 iPhone 12 的媒体查询 */
      @media (max-width: 390px) {
        .buttons-container {
          flex-wrap: nowrap; /* 确保在 iPhone 12 屏幕宽度下不换行 */
        }
      }

  .sign-finish {
    background-color: #fff;
    position: fixed;
    z-index: 666;
    top: -.5%;
    width: 98vw;
    height: 98vh;
  }
  .sign-finish button {
    height: 32px;
    padding: 0 15px;
    font-size: 12px;
    border-radius: 2px;
  }

  .sign-finish .danger {
    color: #fff;
    background: #ee0a24;
    border: 1px solid #ee0a24;
  }
  .sign-finish .warning {
    color: #fff;
    background: #ff976a;
    border: 1px solid #ff976a;
  }
  .sign-finish .primary {
    color: #fff;
    background: #1989fa;
    border: 1px solid #1989fa;
  }
  .sign-finish .success {
    color: #fff;
    background: #3E73FE;
    border: 1px solid #07c160;
  }
  .sign-finish .close {
    color: #fff;
    background: #7F7F7F;
    border: none;
  }
  .sign-finish canvas {
    border-radius: 10px;
    border: 2px dashed #ccc;
  }

.wrap2 {
    padding: 15px;
    height: 100%;
    display: flex;
    justify-content: center;
  }
 .wrap2 .actions {
      margin-right: 10px;
      white-space: nowrap;
      transform: rotate(90deg);
    }

  .wrap2 .actionsWrap {
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .wrap2 .canvas {
      flex: 1;
    }
  

    .wrap2 .actions button{
          margin-right: 20px;
      }

      .custom-message {
        font-size: 14px; /* 设置信息提示的字体大小 */
        min-width: 300px; /* 设置信息提示的最小宽度 */
        max-width: 500px; /* 设置信息提示的最大宽度 */
      }

      .preview {
        border: none; 
        background-color: #F0F9EB;
        border-radius: 5px; 
        padding: 5px 10px; 
        color: #67C23A;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="main-container">
        <div class="videoLeft">
          <el-alert
            title="当前会议未开启"
            type="warning"
            :closable="false">
          </el-alert>
          <!-- 签名按钮 -->
          <div style="margin-top: 40px;padding-bottom: 10px;">
            <span>指导书</span>
            
            <button style="margin-left: 10px;border: none; background-color: #ECF5FF;border-radius: 5px; padding: 5px 10px; color: #346CFE;" @click="goSign">签字确认</button>
            <button style="margin-left: 10px;border: none; background-color: #ECF5FF;border-radius: 5px; padding: 5px 10px; color: #346CFE;" @click="download">下载</button>
          </div>
        </div>
      </div>
     
      <div class="sign-finish" v-show="isSigning">
        <div class="wrap2">
          <div class="actionsWrap">
            <div class="actions">
              <button class="close" @click="closeSign" >关闭</button>
              <button class="danger" @click="handleClear2" >清除</button>
              <button class="warning" @click="handleUndo2" >撤销</button>
              <button class="success" @click="handlePreview2" >提交</button>
            </div>
          </div>
          <canvas class="canvas" ref="canvas2" />
        </div>
      </div>
      
      <!-- <el-dialog title="指导意见" :visible.sync="dialogTableVisible" width="80%"> -->
        <el-table :data="gridData" header-cell-style="background-color: #f5f7fa; color: #0F111A;" stripe border>
          <el-table-column property="describe" label="存在问题" width="200"></el-table-column>
          <el-table-column property="advice" label="指导意见" width="200"></el-table-column>
        </el-table>
      <!-- </el-dialog> -->
      
    </div>
    <script src="{{staticRootPath}}/plugins/axios/0.19.0-beta.1/axios.min.js"></script>
    <script src="{{staticRootPath}}/plugins/vue/2.6.10/vue.min.js"></script>
    
    <script src="{{staticRootPath}}/plugins/html2canvas/html2canvas.min.js"></script>
    
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/smooth-signature/dist/index.umd.min.js"></script>
    <script>

      new Vue({
        el: '#app',
        data: {
          isSigning: false,
          signature2:"",
          channel: '',
          source: '',
          filePath:"",
          isPreview:false,
          pdfSrc:"",
          gridData:[],
          dialogTableVisible: false,
          isChange: true,
        },
        created() {
          const data = JSON.parse('{{data | safe}}');
          this.channel = data._id
          this.gridData = data.advice;
          if('instructions' in data && data.instructions) {
            this.filePath = data.instructions
          }
        },
       
        mounted() {
        
          this.initSignture2();
    
        },
        methods: {
          
          download() {
            if(!this.filePath) {
              this.$message.warning('请等待监管人员发送指导意见书')
              return
            }
            let oA = document.createElement("a");
            oA.download = ''; // 设置下载的文件名，默认是'下载'
            oA.href = this.filePath;
            oA.download = '指导意见书'; // 设置文件名
            document.body.appendChild(oA);
            oA.click();
            oA.remove(); // 下载之后把创建的元素删除
          },
         
        
          goSign() {
            if(!this.filePath) {
              this.$message.warning('请等待监管人员发送指导意见书')
              return
            }
           this.isSigning = true
          },
          closeSign() {
            this.signature2.clear();
            this.isSigning = false
          },
          initSignture2() {
            const canvas = this.$refs["canvas2"];
            const options = {
              width: window.innerWidth - 120,
              height: window.innerHeight - 80,
              minWidth: 2,
              maxWidth: 3,
              openSmooth:true,
            };
            this.signature2 = new SmoothSignature(canvas, options);
          },
      
        handleClear2() {
          this.signature2.clear();
        },
    
        handleUndo2() {
          this.signature2.undo();
        },
        
        handlePreview2() {
          const isEmpty = this.signature2.isEmpty();
          if (isEmpty) {
            this.$message.warning('请签名')
            return;
          }
          const canvas = this.signature2.getRotateCanvas(-90);
          const pngUrl = canvas.toDataURL();
        
          // 创建一个包含Base64数据的对象
          const dataToSend = {
            base64: pngUrl,
            _id: this.channel
          };
          // 发送POST请求到服务器
      
          axios.post('/manage/enterpriseServe/cloudSupervisionSign', dataToSend)
            .then(response => {
              console.log('Success:', response.data.data.instructions);
              this.filePath = response.data.data.instructions
            })
            .catch(error => {
              console.error('Error:', error);
            });

           this.isSigning = false
           this.signature2.clear();
           this.$message.success('已提交签名')
        },
        },
      });
    </script>
  </body>
</html>
