<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta name="renderer" content="webkit">
    <meta name="description" content="{{siteSeo.description}}">
    <meta name="keywords" content="{{siteSeo.keywords}}">
    <meta name="author" content="{{siteSeo.author}}">
		<meta name="version" content="{{appVersion}}">
    <title>{{siteSeo.title}}</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="importmap-type" content="systemjs-importmap">
	<input type="hidden" id="renderMap" value="{{renderMapJson}}">
	<meta name="referrer" content="no-referrer" />
	<script type="systemjs-importmap">
		{
            "imports": {
								"moment": "{{staticRootPath}}/plugins/moment.js/moment.js",
                "single-spa": "{{staticRootPath}}/plugins/single-spa/4.3.7/system/single-spa.min.js",
                "vue": "{{staticRootPath}}/plugins/vue/2.6.10/vue.min.js",
                "vuex": "{{staticRootPath}}/plugins/vuex/3.1.1/vuex.min.js",
                "vue-router": "{{staticRootPath}}/plugins/vue-router/3.0.7/vue-router.min.js",
                "element-ui": "{{staticRootPath}}/plugins/element-ui/2.11.1/index.js",
                "vue-i18n": "{{staticRootPath}}/plugins/vue-i18n/8.14.0/vue-i18n.min.js",
                "axios": "{{staticRootPath}}/plugins/axios/0.19.0-beta.1/axios.min.js",
								"lodash": "{{staticRootPath}}/plugins/lodash.js/4.17.15/lodash.min.js",
								"xlsx": "{{staticRootPath}}/plugins/xlsx/0.16.2/xlsx.core.min.js",
								{% for appItem in renderMap %}
									"{{appItem.name}}": "{{appItem.path}}",
								{% endfor %}
                "crypto-js": "{{staticRootPath}}/plugins/crypto-js/3.1.9/crypto-js.min.js"
            }
        }
    </script>
	<link rel="stylesheet" href="{{staticRootPath}}/plugins/element-ui/2.11.1/theme-chalk/index.css">
	<link rel="preload" href="{{staticRootPath}}/plugins/single-spa/4.3.7/system/single-spa.min.js" as="script"
		crossorigin="anonymous" />
	<link rel="preload" href="{{staticRootPath}}/plugins/vue/2.6.10/vue.min.js" as="script" crossorigin="anonymous" fetchpriority="low" />
	<link rel="preload" href="{{staticRootPath}}/plugins/element-ui/2.11.1/index.js" as="script"
		crossorigin="anonymous" />
	<script src="{{staticRootPath}}/plugins/import-map-overrides/1.7.2/import-map-overrides.js"></script>
	<script src="{{staticRootPath}}/plugins/systemjs/4.1.0/system.min.js"></script>
	<script src="{{staticRootPath}}/plugins/systemjs/4.1.0/extras/amd.min.js"></script>
	<script src="{{staticRootPath}}/plugins/systemjs/4.1.0/extras/named-exports.js"></script>
	<script src="{{staticRootPath}}/plugins/systemjs/4.1.0/extras/named-register.min.js"></script>
	<script src="{{staticRootPath}}/plugins/systemjs/4.1.0/extras/use-default.min.js"></script>
	<script type="text/javascript">
		function loadScript(url, callback) {
		var script = document.createElement("script");
		script.type = "text/javascript";
		script.src = url;
		script.onload = callback;
		document.head.appendChild(script);
		}

		function isIPAddress(hostname) {
		return /^\d+\.\d+\.\d+\.\d+$/.test(hostname);
		}

		if (isIPAddress(window.location.hostname)||/InternalNetDev/.test(window.location.href)) {
		// 内网环境，使用原来的方式引入高德地图和百度地图
		loadScript("/gdWebApi/maps?v=1.4.22&key=3152d98de01c9a889ed923240d7df417", function() {
			loadScript("{{staticRootPath}}/plugins/gaodeJs/ui.js", function() {
			// 引入百度地图
			loadScript("{{staticRootPath}}/plugins/gaodeJs/bd.js", function() {
				window._AMapSecurityConfig = {
				serviceHost: `${window.location.origin}/_AMapService`,
				};
			});
			});
		});
		} else {
			let gdWebApi = document.createElement('script');
				gdWebApi.setAttribute('fetchpriority', 'high');
				gdWebApi.type = 'text/javascript';
				gdWebApi.src = '//webapi.amap.com/ui/1.1/main-async.js';
				document.head.appendChild(gdWebApi);
			let jsapi = document.createElement('script');
				jsapi.setAttribute('fetchpriority', 'high');
				jsapi.charset = 'utf-8';
				jsapi.src = 'https://webapi.amap.com/maps?v=1.4.15&key=7ec041834ee3cfa942049b896b1abb22&callback=onGdLoad';
				document.head.appendChild(jsapi);
				function onGdLoad (){
					if(window.AMap){	
						window.AMap = AMap;
						initAMapUI()
					}
				}
			onGdLoad()
		}
	</script>
	
	<!-- 引入用户跟踪和客服 -->
  	<script src="{{staticRootPath}}/plugins/customerService.js" type="text/javascript"></script>
	<!-- 超时没有操作自动退出登录 -->
	<!-- <script src="{{staticRootPath}}/plugins/outTime.js?v=1.1" type="text/javascript"></script> -->
</head>

<body>
	<script>
		(function () {
			const currentVersion = "{{appVersion}}"; // 当前版本号，由后端模板动态插入
			const localVersionKey = "app_version"; // 存储版本号的 Key
			const storedVersion = localStorage.getItem(localVersionKey); // 获取本地存储的版本号
			const iconStyle = [
				"color: #ff9800", // 橙色
				"font-size: 16px",
			].join(";");
			console.info(`%c🚀 Version: ${currentVersion}`, iconStyle);
			// 检查版本号是否变化
			if (storedVersion && storedVersion !== currentVersion) {
				console.info("版本号已更新，强制刷新页面...");
				// 清理缓存，可以选择性清除
				localStorage.clear(); // 清除 LocalStorage
				sessionStorage.clear(); // 清除 SessionStorage

				// 强制刷新页面
				location.reload(true); // true 表示强制从服务器加载最新资源
			}

			// 更新本地存储版本号
			localStorage.setItem(localVersionKey, currentVersion);
		})();
		(function () {
			// See https://github.com/systemjs/systemjs/issues/1939
			var originalResolve = System.resolve
			var moduleMap = {}
			System.resolve = function (name) {
				return originalResolve.apply(this, arguments).then(resolved => {
					moduleMap[name] = resolved;
					return resolved;
				});
			}
			window.getPublicPath = function (name) {
				const url = moduleMap[name]
				if (url) {
					let index = url.lastIndexOf('/js')
					if (index < 0) {
						index = url.lastIndexOf('/')
					}
					index++
					return url.slice(0, index);
				} else {
					throw Error(`Could not find url for module '${name}'`)
				}
			}

			Promise.all([System.import('single-spa'), System.import('vue'), System.import('vue-router')]).then(
				function (
					modules) {
					var singleSpa = modules[0];
					var Vue = modules[1];
					var VueRouter = modules[2];
					var eventBus = new Vue;
					var staticRootPath = "{{staticRootPath}}";
					var adminBasePath = "{{adminBasePath}}";
					var appVersion = "{{appVersion}}";
					var appName = "{{appName}}";
					Vue.use(VueRouter)

					var renderMap = document.getElementById('renderMap').value;
					var renderMapObj = JSON.parse(renderMap);
					for (const mapItem of renderMapObj) {

						if (mapItem.name == 'navbar') {
							singleSpa.registerApplication(
								mapItem.name,
								() => System.import(mapItem.name),
								location => true, {
								eventBus,
								staticRootPath,
								adminBasePath,
								appVersion,
								appName
							});
						} else {
							singleSpa.registerApplication(
								mapItem.name,
								() => System.import(mapItem.name),
								location => location.pathname.startsWith('{{adminBasePath}}/' + mapItem.name), {
								eventBus,
								staticRootPath,
								adminBasePath,
								appVersion,
								appName
							});
						}
						
					}
					// singleSpa.registerApplication(
					// 		'VisualizationFz',
					// 		() => import('dashboard'), // 导入子应用的入口文件
					// 		(location) => location.pathname.startsWith('/admin/dashboard/VisualizationFz') // 匹配相应的 URL 规则
					// 	);
					singleSpa.start();
					document.getElementById('renderMap').value = '';
				})
		})()
	</script>
	<!-- See https://github.com/joeldenning/import-map-overrides#user-interface  -->
	<import-map-overrides-full show-when-local-storage="overrides-ui"></import-map-overrides-full>
</body>

</html>