<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>云监督</title>
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
            html {
              font-size: 12px;
            }
          
            .reviewVideo {
              /* padding: 20px; */
              position: relative;
              /* height: 77.55vh; */
            }
            .topStyle {
        
              background-color: #000000;
              opacity: 0.6026;
            }
       
            .noJoin {
              /* width: 64.53vw; */
              height: 24.54vh;
              background-color: #000000;
              opacity: 0.5;
            }
          
            .bottomBtn {
              position: absolute;
              /* left: 20px; */
              width: 100%;
              /* height: 3vh; */
              opacity: 0.6026;
              background: #000000;
              font-size: 1rem;
            }
            .el-button {
              background-color: transparent;
              border: 0;
              color: #C0C4CC;
            }
            .el-button+.el-button, .el-checkbox.is-bordered+.el-checkbox.is-bordered {
              margin-left: 0;
            }
            .el-button.is-disabled,
            .el-button.is-disabled:focus,
            .el-button.is-disabled:hover {
              background-color: transparent;
              border: 0;
            }
            .el-button:hover {
              background-color: transparent;
              border: 0;
            }
            .buttons-container {
              display: flex;
              flex-direction: row;
              justify-content: space-around; /* 根据需要调整，space-around 在项目之间提供了均等的空间 */
              flex-wrap: wrap; /* 允许在必要时换行 */
            }

      /* 针对 iPhone 12 的媒体查询 */
      @media (max-width: 390px) {
        .buttons-container {
          flex-wrap: nowrap; /* 确保在 iPhone 12 屏幕宽度下不换行 */
        }
      }
/* 
      .warning {
        margin-top: 50px;
        display: flex;
        flex-direction: column;
      } */
  .sign-finish {
    background-color: #fff;
    position: fixed;
    z-index: 666;
    top: -.5%;
    width: 98vw;
    height: 98vh;
  }
  .wrap2 .canvas {
      flex: 1;
      z-index: 999;
    }
  .sign-finish button {
    height: 32px;
    padding: 0 15px;
    font-size: 12px;
    border-radius: 2px;
  }

  .sign-finish .danger {
    color: #fff;
    background: #ee0a24;
    border: 1px solid #ee0a24;
  }
  .sign-finish .warning {
    color: #fff;
    background: #ff976a;
    border: 1px solid #ff976a;
  }
  .sign-finish .primary {
    color: #fff;
    background: #1989fa;
    border: 1px solid #1989fa;
  }
  .sign-finish .success {
    color: #fff;
    background: #3E73FE;
    border: 1px solid #07c160;
  }
  .sign-finish .close {
    color: #fff;
    background: #7F7F7F;
    border: none;
  }
  .sign-finish canvas {
    border-radius: 10px;
    border: 2px dashed #ccc;
  }

.wrap2 {
    padding: 15px;
    height: 100%;
    display: flex;
    justify-content: center;
  }
 .wrap2 .actions {
   
      margin-right: 10px;
      white-space: nowrap;
      transform: rotate(90deg);
    }

  .wrap2 .actionsWrap {
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  

    .wrap2 .actions button{
          margin-right: 20px;
      }

      .custom-message {
        font-size: 14px; /* 设置信息提示的字体大小 */
        min-width: 300px; /* 设置信息提示的最小宽度 */
        max-width: 500px; /* 设置信息提示的最大宽度 */
      }

      .preview {
        border: none; 
        background-color: #F0F9EB;
        border-radius: 5px; 
        padding: 5px 10px; 
        color: #67C23A;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="main-container">
        <div class="videoLeft">
          <div class="reviewVideo">
            <div class="topStyle">xxxx</div>
            <div
              class="noJoin"
              id="noJoin"
              v-show="Object.keys(remoteUsers).length === 0"
            ></div>
            <div class="videoStyle">
              <div v-if="joined" class="localVideoStyle">
                <agora-video-player
                  :is-local="true"
                  :video-track="videoTrack"
                  :audio-track="audioTrack"
                  :width="'8.896rem'"
                  :height="'9.88625rem'"
                  :style="{
                    position: 'absolute',
                    'z-index':'1',
                    right: '0',
                    top: '1.37rem'
                  }"
                  ref="localVideoPlayer"
                ></agora-video-player>
              </div>
              <div
                v-if="Object.keys(remoteUsers).length"
                class="remoteVideoStyle"
              >
                <agora-video-player
                  v-for="item in remoteUsers"
                  :key="item.uid"
                  :video-track="item.videoTrack"
                  :audio-track="item.audioTrack"
                ></agora-video-player>
              </div>
            </div>
            <div class="bottomBtn buttons-container">
              <el-button
                class="el-button"
                :disabled="joined"
                @click="joinVideo"
                size="mini"
                type="text"
              >
                加入
              </el-button>
              <el-button
                class="el-button"
                :disabled="!joined"
                @click="leave"
                size="mini"
                type="text"
              >
                离开
              </el-button>
              <el-button
                class="el-button"
                :disabled="!joined"
                @click="changeCamera"
                size="mini"
                icon="el-icon-refresh"
                type="text"
              >
                切换摄像头
              </el-button>
              
            </div>
          </div>

          <!-- 签名按钮 -->
          <div style="margin-top: 40px;padding-bottom: 10px;">
            <span>指导书</span>
            
            <button style="margin-left: 10px;border: none; background-color: #346CFE;border-radius: 5px; padding: 5px 10px; color: #fff;" @click="goSign">签字确认</button>
            <button style="margin-left: 10px;border: none; background-color: #346CFE;border-radius: 5px; padding: 5px 10px; color: #fff;" @click="download">下载</button>
          </div>
        </div>
      </div>
     
      <div class="sign-finish" v-show="isSigning">
        <div class="wrap2">
          <div class="actionsWrap">
            <div class="actions">
              <button class="close" @click="closeSign" >关闭</button>
              <button class="danger" @click="handleClear2" >清除</button>
              <button class="warning" @click="handleUndo2" >撤销</button>
              <button class="success" @click="handlePreview2" >提交</button>
            </div>
          </div>
          <canvas class="canvas"  id="canvas2"/>
        </div>
      </div>
      
    
        <el-table :data="gridData" header-cell-style="background-color: #f5f7fa; color: #0F111A;" stripe border>
          <el-table-column property="describe" label="存在问题" width="200"></el-table-column>
          <el-table-column property="advice" label="指导意见" width="200"></el-table-column>
        </el-table>

      
    
      <join-form ref="formRef" v-show="false"></join-form>
      
    </div>
    <script src="{{staticRootPath}}/plugins/axios/0.19.0-beta.1/axios.min.js"></script>
    <script src="{{staticRootPath}}/plugins/vue/2.6.10/vue.min.js"></script>
    <script src="{{staticRootPath}}/plugins/Agora_Web_SDK_FULL/AgoraRTC_N-4.21.0.js"></script>
    <script src="{{staticRootPath}}/plugins/html2canvas/html2canvas.min.js"></script>
    <script src="{{staticRootPath}}/plugins/socket/socket.io.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/smooth-signature/dist/index.umd.min.js"></script>
    <!-- <script src="https://www.jsdelivr.com/package/npm/pdfjs-dist"></script> -->
    <script>
     
      Vue.component('join-form', {
        template: `
                <el-row :gutter="16">
                    <el-col :span="6">
                        <div class="title">APP ID</div>
                        <el-input type="text" placeholder="Enter the appid" v-model="appId"></el-input>
                        <div class="footer">You find your APP ID in the <a href="https://console.agora.io/projects">Agora Console</a></div>
                    </el-col>
                    <el-col :span="6">
                        <div class="title">Token(optional)</div>
                        <el-input type="text" placeholder="Enter the app token" v-model="token"></el-input>
                        <div class="footer">To create a temporary token, <a href="https://console.agora.io/projects">edit your project</a> in Agora Console.</div>
                    </el-col>
                    <el-col :span="6">
                        <div class="title">Channel Name</div>
                        <el-input type="text" placeholder="Enter the channel name" v-model="channel"></el-input>
                        <div class="footer">You create a channel when you create a temporary token. You guessed it, in <a href="https://console.agora.io/projects">Agora Console</a></div>
                    </el-col>
                    <el-col :span="6">
                        <div class="title">User ID(optional)</div>
                        <el-input type="text" placeholder="Enter the user ID (number)" v-model="uid" :formatter="(value) => \`\${value}\`" :parser="(value) => value.replace(/[^0-9]/g, '')"></el-input>
                    </el-col>
                </el-row>
            `,
        data() {
          return {
            appId: '',
            token: '',
            channel: '',
            uid: '',
          };
        },
        methods: {
          getValue() {
            return {
              appId: this.appId,
              token: this.token,
              channel: this.channel,
              uid: this.uid ? Number(this.uid) : null,
            };
          },
          setValue(data) {
            if (data.appId) {
              this.appId = data.appId;
            }
            if (data.token) {
              this.token = data.token;
            }
            if (data.channel) {
              this.channel = data.channel;
            }
            if (data.uid) {
              this.uid = data.uid;
            }
          },
        },
      });

      Vue.component('agora-video-player', {
        template: `
                <div :style="style">
                    <div ref="videoRef" :style="{ width, height }"></div>
                </div>
            `,
        props: {
          videoTrack: {
            type: Object,
            default: null,
          },
          audioTrack: {
            type: Object,
            default: null,
          },
          config: {
            type: Object,
            default: () => ({
              mirror: false,
            })
          },
          isLocal: {
            type: Boolean,
            default: false,
          },
          text: {
            type: [String, Number],
            default: '',
          },
          width: {
            type: String,
            default: '100%',
          },
          height: {
            type: String,
            default: '26rem',
          },
          style: {
            type: Object,
            default: () => ({}),
          },
        },
        data() {
          return {
            videoRef: null,
          };
        },
        mounted() {
          this.videoRef = this.$refs.videoRef;
          if (this.videoTrack) {
            this.videoTrack.play(this.videoRef, this.config);
          }
          if (!this.isLocal && this.audioTrack) {
            this.audioTrack.play();
          }

          this.$watch('videoTrack', (track) => {
            if (track && this.videoRef) {
              track.play(this.videoRef);
            }
          });

          this.$watch('audioTrack', (track) => {
            if (!this.isLocal && track) {
              track.play();
            }
          });
        },
        beforeDestroy() {
          if (this.videoTrack) {
            this.videoTrack.close();
          }
          if (this.audioTrack) {
            this.audioTrack.close();
          }
        },
        methods: {
          handleClick() {
            this.$emit('click');
          },
        },
      });

      new Vue({
        el: '#app',
        data: {
          isSigning: false,
          signature2:"",
          client: AgoraRTC.createClient({
            mode: 'rtc',
            codec: 'vp8',
          }),
          joined: false,
          remoteUsers: {},
          videoTrack: null,
          audioTrack: null,
          recorder: null,
          chunks: [],
          appId: '',
          channel: '',
          token: '',
          uid: null,
          facingMode: 'user',
          source: '',
          filePath:"",
          isPreview:false,
          pdfSrc:"",
          gridData:[],
          dialogTableVisible: false,
          isChange: true,
        },
        created() {
          const data = JSON.parse('{{data | safe}}');
          const { channel} = data;
          if (window.EventSource) {
              this.source = new EventSource(`/sseCloud/${channel}`);
              this.source.onerror = (e) => {//onerror事件中捕获当前连接结束的状态
                  if (e.readyState == EventSource.CLOSED) {
                      console.log("SSE连接关闭");
                  } else if (this.source.readyState == EventSource.CONNECTING) {//当sse完成一个连接后将会继续连接，此时在这里阻止连接
                      console.log("SSE正在重连");
                      this.source.close();
                      console.log('关闭成功')
                  } else {
                      console.log('error', e);
                  }
              };
              this.source.addEventListener('open', function (e) {
                console.log('建立连接。。。')
              })
              this.source.addEventListener('message', (e)=> {//监听message，收到消息
                const messageData = JSON.parse(e.data);
                this.filePath = messageData.message
                if(messageData.message) {
                  this.$message({
                    type:'success',
                    message:"收到监管人员发送的指导意见书",
                    customClass: 'custom-message'
                  })
                }

                this.isChange = !this.isChange
              })
              this.source.addEventListener('myData', function (e) {//监听message，收到消息
                  console.log('收到消息', e.data)
              })
            } else {
                console.log('你的浏览器不支持SSE')
            }

            axios({
                url:'/api/getCloudSuperInfo',
                params:{
                  _id: channel
                },
                method:'get'
            }).then(res=>{
             
                this.gridData = res.data.data.advice;
                if('instructions' in res.data.data && res.data.data.instructions) {
                  this.filePath = res.data.data.instructions
                }
            })
        },
        watch: {
          // isChange(val) {
         
          //   axios({
          //       url:'/manage/enterpriseServe/getCloudSuperInfo',
          //       params:{
          //         _id: this.channel
          //       },
          //       method:'get'
          //   }).then(res=>{
             
          //       this.gridData = res.data.data.advice;
          //       if('instructions' in res.data.data && res.data.data.instructions) {
          //         this.filePath = res.data.data.instructions
          //       }
          //   })
          // }
          isChange: {
            handler(val) {
                  axios({
                    url:'/api/getCloudSuperInfo',
                    params:{
                      _id: this.channel
                    },
                    method:'get'
                }).then(res=>{
                
                    this.gridData = res.data.data.advice;
                    if('instructions' in res.data.data && res.data.data.instructions) {
                      this.filePath = res.data.data.instructions
                    }
                })
            },
            immediate: true
          }
        },
        mounted() {
       
          this.initSignture2();
          const data = JSON.parse('{{data | safe}}');
          const { appId, channel, token, uid } = data;
          this.appId = appId;
          this.channel = channel;
          this.token = token;
          this.uid = uid;
          this.$refs.formRef.setValue(data);
          window.addEventListener('beforeunload', this.handleBeforeUnload);
        },
        beforeDestroy() {
          if (this.source) {
            this.source.close();
          }
          window.removeEventListener('beforeunload', this.handleBeforeUnload);
          if (this.joined) {
            this.$message.warning('请先离开频道!');
            this.leave();
          }
        },
        methods: {
          
          download() {
            if(!this.filePath) {
              this.$message.warning('请等待监管人员发送指导意见书')
              return
            }
            let oA = document.createElement("a");
            oA.download = ''; // 设置下载的文件名，默认是'下载'
            oA.href = this.filePath + '?response-content-type=application/octet-stream';
            oA.download = '指导意见书'; // 设置文件名
            document.body.appendChild(oA);
            oA.click();
            oA.remove(); // 下载之后把创建的元素删除
          },
          handleBeforeUnload(event) {
            if (this.joined) {
              const message = '你确定要离开吗？请确保已经离开频道。';
              event.returnValue = message;
              return message;
            }
          },
          changeCamera() {
            if (this.facingMode === 'user') {
              this.facingMode = 'environment';
            } else {
              this.facingMode = 'user';
            }
            this.videoTrack.setDevice({
              facingMode: this.facingMode,
            });
          },
          async initTracks() {
            if (this.audioTrack && this.videoTrack) {
              return;
            }
            const tracks = await Promise.all([
              AgoraRTC.createMicrophoneAudioTrack(),
              AgoraRTC.createCameraVideoTrack({
                encoderConfig: '1080p_2',
              }),
            ]);
            this.audioTrack = tracks[0];
            this.videoTrack = tracks[1];
          },
          async handleUserPublished(user, mediaType) {
            await this.client.subscribe(user, mediaType);
            this.$set(this.remoteUsers, user.uid, user);
          },
          handleUserUnpublished(user, mediaType) {
            if (mediaType === 'video') {
              this.$delete(this.remoteUsers, user.uid);
            }
          },
          joinVideo() {
            this.join(this.appId, this.channel, this.token, this.uid);
          },
          async join(appId, channel, token, uid) {
            this.client.on('user-published', this.handleUserPublished);
            this.client.on('user-unpublished', this.handleUserUnpublished);

            const options = this.$refs.formRef.getValue();
            // Join a channel
            options.uid = await this.client.join(
              options.appId,
              options.channel,
              options.token || null,
              options.uid || null
            );
            await this.initTracks();
            await this.client.publish([this.videoTrack, this.audioTrack]);
            this.joined = true;
          },
          async leave() {
            if (this.videoTrack) {
              this.videoTrack.close();
              this.videoTrack = null;
            }
            if (this.audioTrack) {
              this.audioTrack.close();
              this.audioTrack = null;
            }
            this.remoteUsers = {};
            await this.client.leave();
            this.joined = false;
            this.$message.success('已离开频道!');
          },
          takeScreenshot() {
            const imageData = this.videoTrack.getCurrentFrameData();
            const canvas = document.createElement('canvas');
            canvas.width = imageData.width;
            canvas.height = imageData.height;
            const ctx = canvas.getContext('2d');
            if (!ctx) return;
            ctx.putImageData(imageData, 0, 0);
            const dataURL = canvas.toDataURL();
            const link = document.createElement('a');
            link.download = 'capture.png';
            link.href = dataURL;
            document.body.appendChild(link);
            link.click();
            link.remove();
            canvas.remove();
          },
          startRecording() {
            const videoElement =
              this.$refs.localVideoPlayer.$refs.videoRef.querySelector('video');
            if (!videoElement) {
              console.error('Video element not found');
              return;
            }
            const stream = videoElement.srcObject;
            if (!stream) {
              console.error('No stream found in video element');
              return;
            }
            this.chunks = [];
            this.recorder = new MediaRecorder(stream);
            this.$message.success('录屏开始');
            this.recorder.ondataavailable = (e) => this.chunks.push(e.data);
            this.recorder.onstop = () => {
              const blob = new Blob(this.chunks, { type: 'video/mp4' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'recording.mp4';
              a.click();
            };
            this.recorder.start();
          },
          stopRecording() {
            this.$message.success('录屏结束');
            this.recorder.stop();
          },

          goSign() {
            if(!this.filePath) {
              this.$message.warning('请等待监管人员发送指导意见书')
              return
            }
        
            this.isSigning = true
           
            
           
          },
          closeSign() {
            this.signature2.clear();
            this.isSigning = false
          },
          initSignture2() {
            // const canvas = this.$refs["canvas2"];
            const canvas = document.getElementById('canvas2')
            const options = {
              width: window.innerWidth - 120,
              height: window.innerHeight - 80,
              minWidth: 2,
              maxWidth: 3,
              openSmooth:true,
              // color: "#1890ff",
              // bgColor: '#f6f6f6',
            };
            this.signature2 = new SmoothSignature(canvas, options);
          },
      
        handleClear2() {
          this.signature2.clear();
        },
    
        handleUndo2() {
          this.signature2.undo();
        },
        
        handlePreview2() {
          const isEmpty = this.signature2.isEmpty();
          if (isEmpty) {
            this.$message.warning('请签名')
            return;
          }

          const now = new Date();
          // 获取当前年份
          const year = now.getFullYear();
          // 获取当前月份（0-11，所以需要加1）
          const month = now.getMonth() + 1;
          // 获取当前日期（1-31）
          const day = now.getDate();
          const canvas = this.signature2.getRotateCanvas(-90);
          const pngUrl = canvas.toDataURL();

        
          // 创建一个包含Base64数据的对象
          const dataToSend = {
            base64: pngUrl,
            _id: this.channel,
            year: String(year),
            month: month.toString().padStart(2, '0'),
            day: day.toString().padStart(2, '0')
          };
          // 发送POST请求到服务器
      
          axios.post('/api/cloudSupervisionSign', dataToSend)
            .then(response => {
              console.log('Success:', response.data.data.instructions);
              this.filePath = response.data.data.instructions
            })
            .catch(error => {
              console.error('Error:', error);
            });

           this.isSigning = false
           this.signature2.clear();
           this.$message.success('已提交签名')
        },
        },
      });
    </script>
  </body>
</html>
