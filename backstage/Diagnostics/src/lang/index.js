import Vue from 'vue';
import VueI18n from 'vue-i18n';
import Cookies from 'js-cookie';
import {
  baseEn,
  baseZhTW,
  baseZhCN,
} from '@root/publicMethods/baseLang';
import elementEnLocale from 'element-ui/lib/locale/lang/en'; // element-ui lang
import elementZhCNLocale from 'element-ui/lib/locale/lang/zh-CN'; // element-ui lang
import elementZHTWLocale from 'element-ui/lib/locale/lang/zh-TW'; // element-ui lang
import enLocale from './en';
import zhCNLocale from './zh-CN';
import zhTWLocale from './zh-TW';

Vue.use(VueI18n);

const messages = {
  en: {
    ...baseEn,
    ...enLocale,
    ...elementEnLocale,
  },
  zh: {
    ...baseZhCN,
    ...zhCNLocale,
    ...elementZhCNLocale,
  },
  zhtw: {
    ...baseZhTW,
    ...zhTWLocale,
    ...elementZHTWLocale,
  },
};

const language = Cookies.get('locale');
let locale = 'zh';
const isFirstDefault = true;

if (!isFirstDefault) {
  switch (language) {
    case 'zh-CN':
    case 'zh-cn':
      locale = 'zh';
      break;
    case 'en-US':
    case 'en-us':
      locale = 'en';
      break;
    case 'zh-TW':
    case 'zh-tw':
      locale = 'zhtw';
      break;
    default:
      locale = 'zh';
  }
}

const i18n = new VueI18n({
  // set locale
  // options: en or zh
  // locale: Cookies.get('language') || 'zh',
  locale,
  // set locale messages
  messages,
});

export default i18n;
