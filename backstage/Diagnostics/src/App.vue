<template>
  <div id="adminUser-app" class="Diagnostics">
    <div :class="classObj">
      <div class="main-container">
        <router-view />
      </div>
    </div>
  </div>
</template>
<script>
import { initEvent } from "@root/publicMethods/events";

export default {
  data() {
    return {
      sidebarOpened:false,
    }
  },
  mounted() {
    initEvent(this);
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  components: {}
};
</script>
<style lang="scss">
.Diagnostics .el-dialog{
  margin-top: 6vh!important;
}
.Diagnostics .el-table .cell{
  padding-left: 4px;
  padding-right: 4px;
}
.xxn{
  .keyWordsSearch{ // 关键字搜索
    .el-input__inner{
      background: #F2F7FD!important;
    }
    .el-input__icon{
      color: #2A91FC!important;
    }
  }
  .searchYear{ // 搜索年份
    .el-date-editor.el-input, .el-date-editor.el-input__inner{
      width: 57px;
    }
    .el-input--small .el-input__inner{
      border-color: #fff;
      color: #0063E0;
      font-weight: bold;
      font-size: 16px;
      letter-spacing: 1px;
      line-height: 33px;
      padding: 0;
    }
    .el-input--small .el-input__icon{
      display: none;
    }
  }
}
</style>
