<template>
  <div class="adminUser">
    <div>
      <GiveBackDetails v-if="givebackProject.show" :dialogTableVisible="givebackProject.show" :projectDetail="givebackProject.data" @success="successGiveback" @close="givebackProject.show = false"/>

      <el-row class="dr-datatable">
        <el-col :span="24">
          <TopBar type="adminUser" :searchParams="searchParams" :totleCount="totleCount" :statisticsData="statistics" @reset="reset" @downLoad="downLoadAll" :sidebarOpened="sidebarOpened" @openGiveBackRecord="openGiveBackRecord"></TopBar>
          <DataTable :tableData="lists" :searchParams="searchParams" :regAdd="regAdd" @openGiveBackDetails="openGiveBackDetails"></DataTable>
          <Pagination :searchParams="searchParams" v-show="lists.length > 0" :totleCount="statistics.totleCount"></Pagination>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import UserForm from "./userForm";
import DataTable from "./dataTable.vue";
import TopBar from "../common/TopBar.vue";
import Pagination from "../common/Pagination.vue";
import { mapGetters } from "vuex";
import { DiagnosticstList } from "@/api/adminUser";
import moment from "moment";
import GiveBackDetails from './giveBackDetails'

export default {
  name: "index",
  data() {
    return {
      searchParams: {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: '',
        org: '', // 机构 id
        dates: [], // 按日期帅选
        year: new Date().getFullYear() + '',
        category: [], // 职业病类别
      },
      originSearchParams: {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: '',
        org: '', // 机构 id
        dates: [], // 按日期帅选
        year: '',
        category: [], // 职业病类别
      },
      totleCount: 0,
      statistics: {},
      loading: false,
      lists: [],
      givebackProject: {
        show: false, // 是否打开退回窗口
        data: {}
      }, // 退回窗口
      regAdd: [], // 当前单位的管辖区域
    };
  },
  computed: {
    ...mapGetters(["DiagnosticstList"]),
  },
  watch: {
    searchParams: {
      deep:true,
      handler:function(newV,oldV){
        this.getLits();
      }
    },
  },
  created() {
    const query = this.$route.query;
    console.log(7777777777, query);
    if(query.year){
      this.searchParams.year = query.year;
    }else{
      this.getLits();
    }
  },
  methods: {
    // 获取列表
    getLits(){
      // console.log('获取项目列表', this.searchParams);
      if(this.loading) return;
      this.loading = true;
      
      const reg = /^(.{6})(?:\d+)(.{4})$/    // 匹配身份证号前6位和后4位的正则表达式  
      const reg2 = /^(\d{3})(\d+)(\d{4})$/    // 匹配手机号前3位和后4位的正则表达式
      DiagnosticstList(this.searchParams).then(res=>{
        if(res.status == 200){
          this.lists = res.data.list;
          this.statistics = res.data.statistics || {};
          this.regAdd = res.data.regAdd.includes('杭州市') ? ['浙江省'] : res.data.regAdd; // 此处因为数据原因所以做了特殊处理
          // console.log(8888888, this.lists);
        }
        this.loading = false;
      })
    },
    // 点击重置
    reset(){
      this.searchParams = JSON.parse(JSON.stringify(this.originSearchParams));
    },
    // 点击下载/导出 下载所有数据
    downLoadAll(){
      DiagnosticstList({
        ...this.searchParams,
        limit: 100000
      }).then(res => {
        const jsonData = res.data.list || [];
        // 列标题，逗号隔开，每一个逗号就是隔开一个单元格
        let str = `序号,姓名,职业病名,性别,年龄,身份证号,联系方式,所在单位,区域,单位联系人,联系方式,接害工龄,诊断机构,诊断时间,报告出具时间\n`;
        // 增加\t为了不让表格显示科学计数法或者其他格式
        for(let i = 0 ; i < jsonData.length ; i++ ){
          const item = jsonData[i];
          const dminOrgClient = item.adminOrgClient && item.adminOrgClient.length ? item.adminOrgClient[0] : {};
          const workAddress = item.workAddress || dminOrgClient.workAddress || [];
          str += `${(i+1) + '\t'},`;
          str += `${item.name + '\t'},`;
          str += `${item.diseaseName ? item.diseaseName.join(' ') : '' + '\t'},`;
          str += `${item.sex == "0" ? "女" : "男" + '\t'},`;
          str += `${item.birthday || '' + '\t'},`;
          str += `${(item.IDNum || '' + ' ') + '\t'},`;
          str += `${(item.phoneNum || '' + ' ') + '\t'},`;
          str += `${item.cname || '' + '\t'},`;
          str += `${workAddress ? (workAddress.map(ele => ele.districts.join('/')).join('；')):'' + '\t'},`;
          str += `${item.enterpriseContactsName ? item.enterpriseContactsName : dminOrgClient.contract || '' + '\t'},`;
          str += `${(item.enterpriseContactsPhonNumber ? item.enterpriseContactsPhonNumber : dminOrgClient.phoneNum || '' )+' ' + '\t'},`;
          str += `${item.age||'' + '\t'},`;
          str += `${item.physicalExaminationOrg && item.physicalExaminationOrg.length ? (item.physicalExaminationOrg[0].name || '') : item.hospital || '' + '\t'},`;
          str += `${item.decideDate?moment(item.decideDate).format("YYYY-MM-DD"):'' || '' + '\t'},`;
          str += `${item.reportTime?moment(item.reportTime).format("YYYY-MM-DD"):'' || '' + '\t'},`;
          str += '\n';
        }
        // encodeURIComponent解决中文乱码
        const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(str);
        // 通过创建a标签实现
        const link = document.createElement("a");
        link.href = uri;
        // 对下载的文件命名
        link.download =  "职业病诊断.csv";
        link.click();
      })
    },
    // 打开退回窗口
    openGiveBackDetails(data) {
      if (data._id) {
        this.givebackProject = {
          show: true,
          data: {
            _id: data._id,
            companyName: data.companyName,
            serviceName: data.serviceName,
            name: data.name,
            diseaseName: data.diseaseName,
          }
        }
      }
    },
    successGiveback() {
      this.givebackProject.show = false
      this.getLits()
    },
    openGiveBackRecord() {
      this.$router.push('/admin/Diagnostics/givebackRecord')
    }
  },
  components: {
    DataTable,
    TopBar,
    UserForm,
    Pagination,
    GiveBackDetails
  },
};
</script>

<style lang="scss" scoped>
</style>
