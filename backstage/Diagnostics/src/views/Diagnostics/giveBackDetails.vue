<template>
  <el-dialog title="退回详情" :visible.sync="dialogTableVisible" @close="close" :close-on-click-modal="false" top="5vh">
    <div>
      <p>用人单位: {{ projectDetail.companyName }}</p>
      <p>检测机构: {{ projectDetail.serviceName }}</p>
      <p>姓名: {{ projectDetail.name }}</p>
      <p>职业病: {{ projectDetail.diseaseName.join('-') }}</p>
    </div>
    
    <p>退回备注：</p>
    <div style="border: 1px solid #ccc;">
        <Editor
          style="height: 300px; overflow-y: hidden;"
          :defaultConfig="editorConfig"
          v-model="content"
          mode="default"
          @onCreated="onCreated"
      />
    </div>
    <div class="bottom">
      <p>退回内容将即时通知机构</p>
      <el-button type="primary" @click="submit">发 送</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { Editor } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'
import { giveBack, giveBackDelImgs } from "@/api/giveBack";

export default {
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: false
    },
    projectDetail: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  components: { Editor },
  data() {
    const selt = this;
    return {
      content: '',
      editor: null,
      editorConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: {
          uploadImage: {
            server: '/manage/projectGiveBack/uploadImage',
            fieldName: 'your-fileName',
          },
          insertImage: {
            onInsertedImage: selt.onInsertedImage,
          }
        }
      },
      insertImageList: []
    }
  },
  methods: {
    onInsertedImage(imageNode) {
      this.insertImageList.push(imageNode.src)
    },
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    async submit() {
      const content = this.editor.getHtml();
      if (!content || content === '<p><br></p>') {
        this.$message('请输入退回原因')
        return;
      }

      const images = this.editor.getElemsByType('image').map(item => {
        return item.src
      })
      // 对比最后提交的图片和插入的图片
      const delImageList = this.insertImageList.filter(item => !images.includes(item))

      this.$confirm('是否退回该项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await giveBack({
          type: 'diagnostics',
          projectID: this.projectDetail._id,
          remark: content,
          delImageList,
        })

        if (res.status === 200) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.$emit('success')
        }
      }).catch(() => {});
      
    },
    async close() {
      const images = this.editor.getElemsByType('image').map(item => {
        return item.src
      })
      const delImageList = this.insertImageList.filter(item => !images.includes(item))
      const allImgList = Array.from(new Set([ ...images, ...delImageList ]))
      if (allImgList && allImgList.length > 0) {
        const res = await giveBackDelImgs({
          delImageList: allImgList,
        })
      }
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
  .bottom {
    text-align: right;
  }
</style>