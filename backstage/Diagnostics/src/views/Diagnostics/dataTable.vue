<template>
<div>
  <el-table :data="tableData" style="width: 100%" stripe :height="tableHeight" ref="table">
    <el-table-column width="40" label="序号" type="index" fixed align="center">
        <template scope="scope"> {{ searchParams.limit * (searchParams.curPage - 1) + scope.$index + 1 }} </template>
      </el-table-column>
    <el-table-column prop="name" label="姓名" width="50" align="center" fixed> 
      <template scope="scope">
          <span @click="handleDecryptField(scope.row._id, 'name', scope.row.nameForStore, scope.row.name, scope.$index)" class ="myBtn">{{ scope.row.name}}</span>
        </template>
    </el-table-column>
    <el-table-column
      prop="diseaseName"
      label="职业病名"
      align="center" width="230"
    >
      <template slot-scope="scope">
        {{ scope.row.diseaseName ? scope.row.diseaseName.join(' ') : ''}}
      </template>
    </el-table-column>
    <el-table-column prop="sex" label="性别" width="40" align="center">
      <template slot-scope="scope">
        {{ scope.row.sex == "0" ? "女" : "男" }}
      </template>
    </el-table-column>
    <el-table-column
      prop="birthday"
      label="年龄"
      width="60" align="center"
      sortable 
      :sort-method="sortByBirthday"
    >
    </el-table-column>

    <el-table-column prop="IDNum" label="身份证号" width="150" align="center">
      <template scope="scope">
          <span @click="handleDecryptField(scope.row._id, 'IDNum', scope.row.IDNumForStore, scope.row.IDNum, scope.$index)" class ="myBtn">{{ scope.row.IDNum}}</span>
        </template>
    </el-table-column>

    <el-table-column
      prop="phoneNum"
      label="联系方式"
      width="110"
      align="center"
    >
      <template scope="scope">
          <span @click="handleDecryptField(scope.row._id, 'phoneNum', scope.row.phoneNumForStore, scope.row.phoneNum, scope.$index)" class ="myBtn">{{ scope.row.phoneNum}}</span>
        </template>
    </el-table-column>

    <el-table-column prop="cname" label="所在单位" width="200" show-overflow-tooltip>
      <template slot-scope="scope">
        {{ scope.row.adminOrgClient.length ? scope.row.adminOrgClient[0].cname || '' : scope.row.cname}}
      </template>
    </el-table-column>
    <el-table-column label="区域" align="center" width="220">
      <template slot-scope="scope">
        <span v-if="scope.row.adminOrgClient.length===0"></span>
        <template v-else>
          <p v-for="(addrArr,i) in (scope.row.workAddress || scope.row.adminOrgClient[0].workAddress)" :key="i">
            <span v-for="name in addrArr.districts" :key="name">{{regAdd.includes(name)?'':name}} </span>
          </p>
        </template>
        <!-- {{ scope.row.adminOrgClient.length ? scope.row.adminOrgClient[0].workAddress.map(ele => ele.districts.join('/')).join('；') : ''}} -->
      </template>
    </el-table-column>
    <el-table-column label="单位联系人" align="center" width="80">
      <template slot-scope="scope">
        {{ scope.row.enterpriseContactsName ? scope.row.enterpriseContactsName : (scope.row.adminOrgClient[0] ? scope.row.adminOrgClient[0].contract || '' : '')}}
      </template>
    </el-table-column>
    <el-table-column label="联系方式" align="center" width="110">
      <template slot-scope="scope">
        {{ scope.row.enterpriseContactsPhonNumber ? scope.row.enterpriseContactsPhonNumber : (scope.row.adminOrgClient.length ? scope.row.adminOrgClient[0].phoneNum || '' : '')}}
      </template>
    </el-table-column>
    <el-table-column prop="age" label="接害工龄" width="95" align="center"
     :sort-method="sortByAge"
       sortable>
    </el-table-column>
    <el-table-column
      label="诊断机构" width="190" show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.physicalExaminationOrg && scope.row.physicalExaminationOrg.length ? (scope.row.physicalExaminationOrg[0].name || '') : scope.row.hospital }}
      </template>
    </el-table-column>
    <el-table-column
      prop="decideDate"
      label="诊断时间"
      width="80" 
    >
      <template slot-scope="scope">
        {{ scope.row.decideDate?doMomentyear(scope.row.decideDate):'' }}
      </template>
    </el-table-column>

    <el-table-column prop="reportTime" label="报告出具时间" width="100" align="center">
      <template slot-scope="scope">
        {{scope.row.reportTime?doMomentyear(scope.row.reportTime):''}}
      </template>
    </el-table-column>
    <el-table-column label="诊断证明书" width="120" align="center" fixed="right">
      <template slot-scope="scope">
        <span v-if="!scope.row.certificates">未上传</span>
        <template v-else>
          <el-image v-for="file in scope.row.certificates" :key="file" style="height: 28px" src="/static/images/certificate.jpg" fit="contain" @click="handlePreview(file)"></el-image>
        </template>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="80px" fixed="right" align="center">
      <template slot-scope="scope">
        <el-button @click.native.stop="openGiveBackDetails(scope.row)" size="mini" type="warning">退回</el-button>
      </template>
    </el-table-column>
  </el-table>
  <!-- 图片预览 -->
  <el-dialog :visible.sync="dialogVisible" title="诊断证明书">
    <el-button type="primary" plain size="mini" @click="handleDownload(dialogImageUrl)">下载</el-button>
    <img width="100%" :src="dialogImageUrl" :preview-src-list="previewList"/>
  </el-dialog>
  <!-- 预览word文件的组件（用于渲染） -->
  <preview :filePath="dialogPreviewUrl" :preview="showPreview" @fatherMethod="showPreview=false"></preview>
</div>
</template>

<script>
import _ from "lodash";
import moment from "moment";
import preview from "../../../../publicMethods/components/preview";
import {checkPhoneLog,checkIDNumLog, decryptField} from "@/api/adminUser"
export default {
  props: {
    tableData: Array,
    searchParams: Object,
    regAdd: Array, // 当前单位的管辖区域
  },
  components: {
    preview,
  },
  data() {
    return {
      dialogImageUrl: "",
      dialogVisible: false,
      previewList: [],
      showPreview: false,
      dialogPreviewUrl: '',
    };
  },
  methods: {
    handleDecryptField(_id, dataType, combinedText, originalText, index) {
      if(!combinedText) return;
      if(!originalText || !originalText.includes('*')) return;
      decryptField({combinedText, _id: _id, field: dataType, modelName: 'Odisease'}).then(res => {
        if(res && res.status === 200 && res.data.result){
          this.$set(this.tableData[index], dataType, res.data.result);
        }else{
          this.$message({
            type: 'info',
            message: res.message || '解密失败'
          });
        }
      })
    },
    // 查看身份证号
    async showIDNum(row){
      if(row.IDNum){
        row.IDNum = row.IDNum2;
        await checkIDNumLog({model:'Odisease',IDNum:row.IDNum})
      }
    },
    // 查看手机号
   async showPhone(row){
    if(row.phoneNum){
      row.phoneNum = row.phoneNum2
      await checkPhoneLog({model:'Odisease',phoneNum:row.phoneNum2})
    }
    },
    // 预览文件
    handlePreview(file) {
      let fileSplite = file.split(".");
      const fileType = fileSplite[fileSplite.length - 1];
      if (fileType === "pdf") {
        let a = document.createElement("a");
        a.href = file;
        a.target = "_blank";
        a.click();
      } else if ( fileType === "doc" || fileType === "docx") {
        this.dialogVisible = false;
        // window.open('https://view.officeapps.live.com/op/view.aspx?src='+ window.location.origin + file);
        this.dialogPreviewUrl = file;
        this.showPreview = true;
      } else {
        this.dialogImageUrl = file;
        this.previewList = [file];
        this.dialogVisible = true;
      }
    },
    // 下载文件
    handleDownload(file) {
      console.log('下载', file)
      const a = document.createElement("a");
      let event = new MouseEvent("click");
      a.href = file;
      a.download = '诊断证明书';
      a.dispatchEvent(event); // a.click()火狐浏览器不触发
    },
    sortByBirthday(row, row1) {
      if (row.birthday === 0) {
        return -1;
      }
      if (row.birthday < row1.birthday) {
        return -1;
      } else {
        return 1;
      }
    },
    sortByAge: (row, row1) => parseInt(row.age) - parseInt(row1.age),
    openGiveBackDetails(row){
      this.$emit('openGiveBackDetails', { 
        _id: row._id,
        companyName: row.cname,
        serviceName: row.hospital,
        name: row.name,
        diseaseName: row.diseaseName,
      })
    },
  },
  computed: {
    //将时间 转换成 简化的
    doMomentyear(nowTime) {
      return function (nowTime) {
        return moment(nowTime).format("YYYY-MM-DD");
      };
    },
    tableHeight(){
      const innerHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      // console.log(*********, this.$refs.table);
      return innerHeight - 380;
    },
  },
};
</script>
<style scoped>
  iframe img{
    max-height: 100%;
    max-width: 100%;
  }
  .el-table{
    border: 1px solid #ECEEF5;
  }
  .myBtn{
  cursor: pointer;
}
</style>