<template>
  <div class="pageBody">
    <el-page-header @back="$router.go(-1)" content="退回项目历史记录"></el-page-header><br/>
    <el-row class="search">
      <el-input style="width: 200px" size="small" placeholder="关键字搜索" v-model="searchParams.keyword" suffix-icon="el-icon-search" @keyup.enter.native="getData"></el-input>
    </el-row><br/>

    <el-table
      :max-height="tableMaxHeight"
      :data="tableData"
      style="width: 100%">
      <el-table-column width="50" label="序号" type="index" fixed align="center">
        <template scope="scope"> {{ searchParams.pageSize * (searchParams.curPage - 1) + scope.$index + 1 }} </template>
      </el-table-column>
    <el-table-column prop="name" label="姓名" width="50" align="center" fixed> </el-table-column>
    <el-table-column
      prop="diseaseName"
      label="职业病名"
      align="center" width="230"
    >
      <template slot-scope="scope">
        {{ scope.row.diseaseName ? scope.row.diseaseName.join(' ') : ''}}
      </template>
    </el-table-column>
    <el-table-column prop="sex" label="性别" width="40" align="center">
      <template slot-scope="scope">
        {{ scope.row.sex == "0" ? "女" : "男" }}
      </template>
    </el-table-column>
    <el-table-column
      prop="birthday"
      label="年龄"
      width="60" align="center"
    >
    </el-table-column>

    <el-table-column prop="IDNum" label="身份证号" width="150" align="center">
    </el-table-column>

    <el-table-column
      prop="phoneNum"
      label="联系方式"
      width="110"
      align="center"
    >
    </el-table-column>

    <el-table-column prop="cname" label="所在单位" width="200" show-overflow-tooltip>
      <template slot-scope="scope">
        {{ scope.row.adminOrgClient.length ? scope.row.adminOrgClient[0].cname || '' : scope.row.cname}}
      </template>
    </el-table-column>
    <el-table-column label="单位联系人" align="center" width="80">
      <template slot-scope="scope">
        {{ scope.row.adminOrgClient.length ? scope.row.adminOrgClient[0].contract || '' : ''}}
      </template>
    </el-table-column>
    <el-table-column label="联系方式" align="center" width="110">
      <template slot-scope="scope">
        {{ scope.row.adminOrgClient.length ? scope.row.adminOrgClient[0].phoneNum || '' : ''}}
      </template>
    </el-table-column>
    <el-table-column prop="age" label="接害工龄" width="95" align="center"></el-table-column>
    <el-table-column
      label="诊断机构" width="190" show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.physicalExaminationOrg && scope.row.physicalExaminationOrg.length ? (scope.row.physicalExaminationOrg[0].name || '') : scope.row.hospital }}
      </template>
    </el-table-column>
    <el-table-column
      prop="decideDate"
      label="诊断时间"
      width="80" 
    >
      <template slot-scope="scope">
        {{ formatTime(scope.row.decideDate) }}
      </template>
    </el-table-column>

    <el-table-column prop="reportTime" label="报告出具时间" width="100" align="center">
      <template slot-scope="scope">
        {{ formatTime(scope.row.reportTime) }}
      </template>
    </el-table-column>
    <el-table-column label="诊断证明书" width="120" align="center" fixed="right">
      <template slot-scope="scope">
        <span v-if="!scope.row.certificates">未上传</span>
        <template v-else>
          <el-image v-for="file in scope.row.certificates" :key="file" style="height: 28px" src="/static/images/certificate.jpg" fit="contain" @click="handlePreview(file)"></el-image>
        </template>
      </template>
    </el-table-column>
     <el-table-column label="退回时间" width="100px" prop="giveBackTime" fixed="right">
        <template slot-scope="scope">
          {{ formatTime(scope.row.giveBackTime) }}
        </template>
      </el-table-column>
      <el-table-column label="退回备注" width="100px" prop="giveBackRemark" fixed="right">
        <template slot-scope="scope">
          <el-popover
            placement="bottom-start"
            title="退回备注："
            width="400"
            trigger="hover">
            <div class="RenderDv">
              <div v-html="scope.row.giveBackRemark"></div>
              <div class="tip" @click="lookUpRemark(scope.row.giveBackRemark)">点击查看完整备注</div>
            </div>

            <el-link slot="reference">查看详情</el-link>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        background
        @size-change="sizeChange"
        @current-change="getData"
        :page-sizes="[ 10, 30, 50 ]"
        :current-page.sync="searchParams.curPage"
        :page-size="searchParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchParams.allSize">
      </el-pagination>
    </div>

    <el-dialog title="退回备注" :visible.sync="showGiveBackRemark" :close-on-click-modal="false">
      <div class="remark_details">
        <div v-html="giveBackRemarcontent"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showGiveBackRemark = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { giveBackRecord } from "@/api/giveBack";
import { mapGetters } from 'vuex';
import moment from 'moment'

export default {
  data() {
    return {
      searchParams: {
        keyword: '',
        curPage: 1,
        pageSize: 10,
        allSize: 0,
        source: 'diagnostics'
      },
      tableData: [],
      showGiveBackRemark: false,
      giveBackRemarcontent: '',
    }
  },
  computed: {
    ...mapGetters(['serviceAreaOptions']),
    tableMaxHeight() {
      return window.innerHeight - 280
    }
  },
  async created() {
    await this.getData()
  },
  methods: {
    lookUpRemark(content) {
      this.giveBackRemarcontent = ''
      this.showGiveBackRemark = true
      this.giveBackRemarcontent = content
    },
    formatTime(time) {
      if (!time) return '-'
      return moment(time).format('YYYY-MM-DD')
    },
    getServiceArea(serviceAreas){
      const serviceAreas2 = []
      this.serviceAreaOptions.forEach(item=>{
        serviceAreas.forEach(item2=>{
          if(item.value === item2){
            serviceAreas2.push(item.label)
          }
        })
      })
      return serviceAreas2.join('；')
    },
    async getData() {
      const res = await giveBackRecord(this.searchParams)
      console.log(22, res)
      if (res && res.status === 200) {
        this.searchParams.allSize = res.data.allSize
        this.tableData = res.data.doc
      }
      console.log(1111111, this.tableData)
    },
    async sizeChange(size) {
      this.searchParams.pageSize = size
      await this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
  .pageBody {
    padding: 16px;
  }
  .pagination {
    padding: 20px;
    text-align: center;
  }
  .RenderDv {
  overflow: hidden;
  height: 150px;
  position: relative;

  .tip {
    position: absolute;
    background: rgba(0,0,0,0.4);
    bottom: 0;
    width: 100%;
    text-align: center;
    color: white;
    padding: 4px 0;
    cursor: pointer;
  }
  img {
    width: 100%;
  }
}
.remark_details {
  height: 400px;
  overflow: scroll;

}
</style>