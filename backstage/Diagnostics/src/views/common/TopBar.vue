<template>
  <div class="dr-toolbar xxn">
    <!-- 左边统计区域 -->
    <div class="statisticsWrap">
      <div class="searchYear">
        <span v-if="searchParams.dates && searchParams.dates.length" class="searchDates">
          {{doMomentTime(searchParams.dates[0])}} 至 {{doMomentTime(searchParams.dates[1])}}
        </span>
        <el-date-picker v-else v-model="searchParams.year" type="year" placeholder="选择年" size="small" format="yyyy年" :clearable="false" value-format="yyyy"></el-date-picker>
        统计
      </div>
      <Statistics :categoryList="categoryList" :statisticsData="statisticsData" v-if="statisticsData && categoryList" :searchParams="searchParams"/>
    </div>
    <!-- 右边搜索区域 -->
    <div class="searchWrap">
      <el-row class="searchRow1">
        <el-col>
          <el-input v-model="keyWords" class="keyWordsSearch" size="small" suffix-icon="el-icon-search" placeholder="用人单位搜索"></el-input>
        </el-col>
        <el-col class="smallScreen">
          <el-date-picker v-model="searchParams.dates" type="daterange" range-separator="至" start-placeholder="诊断开始日期" end-placeholder="诊断结束日期" size="small" style="width:100%" :picker-options="pickerOptions"> </el-date-picker>
        </el-col>
        <el-col class="operate">
          <el-button plain size="small" @click="$emit('downLoad')">导出</el-button>
          <el-button type="danger" plain size="small" @click="$emit('reset')">重置</el-button>
          <el-button plain size="small" @click="$emit('openGiveBackRecord')">退回记录</el-button>
        </el-col>
      </el-row>
      <el-row class="bigScreen">
        <el-col :span="24">
          <el-date-picker v-model="searchParams.dates" type="daterange" range-separator="至" start-placeholder="诊断开始日期" end-placeholder="诊断结束日期" size="small" style="width:100%" :picker-options="pickerOptions"> </el-date-picker>
        </el-col>
      </el-row>
      <el-row class="bigScreen">
        <el-col :span="24">
          <el-cascader style="width: 100%" :props="districtListProps" @change="regaddChangeOptionFunc"  v-model="searchParams.regAddr" clearable ref="regAddCas" size="small"  :placeholder="regAddrPlaceholder ? regAddrPlaceholder : '请选择地区'" >
            <template slot-scope="{ data }">
              <span>{{ data.label }}</span>
            </template>
          </el-cascader>
        </el-col>
      </el-row>
      <el-row class="searchRow2">
        <el-col class="smallScreen">
          <el-cascader style="width: 100%" :props="districtListProps" @change="regaddChangeOptionFunc"  v-model="searchParams.regAddr" clearable ref="regAddCas" size="small"  :placeholder="regAddrPlaceholder ? regAddrPlaceholder : '请选择地区'" >
            <template slot-scope="{ data }">
              <span>{{ data.label }}</span>
            </template>
          </el-cascader>
        </el-col>
        <el-col>
          <el-select v-model="searchParams.category" placeholder="请选择职业病类别" size="small" clearable style="width: 100%" :multiple="true">
            <el-option :value="item" :label="item" v-for="item in categoryList" :key="item"></el-option>
          </el-select>
        </el-col>
        <el-col>
          <el-select v-model="searchParams.org" placeholder="请选择/输入机构" size="small" clearable style="width: 100%" filterable>
            <el-option value="" label="" >全部机构</el-option>
            <el-option :value="item._id" :label="item.name" v-for="item in orgList" :key="item._id"></el-option>
          </el-select>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { getDistrictList, serviceOrgs, diseaseCategory } from "@/api/adminUser";
import Statistics from "@/components/Statistics.vue";
import moment from 'moment';
import { pickerOptions } from '@root/publicMethods/timePicker';

export default {
  props: {
    searchParams: Object,
    totleCount: Number,
    statisticsData: Object,
    sidebarOpened: Boolean,
  },
  data() {
    return {
      pickerOptions: pickerOptions,
      regAddrPlaceholder: '', // 地址
      orgList: [], 
      visible:true,
      keyWords: '', //关键字
      industryOptions: [], // 可选行业
      district: [],
      categoryList: [], // 职业病类别
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.data.area_code;
          }
          getDistrictList(params).then((response) => {
            try {
              const districts = Array.from(response.data.docs);
              let nodes = districts.map((item) => ({
                value: item.name,
                label: item.name,
                area_code: item.area_code,
                leaf: item.level >= 3,
                disabled: item.name === '市辖区' ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
    };
  },
  computed: {
    //将时间 转换成 简化的
    doMomentTime() {
      return function (nowTime) {
        return moment(nowTime).format("YYYY-MM-DD");
      };
    },
  },
  watch: {
    'searchParams.dates': {
      handler(dates) {
        this.searchParams.year = new Date(dates[0]).getFullYear() + '';
      },
      deep: true
    },
    'searchParams.org': {
      handler(orgId) {
        if(orgId){
          console.log(this.orgList, '体检机构列表');
          let org = this.orgList.filter(ele => ele._id == orgId);
            if(org.length){
              this.searchParams.orgCode = org[0]._id;
            }
           
        }else{
          this.searchParams.orgCode = '';
        }
      },
      deep: true
    },
    keyWords(newVal, oldVal){
      if(newVal == oldVal) return;
      setTimeout( ()=> {
        this.searchParams.keyWords = this.keyWords;
      }, 1000)
    }
  },
  created() {
    this.getOrgLits();
    this.keyWords = this.searchParams.keyWords;
    diseaseCategory().then(res => {
      this.categoryList = res.data.map(ele => ele.category).concat(['无']);
    });
  },
  methods: {
     // 获取机构列表
    getOrgLits(){
      serviceOrgs({limit: 1000}).then(res=>{
        if(res.status == 200){
          this.orgList = res.data.filter(item => item.qualifies && item.qualifies.some(ele => ele.type === '1' && ele.files.length));
          // 判断是否已有指定机构
          let hash = location.hash.replace('#', '');
          if(hash){ // 从机构列表跳转过来的
            this.searchParams.org = hash;
            this.searchParams.year = '';
          }
        }
      })
    },
    // 选择地区
    regaddChangeOptionFunc(v) {
      // console.log(v)
      console.log(this.searchParams.regAddr, '前端传的啥');
      this.searchParams.regAddr = v || [];
      this.$refs.regAddCas.dropDownVisible = false;
    },
  },
  components: {
    Statistics
  },
};
</script>
<style scoped lang="scss">
.dr-toolbar{
  min-height: 209px;
  margin-bottom: 16px;
  display: flex;
  .statisticsWrap{ // 左边
    flex: 2;
    padding: 10px 15px 15px;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.06);
    border: 1px solid #ECEEF5;
    border-radius: 2px;
    .searchYear{
      color: #424242;
      letter-spacing: 1px;
      font-size: 16px;
      font-weight: bold;
    }
    .searchDates{
      color: #0063E0;
      line-height: 33px;
    }
  }
  .searchWrap{ // 右边
    flex: 1;
    border: 1px solid #ECEEF5;
    padding: 18px 2px 0 16px;
    margin-left: 16px;
    .smallScreen{
      display: none!important;;
    }
    .searchRow1{
      display: flex;
      .el-col {
        flex: 1;
      }
    }
    .searchRow2{
      // display: flex;
      .el-col {
        display: inline-block;
        width: 50%;
        box-sizing: border-box;
      }
    }
    .operate{
      text-align: right;
    }
    .el-row{
      .el-col{
        padding-right: 14px;
        margin-bottom: 14px;
      }
    }
  }
} 
@media screen and (max-width: 1440px) { // 示屏幕尺寸大于等于1440px时
  .dr-toolbar{
    display: block;
    .searchWrap{ // 右边
      .smallScreen{
        display: inline-block!important;
        flex: 2!important;
      }
      .bigScreen{
        display: none;
      }
    }
    .searchRow2{
      .el-col {
        width: 33.33%!important;
      }
    }
  }
}
</style>
