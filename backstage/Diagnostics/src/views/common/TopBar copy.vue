<template>
  <div class="dr-toolbar">
    <div class="option-button el-col-12 searchStyle">
      <el-date-picker
        v-model="searchTime"
            size="small"
            type="daterange"
            range-separator="-"
            start-placeholder="诊断开始日期"
            end-placeholder="诊断结束日期"
            @change="timesChange"
      >
      </el-date-picker>
      <el-input
        size="small"
        placeholder="可通过 [姓名] | [公司名] | [手机号] | [疾病]搜索"
         @keyup.enter.native="search($event)"
        v-model="searchQuery"
        clearable
      >
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="search"
        ></el-button>
      </el-input>
    </div>
    <div class="el-col-18">&nbsp;</div>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  props: {
    device: String,
    pageInfo: Object,
    type: String,
    ids: Array,
  },
  data() {
    return {
      // selectUserList: [],
      // searchkey: "",
      searchQuery: '',
      searchTime: "",
    };
  },
  created() {
    const { year, area_code } = this.$route.query;
    if (year) {
      this.searchTime = [ moment(year), moment(year).add('y', 1) ]
    }

    if(location.search){
      const searchArr = location.search.replace('?', '').split('&');
      if(searchArr.length){
        const dates = [];
        searchArr.forEach(item => {
          switch(item.split('=')[0]){
            case 'gt': dates[0] = item.split('=')[1]; break;
            case 'lt': dates[1] = item.split('=')[1]; break;
          }
        });
        if(dates[0]) this.searchTime = [new Date(dates[0]), new Date(dates[1])];
        this.$store.dispatch("adminUser/DiagnosticstList",{TimeQuery: this.searchTime});
      }
    }
  },
  methods: {
    search(event) {
      const searchkey = event.target.value
      this.$store.dispatch("adminUser/DiagnosticstList",{searchkey});
    },
    // TOPBARLEFTOPTION
    timesChange(){
      const TimeQuery = this.searchTime;
      console.log(333333333, TimeQuery);
      this.$store.dispatch("adminUser/DiagnosticstList",{TimeQuery})
    }

  },
  components: {},
};
</script>
<style lang="scss">
.searchStyle {
  position: absolute;
  right: 0px;
  display: flex;
  justify-content: space-around;
}
</style>
