import request from '@root/publicMethods/request';


export function DiagnosticstList(data) {
  return request({
    url: '/manage/diagnostics/diagnosticstList',
    method: 'post',
    data,
  });
}
// 获取职业病所有类别
export function diseaseCategory(params) {
  return request({
    url: '/manage/diagnostics/diseaseCategory',
    method: 'get',
    params,
  });
}
// 获取管辖区获取机构列表
export function serviceOrgs(data) {
  return request({
    url: '/manage/physicalExaminationOrg/getAllOrg',
    data,
    method: 'post',
  });
}
export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}
export function getIndustry(data) {
  return request({
    url: '/manage/adminServiceProject/getIndustry',
    data,
    method: 'get',
  });
}


// 查看手机号记录到日志中
export function checkPhoneLog(params) {
  return request({
    url: '/manage/superUser/checkPhoneLog',
    params,
    method: 'get',
  });
}
// 查看手机号记录到日志中
export function checkIDNumLog(params) {
  return request({
    url: '/manage/superUser/checkIDNumLog',
    params,
    method: 'get',
  });
}

export function decryptField(params) {
  return request({
    url: '/manage/decryptField',
    method: 'get',
    params,
  });
}
