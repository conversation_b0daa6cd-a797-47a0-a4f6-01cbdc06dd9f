import * as types from '../types.js';
import {
  DiagnosticstList,
} from '@/api/adminUser';
// import _ from 'lodash';

const state = {
  // list: [],
  DiagnosticstList: {
    list: [],
    pageInfo: {
      current: 1,
      pageSize: 1,
      totalItems: 0,
    },
  },
};

const mutations = {
  [types.ADIAGNOSTICSLIST](state, result) {
    state.DiagnosticstList = result;
  },
};

const actions = {


  DiagnosticstList({
    commit,
  }, params = {}) {
    DiagnosticstList(params).then(result => {
      commit(types.ADIAGNOSTICSLIST, result.data);
      console.log(result.data, 'ddddddddddddddddd');
    });
  },


};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
