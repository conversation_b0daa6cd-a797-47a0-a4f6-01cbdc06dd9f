<template>
  <div class="statistics">
    <div class="item">
      诊断人数
      <h2>{{statisticsData.totleCount || ''}}</h2>
    </div>
    <div class="item">
      职业病人数
      <h2>{{statisticsData.diseaseNum || ''}}</h2>
    </div>
    <div class="echart">
      <div id="main9"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  props: {
    statisticsData: Object,
    // categoryList: Array
    searchParams: Object,
  },
  data() {
    return {
      myChart: null,
    }
  },
  watch: {
    statisticsData(statisticsData){
      if(statisticsData && statisticsData.categoryList) this.initEchart();
    },
    // categoryList(categoryList){
    //   console.log(22222222222, categoryList);
    //   if(this.statisticsData && this.statisticsData.categoryList) this.initEchart();
    // }
  },
  mounted(){
    this.myChart = echarts.init(document.getElementById('main9'));
    this.myChart.on('click', params => {
      if(params.name){
        this.searchParams.category = [ params.name ];
      }
    });
  },
  methods: {
    initEchart(){
      const data = Object.keys(this.statisticsData.categoryList).map(ele => {
        return { 
          name: ele, 
          shortName: ele.replace('职业性', '').replace('腔疾病','').replace('职业病','').replace('所致', '').replace('及其他呼吸系统疾病', ''),
          value: this.statisticsData.categoryList[ele] || 0,
        }
      }).sort((a,b) => b.value - a.value);
      // console.log(1111111111, data);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: (item) => {
            return `${item.seriesName}<br />${item.data.name}：${item.value}人`
          }
        },
        color: ['#5AD8A6'],
        grid: {
          top: 30,
          left: 36,
          right: 20,
          bottom: 20,
          // containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: data.map(ele => ele.shortName),
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {  //x轴文字的配置
              show: true,
              interval: 0,//使x轴文字显示全
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '人数',
            // nameLocation: 'center',
            minInterval: 1, // 坐标轴最小间隔大小
          }
        ],
        series: [
          {
            name: '人数统计',
            type: 'bar',
            barWidth: '50%',
            data,
          }
        ]
      };
      option && this.myChart.setOption(option);
    }
  },
};
</script>
<style lang="scss" scoped>
.statistics{
  display: flex;
  height: 145px;
  .item{
    flex: 2;
    min-width: 100px;
    margin-right: 12px;
    color: #fff;
    border-radius: 3px;
    background: url('./../assets/bj.png') no-repeat;
    background-size: cover;
    padding: 27px 0 0 16px;
    font-size: 14px;
    h2{
      font-size: 28px;
      font-family: SourceHanSansCN-Bold, SourceHanSansCN;
      letter-spacing: 1px;
      margin-top: 22px;
    }
  }
  .echart{ // 图表区
    flex: 5;
    #main9{
      width: 100%;
      height: 100%;
    }
  }
  
}
@media screen and (max-width: 1440px) { // 示屏幕尺寸大于等于1440px时
  .statistics{
    .echart{
      flex: 6;
    }
  }
}

</style>
