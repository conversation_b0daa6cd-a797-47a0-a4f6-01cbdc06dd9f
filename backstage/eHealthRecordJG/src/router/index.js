import Vue from 'vue';
import Router from 'vue-router';
import settings from '@root/publicMethods/settings';

import recordManagement from '@/views/recordManagement';
import eHealthRecordDetail from '@/views/recordManagement/detail.vue';
import complaintList from '@/views/complaintList';
import authRequestList from '@/views/authRequestList';
import complaintHandle from '@/views/complaintHandle';
import recordSearch from '@/views/recordSearch';
import recordStatistic from '@/views/recordStatistic';
import dataQuery from '@/views/dataQuery';
import sensitiveDataApproval from '@/views/sensitiveDataApproval';

Vue.use(Router);

const createRouter = () =>
  new Router({
    mode: 'history',
    base: process.env.BASE_URL,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: [
      {
        path: settings.admin_base_path + '/eHealthRecordJG',
        name: 'eHealthRecord',
        component: recordManagement,
      },
      { // 档案列表
        path: settings.admin_base_path + '/eHealthRecordJG/recordManagement',
        name: 'recordManagement',
        component: recordManagement,
      },
      { // 档案详情
        path: settings.admin_base_path + '/eHealthRecordJG/recordManagement/detail',
        name: 'eHealthRecordDetail',
        component: eHealthRecordDetail,
      },
      { // 企业申请授权列表
        path: settings.admin_base_path + '/eHealthRecordJG/authRequestList',
        name: 'authRequestList',
        component: authRequestList,
      },
      { // 企业申诉列表
        path: settings.admin_base_path + '/eHealthRecordJG/complaintList',
        name: 'complaintList',
        component: complaintList,
      },
      { // 员工申诉处理
        path: settings.admin_base_path + '/eHealthRecordJG/complaintHandle',
        name: 'complaintHandle',
        component: complaintHandle,
      },
      { // 历史电子健康档案查阅
        path: settings.admin_base_path + '/eHealthRecordJG/recordSearch',
        name: 'recordSearch',
        component: recordSearch,
      },
      { // 档案统计
        path: settings.admin_base_path + '/eHealthRecordJG/recordStatistic',
        name: 'recordStatistic',
        component: recordStatistic,
      },
      { // 电子健康档案各类对接数据查询
        path: settings.admin_base_path + '/eHealthRecordJG/dataQuery',
        name: 'dataQuery',
        component: dataQuery,
      },
      {
        // 敏感数据上报审批
        path: settings.admin_base_path + '/eHealthRecordJG/sensitiveDataApproval',
        name: 'sensitiveDataApproval',
        component: sensitiveDataApproval,
      },
    ],


  });

const router = createRouter();

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
