<template>
  <div class="padding" style="height:100%">
    <TitleTag :titleName="'查询条件'"> </TitleTag>
    <div class="eHealthRecord">
      <el-row>
        <el-col :span="24">
          <div class="first-line">
            <!-- <div class="search-item">
              <span class="label-title">时间点</span>
              <el-date-picker v-model="searchData.timePoint" type="date" placeholder="选择日期" @change="timePointChange">
              </el-date-picker>
            </div>

            <div class="search-item">
              <span class="label-title">时间段</span>
              <el-date-picker v-model="searchData.daterange" type="daterange" placeholder="选择日期" range-separator="至"
                start-placeholder="起始日期" end-placeholder="结束日期" @change="timeRangeChange">
              </el-date-picker>
            </div> -->

            <div class="search-item">
              <span class="label-title">姓名</span>
              <el-input v-model="searchData.name" placeholder="请输入姓名"></el-input>
            </div>
            <div class="search-item">
              <span class="label-title">联系方式</span>
              <el-input v-model="searchData.phoneNum" placeholder="请输入联系方式"></el-input>
            </div>

            <div class="search-item">
              <el-button type="primary" size="mini" style="margin-left: 10px;" @click="search" icon="el-icon-search"
                plain>查询</el-button>
              <el-button type="warning" size="mini" @click="reset" icon="el-icon-refresh-right" plain>重置</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <TitleTag :titleName="'劳动者列表'"> </TitleTag>
    <div class="eHealthRecord">
      <div class="table">
        <el-table :data="tableData" stripe border tooltip-effect="dark"
          :header-cell-style="{ background: '#F5F7FA', fontSize: '14px', fontWeight: 700, color: '#333' }">
          <el-table-column type="index" :index="indexMethod" label="序号" align="center" width="60"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="gender" label="性别" align="center" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ getGender(scope.row.gender) }}
            </template>
          </el-table-column>
          <el-table-column prop="age" label="年龄" align="center" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="phoneNum" label="联系方式" align="center" width="120"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="IDNum" label="证件号" align="center" min-width="150" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="EnterpriseID.cname" label="用人单位" align="center" min-width="150"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="EnterpriseID.districtRegAdd" label="用人单位所在区域" align="center" min-width="150"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tooltip v-if="scope.row.EnterpriseID.districtRegAdd && scope.row.EnterpriseID.districtRegAdd.length"
                placement="top" :content="scope.row.EnterpriseID.districtRegAdd.join('/')">
                <span>{{ scope.row.EnterpriseID.districtRegAdd[scope.row.EnterpriseID.districtRegAdd.length - 1]
                  }}</span>
              </el-tooltip>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" plain @click="geDetail(scope.row)">详情</el-button>
              <!-- <el-button type="warning" size="mini" plain @click="openDetail(scope.row)">申请授权</el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination @size-change="getData" @current-change="getData" :current-page.sync="pageInfo.pageNum"
          :page-size.sync="pageInfo.pageSize" :page-sizes="[10, 20, 30, 50, 100]" background
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getEmployeeList } from '@/api'

export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      isShowDetail: false,
      recordFilingData: null,
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      searchData: {
        // timePoint: '',
        // daterange: [],
        name: '',
        phoneNum: '',
        // address: ''
      }
    }
  },

  created() {
    this.getData()
  },

  methods: {

    async getData() {
      const res = await getEmployeeList({
        ...this.pageInfo,
        ...this.searchData
      })
      this.tableData = res.data.list
      this.total = res.data.total
    },

    // 时间点与时间段互斥，选择其中一个时，另一个清空
    timePointChange(val) {
      if (val) {
        this.searchData.daterange = []
      }
    },
    timeRangeChange(val) {
      if (val) {
        this.searchData.timePoint = ''
      }
    },
    search() {
      this.pageInfo = this.$options.data()['pageInfo']
      this.getData()
    },
    reset() {
      this.searchData = this.$options.data()['searchData']
      this.pageInfo = this.$options.data()['pageInfo']
      this.getData()
    },
    openDetail(item) {
      this.$confirm(`确认向 ${item.name} 提出申请授权？\n待员工同意后可以查看不在本单位就职时的档案内容`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'success',
          message: '已提交申请!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消申请'
        });
      });

    },
    geDetail(row) {
      this.$router.push({
        name: 'eHealthRecordDetail',
        query: { id: row._id }
      })
    },
    indexMethod(index) {
      return (this.pageInfo.pageNum - 1) * this.pageInfo.pageSize + (index + 1);
    },

    // 监听子组件更新成功事件
    handleUpdateSuccess() {
      // 模拟数据更新
      this.$message.success('数据更新成功');
      this.getData();
    },

    getGender(gender) {
      if (gender == 0) {
        return '男'
      } else if (gender == 1) {
        return '女'
      } else {
        return
      }
    }
  },
  // 监听路由变化
  watch: {
    '$route'(to, from) {
      if (to.name === 'eHealthRecord') {
        this.getData();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.padding {
  padding: 20px;
}



.table {
  margin-top: 15px;
}

.label-title {
  font-size: 14px;
  margin-right: 0.5em;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

::v-deep .el-input {
  width: 250px;
}

::v-deep .el-date-editor.el-input {
  width: 250px;
}

::v-deep .el-date-editor .el-range-input {
  margin-left: 15px;
}

::v-deep .custom-input .el-input__inner {
  border: none;
  height: 24px;
  line-height: 24px;
  text-align: center;
}


.custom-input {
  font-size: 12px;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: inline-block;
  margin-left: 6px;
  margin-right: 6px;
  height: 28px;
  line-height: 28px;
  padding: 0 0;
  box-sizing: border-box;
}

.eHealthRecord {
  position: relative;

  .handleBtn {
    position: absolute;
    right: 0;
    top: -58px;
  }
}

.first-line {
  display: flex;
  flex-wrap: wrap;

  .label-title {
    text-align: right;
    display: inline-block;
    width: 80px;
    font-size: 14px;
    // background-color: red;
  }

  .label-title-uni {
    text-align: right;
    display: inline-block;
    width: 140px;
    font-size: 14px;
  }

  .search-item {
    display: flex;
    align-items: center;
  }
}

::v-deep .i-select .el-input {
  width: 200px;
  margin: 5px 5px;
}

::v-deep .el-tag {
  border-radius: 28px;
}

::v-deep .eHealthRecord .table .el-tag {
  border-radius: 0px;
  text-align: center;
}
</style>