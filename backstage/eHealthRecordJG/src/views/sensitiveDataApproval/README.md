# 数据上报与审批页面

## 页面概述
这是一个用于管理数据上报与审批流程的页面，主要功能包括：
- 查看数据上报列表
- 筛选和搜索上报记录
- 查看审批流程详情
- 跟踪审批状态

## 页面结构

### 1. 查询条件区域
- **劳动者姓名**：支持模糊搜索
- **审批状态**：下拉选择（待审批、审批中、已通过、已拒绝）
- **上报时间**：日期范围选择器
- **查询按钮**：执行搜索
- **重置按钮**：清空搜索条件

### 2. 数据列表区域
包含以下字段：
- **序号**：自动编号
- **劳动者姓名**：上报涉及的劳动者
- **审批状态**：当前审批状态（带颜色标签）
- **上报理由**：详细的上报原因说明
- **当前审批节点**：显示当前处于哪个审批环节
- **上报时间**：数据上报的时间
- **上报单位**：提交上报的企业名称
- **操作**：查看上报流程按钮

### 3. 审批流程弹窗
点击"查看上报流程"按钮后显示：
- **流程概览**：步骤条显示整个审批流程
- **审批历史**：详细的审批记录表格
  - 审批节点
  - 审批人
  - 审批时间
  - 审批结果
  - 审批意见

## 技术实现

### 组件依赖
- `TitleTag`：标题组件
- Element UI 组件：表格、分页、弹窗、表单等

### API 接口
- `getDataReportApprovalList`：获取上报审批列表
- `getApprovalFlowDetail`：获取审批流程详情

### 状态管理
- `tableData`：列表数据
- `pageInfo`：分页信息
- `searchData`：搜索条件
- `currentFlowData`：当前查看的流程数据

## 样式特点
- 参考 `recordManagement/index.vue` 的布局和样式
- 响应式设计，支持不同屏幕尺寸
- 统一的色彩方案和交互效果
- 清晰的信息层次结构

## 使用说明

### 开发环境
1. 确保已安装项目依赖
2. 将页面文件放置在 `src/views/dataReportApproval/` 目录下
3. 在路由配置中添加对应路由
4. 根据实际后端接口调整 API 调用

### 生产环境
1. 替换模拟数据为真实 API 调用
2. 根据实际业务需求调整字段显示
3. 配置相应的权限控制
4. 添加错误处理和加载状态

## 自定义配置

### 修改搜索条件
在 `searchData` 中添加或修改字段，并在模板中添加对应的表单控件。

### 调整表格列
在 `el-table-column` 中修改列配置，包括宽度、对齐方式、显示内容等。

### 自定义审批状态
在 `getApprovalStatusType` 和 `getApprovalStatusText` 方法中修改状态映射。

## 注意事项
1. 当前使用模拟数据，实际部署时需要连接真实后端接口
2. 审批流程的步骤和节点需要根据实际业务流程调整
3. 权限控制需要根据用户角色进行配置
4. 建议添加数据导出功能以便于数据分析
