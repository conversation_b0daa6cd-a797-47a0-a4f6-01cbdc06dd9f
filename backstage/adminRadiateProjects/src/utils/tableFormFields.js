export const descriptionFields = {
  radProtDetectInWrkplc: [
    {
      label: '检测编号',
      key: 'detectionNumber',
      span: 1,
      require: true,
    },
    {
      label: '受检场所',
      key: 'inspectedLocation',
      span: 1,
      require: true,
    },
    {
      label: '设备名称',
      key: 'deviceName',
      span: 1,
      require: true,
    },
    {
      label: '设备型号',
      key: 'deviceModel',
      span: 1,
      require: true,
    },
    {
      label: '设备编号',
      key: 'equipmentNumber',
      require: true,
    },
    {
      label: '设备额定容量',
      key: 'ratedCapacity',
      span: 1,
    },
    {
      label: '射线装置类别',
      key: 'categoryOfRadiation',
      span: 1,
      require: true,
      inputType: 'select',
      options: [
        {
          label: 'Ⅰ类射线装置',
          value: 'Ⅰ类射线装置',
        }, {
          label: 'Ⅱ类射线装置',
          value: 'Ⅱ类射线装置',
        }, {
          label: 'Ⅲ类射线装置',
          value: 'Ⅲ类射线装置',
        },
      ],
    },

    {
      label: '出厂编号',
      key: 'factoryNo',
      span: 1,
    },

    {
      label: '警示标识',
      key: 'warningSigns',
      span: 1,
      inputType: 'select',
      options: [
        {
          label: '有',
          value: true,
        }, {
          label: '无',
          value: false,
        },
      ],
      type: 'Boolean',
    },
    {
      label: '工作指示灯',
      key: 'workLight',
      span: 1,
      inputType: 'select',
      options: [
        {
          label: '有',
          value: true,
        }, {
          label: '无',
          value: false,
        },
      ],
      type: 'Boolean',
    },
    {
      label: '警示线',
      key: 'warningLine',
      span: 1,
      inputType: 'select',
      options: [
        {
          label: '有',
          value: true,
        }, {
          label: '无',
          value: false,
        },
      ],
      type: 'Boolean',
    },
    {
      label: '机房是否杂物',
      key: 'clutterInServerRoom',
      span: 1,
      inputType: 'select',
      options: [
        {
          label: '有',
          value: true,
        }, {
          label: '无',
          value: false,
        },
      ],
      type: 'Boolean',
    },
    {
      label: '危害告示',
      key: 'hazardWarningSign',
      span: 1,
      inputType: 'select',
      options: [
        {
          label: '有',
          value: true,
        }, {
          label: '无',
          value: false,
        },
      ],
      type: 'Boolean',
    },
    {
      label: '个人防护用品及提示语',
      key: 'protectionPrompt',
      span: 1,
      inputType: 'select',
      options: [
        {
          label: '有',
          value: true,
        }, {
          label: '无',
          value: false,
        },
      ],
      type: 'Boolean',
    },
    {
      label: '年工作量',
      key: 'annualWorkload',
      span: 1,
    },
    {
      label: '防护规程上墙',
      key: 'protProcOnWall',
      span: 1,
      inputType: 'select',
      options: [
        {
          label: '有',
          value: true,
        }, {
          label: '无',
          value: false,
        },
      ],
      type: 'Boolean',
    },
    {
      label: '生产单位',
      key: 'productionUnit',
      span: 2,
    },
    {
      label: '机房内通风',
      key: 'serverRoomVentilation',
      span: 1,
    },
    {
      label: '电缆线设置',
      key: 'cableInstallation',
      span: 2,
    },
    {
      label: '机房使用面积',
      key: 'roomUsageArea',
      span: 1,
    },
    {
      label: '最小单边长m',
      key: 'minSideLen',
      span: 1,
    },
  ],
  eqptQualCtrlDetect: [
    {
      label: '检测编号',
      key: 'detectionNumber',
      require: true,
    },
    {
      label: '设备所在场地',
      key: 'inspectedLocation',
      require: true,
    },
    {
      label: '设备名称',
      key: 'deviceName',
      span: 1,
      require: true,
    },
    {
      label: '设备型号',
      key: 'deviceModel',
      span: 1,
      require: true,
    },
    {
      label: '设备编号',
      key: 'equipmentNumber',
      require: true,
    },
    {
      label: '设备额定容量',
      key: 'ratedCapacity',
      require: true,
    },
    {
      label: '生产单位',
      key: 'productionUnit',
      require: true,
    },
  ],
};

export const radProtDetectInWrkplc = [
  {
    label: '检测点位置',
    key: 'location',
    require: true,
  },
  {
    label: '检测结果（μSv/h）',
    key: 'detectionValue',
    require: true,
  },
  {
    label: '限值',
    key: 'limitVal',
    require: true,
  },
  {
    label: '结果判定',
    key: 'result',
    inputType: 'select',
    require: true,
    options: [
      {
        label: '合格',
        value: '合格',
      }, {
        label: '不合格',
        value: '不合格',
      }, {
        label: '-',
        value: '-',
      },
    ],
  },
];

export const eqptQualCtrlDetect = [
  {
    label: '检测项目',
    key: 'detectionProject',
    require: true,
  },
  {
    label: '检测条件',
    key: 'detectionConditions',
    require: true,
  },
  {
    label: '检测结果（μSv/h）',
    key: 'detectionValue',
    require: true,
  },
  {
    label: '标准要求',
    key: 'limitVal',
    require: true,
  },
  {
    label: '单项判定',
    key: 'result',
    inputType: 'select',
    require: true,
    options: [
      {
        label: '合格',
        value: '合格',
      }, {
        label: '不合格',
        value: '不合格',
      }, {
        label: '-',
        value: '-',
      },
    ],
  },
];

export const personalDoseMonitoring = [
  {
    label: '样品编号',
    key: 'sampleSN',
    require: true,
  },
  {
    label: '姓名',
    key: 'name',
    require: true,
  },
  {
    label: '性别',
    key: 'gender',
    inputType: 'select',
    require: true,
    options: [
      { value: '男', label: '男' },
      { value: '女', label: '女' },
    ],
  },
  {
    label: '职业类别',
    key: 'occupationalCategory',
  },
  {
    label: '辐射品质',
    key: 'radiationQuality',
    require: true,
  },
  {
    label: '检测结果',
    key: 'result',
    inputType: 'select',
    require: true,
    options: [
      {
        label: '合格',
        value: '合格',
      }, {
        label: '不合格',
        value: '不合格',
      }, {
        label: '-',
        value: '-',
      },
    ],
  },
];


export default {
  radProtDetectInWrkplc,
  eqptQualCtrlDetect,
  personalDoseMonitoring,
};
