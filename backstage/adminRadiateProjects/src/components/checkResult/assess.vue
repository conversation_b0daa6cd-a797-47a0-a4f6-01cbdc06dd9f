<template>
  <div>
    <el-form v-if="this.isAssess !== -1">
      <el-table :data="tableData" style="width: 100%" border>
        <el-table-column label="排查时间" align="center">
          <template slot-scope='scope'>
            <span>{{doMoment(scope.row.assessDate)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="自查项目" align="center">
          <template slot-scope="scope">
            <div type="primary">{{ scope.row.formData }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="unConformityCount" label="不符合项" align="center">
        </el-table-column>
        <el-table-column prop="reasonableDeficiencyCount" label="合理缺项" align="center">
        </el-table-column>
        <el-table-column prop="mark" label="得分" align="center">
        </el-table-column>
        <el-table-column prop="level" label="等级" align="center">
          <template slot-scope='scope'>
            <!-- <div :style="{color:scope.row.levelColor,fontWeight:'700'}">{{scope.row.level}}</div> -->
            <el-link :underline="false" type="success" v-if="scope.row.level === 'A'" @click="yearPrevent2(scope.row.year, scope.row)">{{scope.row.level}}</el-link>
            <el-link :underline="false" type="warning" v-if="scope.row.level === 'B'" @click="yearPrevent2(scope.row.year, scope.row)">{{scope.row.level}}</el-link>
            <el-link :underline="false" type="danger" v-if="scope.row.level === 'C'" @click="yearPrevent2(scope.row.year, scope.row)">{{scope.row.level}}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="暴露风险" align="center">
          <template slot-scope='scope'>
            <div v-if="scope.row.report">
              <el-link :underline="false" type="success" v-if="scope.row.report.assessExposeLevel === 0">低</el-link>
              <el-link :underline="false" type="warning" v-if="scope.row.report.assessExposeLevel === 1">中</el-link>
              <el-link :underline="false" type="danger" v-if="scope.row.report.assessExposeLevel === 2">高</el-link>
              <div v-else>-</div>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="综合风险" align="center">
          <template slot-scope='scope'>
            <div v-if="scope.row.report">
              <el-link :underline="false" type="success" v-if="scope.row.report.assessmentResult === 0">丙</el-link>
              <el-link :underline="false" type="warning" v-if="scope.row.report.assessmentResult === 1">乙</el-link>
              <el-link :underline="false" type="danger" v-if="scope.row.report.assessmentResult === 2">甲</el-link>
              <div v-else>-</div>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="职业卫生分类" align="center">
          <template slot-scope='scope'>
            <div v-if="scope.row.report">
              <el-link disabled type="success" v-if="scope.row.report.assessmentSort === 1">丙</el-link>
              <el-link disabled type="warning" v-if="scope.row.report.assessmentSort === 2">乙</el-link>
              <el-link disabled type="danger" v-if="scope.row.report.assessmentSort === 3">甲</el-link>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column> -->
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { findAssess} from "@/api/adminorg";
import moment from 'moment';
export default {
  props: {
    id: String,
  },
  data(){
    return{
      tableData:[],
      editItem: {},
      showEdit: {},
      isAssess: 0,
    }
  },
  computed: {
    //将时间 转换成 简化的 
    doMoment(nowTime){
      return function(nowTime){
        return moment(nowTime).format('YYYY-MM-DD');
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
      }
    },
  },
  created(){
      this.findAssess()
  },
  methods:{
    formatStatus(status) {
      if (status === 0) {
        return { color: "#909399", name: "未审核" };
      } else if (!status) {
        return { color: "#909399", name: "未生成" };
      } else if (status === 1) {
        return { color: "#67C23A", name: "审核通过" };
      } else if (status === 2) {
        return { color: "#F56C6C", name: "已打回" };
      }
    },
    yearPrevent2(year, row) {
      this.$router.push({
        name: "yearPrevent",
        query: {
          year: year,
          EnterpriseID:this.id,
        },
      });
    },
    async findAssess(){
      // 获取当前年份
      // let nowyear = new Date();
      // nowyear = nowyear.getFullYear();
      // let res = await findAssess({year: nowyear,EnterpriseID:this.id});
      let res = await findAssess({EnterpriseID:this.id});
      // console.log(res,'resdddddddddddddddddddddddddddddd');
      if (res.status === 200) {
        if (res.data.data.length > 0) {
          const nowTime = moment();
          const monthD = moment(res.data.data[0].assessDate);
          const diffTime = nowTime.diff(monthD, 'months', true);
          if(diffTime <= 12){
            this.isAssess = 1;
          }
          this.tableData = res.data.data;
        }else{
          this.isAssess = -1;
        }
        this.$store.dispatch("adminorg/remind", {
          isOnlineDeclaration: this.$store.getters.remind.isOnlineDeclaration,
          monthD: this.$store.getters.remind.monthD,
          isjobHealth: this.$store.getters.remind.isjobHealth,
          reportTimes: this.$store.getters.remind.reportTimes,
          isratioArr: this.$store.getters.remind.isratioArr,
          checkDate: this.$store.getters.remind.checkDate,
          isReported: this.$store.getters.remind.isReported,
          isAssess: this.isAssess,
        });
      }
    },
  }
}
</script>
