<template>
  <div class="checkResult">
    <div v-if="checkAssessment">
      <el-row>
        <br/>
        <strong class="title">{{category[categoryIndex - 1]}}</strong>
      </el-row>
      <div v-if="this.report.length > 0" class="files">
        <ul>
          职业病危害检测与评价报告书：
          <li v-for="file in this.report" :key="file._id">
            <span class="el-icon-document"></span>
            <el-link :underline="false" @click="showReport(file.url, file.fileName)">{{file.fileName}} </el-link>
            <el-button type="primary" @click.prevent="handlePreview(file.url)" size="small" plain style="margin-left:20px">预览</el-button>
            <el-button type="primary" @click="showReport(file.url, file.fileName)" size="small" style="margin-left:20px">下载</el-button>
          </li>
        </ul>
      </div>
      <el-tabs v-model="activeName" type="border-card" style="margin-top:20px">
        <el-tab-pane v-for="(value, key, index) in checkData" :label="value.name" :name="index" :key="key">
          <component v-bind:is="key" :datas="value.formData" :category="categoryIndex"></component>
        </el-tab-pane>
      </el-tabs>

      <el-row>
        <br/><br/>
        <strong class="title">危害因素检测数据</strong>
      </el-row>
      <div class="statistics">
        <div v-for="(item,index) in statistics" :key="index" class="item">
          <span class="lable">{{item.name}}：</span>
          <span class="txt">{{item.totle}}</span>
          <span class="lable lable2">{{index===0?'总':''}}超标数：</span>
          <span class="txt" :class="item.abnormal?'red':''">{{item.abnormal}}</span>
        </div>
      </div>
    </div>
    <div v-else class="noData">
      <img src="/static/images/icon/noData.png" />
      <p align="center">暂无数据</p>
    </div>
    <!-- 查看报告 -->
    <el-dialog :visible.sync="dialogVisibleurl">
      <img width="100%" :src="dialogImageUrl" />
    </el-dialog>
    <!-- 预览word文件的组件（用于渲染） -->
    <preview :filePath="dialogPreviewUrl" :preview="showPreview" @fatherMethod="showPreview=false"></preview>
  </div>
</template>
<script>
import chemistryFactors from "./chemistryFactors.vue"; // 化学
// import physicFactors from "./physicFactors.vue"; 
import noiseFactors from "./noiseFactors.vue";
import heatFactors from "./heatFactors.vue"; // 高温
import biologicalFactors from "./biologicalFactors.vue"; // 生物
import dustFactors from "./dustFactors.vue"; // 粉尘
import handBorneVibrationFactors from "./handBorneVibrationFactors.vue"; // 手传震动
import highFrequencyEleFactors from "./highFrequencyEleFactors.vue"; // 高频电磁场
import laserFactors from "./laserFactors.vue"; // 激光辐射
import microwaveFactors from "./microwaveFactors.vue"; // 微波辐射
import powerFrequencyElectric from "./powerFrequencyElectric.vue"; // 工频电场
import SiO2Factors from "./SiO2Factors.vue";
import ultraHighRadiationFactors from "./ultraHighRadiationFactors.vue"; // 超高频辐射
import ultravioletFactors from "./ultravioletFactors.vue"; // 紫外辐射;
import ionizatioSourceFactors from "./ionizatioSourceFactors.vue"; // 电离辐射-含源装置
import ionizatioRadialFactors from "./ionizatioRadialFactors.vue"; // 电离辐射-射线装置
import preview from "../../../../publicMethods/components/preview";
// import moment from "moment";
export default {
  props: {
    checkAssessment: Object,
    report: Array, // 职业病危害检测与评价报告书
    statistics: Array, // 危害因素检测数据 统计 点数 超标点数
  },
  data() {
    return {
      category: ['职业病危害因素检测', '职业病危害现状评价', '职业病防护设备设施与防护用品效果评价'],
      categoryIndex: 0,
      checkData: [], // 整理完的检测数据
      activeName: '0',
      dialogImageUrl: '',
      dialogVisibleurl: false,
      dialogPreviewUrl: "",
      showPreview: false,
    }
  },
  watch: {
    report(data){
      console.log('文件：', data)
    },
    checkAssessment(data){
      const checkData = {};
      for(let key in data){
        if(typeof data[key] === 'object' && data[key]){
          if(data[key].formData && data[key].formData.length){
            checkData[key] = data[key];
          }
        }
      }
      this.checkData = checkData;
      this.activeName = '0';
      this.categoryIndex = +data.category;
      console.log(55555555, '检测结果', this.checkData);
      console.log(666666666, this.statistics)
    }
  },
  methods: {
    // 预览
    handlePreview(url){
      const index = url.indexOf('.');
      const fileType = url.slice(index+1);
      const file = url;
      if (fileType === "pdf") {
        let a = document.createElement("a");
        a.href = file;
        a.target = "_blank";
        a.click();
      } else if (fileType === "doc" || fileType === "docx") {
        this.dialogVisible = false;
        // window.open('https://view.officeapps.live.com/op/view.aspx?src='+ window.location.origin + file)
        this.dialogPreviewUrl = file;
        this.showPreview = true;
      } else {
        this.dialogImageUrl = file;
        this.dialogVisibleurl = true;
      }
    },
    // 点击查看按钮,下载
    showReport(url, fileName) {
      // url = '/static/upload/images/auTQ3Rf4aF/warning_files/k-2mDU2IXQ/多谱LOGO源图.png';
      const index = url.indexOf('.');
      const fileType = url.slice(index+1);
      const file = url;
      if(fileType !== 'pdf' && fileType !== 'docx' && fileType !== 'doc'){
        // 图片下载
        let image = new Image();
        image.setAttribute("crossOrigin", "anonymous"); // 解决跨域 Canvas 污染问题
        image.onload = function() {
          let canvas = document.createElement("canvas");
          canvas.width = image.width;
          canvas.height = image.height;
          let context = canvas.getContext("2d");
          context.drawImage(image, 0, 0, image.width, image.height);
          let url2 = canvas.toDataURL("image/png"); //得到图片的base64编码数据
          let a = document.createElement("a"); // 生成一个a元素
          let event = new MouseEvent("click"); // 创建一个单击事件
          a.download = fileName; // 设置图片名称
          a.href = url2; // 将生成的URL设置为a.href属性
          a.dispatchEvent(event); // 触发a的单击事件
        };
        image.src = file; // src = 链接地址
      }else{
        let a = document.createElement("a");
        a.download = fileName;
        a.href = file;
        a.target = "_blank";
        a.click();
      }
    },
  },
  components: {
    // physicFactors,
    noiseFactors,
    heatFactors,
    biologicalFactors,
    dustFactors,
    handBorneVibrationFactors, 
    highFrequencyEleFactors,
    laserFactors,
    microwaveFactors,
    powerFrequencyElectric,
    SiO2Factors,
    ultraHighRadiationFactors,
    ultravioletFactors,
    ionizatioSourceFactors,
    ionizatioRadialFactors,
    chemistryFactors,
    preview
  }   
};
</script>
<style lang="scss" scoped>
.statistics{ // 超标点数
  line-height: 30px;
  width: 100%;
  margin: 20px 5px 20px 0;
  .item{
    display: inline-block;
  }
  .item:first-of-type{
    font-weight: bolder;
  }
  .item:hover{
    background: #f8f8f8;
  }
  .lable{
    color: #666;
    display: inline-block;
    width: 13em;
    padding-left: 20px;
  }
  .txt{
    color: #424242;
    display: inline-block;
    width: 4em;
  }
  .txt.red{
    color: #F56C6C;
    font-weight: bolder;
  }
  .lable.lable2{
    width: 7em;
  }
}
.files{
  margin: 20px 0;
  ul{
    margin: 0;
    padding: 0;
  }
  li{
    list-style: none;
    margin-top: 10px;
    span{
      margin-right: 10px;
    }
  }
}
.title {
  margin-bottom: 10px;
  font-weight: 700;
  // border-left: 3px solid #f56c6c;
  // border-left: 0.275rem solid #25aff3;
  padding: 3px 10px;
  font-size: 15px;
  color: #333;
}
.noData{
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img{
    height: 16%;
  }
  p{
    font-size: 14px;
    margin: 30px 0 0;
    color: #424242;
    a{
      text-decoration: underline;
      margin-left: 1px;
    }
  }
}
</style>