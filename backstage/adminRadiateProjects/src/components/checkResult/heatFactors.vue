<template>
  <div>
    <el-form>
      <el-table
        :data="datas"
        style="width: 100%"
        border size="small"
      >
      <el-table-column align="center" prop="workType" label="工种">
      </el-table-column>
      <el-table-column min-width="150px" align="center" label="检测点位置">
        <el-table-column align="center" label="车间" prop="workspace">
        </el-table-column>
        <el-table-column align="center" label="岗位" prop="station">
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" prop='averageValue' label="WBGT平均值（℃）">
          <template slot='header'>
              <span>WBGT平均值(</span>
              <sup>o</sup>
              <span>C)</span>
          </template>
      </el-table-column>
      <el-table-column prop='touchTime' align="center" label="接触时间率(%)">
      </el-table-column>
      <el-table-column align="center" prop='labourIntensity' label="体力劳动强度">
      </el-table-column>
      <el-table-column align="center" prop='touchLimit' label="职业接触限值（℃）">
          <template slot='header'>
              <span>职业接触限值(</span>
              <sup>o</sup>
              <span>C)</span>
          </template>
      </el-table-column>
      <el-table-column align="center" label="评定结果">
        <template slot-scope="props">
          <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult || ''}}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
        <template slot-scope="props">
          {{doMomentmonthD(props.row.reportTime)}}
        </template>
      </el-table-column> -->
    </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
  },
  data(){
    return{
      tableData:[],
      editItem: {},
      showEdit: {},
    }
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created(){
  },
  methods:{
    
  }
}
</script>
