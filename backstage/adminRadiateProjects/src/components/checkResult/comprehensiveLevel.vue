<template>
  <div>
    <div v-if="this.isReported">
    <div class="reportHeader">
      <div class="title">{{`${new Date().getFullYear()}年度${assessmentReport.cname}落实职业病防治责任自查及风险评估报告`}}</div>
    </div>

    <el-row class="reportBody" type="flex" justify="center">
      <el-card class="box-card" shadow="hover" style="margin-top: 30px; margin-bottom: ">
        <div class="reportCardBody">
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">企业信息</div>
            </el-col>
            <el-col :span="12">
              <span class="labelText">单位名称</span>
              {{ assessmentReport.cname }}
            </el-col>
            <el-col :span="12">
              <span class="labelText">统一社会信用代码（或统一社会信用代码）</span>
              {{ assessmentReport.code }}
            </el-col>
          </el-row>
          <p />
          <el-row>
            <el-col :span="12">
              <span class="labelText">单位注册地址</span>
              {{
                assessmentReport.districtRegAdd &&
                assessmentReport.districtRegAdd.join("-")
              }}
              {{ assessmentReport.regAdd }}
            </el-col>
            <el-col :span="12">
              <span class="labelText">工作场所地址</span>
              <p v-for="(item, i) in assessmentReport.workAddress" :key="i">
                {{ item }}
              </p>
            </el-col>
          </el-row>
          <p />
          <el-row>
            <el-col :span="12">
              <span class="labelText">单位规模</span>
              {{ assessmentReport.companyScale || "" }} 型
            </el-col>
            <el-col :span="12">
              <span class="labelText">行业分类</span>
              <p v-for="(item, i) in assessmentReport.industryCategory" :key="i">
                {{ item }}
              </p>
            </el-col>
          </el-row>
          <p />
          <el-row>
            <el-col :span="12">
              <span class="labelText">上属单位</span>
              {{ assessmentReport.parentCompany }}
            </el-col>
            <el-col :span="12">
              <span class="labelText">注册类型</span>
              {{ assessmentReport.regType || "" }}
            </el-col>
          </el-row>
          <p />
          <el-row>
            <el-col :span="12">
              <span class="labelText">法定代表人</span>

              {{ assessmentReport.corp }}
            </el-col>
            <el-col :span="12">
              <span class="labelText">联系电话</span>
              {{ assessmentReport.corpPhoneNumber }}
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">职业卫生管理</div>
            </el-col>
            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">职业卫生管理机构</span>
              {{ assessmentReport.hasRole ? "有" : "无" }}
            </el-col>
            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">专职</span>
              {{ assessmentReport.majorManegerCount }}
              <!-- {{ assessmentReport.managerCount.zhuanzhi }} 人 -->
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">兼职</span>
              {{ assessmentReport.partTimeManegerCount }}
              <!-- {{ assessmentReport.managerCount.jianzhi }} 人 -->
            </el-col>

            <!-- <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">总人数</span>
              {{ assessmentReport.managerCount.totalCount }} 人
            </el-col> -->
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">职业健康检查人数</div>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">上岗人数</span>
              <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                {{ assessmentReport.healthCheckCount.shangGang }}
              </span>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">在岗人数</span>
              <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                {{ assessmentReport.healthCheckCount.zaiGang }}
              </span>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">离岗人数</span>
              <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                {{ assessmentReport.healthCheckCount.liGang }}
              </span>
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row v-if="assessmentReport.isHealthCheck">
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">职业卫生概况</div>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 6, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">劳动者总人数</span>
              {{ assessmentReport.allEmployeesCount }}
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 6, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">接触职业病危害总人数</span>
              {{ assessmentReport.contactHazardCount }}
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 6, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">职业病累计人数 - 目前在岗</span>
              {{ assessmentReport.ODzaigangCount }}
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 6, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">职业病累计人数 - 历年累计</span>
              {{ assessmentReport.cumulativeTotalCount }}
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <div class="formSubTitle">
                职业病危害因素接触情况
                <span class="smallText">
                  危害因素分类：粉尘、化学物质、物理因素、生物因素、放射性物质
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <!-- <h4>职业病危害因素种类</h4> -->
              <!-- {{ assessmentReport.HazardFactors }} -->
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane
                  v-for="(item, i) in assessmentReport.HazardFactors"
                  :key="i"
                  :name="item.label"
                >
                  <div slot="label">
                    {{ item.label }}
                    <span
                      v-if="!item.harmFactors || item.harmFactors.length === 0"
                      style="color: #f56c6c; font-size: 12px"
                      >（无）</span
                    >
                  </div>
                  <el-table border :data="item.harmFactors">
                    <el-table-column
                      align="center"
                      v-for="(item2, i2) in harmFactorsFields"
                      :key="i2"
                      :label="item2.label"
                      :prop="item2.value"
                    >
                      <template slot-scope="scope">
                        {{ scope.row[item2.value] || 0 }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
              </el-tabs>
            </el-col>
            <!-- <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 8, offset: 0 }"
              :xl="{ span: 8, offset: 0 }"
              v-for="item in assessmentReport.HazardFactors"
              :key="item"
            >
              <el-card
                class="harmCardBody"
                :body-style="{ padding: '0px' }"
                shadow="hover"
              >

                <div class="harmCardContent">
                  <div class="harmTitleBox">
                    <span class="harmCardTitle">{{ item.name }} </span> <br />
                    <el-tag size="mini" class="button"> {{ item.sort }} </el-tag>
                    <el-tag
                      size="mini"
                      type="danger"
                      class="button"
                      v-if="item.isHighHarm"
                    >
                      严重
                    </el-tag>
                  </div>

                  <div class="harmCountBox">
                    <el-col :span="24" class="harmCount">
                      <span class="labelText">接触人数</span>
                      {{ item.tatalCount }}
                    </el-col>
                  </div>
                </div>
              </el-card>
            </el-col> -->
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">
                职业病危害接触水平
                <span class="smallText">
                  危害接触水平分为：一般职业病危害因素；严重职业病危害因素；
                </span>
              </div>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 12, offset: 0 }"
              :lg="{ span: 12, offset: 0 }"
              :xl="{ span: 12, offset: 0 }"
            >
              <el-card
                class="harmCardBody"
                :body-style="{ padding: '0px' }"
                shadow="hover"
              >
                <div class="harmCardContent">
                  <div class="harmTitleBox">
                    <span class="">一般职业病危害因素 </span>
                  </div>

                  <div class="harmCountBox">
                    <el-col :span="12" class="harmCount">
                      <span class="labelText">超标人数</span>
                      <span style="color: #f56c6c">{{
                        assessmentReport.noSeriousHarmExceedEmployees || 0
                      }}</span>
                    </el-col>
                    <el-col :span="12" class="harmCount">
                      <span class="labelText">不超标人数</span>
                      <span>{{
                        assessmentReport.noSeriousHarmNoExceedEmployees || 0
                      }}</span>
                    </el-col>
                  </div>
                </div>
              </el-card>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 12, offset: 0 }"
              :lg="{ span: 12, offset: 0 }"
              :xl="{ span: 12, offset: 0 }"
            >
              <el-card
                class="harmCardBody"
                :body-style="{ padding: '0px' }"
                shadow="hover"
              >
                <div class="harmCardContent">
                  <div class="harmTitleBox">
                    <span class="">严重职业病危害因素 </span>
                  </div>

                  <div class="harmCountBox">
                    <el-col :span="12" class="harmCount">
                      <span class="labelText">超标人数</span>
                      <span style="color: #f56c6c">{{
                        assessmentReport.seriousHarmExceedEmployees || 0
                      }}</span>
                    </el-col>
                    <el-col :span="12" class="harmCount">
                      <span class="labelText">不超标人数</span>
                      <span>{{
                        assessmentReport.seriousHarmNoExceedEmployees || 0
                      }}</span>
                    </el-col>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">职业卫生自查风险评估</div>
            </el-col>

            <el-col :span="8">
              <span class="labelText">防治责任自查等级</span>
              {{ selfCheckLevel[assessmentReport.assessManageLevel] }}
            </el-col>

            <el-col :span="8">
              <span class="labelText">暴露风险等级</span>
              {{ exposeLevel[assessmentReport.assessExposeLevel] }}
            </el-col>

            <el-col :span="8">
              <span class="labelText">综合风险等级</span>
              {{ result[assessmentReport.assessmentResult] }}
            </el-col>
          </el-row>
        </div>
      </el-card>
    </el-row>

    <!-- 这里表单开始，在申报的时候填写。 -->
    <el-form ref="form">
      <el-row>
        <el-row style="margin: 20px auto auto 20px">
          <el-col
            :xs="{ span: 24, offset: 0 }"
            :sm="{ span: 2, offset: 0 }"
            :md="{ span: 4, offset: 0 }"
            :lg="{ span: 5, offset: 0 }"
            :xl="{ span: 6, offset: 0 }"
          >
            <!-- <el-button class="title" @click="goback">返回</el-button> -->
            <br />
          </el-col>

          <el-col
            :xs="{ span: 24, offset: 0 }"
            :sm="{ span: 22, offset: 0 }"
            :md="{ span: 20, offset: 0 }"
            :lg="{ span: 19, offset: 0 }"
            :xl="{ span: 18, offset: 0 }"
          >
            <span class="pageTitle">上报自查报告 </span>
            <!-- <span class="smallText">此报告将上报给：</span> -->

            <el-tag
              size="mini"
              type="info"
              v-if="assessmentReport.supperUser && assessmentReport.supperUser.regAdd"
            >
              {{ assessmentReport.supperUser.regAdd[0] }}
              -
              {{ assessmentReport.supperUser.regAdd[1] }}
              &nbsp;&nbsp;
              {{ assessmentReport.supperUser.cname }}
            </el-tag>
          </el-col>
        </el-row>
      </el-row>

      <el-row>
        <el-col
          :xs="{ span: 24, offset: 0 }"
          :sm="{ span: 20, offset: 2 }"
          :md="{ span: 16, offset: 4 }"
          :lg="{ span: 14, offset: 5 }"
          :xl="{ span: 12, offset: 6 }"
        >
          <el-card
            class="box-card"
            shadow="hover"
            style="margin-top: 30px; margin-bottom: 30px"
          >
            <div slot="header" class="clearfix">
              <el-row :gutter="20">
                <el-col
                  :xs="{ span: 24, offset: 0 }"
                  :sm="{ span: 24, offset: 0 }"
                  :md="{ span: 8, offset: 0 }"
                  :lg="{ span: 8, offset: 0 }"
                  :xl="{ span: 8, offset: 0 }"
                >
                  <el-form-item label="填表人">
                    {{assessmentReport.fillerName}}
                  </el-form-item>
                </el-col>
                <el-col
                  :xs="{ span: 24, offset: 0 }"
                  :sm="{ span: 24, offset: 0 }"
                  :md="{ span: 8, offset: 0 }"
                  :lg="{ span: 8, offset: 0 }"
                  :xl="{ span: 8, offset: 0 }"
                >
                  <el-form-item label="联系电话">
                    {{assessmentReport.fillerPhoneNumber}}
                  </el-form-item>
                </el-col>

                <el-col
                  :xs="{ span: 24, offset: 0 }"
                  :sm="{ span: 24, offset: 0 }"
                  :md="{ span: 8, offset: 0 }"
                  :lg="{ span: 8, offset: 0 }"
                  :xl="{ span: 8, offset: 0 }"
                >
                  <el-form-item label="填表日期">
                    {{doMomentmonthD(assessmentReportfillDate)}}
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <el-row :gutter="20">
              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 24, offset: 0 }"
                :md="{ span: 24, offset: 0 }"
                :lg="{ span: 24, offset: 0 }"
                :xl="{ span: 24, offset: 0 }"
              >
                <el-form-item label="情况概述">
                  {{assessmentReport.situation ? assessmentReport.situation : '暂无'}}
                </el-form-item>
              </el-col>
              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 24, offset: 0 }"
                :md="{ span: 24, offset: 0 }"
                :lg="{ span: 24, offset: 0 }"
                :xl="{ span: 24, offset: 0 }"
              >
                <el-form-item label="发现问题">
                  {{assessmentReport.problem ? assessmentReport.problem : '暂无'}}
                </el-form-item>
              </el-col>
              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 24, offset: 0 }"
                :md="{ span: 24, offset: 0 }"
                :lg="{ span: 24, offset: 0 }"
                :xl="{ span: 24, offset: 0 }"
              >
                <el-form-item label="整改情况">
                  {{assessmentReport.rectification ? assessmentReport.rectification : '暂无'}}
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    </div>
    <!-- <div v-else>
      未上报
    </div> -->
  </div>
</template>
<script>
import { getAllAssessmentInfo } from "@/api/adminorg";
import moment from 'moment';
export default {
  props: {
    id: String,
  },
  data(){
    return{
      activeName: "化学有害因素",
      harmFactorsFields: [
        { label: "危害因素", value: "label" },
        {
          label: "接触人数",
          value: "value",
        },
      ],
      harmFactorsData: [],
      selfCheckLevel: ["A", "B", "C"],
      exposeLevel: ["Ⅰ", "Ⅱ", "Ⅲ", "-"],
      result: ["丙","乙", "甲",  "-"],
      assessmentReport:{},
      isReported:false,
      nowyear:0,
    }
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created(){
    // 获取当前年份
    this.nowyear = new Date().getFullYear();
    this.getAllAssessmentInfo();
  },
  methods:{
    getAllAssessmentInfo(){
      let obj = {
        EnterpriseID: this.id,
        year: this.nowyear,
      }
      // 通过当年年份和企业id获取该企业今年的责任自查与风险评估信息
      getAllAssessmentInfo(obj).then(async res=>{
        if(res.data.code){
          this.assessmentReport = res.data;
          this.isReported = this.assessmentReport.isReported ? this.assessmentReport.isReported : false;
          this.assessmentReport.workAddress = this.assessmentReport.workAddress.map(
            (item) => {
              if (item.districts) {
                item = item.districts.join("-") + item.address;
              }
              return item;
            }
          );
          switch (this.assessmentReport.assessManageLevel) {
            case "A":
              this.assessmentReport.assessManageLevel = 0;
              break;
            case "B":
              this.assessmentReport.assessManageLevel = 1;
              break;
            case "C":
              this.assessmentReport.assessManageLevel = 2;
              break;
          }
          this.assessmentReport.fillDate = Date.now();
          this.$store.dispatch("adminorg/remind", {
            isOnlineDeclaration: this.$store.getters.remind.isOnlineDeclaration,
            monthD: this.$store.getters.remind.monthD,
            isjobHealth: this.$store.getters.remind.isjobHealth,
            reportTimes: this.$store.getters.remind.reportTimes,
            isratioArr: this.$store.getters.remind.isratioArr,
            checkDate: this.$store.getters.remind.checkDate,
            isReported: this.isReported,
            isAssess: this.$store.getters.remind.isAssess,
          });
        }
      })
    },
  },
}
</script>

<style scoped>
  .assessmentSortStyle{
    height: 38px;
  }
  .pageTitle {
    font-size: 20px;
    line-height: 32px;
  }
  .reportCardBody {
    margin-top: 0;
  }
  .smallText {
    font-size: 12px;
    color: #c0c4cc;
  }
  .harmTitleBox {
    text-align: center;
  }
  .harmCardContent {
    position: relative;
    padding: 14px;
    height: 125px;
    overflow: hidden;
  }
  .harmCountBox {
    position: absolute;
    left: 0;
    bottom: 15px;
    width: 100%;
    font-size: 24px;
  }
  .harmCount {
    text-align: center;
  }
  .labelText {
    font-size: 12px;
    color: #c0c4cc;
    display: block;
    margin-bottom: 10px;
  }
  .formSubTitle {
    padding: 10px 0;
    font-size: 13pt;
  }
  .harmCardBody {
    margin: 5px;
  }
  .chartsCardHeader {
    text-align: center;
    /* font-size: 16px; */
    width: 100%;
    display: inline-block;
  }
  .chartsBody {
    text-align: center;
    width: 100%;
    height: 120px;
  }
  .chartsBody>div{
    margin: 0 auto !important;
  }
</style>