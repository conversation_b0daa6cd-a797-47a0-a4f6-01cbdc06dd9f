<template>
  <div>
    <el-form>
    <el-table :data="datas" style="width: 100%" border size="small">
      <el-table-column align="center" prop="workType" label="工种">
      </el-table-column>
      <el-table-column min-width="150px" align="center" label="检测点位置">
        <el-table-column align="center" label="车间" prop="workspace">
        </el-table-column>
        <el-table-column align="center" label="岗位" prop="station">
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="检测值[dB(A)]" prop="checkData">
      </el-table-column>
      <el-table-column align="center" label="接触时间" prop="touchTime">
      </el-table-column>
      <el-table-column align="center" label="8/40h等效声级检测数值[dB(A)]" prop="equalLevel">
      </el-table-column>
      <el-table-column align="center" label="职业接触限值dB(A)" prop="touchLimit">
      </el-table-column>
      <el-table-column prop="checkResult" label="判定结果" align="center">
          <template slot-scope="props">
            <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult}}</span>
          </template>
        </el-table-column>
      <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
        <template slot-scope="props">
          {{doMomentmonthD(props.row.reportTime)}}
        </template>
      </el-table-column> -->
    </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
    };
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  async created() {
  },
  methods: {

  },
};
</script>

<style>
</style>