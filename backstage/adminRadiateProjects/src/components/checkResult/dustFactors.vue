<template>
  <div class="physicFactors">
    <el-form>
      <el-table :data="datas" style="width: 100%" border size="small">
        <el-table-column align="center" min-width="100px" prop="workType" fixed label="工种">
        </el-table-column>
        <el-table-column align="center" label="检测点位置">
          <el-table-column min-width="100px" align="center" label="车间" prop="workspace">
          </el-table-column>
          <el-table-column min-width="100px" align="center" label="岗位" prop="station">
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" min-width="150px" prop="checkProject" label="检测项目">
        </el-table-column>
        <el-table-column label="结果(mg/m³)" align="center" v-if="this.category === 1">
          <el-table-column min-width="60px" label="MAC" align="center">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.MAC}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="TWA" align="center" min-width="60px">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.TWA}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="STEL" align="center" min-width="60px">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.STEL}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column min-width="60px" label="PE" align="center">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.PE}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column min-width="100px" label="超限倍数" prop="excursionLimits" align="center">
          </el-table-column> -->
        </el-table-column>
        <el-table-column label="检测日期" align="center" min-width="100px" v-if="this.category === 2 || this.category === 3">
          <template slot-scope="scope">
            <div class="branchWrap">
              <div v-for="(item, index) in scope.row.checkResults" :key="index">
                <span>{{doMomentmonthD(item.date)}}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="检测结果(mg/m³)" align="center" v-if="this.category === 2 || this.category === 3">
          <el-table-column label="MAC" align="center" min-width="60px">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.MAC}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="TWA" align="center" min-width="60px">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.TWA}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="STEL" align="center" min-width="60px">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.STEL}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column min-width="60px" label="PE" align="center">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.PE}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="超限倍数" align="center" min-width="70px">
            <template slot-scope="scope">
              <div class="branchWrap">
                <div v-for="(item, index) in scope.row.checkResults" :key="index">
                  <span>{{item.excursionLimits}}</span>
                </div>
              </div>
            </template>
          </el-table-column> -->
        </el-table-column>
        <el-table-column label="评价结论" align="center" min-width="70px" v-if="this.category === 2 || this.category === 3">
          <template slot-scope="scope">
            <div class="branchWrap">
              <div v-for="(item, index) in scope.row.checkResults" :key="index">
                <span>{{item.checkResultItem}}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="职业接触限值(mg/m³)" align="center" v-if="this.category === 2 || this.category === 3">
          <el-table-column label="TWA" prop="TWALimit" align="center" min-width="70px">
          </el-table-column>
          <el-table-column label="PE" prop="PELimit" align="center" min-width="70px">
          </el-table-column>
          <!-- <el-table-column label="总尘(TWA)" prop="allDustTWALimit" align="center" min-width="70px">
          </el-table-column>
          <el-table-column label="呼尘(TWA)" prop="respirableDustTWALimit" align="center" min-width="70px">
          </el-table-column>
          <el-table-column label="总尘(PE)" prop="allDustPELimit" align="center" min-width="70px">
          </el-table-column>
          <el-table-column label="呼尘(PE)" prop="respirableDustPELimit" align="center" min-width="70px">
          </el-table-column> -->
        </el-table-column>
        <el-table-column prop="checkResult" label="判定结果" align="center">
          <template slot-scope="props">
            <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
          <template slot-scope="props">
            {{doMomentmonthD(props.row.reportTime)}}
          </template>
        </el-table-column> -->
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { findPhysicFactors} from "@/api/adminorg";
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
    category: Number,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
      showEdit: {},
    };
  },
  // updated() {
  //   console.log(this.datas,'sssssssssssssssssssssssssssssssssssssssssssss');
  // },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created() {
  },
  methods: {
  },
};
</script>
