<template>
  <div>
    <el-form>
    <el-table :data="datas" style="width: 100%" border size="small">
      <el-table-column align="center" prop="checkProject" label="检测项目">
      </el-table-column>
      <el-table-column min-width="150px" align="center" label="检测点位置">
        <el-table-column align="center" label="车间" prop="workspace">
        </el-table-column>
        <el-table-column align="center" label="岗位" prop="station">
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="装置名称" prop="device">
      </el-table-column>
      <el-table-column align="center" label="编号" prop="model">
      </el-table-column>
      <el-table-column align="center" label="额定容器" prop="ratedCapacity">
      </el-table-column>
      <el-table-column align="center" label="检测条件" prop="condition">
      </el-table-column>
      <el-table-column align="center" label="检测点位置" prop="checkAddress">
      </el-table-column>
      <el-table-column align="center" label="检测结果（μSv/h）" prop="checkResultValue">
      </el-table-column>
      <el-table-column align="center" label="判定结果" prop="checkResult">
        <template slot-scope="scope">
          <div class="branchWrap">
              <span :class="scope.row.checkResult[0]=='不'?'incompatible':''">{{scope.row.checkResult}}</span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
        <template slot-scope="props">
          {{doMomentmonthD(props.row.reportTime)}}
        </template>
      </el-table-column> -->
    </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
    };
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  async created() {
  },
  methods: {

  },
};
</script>

<style>
.branchWrap div:last-child {
  border: none;
}
</style>