import * as types from '../types.js';
// import * as projectsApi from '@/api/adminProjects';
// import * as radiateProjectsApi from '@/api/radiateProjects';
import _ from 'lodash';

const state = {
  // fileDir: '', // 存在服务端的文件目录
  // uploadFileDir: '', // 用户上传文件的目录
  // superDomain: '', // 监管段域名（用于文件存在监管端时使用）
  AllParams: {}, // 所有筛选的条件
  serviceUnitTypeOptions: [{
    label: '综合性医院',
    value: 'generalHospital',
  }, {
    label: '专科医院',
    value: 'specialtyHospital',
  }, {
    label: '其他医疗机构',
    value: 'otherMedicalInstitutions',
  }, {
    label: '企业',
    value: 'enterprise',
  }, {
    label: '其他单位',
    value: 'otherUnits',
  }],
  serviceTypeOptions: [{
    label: '放射卫生防护检测',
    value: 'radHProtDetect',
  }, {
    label: '放射诊疗建设项目职业病危害放射防护评价',
    value: 'radProtAssessForOccDis',
  }, {
    label: '个人剂量监测',
    value: 'personalDoseMonitoring',
  }, {
    label: '放射防护器材和含放射性产品检测',
    value: 'radProtEquipAndProdDetect',
  }],
  formState: {
    show: false,
    edit: false,
    formData: {
      personInCharge: {
        name: '', // 姓名
        phoneNumber: '', // 手机号
        technicalTittle: '', // 技术职称
        major: '', // 专业
      }, // '项目负责人',
      personsOfProject: [{
        name: '', // 姓名
        phoneNum: '', // 手机号
        certificate: [{
          professional: '', // 专业
          technical: '', // 职称
        }],
      }], // '项目组成员',
      companyContact: '',
      companyContactPhoneNumber: '',
      serviceUnitType: '',
      technicalServiceType: [],
      year: '',
      radProtDetectInWrkplc: {
        selected: false,
        detectionCount: '',
        exceedNum: '',
        hazardType: [],
        majorCategory: 'radHProtDetect',
      },
      eqptQualCtrlDetect: {
        selected: false,
        detectionCount: '',
        exceedNum: '',
        majorCategory: 'radHProtDetect',
      },
      preAssessment: {
        selected: false,
        exceedNum: '',
        hazardType: [],
        majorCategory: 'radProtAssessForOccDis',
      },
      ctrlEffctAssess: { // 控制效果评价
        selected: false, // 是否被选择
        detectionCount: '', // 检测点位数
        exceedNum: '', // 不合格数
        hazardType: [], // 危害类型
        majorCategory: 'radProtAssessForOccDis', // 所属大类 - 指向 technicalServiceType
      },
      personalDoseMonitoring: { // 个人剂量监测
        selected: false, // 是否被选择
        peopleCount: '',
        fiveTo20mSvCount: '', // 5~20mSv人数
        exceed20mSvCount: '', // 超过20mSv的人数
        majorCategory: 'personalDoseMonitoring', // 所属大类 - 指向 technicalServiceType
      },
      radProtEquipDetect: { // 放射防护器材检测
        selected: false, // 是否被选择
        detectionCount: '', // 检测点位数
        exceedNum: '', // 超标点位数
        exceedInfo: '', // 超标样品名称
        majorCategory: 'radProtEquipAndProdDetect', // 所属大类 - 指向 technicalServiceType
      },
      radProdDetect: { // 含放射性产品检测
        selected: false, // 是否被选择
        detectionCount: '', // 检测点位数
        exceedNum: '', // 超标点位数
        exceedInfo: '', // 超标样品名称
        majorCategory: 'radProtEquipAndProdDetect', // 所属大类 - 指向 technicalServiceType
      },
    },
  },

  defaultFormData: {
    personInCharge: {
      name: '', // 姓名
      phoneNumber: '', // 手机号
      technicalTittle: '', // 技术职称
      major: '', // 专业
    }, // '项目负责人',
    personsOfProject: [], // '项目组成员',
    companyContact: '',
    companyContactPhoneNumber: '',
    serviceUnitType: '',
    year: '',
    technicalServiceType: [],
    radProtDetectInWrkplc: {
      selected: false,
      detectionCount: '',
      exceedNum: '',
      hazardType: [],
      majorCategory: 'radHProtDetect',
    },
    eqptQualCtrlDetect: {
      selected: false,
      detectionCount: '',
      exceedNum: '',
      majorCategory: 'radHProtDetect',
    },
    preAssessment: {
      selected: false,
      exceedNum: '',
      hazardType: [],
      majorCategory: 'radProtAssessForOccDis',
    },
    ctrlEffctAssess: { // 控制效果评价
      selected: false, // 是否被选择
      detectionCount: '', // 检测点位数
      exceedNum: '', // 不合格数
      hazardType: [], // 危害类型
      majorCategory: 'radProtAssessForOccDis', // 所属大类 - 指向 technicalServiceType
    },
    personalDoseMonitoring: { // 个人剂量监测
      selected: false, // 是否被选择
      peopleCount: '',
      fiveTo20mSvCount: '', // 5~20mSv人数
      exceed20mSvCount: '', // 超过20mSv的人数
      majorCategory: 'personalDoseMonitoring', // 所属大类 - 指向 technicalServiceType
    },
    radProtEquipDetect: { // 放射防护器材检测
      selected: false, // 是否被选择
      detectionCount: '', // 检测点位数
      exceedNum: '', // 超标点位数
      exceedInfo: '', // 超标样品名称
      majorCategory: 'radProtEquipAndProdDetect', // 所属大类 - 指向 technicalServiceType
    },
    radProdDetect: { // 含放射性产品检测
      selected: false, // 是否被选择
      detectionCount: '', // 检测点位数
      exceedNum: '', // 超标点位数
      exceedInfo: '', // 超标样品名称
      majorCategory: 'radProtEquipAndProdDetect', // 所属大类 - 指向 technicalServiceType
    },
  },

  projectsList: {
    pageInfo: {},
    docs: [],
  },

  selectedList: [],
  serviceInfo: {},
  companyList: [],
  applyForm: {
    show: false,
    formData: {},
  },
  employees: [],

  checkResult: [], // 检测结果
  serviceType: '', // 检测类型，年度检测和现状评估
  currentTab: '', // 当前活跃的tab
  checkAssessment: [], // 现状评价数据
  disableApplys: [], // 无法上报的项目
  applylist: [], // 选中的符合上报条件的项目
};

const mutations = {
  updateDisableApplys(state, disableApplys) { // 更新无法上报的项目
    state.disableApplys = disableApplys;
  },
  updateApplylist(state, applylist) { // 更新上报的项目
    state.applylist = applylist;
  },
  getJobHealthInfo(state, jobHealthInfo) {
    state.jobHealthInfo = jobHealthInfo;
    state.serviceType = jobHealthInfo.serviceType;
  },

  getCheckResult(state, checkResult) {
    state.checkResult = checkResult;
  },
  getCheckAssessment(state, checkAssessment) {
    state.checkAssessment = checkAssessment;
  },
  getCurrentTab(state, currentTab) {
    state.currentTab = currentTab;
  },
  getServiceType(state, serviceType) {
    state.serviceType = serviceType;
  },

  [types.ADMINPROJECTS_SELECTLIST](state, selectedList) {
    // console.log('选中某些表', selectedList);
    state.selectedList = selectedList;
  },

  [types.ADMINPROJECTS_LIST](state, projectsList) {
    state.projectsList = projectsList;
  },

  [types.ADMINPROJECTS_COMPANYLIST](state, companyList) {
    state.companyList = companyList;
  },

  [types.SERVICEINFO](state, serviceInfo) {
    state.serviceInfo = serviceInfo;
  },
  [types.ADMINPROJECTSFORMSTATE](state, formState) {
    state.formState = formState;
  },

  [types.APPLYFORM](state, applyForm) {

    state.applyForm.show = applyForm.show;
    if (!_.isEmpty(applyForm.formData)) {
      state.applyForm.formData = applyForm.formData;
    } else {
      // 设置默认值
      state.applyForm.formData = {};
    }

  },

  [types.GET_EMPLOYEES](state, list) {
    state.employees = list;
  },
  // 修改 AllParams 合并所有条件
  [types.AllParams](state, newParams) {
    // console.log(newParams.newParams.searchkey===undefined,state.AllParams);
    if (newParams.searchkey === undefined) {
      // 说明是筛选条件操作 将搜索关键词清零
      state.AllParams.searchkey = '';
    }
    state.AllParams = Object.assign(state.AllParams, newParams);
  },
};


const actions = {


  showAdminProjectsForm: ({
    commit,
    state,
  }, params = {
    edit: false,
    formData: {},
  }) => {
    console.log(213213, params);
    const formData = params.formData ? params.formData : JSON.parse(JSON.stringify(state.defaultFormData));
    commit(types.ADMINPROJECTSFORMSTATE, {
      show: true,
      edit: params.edit,
      formData,
    });
  },

};


export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
