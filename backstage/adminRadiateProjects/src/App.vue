<template>
  <div id="serviceOrg-app" class="serviceProject">
    <div :class="classObj">
      <div class="main-container">
        <router-view />
      </div>
    </div>
  </div>
</template>
<script>
import { initEvent } from "@root/publicMethods/events";
export default {
  mounted() {
    initEvent(this);
  },
  data() {
    return {
      sidebarOpened: true,
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
};
</script>
<style lang="scss">
.serviceProject .xxn{
  .keyWordsSearch{ // 关键字搜索
    .el-input__inner{
      background: #F2F7FD!important;
    }
    .el-input__icon{
      color: #2A91FC!important;
    }
  }
  .searchYear{ // 搜索年份
    .el-date-editor.el-input, .el-date-editor.el-input__inner{
      width: 90px;
    }
    .el-input--small .el-input__inner{
      border-color: transparent;
      color: #fff;
      font-weight: bold;
      font-size: 16px;
      letter-spacing: 1px;
      line-height: 33px;
      padding: 0;
      background: transparent!important;
    }
    .el-input--small .el-input__icon.el-icon-date{
      display: none;
    }
  }
}
.serviceProject{
  .incompatible{
    color: #F56C6C!important;
    font-weight: bold;
  }
  .el-table .el-breadcrumb {
    line-height: 23px;
  }
  .wrap .el-form-item__label{
    color: #555;
  }
  .wrap .el-form-item {
    margin-bottom: 18px;
  }
  .el-card__header{
    padding: 5px 20px;
  }
  .box-card.box .el-icon-circle-close{
    color: #f5f5ff!important;
  }
  .el-card__body {
    height: 80vh;
    // overflow-y: scroll;
  }
  .el-card__body .el-card__body{
    height: auto!important;
  }
  .el-card__body .el-card{
    margin-bottom: 20px;
  }
   /* 设置滚动条的样式 */
  // .box-card.box ::-webkit-scrollbar {
  //   width:5px;
  //   background-color: #999;
  // }
   /* 滚动槽 */
  .box-card.box ::-webkit-scrollbar-track {
    border-radius:4px;
    background-color: #fff;
  }

  /* 滚动条滑块 */
  .box-card.box ::-webkit-scrollbar-thumb {
    border-radius:4px;
    /* background:black; */
  }
  .el-collapse .el-col-4{
    font-weight: bold;
    color: #666;
  }
  .el-collapse-item__header{
    font-size: 14px!important;
  }
   .el-collapse-item__header .el-collapse-item__arrow.el-icon-arrow-right {
    color: #303133;
  } 
  .el-collapse-item__header.is-active{
    text-align: center!important;
    color: #409EFF!important;
    font-weight: bold!important;
  }
  .el-input--medium .el-input__inner,
  .el-textarea__inner{
    border: none;
    font-weight: bold;
  }
  .wrap .el-form-item__label{
    color: #555;
    font-weight: normal;
  }
}
.yearReportInfo .el-card__body {
  height: auto;
  padding: 30px;
}
.yearAssessInfo .el-card__body {
  height: auto;
}
.yearAssessInfo .result .el-card__body {
  height: auto;
  padding: 0;
}
.comprehensiveLevel .el-textarea__inner {
  border: 1px solid;
}
</style>
