import request from '@root/publicMethods/request';

export function message_send(data) {
  return request({
    url: '/manage/sendMessage/send',
    method: 'post',
    data,
  });
}
export function repulse(params) {
  return request({
    url: '/manage/adminorgGov/repulse',
    params,
    method: 'get',
  });
}
export function getAllAssessmentInfo(data) {
  return request({
    url: '/manage/adminorgGov/getAllAssessmentInfo',
    data,
    method: 'post',
  });
}
export function getsuperUserInfo(params) {
  return request({
    url: '/api/adminorgGov/getsuperUserInfo',
    params,
    method: 'get',
  });
}

export function gosendMessages(data) {
  return request({
    url: '/api/adminorgGov/gosendMessages',
    data,
    method: 'post',
  });
}
export function count(params) {
  return request({
    url: '/manage/supervision/count',
    params,
    method: 'get',
  });
}

export function supervisionList(data) { // 监管信息
  return request({
    url: '/manage/supervision/list',
    data,
    method: 'post',
  });
}
export function addSupervision(data) {
  return request({
    url: '/manage/supervision/add',
    data,
    method: 'post',
  });
}
export function findAssess(data) {
  return request({
    url: '/manage/adminorgGov/findAssess',
    data,
    method: 'post',
  });
}
export function findApplyForm(data) {
  return request({
    url: '/manage/adminorgGov/findApplyForm',
    data,
    method: 'post',
  });
}
export function chooseYear(data) {
  return request({
    url: '/manage/adminorgGov/chooseYear',
    data,
    method: 'post',
  });
}

export function findCheckResult(data) {
  return request({
    url: '/manage/adminorgGov/findCheckResult',
    data,
    method: 'post',
  });
}

// export function deleteAdminorg(params) {
//   return request({
//     url: '/manage/adminorgGov/deleteAdminorg',
//     params,
//     method: 'get',
//   });
// }

export function getIndustryCategory() {
  return request({
    method: 'get',
    url: '/api/adminorgGov/getIndustryCategory',
  });
}

export function getAlladminorgList(params) {
  return request({
    url: '/manage/adminorgGov/getList',
    params,
    method: 'get',
  });
}

export function adminorgList(params) {
  return request({
    url: '/manage/adminorgGov/getList',
    params,
    method: 'get',
  });
}

export function getOneAdminorg(params) {
  return request({
    url: '/manage/adminorgGov/getCompany',
    params,
    method: 'get',
  });
}

export function passAudit(data) {
  return request({
    url: '/manage/adminorgGov/passAudit',
    data,
    method: 'post',
  });
}

export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

export function addCompany(data) {
  return request({
    url: '/api/adminorgGov/addCompany',
    data,
    method: 'post',
  });
}


// 获取参数检测合格率

export function getMainData(params) {
  console.log(1212);
  return request({
    url: '/api/adminorgGov/getMainData',
    params,
    method: 'get',
  });
}

export function getCheckStatisticsApi(params) {
  // console.log('0000000000api');

  return request({
    url: '/api/adminorgGov/getCheckStatistics',
    method: 'get',
    params,
  });
}


// 获取企业档案完成情况
export function getFilesCompleteness(params) {
  // console.log('api-----------------');
  return request({
    url: '/api/adminorgGov/getFilesCompleteness',
    method: 'get',
    params,

  });
}

// 退回
export function giveBack(data) {
  return request({
    url: '/manage/projectGiveBack/giveBack',
    method: 'post',
    data,
  });
}

// 退回窗口关闭后删除图片
export function giveBackDelImgs(data) {
  return request({
    url: '/manage/projectGiveBack/giveBackDelImgs',
    method: 'post',
    data,
  });
}

// 退回历史
export function giveBackRecord(data) {
  return request({
    url: '/manage/projectGiveBack/giveBackRecord',
    method: 'post',
    data,
  });
}

