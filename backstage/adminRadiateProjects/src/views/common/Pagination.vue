<template>
  <div class="block dr-pagination">
    <div v-if="searchParams">
      <div>
        <el-pagination
          background
          :hide-on-single-page="false"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="searchParams.curPage"
          :page-sizes="[30, 50, 100]"
          :page-size="searchParams.limit"
          layout="total, sizes, prev, pager, next"
          :total="totleCount"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    searchParams: Object,
    totleCount: Number, // 总条数
  },
  methods: {
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchParams.limit = val;
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchParams.curPage = val;
    },
  },
  
};
</script>

<style lang="scss">
.dr-pagination {
  text-align: center;
  margin: 15px auto;
}
// 福州的样式
#adminorg-app .el-pagination.is-background .el-pager li{
  color:#1c1e21;
  font-weight:400;
  background-color:#fff;
  border: 1px solid #d4d9e1;
}
#adminorg-app .el-pagination.is-background .el-pager li:not(.disabled).active{
  border: 1px solid #50A4FC;
  background:#fff;
  color: #50A4FC;
}
.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev{
  background-color:#fff !important;
  border:1px solid #d4d9e1
}
////////////////////////
</style>
