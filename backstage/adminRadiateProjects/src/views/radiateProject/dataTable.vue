<template>
  <el-row>
    <div class="table">
      <el-table align="center" v-loading="loading" ref="multipleTable" :data="lists" tooltip-effect="dark" style="width: 100%" row-key="_id" default-expand-all stripe  :height="tableMaxHeight"
      header-cell-style="background-color: #FAFAFA;height:60px">
        <el-table-column width="60" label="序号" type="index" fixed align="center">
          <template scope="scope"> {{ searchParams.limit * (searchParams.curPage - 1) + scope.$index + 1 }} </template>
        </el-table-column>
        <el-table-column prop="companyName" label="用人单位" align="center" fixed min-width="150px" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="isHz" @click="openDetails(scope.row)">{{ scope.row.companyName }}</span>
            <span v-else @click.stop="toUrl('/admin/adminorgGov/editAdminorg/' + scope.row.EnterpriseID)" class="point"  @click="openDetails(scope.row)">{{ scope.row.companyName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="companyContact" label="联系人" align="center" width="80px">
          <template slot-scope="scope">
            <div @click="openDetails(scope.row)">
              {{ scope.row.companyContact }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="companyContactPhoneNumber" label="联系号码" align="center" width="120px">
          <template slot-scope="scope">
            <div @click="showPhone(scope.row)" style="cursor:pointer">
              {{ scope.row.companyContactPhoneNumber }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="申报机构" align="center" min-width="150px" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectNumber" label="项目编号" align="center" width="140px">
          <template slot-scope="scope">
            <div @click="openDetails(scope.row)">
              {{ scope.row.projectNumber }}
            </div>
          </template>
        </el-table-column>
        </el-table-column>
        <el-table-column label="项目负责人" width="100px" align="center">
          <template slot-scope="scope">
            <div @click="openDetails(scope.row)">
              {{scope.row.personInCharge[0]?scope.row.personInCharge[0].name || '':''}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="联系电话" align="center" width="120px">
          <template slot-scope="scope">
            {{scope.row.personInCharge[0]?scope.row.personInCharge[0].phoneNum || '':''}}
          </template>
        </el-table-column>
        <el-table-column label="上报时间" width="100px" prop="applyTime" fixed="right"></el-table-column>
        <el-table-column label="完成时间" width="100px" prop="completeTime" fixed="right"></el-table-column>
        <el-table-column label="操作" width="80px" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button @click.native.stop="openGiveBackDetails(scope.row)" size="mini" type="warning">退回</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-row>
</template>

<script>
import { mapGetters } from 'vuex';
import {checkPhoneLog} from '@/api/adminRadiateProjects'
export default {
  props: {
    lists: Array,
    projectDetail: Object,
    searchParams: Object,
    dialogState: Object,
  },
  data() {
    return {
      green: { color: "#13CE66" },
      red: { color: "#FF4949" },
      loading: false,
      assessExposeLevel: ['Ⅰ风险', 'Ⅱ风险', 'Ⅲ风险'],
      assessmentResult: [ '丙类','乙类', '甲类', ''],
      assessManageLevel: ["A级", "B级", "C级"],
      isHz: location.host.indexOf('hzzyws') != -1, // 杭州分支
    };
  },
  computed: {
    ...mapGetters(['serviceAreaOptions']),
    tableMaxHeight() {
      return window.innerHeight - 280;
    },
  },
  methods: {
    // 查看手机号
    async showPhone(row){
      if(row.companyContactPhoneNumber){
        row.companyContactPhoneNumber = row.companyContactPhoneNumber2
        await checkPhoneLog({model:"RadiateProjects",phoneNum:row.companyContactPhoneNumber2})
      }
      
    },
    getServiceArea(serviceAreas){
      const serviceAreas2 = []
      this.serviceAreaOptions.forEach(item=>{
        serviceAreas.forEach(item2=>{
          if(item.value === item2){
            serviceAreas2.push(item.label)
          }
        })
      })
      return serviceAreas2.join('；')
    },
    openDetails(row) {
      this.$store.dispatch("adminProjects/showAdminProjectsForm", {
            edit: true,
            formData: row,
      });
      this.projectDetail.show = true;
      this.projectDetail.data = row;
    },
    openGiveBackDetails(row) {
      console.log(*********, row)
      this.$emit('openGiveBackDetails', { 
        _id: row._id,
        companyName: row.companyName,
        serviceName: row.name,
        projectName: row.projectName,
        projectNumber: row.projectNumber
      })
    },
    toUrl(url){
      location.href = url;
    }
  },
};
</script>

<style scoped>
  .el-table{
    border: 1px solid #ECEEF5;
  }
  table .el-icon-check{
    color: #67C23A;
  }
  table .el-icon-close{
    /* color: #606266; */
    color: #F56C6C;
  }
  .point:hover{
    cursor: pointer;
    color: #409EFF;
  }
  /* .table{
    position: absolute;
    top: 0px;
    left: 210px;
    bottom: 0px;
    width: auto;
    right: 0px;
    overflow-y: auto;
  }
  .next-content-table {
    min-width: 1350px;
    overflow: hidden;
  } */
</style>
