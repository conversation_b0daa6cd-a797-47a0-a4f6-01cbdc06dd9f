<template>
  <div class="wrap">
    <el-card class="box-card box" shadow="always">
      <div class="clearfix header">
        <span class="el-icon-error close2" @click="closeDetail"></span>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane name="info1">
            <span slot="label"><i class="el-icon-document"></i> 检测结果</span>
          </el-tab-pane>
          <el-tab-pane name="info2">
            <span slot="label"><i class="el-icon-s-custom"></i> 项目报告卡</span>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="text item" ref="content">
        <el-form class="applyForm info1" v-show="activeName === 'info1'">
          <radiateProjectsResult :data="datas" />
        </el-form>
        <el-form class="applyForm info2" v-show="activeName === 'info2'">
          <div class="divider">
            <el-divider content-position="center">服务的用人单位信息</el-divider>
          </div>
          <el-row :gutter="10">
            <el-col :span="8">
              <div>单位名称：{{ datas.companyName }}</div>
            </el-col>
            <el-col :span="8">
              <div>联系人：{{ datas.companyContact }}</div>
            </el-col>
            <el-col :span="8">
              <div>联系电话：{{ datas.personInCharge[0].phoneNum }}</div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="3">
              <div>服务单位类型：</div>
            </el-col>
            <el-radio disabled :label="item.value" v-for="(item, i) in serviceUnitTypeOptions" :key="i"
              v-model="datas.serviceUnitType">{{ item.label }}</el-radio>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <div>注册地址：{{ registerAddress[0] }}</div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <div style="margin-left: 4px">服务地址：</div>
            <div style="margin-left: 74px; margin-top: -20px">
              <div v-for="(item, i) in registerAddress">{{ item }}</div>
            </div>
          </el-row>
          <div class="divider">
            <el-divider content-position="center">技术服务信息</el-divider>
          </div>
          <el-row :gutter="10">
            <el-col :span="3">
              <div>技术服务类型：</div>
            </el-col>
            <el-checkbox-group v-model="datas.technicalServiceType" disabled>
              <el-checkbox :label="item.value" v-for="(item, i) in serviceTypeOptions"
                :key="i">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="6">
              <div>现场调查时间：{{ datas.investigationTime }}</div>
            </el-col>
            <el-col :span="6">
              <div>现场检测时间：{{ datas.reportTime }}</div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 服务类型 -->
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="技术服务结果" prop="serviceType">
                <table style="width: 70%" border="1" cellspacing="0" class="resultTable">
                  <tr>
                    <td class="firstTd" rowspan="2">
                      <div class="td-header" @click="selectTechnicalServiceType('radHProtDetect')">
                        <div class="check-border">
                          <i v-if="datas.technicalServiceType.includes('radHProtDetect')" class="el-icon-check"></i>
                        </div>
                        <span>放射卫生防护检测</span>
                      </div>
                    </td>
                    <td>
                      <el-checkbox disabled v-model="datas.radProtDetectInWrkplc.selected"
                        class="single-checkbox"></el-checkbox>
                      <span>开展放射诊疗工作场所放射防护检测，共检测点位</span>
                      <el-input disabled v-model="datas.radProtDetectInWrkplc.detectionCount" style="width: 50px"
                        class="custom-input" />
                      <span>个，其中，超标点位</span>
                      <el-input disabled v-model="datas.radProtDetectInWrkplc.exceedNum" style="width: 50px"
                        class="custom-input" />
                      <span>超标点位放射性危害类型：</span>
                      <el-checkbox-group disabled v-model="datas.radProtDetectInWrkplc.hazardType"
                        style="display: inline;">
                        <el-checkbox v-for="(item, i) in hazardType" :key="i" :label="item.label"></el-checkbox>
                      </el-checkbox-group>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <el-checkbox disabled v-model="datas.eqptQualCtrlDetect.selected"
                        class="single-checkbox"></el-checkbox>
                      <span>开展放射诊疗设备质量控制检测，共检测设备</span>
                      <el-input disabled v-model="datas.eqptQualCtrlDetect.detectionCount" style="width: 50px"
                        class="custom-input" />
                      <span>台（套），其中，检测结果有一项以上指标不合格的设备</span>
                      <el-input disabled v-model="datas.eqptQualCtrlDetect.exceedNum" style="width: 50px"
                        class="custom-input" />
                      <span>台（套）</span>
                    </td>
                  </tr>

                  <tr>
                    <td class="firstTd" rowspan="2">
                      <div class="td-header" @click="selectTechnicalServiceType('radProtAssessForOccDis')">
                        <div class="check-border">
                          <i v-if="datas.technicalServiceType.includes('radProtAssessForOccDis')"
                            class="el-icon-check"></i>
                        </div>
                        <span>放射诊疗建设项目职业病危害放射防护评价</span>
                      </div>
                    </td>
                    <td>
                      <el-checkbox disabled v-model="datas.preAssessment.selected" class="single-checkbox"></el-checkbox>
                      <span>预评价（剂量估算超标点位</span>
                      <el-input disabled v-model="datas.preAssessment.exceedNum" style="width: 50px" class="custom-input" />
                      <span>个，超标点位放射性危害类型：</span>
                      <el-checkbox-group disabled v-model="datas.preAssessment.hazardType" style="display: inline;">
                        <el-checkbox v-for="(item, i) in hazardType" :key="i" :label="item.label"></el-checkbox>
                      </el-checkbox-group>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <el-checkbox disabled v-model="datas.ctrlEffctAssess.selected"
                        class="single-checkbox"></el-checkbox>
                      <span>控制效果评价（现场共检测点位</span>
                      <el-input disabled v-model="datas.ctrlEffctAssess.detectionCount" style="width: 50px"
                        class="custom-input" />
                      <span>个，其中，超标点位</span>
                      <el-input disabled v-model="datas.ctrlEffctAssess.exceedNum" style="width: 50px"
                        class="custom-input" />
                      <span>超标点位放射危害类型：</span>
                      <el-checkbox-group disabled v-model="datas.ctrlEffctAssess.hazardType" style="display: inline;">
                        <el-checkbox v-for="(item, i) in hazardType" :key="i" :label="item.label"></el-checkbox>
                      </el-checkbox-group>
                    </td>
                  </tr>

                  <tr>
                    <td class="firstTd">
                      <div class="td-header" @click="selectTechnicalServiceType('personalDoseMonitoring')">
                        <div class="check-border">
                          <i v-if="datas.technicalServiceType.includes('personalDoseMonitoring')"
                            class="el-icon-check"></i>
                        </div>
                        <span>个人剂量监测</span>
                      </div>
                    </td>
                    <td>
                      <el-checkbox disabled v-model="datas.personalDoseMonitoring.selected" class="single-checkbox">
                      </el-checkbox>
                      <span>个人剂量监测人数</span>
                      <el-input disabled v-model="datas.personalDoseMonitoring.peopleCount" style="width: 50px"
                        class="custom-input" />
                      <span>人，其中，5~20mSv</span>
                      <el-input disabled v-model="datas.personalDoseMonitoring.fiveTo20mSvCount" style="width: 50px"
                        class="custom-input" />
                      <span>人，超过20mSv</span>
                      <el-input disabled v-model="datas.personalDoseMonitoring.exceed20mSvCount" style="width: 50px"
                        class="custom-input" />
                      <span>人。</span>
                    </td>
                  </tr>

                  <tr>
                    <td class="firstTd" rowspan="2">
                      <div class="td-header" @click="selectTechnicalServiceType('radProtEquipAndProdDetect')">
                        <div class="check-border">
                          <i v-if="datas.technicalServiceType.includes('radProtEquipAndProdDetect')"
                            class="el-icon-check"></i>
                        </div>
                        <span>放射防护器材和含放射性产品检测</span>
                      </div>
                    </td>
                    <td>
                      <el-checkbox disabled v-model="datas.radProtEquipDetect.selected"
                        class="single-checkbox"></el-checkbox>
                      <span>开展放射防护器材检测，共检测样品数量</span>
                      <el-input disabled v-model="datas.radProtEquipDetect.detectionCount" style="width: 50px"
                        class="custom-input" />
                      <span>个，其中超标样品数量</span>
                      <el-input disabled v-model="datas.radProtEquipDetect.exceedNum" style="width: 50px"
                        class="custom-input" />
                      <span>个，超标样品名称</span>
                      <el-input disabled v-model="datas.radProtEquipDetect.exceedInfo" style="width: 400px"
                        class="custom-input" />
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <el-checkbox disabled v-model="datas.radProdDetect.selected" class="single-checkbox"></el-checkbox>
                      <span>开展含放射性产品检测，共检测样品数量</span>
                      <el-input disabled v-model="datas.radProdDetect.detectionCount" style="width: 50px"
                        class="custom-input" />
                      <span>个，其中超标样品数量</span>
                      <el-input disabled v-model="datas.radProdDetect.exceedNum" style="width: 50px"
                        class="custom-input" />
                      <span>个，超标样品名称</span>
                      <el-input disabled v-model="datas.radProdDetect.exceedInfo" style="width: 400px;"
                        class="custom-input" />
                    </td>
                  </tr>
                </table>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 服务类型 -->
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="技术服务结果*" prop="serviceType">
                <table style="width: 70%" border="1" cellspacing="0" class="resultTable">
                  <tr>
                    <th>序号</th>
                    <th>姓名</th>
                    <th>承担的服务事项</th>
                  </tr>
                  <tr v-for="(item, i) in datas.personsOfProject" :key="i">
                    <td>{{ i + 1 }}</td>
                    <td>{{ item.name }}</td>
                    <td>
                      <el-checkbox-group disabled v-model="datas.serviceMatters[0]" style="display: inline;">
                        <el-checkbox v-for="(item, i) in serviceMatters" :key="i" :label="item.label"></el-checkbox>
                      </el-checkbox-group>
                    </td>
                  </tr>
                </table>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="divider">
            <el-divider content-position="center">机构信息</el-divider>
          </div>
          <el-row :gutter="10">
            <el-col :span="8">
              <div>机构名称：{{ datas.name[0] }}</div>
            </el-col>
            <el-col :span="8">
              <div>法人代表：{{ serviceOrgInfo.corp }}</div>
            </el-col>
            <el-col :span="8">
              <div>注册地址：{{ datas.serviceAddress[0].join('') }}</div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <div>项目负责人：{{ datas.personInCharge[0].name }}</div>
            </el-col>
            <el-col :span="8">
              <div>联系电话：{{ datas.personInCharge[0].phoneNum }}</div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="16">
              <div>项目资质业务范围：{{ serviceRanges.join('') }}</div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import radiateProjectsResult from '@/views/radiateProjectsResult/index';
import { getQualifies, getCheckAssessment } from "@/api/serviceOrg";
export default {
  components: {
    radiateProjectsResult
  },
  props: {
    projectDetail: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      serviceMatters: [{
        label: '现场调查',
        value: '现场调查'
      }, {
        label: '现场检测',
        value: '现场检测'
      }, {
        label: '实验室检测',
        value: '实验室检测'
      }, {
        label: '评价',
        value: '评价'
      }],
      hazardType: [{
        label: 'xγ射线',
        value: 'xγ射线'
      }, {
        label: 'αβ表面污染',
        value: 'αβ表面污染'
      }, {
        label: '中子',
        value: '中子'
      }, {
        label: '电子射线',
        value: '电子射线'
      }, {
        label: '质子',
        value: '质子'
      }, {
        label: '重离子',
        value: '重离子'
      }, {
        label: '其他放射性因素',
        value: '其他放射性因素'
      },],
      date: '',
      activeName: 'info1',
      personsOfProject: [],
      qualifiesCode: '', // 证书编号
      checkAssessment: null, // 检测结果
      report: [], // 职业病危害检测与评价报告书
      resultDes: { // 技术服务结果描述
        totle: 0, // 共检测岗位或工种数量
        abnormal: 0,
        hazards: [], // 超标危害因素类型
      },
      allHazards: ['粉尘', '化学因素', '物理因素', '放射性因素', '生物因素', '其他因素。'],
      serviceArea: ['采矿业', '化工、石化及医药', '冶金、建材', '机械制造、电力、纺织、建筑和交通运输等行业领域', '核设施', '核技术应用'],
      statistics: [], // 危害因素检测数据 统计 点数 超标点数
      serviceRanges: '', // 机构的资质业务范围
      EnterpriseID: '',
      jobHealthId: '',
      serviceOrgInfo: {},
    }
  },
  watch: {
    async datas(data) {
      console.log('项目信息：', data);
      if (data) {
        this.personsOfProject = data.personsOfProject || [];
        this.report = data.report.map(ele => {
          return {
            url: data.file_static_path + '/' + data.EnterpriseID + '/' + ele.url,
            fileName: ele.fileName,
          }
        });
        this.getQualifiesCode();
        // this.getCheckAssessmentHandle(data._id); // 获取检测结果
        this.EnterpriseID = data.EnterpriseID;
        this.jobHealthId = data._id;
        this.year = data.completeTime ? new Date(data.completeTime).getFullYear() + '' : new Date(data.date).getFullYear() + '';
        // console.log(this.EnterpriseID, 'ddddddddddddddddddddddd');
      }
    },
    async show(show) {
      if (show) {
        // console.log(show, '1111111111111');
        this.date = new Date();
      }
    }
  },
  computed: {
    ...mapGetters(['serviceTypeOptions', 'serviceUnitTypeOptions']),
    datas() {
      return this.projectDetail.data || {};
    },
    show() {
      return this.projectDetail.show;
    },
    registerAddress() {
      let registerAddress = [];
      this.datas.workPlaces.forEach(item => {
        item.name = item.workAddName.replace(/\//g, '') + item.name;
        registerAddress.push(item.name);
      })
      return registerAddress;
    },
  },
  filters: {
    companyAddress(addrArr) {
      // console.log(1111111,addrArr)
      return addrArr.map(ele => ele.name).join(' ');
    }
  },
  methods: {
    getQualifiesCode() {
      getQualifies({
        organization: this.datas.serviceOrgID
      }).then(res => {
        if (res.status == 200 && res.data.length) {
          this.serviceOrgInfo = res.data[0];
          this.qualifiesCode = res.data.map(ele => ele.NO).join('; ');
          if (typeof res.data[0].lineOfBusiness === 'object') {
            const serviceRanges = [];
            res.data.forEach(ele => {
              serviceRanges.push(...ele.lineOfBusiness)
            })
            for (let i in serviceRanges) {
              if (serviceRanges[i].includes('核') && serviceRanges[i].includes('工业应用')) {
                serviceRanges[i] = '核技术应用'
              }
            }

            this.serviceRanges = [...new Set(serviceRanges)];

            console.log(this.serviceRanges, "sadaasdsad")
          } else {
            const serviceRanges = res.data.map(ele => ele.lineOfBusiness).join(',');
            this.serviceRanges = [...new Set(serviceRanges.split(','))];

            for (let i in this.serviceRanges) {
              if (this.serviceRanges[i].includes('核') && this.serviceRanges[i].includes('工业应用')) {
                this.serviceRanges[i] = '核技术应用'
              }
            }
          }
        } else {
          this.qualifiesCode = '';
          this.serviceRanges = [];
        }
      })
    },
    // 点击tab
    handleClick(tab) {
      this.activeName = tab.name;
    },
    closeDetail() {
      this.projectDetail.show = false;
      this.jobHealthId = '';
      this.activeName = 'info1';
    }
  },
};
</script>
<style scoped lang="scss">
#org,
#project {
  font-size: 14px;
  line-height: 30px;

  .lable {
    display: inline-block;
    width: 8em;
    color: #555;
  }

  .txt {
    font-weight: bold;
    color: #4b4b4e;

    .el-radio {
      margin-right: 10px;
    }
  }

  .txt.serviceRanges {
    vertical-align: top;
    display: inline-block;
    width: calc(100% - 8em);
  }
}

.info1 #org,
.info1 #project {
  .lable {
    width: 10em;
  }

  .files {
    p {
      color: #409EFF;
      padding: 0;
      margin: 0;
      line-height: 28px;
      font-size: 14px;
    }

    p:hover {
      cursor: pointer;
      font-weight: bold;
    }
  }
}

.companyIndustry {
  display: inline-block;
  vertical-align: middle;
}

.person-table {
  width: 100%;

  tr {
    th {
      text-align: center;
    }

    td {
      color: #606266;
      text-align: center;
    }
  }
}

.person-table tr:hover {
  background-color: #f5f7fa !important;
}

.wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba($color: (
      #000000),
    $alpha: 0.6
  );
z-index: 10000;
display: flex;
align-items: center;
justify-content: center;

.el-card__body {
  .text.item {
    height: calc(100% - 34px);
    width: calc(100% - 17px);
    overflow: auto;
    // height: 100%;
  }

  .text.item::-webkit-scrollbar {
    display: none;
  }

  .title {
    margin-bottom: 10px;
    font-weight: 700;
    // border-left: 3px solid #f56c6c;
    border-left: 0.275rem solid #25aff3;
    padding: 3px 10px;
    font-size: 15px;
    color: #333;
  }

  .header {
    position: relative;

    .close2 {
      padding: 3px 0;
      font-size: 1.3em;
      cursor: pointer;
      opacity: .7;
      z-index: 100;
      position: absolute;
      right: 0;
      top: 0;

      :hover {
        opacity: .9;
      }

    }
  }
}

.box {
  // padding: 10px 3% 30px;
  box-sizing: border-box;
  width: 70%;
  min-width: 500px;
  max-height: 88vh;
  overflow: hidden;
  position: relative;

  .el-row:hover {
    background-color: #f5f5f5;
  }

  .el-row {
    padding: 10px 0;
  }
}
}

.text {
  font-size: 14px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  clear: both
}

[v-cloak] {
  display: none;
}

.resultTable {
  width: 70%;
  margin-top: 30px;
  margin-left: 150px;
  // border: 1px solid #EBEEF5;
  border-collapse: collapse;
  border-color: #C0C4CC;

  ::v-deep .custom-input .el-input__inner {
    border-radius: 0;
    border: 0px;
    border-bottom: 1px solid #DCDFE6;
    padding: 0 4px;
    text-align: center;
  }

  ::v-deep .custom-input .el-input__inner,
  .custom-input .is-disabled {
    background-color: transparent !important;
  }

  .single-checkbox {
    margin-right: 10px !important;
  }

  td {
    padding: 10px;
  }

  .firstTd {
    width: 200px;
  }
}

.td-header {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.check-border {
  border: 1px solid #909399;
  margin-right: 10px;
  width: 16px;
  height: 16px;
  flex-shrink: 0;

  i {
    display: flex;
    padding: 0;
  }

  // align-items: center;
  // justify-content: center;
}

::v-deep .el-divider {
  width: 40%;
}

.divider {
  display: flex;
  justify-content: center;
}

.info2 {
  padding-left: 20px;
}
::v-deep .el-checkbox__input.is-disabled + .el-checkbox__label {
  color: #808080 !important;
}
::v-deep .el-radio__input.is-disabled + .el-radio__label {
  color: #808080 !important;
}

::v-deep .el-checkbox__input.is-disabled.is-checked + .el-checkbox__label {
  color: #1890ff !important;
}
::v-deep .el-radio__input.is-disabled.is-checked + .el-radio__label {
  color: #1890ff !important;
}

::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}
::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}
::v-deep .el-input.is-disabled .el-input__inner,textarea:disabled{
  color: #606266 !important;
}

</style>

