<template>
  <div class="pageBody">
    <el-page-header @back="$router.go(-1)" content="退回项目历史记录"></el-page-header><br/>
    <el-row class="search">
      <el-input style="width: 200px" size="small" placeholder="关键字搜索" v-model="searchParams.keyword" suffix-icon="el-icon-search" @keyup.enter.native="getData"></el-input>
    </el-row><br/>

    <el-table
      :max-height="tableMaxHeight"
      :data="tableData"
      style="width: 100%">
      <el-table-column width="60" label="序号" type="index" fixed align="center">
        <template scope="scope"> {{ searchParams.pageSize * (searchParams.curPage - 1) + scope.$index + 1 }} </template>
      </el-table-column>
      <el-table-column prop="companyName" label="用人单位" fixed min-width="180px" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="companyContact" label="联系人" width="80px"></el-table-column>
      <el-table-column prop="companyContactPhoneNumber" label="联系号码" width="120px"></el-table-column>
      <el-table-column prop="name" label="申报机构" min-width="150px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="projectNumber" label="项目编号" width="140px"></el-table-column>
      <el-table-column label="项目负责人" width="100px" align="center">
        <template slot-scope="scope">
          {{scope.row.personInCharge[0]?scope.row.personInCharge[0].name || '':''}}
        </template>
      </el-table-column>
      <el-table-column label="联系电话" width="120px">
        <template slot-scope="scope">
          {{scope.row.personInCharge[0]?scope.row.personInCharge[0].phoneNum || '':''}}
        </template>
      </el-table-column>
      <el-table-column label="上报时间" width="100px" prop="applyTime" fixed="right">
        <template slot-scope="scope">
          {{ formatTime(scope.row.applyTime) }}
        </template>
      </el-table-column>
      <el-table-column label="完成时间" width="100px" prop="completeTime" fixed="right">
        <template slot-scope="scope">
          {{ formatTime(scope.row.completeTime) }}
        </template>
      </el-table-column>
      <el-table-column label="退回时间" width="100px" prop="$set.giveBackTime" fixed="right">
        <template slot-scope="scope">
          {{ formatTime(scope.row.$set.giveBackTime) }}
        </template>
      </el-table-column>
      <el-table-column label="退回备注" width="100px" prop="$set.giveBackRemark" fixed="right">
        <template slot-scope="scope">
          <el-popover
            placement="bottom-start"
            title="退回备注："
            width="400"
            trigger="hover">
            <div class="RenderDv">
              <div v-html="scope.row.$set.giveBackRemark"></div>
              <div class="tip" @click="lookUpRemark(scope.row.$set.giveBackRemark)">点击查看完整备注</div>
            </div>

            <el-link slot="reference">查看详情</el-link>
          </el-popover>
        </template>
      </el-table-column>

    </el-table>

    <div class="pagination">
      <el-pagination
        background
        @size-change="sizeChange"
        @current-change="getData"
        :page-sizes="[ 10, 30, 50 ]"
        :current-page.sync="searchParams.curPage"
        :page-size="searchParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchParams.allSize">
      </el-pagination>
    </div>

    <el-dialog title="退回备注" :visible.sync="showGiveBackRemark" :close-on-click-modal="false">
      <div class="remark_details">
        <div v-html="giveBackRemarcontent"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showGiveBackRemark = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { giveBackRecord } from "@/api/adminorg";
import { mapGetters } from 'vuex';
import moment from 'moment'

export default {
  data() {
    return {
      searchParams: {
        keyword: '',
        curPage: 1,
        pageSize: 10,
        allSize: 0,
        source: 'radiate'
      },
      tableData: [],
      assessExposeLevel: ['低风险', '中风险', '高风险'],
      assessmentResult: ['甲类', '乙类', '丙类', ''],
      assessManageLevel: ["A级", "B级", "C级"],
      showGiveBackRemark: false,
      giveBackRemarcontent: '',
    }
  },
  computed: {
    ...mapGetters(['serviceAreaOptions']),
    tableMaxHeight() {
      return window.innerHeight - 280
    }
  },
  async created() {
    await this.getData()
  },
  methods: {
    lookUpRemark(content) {
      this.giveBackRemarcontent = ''
      this.showGiveBackRemark = true
      this.giveBackRemarcontent = content
    },
    formatTime(time) {
      if (!time) return '-'
      return moment(time).format('YYYY-MM-DD')
    },
    getServiceArea(serviceAreas){
      const serviceAreas2 = []
      this.serviceAreaOptions.forEach(item=>{
        serviceAreas.forEach(item2=>{
          if(item.value === item2){
            serviceAreas2.push(item.label)
          }
        })
      })
      return serviceAreas2.join('；')
    },
    async getData() {
      const res = await giveBackRecord(this.searchParams)
      if (res && res.status === 200) {
        this.searchParams.allSize = res.data.allSize
        this.tableData = res.data.doc
      }
    },
    async sizeChange(size) {
      this.searchParams.pageSize = size
      await this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
  .pageBody {
    padding: 16px;
  }
  .pagination {
    padding: 20px;
    text-align: center;
  }
  .RenderDv {
  overflow: hidden;
  height: 150px;
  position: relative;

  .tip {
    position: absolute;
    background: rgba(0,0,0,0.4);
    bottom: 0;
    width: 100%;
    text-align: center;
    color: white;
    padding: 4px 0;
    cursor: pointer;
  }
  img {
    width: 100%;
  }
}
.remark_details {
  height: 400px;
  overflow: scroll;

}
</style>