<template>
  <div>
    <transition name="el-zoom-in-center">
      <Details v-show="showDetail" :projectDetail="projectDetail" />
    </transition>

    <GiveBackDetails v-if="givebackProject.show" :dialogTableVisible="givebackProject.show" :projectDetail="givebackProject.data" @success="successGiveback" @close="givebackProject.show = false"/>
  
    <el-row class="dr-datatable">
      <TopBar :searchParams="searchParams" @search="getLits" :totleCount="totleCount" @reset="reset" @downLoad="downLoadAll" @openGiveBackRecord="openGiveBackRecord"></TopBar>
      <DataTable :searchParams="searchParams" :lists="lists" :projectDetail="projectDetail" @openGiveBackDetails="openGiveBackDetails"></DataTable>
      <Pagination :searchParams="searchParams" v-show="lists.length > 0" :totleCount="totleCount"></Pagination>
    </el-row>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import Details from "./details";
import GiveBackDetails from './giveBackDetails'
import DataTable from "./dataTable.vue";
import TopBar from "../common/TopBar.vue";
import Pagination from "../common/Pagination.vue";
import { projectsList } from '@/api/adminRadiateProjects'
import moment from 'moment';

export default {
  name: "serviceProject",
  data() {
    return {
      lists: [], 
      searchParams: {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: '',
        org: '', // 机构 id
        orgCode: '', // 机构的组织结构代码
        company: '', // 用人单位/企业 统一社会信用代码
        industry: [], // 所属行业
        status: '', // 上报状态
        dates: [],
        dateType: 'completeTime',
        serviceAreas: [],
        year: new Date().getFullYear() + '',
      },
      originSearchParams: null,
      totleCount: 0, // 总条数
      completed: 0, // 已完成项目数
      updated: 0, // 已更新项目数
      projectDetail: {
        show: false,
        data: []
      },
      givebackProject: {
        show: false, // 是否打开退回窗口
        data: {}
      }, // 退回窗口
      // sidebarOpened: false,
      device: "desktop",
    };
  },
  components: {
    DataTable,
    TopBar,
    Details,
    Pagination,
    GiveBackDetails
  },
  watch: {
    searchParams: {
      deep:true,
      handler:function(newV,oldV){
        this.getProjectLits();
      }
    },
    
  },
  created() {
    this.originSearchParams = JSON.parse(JSON.stringify(this.searchParams));
    if(location.search){
      const searchArr = location.search.replace('?', '').split('&');
      if(searchArr.length){
        const dates = [];
        searchArr.forEach(item => {
          switch(item.split('=')[0]){
            case 'gt': dates[0] = item.split('=')[1]; break;
            case 'lt': dates[1] = item.split('=')[1]; break;
            case 'org': this.searchParams.org = item.split('=')[1]; break;
            case 'kw': this.searchParams.keyWords = decodeURIComponent(item.split('=')[1]); break;
            case 'year': this.searchParams.year = item.split('=')[1] + ''; break;
          }
        });
        if(dates[0]) this.searchParams.dates = [new Date(dates[0]), new Date(dates[1])];
      }
    }else if(!location.hash.replace('#', '')){
      this.getProjectLits(); // 获取项目列表
    }

    const { year, area_code, area_name } = this.$route.query
    if (year) {
      this.searchParams.dates = [ moment(year), moment(year).add('y', 1) ]
    }
    if (area_name) {
      this.regAddrPlaceholder = area_name
    }
    if (area_code) {
      this.searchParams.regAddr = [{ area_code }]
    }
  },
  computed: {
    ...mapGetters(['serviceAreaOptions', 'adminProjectsList']),
    showDetail(){
      return this.projectDetail.show;
    },
    formState() {
      return this.$store.getters.adminProjectsFormState;
    },
  },
  methods: {
     // 获取项目列表
    getProjectLits() {
        projectsList(this.searchParams).then(res=>{
        if(res.status == 200){
          const reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
          this.lists = res.data;
          for(let i = 0;i< this.lists.length;i++){
            let item = this.lists[i];
            if(item.companyContactPhoneNumber){
              item.companyContactPhoneNumber2 = item.companyContactPhoneNumber;
              item.companyContactPhoneNumber = item.companyContactPhoneNumber.toString().replace(reg, '$1***$2');
            }
          }
          this.totleCount = +res.count.total || 0;
          this.completed = +res.count.completed || 0;
          this.updated = +res.count.updated || 0;
        }
      })
    },
    // 点击重置
    reset(){
      this.searchParams = JSON.parse(JSON.stringify(this.originSearchParams));
    },
    // 点击下载/导出 下载所有数据
    downLoadAll(){
        projectsList({
        ...this.searchParams,
        limit: 100000
      }).then(async res => {
        const jsonData = res.data;
        console.log('jsonData:', jsonData);
        const assessExposeLevel = ['低风险', '中风险', '高风险'];
        const assessmentResult = ['甲类', '乙类', '丙类', ''];
        const assessManageLevel = ["A级", "B级", "C级"];
        // 列标题，逗号隔开，每一个逗号就是隔开一个单元格
        let str = `序号,用人单位,联系人,联系号码,申报机构,项目编号,项目负责人,联系电话,上报时间,完成时间\n`;
        // 增加\t为了不让表格显示科学计数法或者其他格式
        for(let i = 0 ; i < jsonData.length ; i++ ){
          const item = jsonData[i];
          // const serviceArea = item.serviceArea ? await this.getServiceArea(item.serviceArea): '';
          const personInCharge = item.personInCharge[0] || {};
          str += `${(i+1) + '\t'},`;
          str += `${item.companyName||'' + '\t'},`;
          str += `${item.companyContact||'' + '\t'},`;
          str += `${item.companyContactPhoneNumber || '' + '\t'},`;
          str += `${item.name || '' + '\t'},`;
          str += `${item.projectNumber || '' + '\t'},`;
          // str += `${serviceArea + '\t'},`;
          str += `${item.personInCharge[0].name || '' + '\t'},`;
          str += `${item.personInCharge[0].phoneNum || '' + '\t'},`;
          str += `${item.applyTime || '' + '\t'},`;
          str += `${item.completeTime || '' + '\t'},`;
          str += '\n';
        }
        // encodeURIComponent解决中文乱码
        const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(str);
        // 通过创建a标签实现
        const link = document.createElement("a");
        link.href = uri;
        // 对下载的文件命名
        link.download =  "技术服务项目.csv";
        link.click();
      })
    },
    getServiceArea(serviceAreas){
      const serviceAreas2 = []
      this.serviceAreaOptions.forEach(item=>{
        serviceAreas.forEach(item2=>{
          if(item.value === item2){
            serviceAreas2.push(item.label)
          }
        })
      })
      return serviceAreas2.join('；')
    },
    // 打开退回窗口
    openGiveBackDetails(data) {
      if (data._id) {
        this.givebackProject = {
          show: true,
          data: {
            _id: data._id,
            companyName: data.companyName,
            serviceName: data.serviceName,
            projectName: data.projectName,
            projectNumber: data.projectNumber
          }
        }
      }
    },
    successGiveback() {
      this.givebackProject.show = false
      this.getProjectLits()
    },
    openGiveBackRecord() {
      this.$router.push('/admin/adminRadiateProjects/givebackRecord')
    }
  },
};
</script>

<style lang="">
</style>