<template>
  <div>
    <el-table :data="tableData" max-height="600px" border>
      <el-table-column
        type="index"
        :index="indexMethod"
        width="80px"
        label="编号"></el-table-column>
      <el-table-column
        v-for="item in tableFields"
        :key="item.key"
        :prop="item.key"
        :label="item.label"
        >
        <template slot-scope="scope">
          <div>
            {{ scope.row[item.key] }}
          </div>
          
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

import tableFormFields from '@/utils/tableFormFields.js'
export default {
  props: {
    tableData: {
      type: Array,
      default: function() {
        return []
      }
    },
    tableType: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.tableFields = tableFormFields[this.tableType]
  },
  data() {
    return {
      tableFields: [],
    }
  },
}
</script>

<style lang="scss" scoped>
  ::v-deep .el-table {
    width: 70%;
    font-size: 12px;
    margin-left: 150px;
  }
</style>