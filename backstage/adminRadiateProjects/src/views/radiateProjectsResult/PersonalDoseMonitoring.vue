<template>
  <div>
    <div v-if="data.length>0">
    <el-tabs v-model="curTab" tab-position="top">
      <el-tab-pane v-for="(item, index) in data" :key="index" :name="item.name">
        <span slot="label">{{item.name}} </span>
        <div class="resultTable">
        <div>检测结果：</div>
        <TableForm :tableData="item.formData" :tableType="typeItem.key" />
        </div>
      </el-tab-pane>
    </el-tabs> 
    </div>
    <div v-else class="noData">
    <img src="../../assets/nodata.png"></img>
    <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import TableForm from './TableForm'
import { descriptionFields } from '@/utils/tableFormFields.js'
import tableFormFields from '@/utils/tableFormFields.js'

export default {
  name: 'radProtDetectInWrkplc',
  components: {
    TableForm,
  },
  props: {
    typeItem: {
      type: Array
    },
    data: {
      type: Array
    },
  },
  data() {
    return {
      descriptionFields: [],
      curTab: '',
      dialogVisible: false,
      checkResult: null,
      edit: false
    }
  },
  created() {
    if (this.typeItem && this.typeItem.key && descriptionFields[this.typeItem.key]) {
      this.descriptionFields = descriptionFields[this.typeItem.key]
    }
  },
  mounted(){
    if (this.data && this.data[0] && this.data[0].name) {
      this.curTab = this.data[0].name
    }
    // 只监听一次
    const unWatch = this.$watch("data", (newVal, oldVal) => {
      if (newVal && newVal[0] && newVal[0].name) {
        this.curTab = newVal[0].name
      }
      unWatch(); // 取消监听
    });
  },
  methods: {
    
  },
}
</script>

<style lang="scss" scoped>
  .resultTable {
    margin-top: 20px;
  }
    .noData{
  position: relative;
  top: 0;
  right: 0;
  bottom: 15px;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img{
    height: 26%;
  }
  p{
    font-size: 14px;
    margin: 30px 0 0;
    color: #424242;
  }
}
</style>