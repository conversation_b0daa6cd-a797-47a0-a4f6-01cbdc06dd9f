<template>
  <div class="page">
    <div class="radiateTitle">放射卫生检测结果</div>
    <el-form label-position="left">
      <el-form-item label="放射卫生报告书：">
        <div style="margin-left: 123px;">
          <div v-for="(item, i) in data.report" :key="i">
            {{ item.name }}
            <el-button size="mini" type="primary" @click="handlePreview(item)">预览</el-button>
            <el-button size="mini" type="primary" @click="downLoad(item)">下载</el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <page-header>
      <div class="project-info">
        <div class="project-info-item">
          <span class="info-item-label">受检单位：</span>
          <span class="info-item-label">
            {{ data.companyName }}
          </span>
        </div>
        <div class="project-info-item">
          <span class="info-item-label">项目编号：</span>
          <span class="info-item-label">
            {{ data.projectNumber }}
          </span>
        </div>
        <div class="project-info-item">
          <span class="info-item-label">项目名称：</span>
          <span class="info-item-label">
            {{ data.projectName }}
          </span>
        </div>
        <div class="project-info-item">
          <span class="info-item-label">设备数量：</span>
          <span class="info-item-label">
            {{ equipmentNum + '台' }}
          </span>
        </div>
        <div class="project-info-item">
          <span class="info-item-label">合格数量：</span>
          <span class="info-item-label">
            {{ qualifiedNum + '台' }}
          </span>
        </div>
      </div>
    </page-header>
    <el-tabs v-model="curTab" tab-position="top">
      <el-tab-pane :name="item.key" v-for="item in typeTabs" :key="item.key" :label="item.label">
        <div class="pane">
          <RadProtDetectInWrkplc :typeItem="item" :data="resultData.radProtDetectInWrkplc"
            v-if="curTab === 'radProtDetectInWrkplc'" />
          <EqptQualCtrlDetect :typeItem="item" :data="resultData.eqptQualCtrlDetect"
            v-if="curTab=== 'eqptQualCtrlDetect'" />
          <PersonalDoseMonitoring :typeItem="item" :data="resultData.personalDoseMonitoring"
            v-if="curTab === 'personalDoseMonitoring'" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import pageHeader from "../common/pageHeader.vue";
import { getRadiateProjectResult } from "@/api/adminRadiateProjects";
import TableForm from './TableForm'
import EqptQualCtrlDetect from './EqptQualCtrlDetect'
import RadProtDetectInWrkplc from './RadProtDetectInWrkplc'
import PersonalDoseMonitoring from './PersonalDoseMonitoring'


export default {
  components: {
    pageHeader,
    TableForm,
    RadProtDetectInWrkplc,
    EqptQualCtrlDetect,
    PersonalDoseMonitoring,
  },
  async created() {
    await this.getData()
  },
  data() {
    return {
      showPreview: true,
      curTab: '', // 当前类别
      typeTabs: [],
      radiateProject: {},
      resultData: {},
      radProtDetectInWrkplc: [
        {
          name: '3号机房（X射线影像诊断）',
          parameters: {},
          result: []
        }
      ],
    }
  },
  props: ['data'],
  watch: {
    curTab: {
      handler: function (newVal, oldVal) {
       console.log('curTab', newVal, oldVal)
        if (newVal && newVal !== '0') {
        } else {
        }
      },
      immediate: true,
    },
    async data(data) {
      await this.getResult();
    }
  },
  methods: {
    async getData() {
      await this.getResult()
    },
    async getResult() {
      const radiateProjectId = this.data._id;
      const res = await getRadiateProjectResult({ id: radiateProjectId })
      if (res.data) {
        this.resultData = res.data
        this.radiateProject = res.data;
        this.equipmentNum = this.radiateProject.radProtDetectInWrkplc.length + this.radiateProject.eqptQualCtrlDetect.length + this.radiateProject.personalDoseMonitoring.length;

        const types = [
          {
            key: 'radProtDetectInWrkplc',
            label: '防护检测',
          },
          {
            key: 'eqptQualCtrlDetect',
            label: '质量控制',
          },
          {
            key: 'personalDoseMonitoring',
            label: '个人剂量监测',
          },
        ]
        this.typeTabs = types;
        this.curTab = types[0].key
      }
    },
    handlePreview(file) {
      let url = file.url;
      const fileSplite = file.name.split(".")
      if (fileSplite[fileSplite.length - 1] === "pdf") {
        const a = document.createElement("a")
        a.href = url
        a.target = "_blank"
        a.click()
      } else if (
        fileSplite[fileSplite.length - 1] === "doc" ||
        fileSplite[fileSplite.length - 1] === "docx"
      ) {
        window.open(
          "https://view.officeapps.live.com/op/view.aspx?src=" +
          window.location.origin + url
        )
      } else {
        this.dialogImageUrl = file.raw ? URL.createObjectURL(file.raw) : file.formatUrl
        this.dialogImageName = file.name
        this.dialogVisible = true
      }
    },
    downLoad(file) {
      const a = document.createElement("a");
      a.setAttribute("download", file.name);
      a.setAttribute("href", file.url);
      a.click();
    }
  },
  // 计算属性
  computed: {
    // 合格数量
    qualifiedNum() {
      return this.radiateProject.qualifiedNum;
    },
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding: 20px;
}

.project-info {
  // margin: 20px 0;
  display: flex;

  .project-info-item {
    margin-right: 20px;
    // font-weight: bold;
    color: #606266;

    .info-item-label {
      color: #000;
      font-weight: bold;
      font-size: 14px;
    }
  }
}

.pane {
  padding: 0 20px;
}

.radiateTitle {
  width: 268px;
  height: 25px;
  // 字重Semibold
  font-family: PingFangSC;
  font-size: 18px;
  font-weight: 600;
  font-stretch: normal;
  line-height: 25px;
  letter-spacing: 0px;
  color: #000000;
  margin: 20px 0;
}

.table-title {
  margin-bottom: 20px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 16px;
  line-height: 1.5;
  margin-top: 20px;
}

::v-deep .el-button {
  margin: 0 5px;
}

.project-menu {
  display: flex;
  justify-content: space-between;
}</style>