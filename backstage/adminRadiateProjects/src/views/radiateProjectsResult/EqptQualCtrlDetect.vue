<template>
  <div>
    <div v-if="data.length>0">
    <el-tabs v-model="curTab" tab-position="top">
      <el-tab-pane v-for="(item, index) in data" :key="index" :name="item.name">
        <span slot="label">{{item.name}} </span>
        <div>设备参数：</div>
        <a-descriptions bordered :column="2" size="smaller">
          <a-descriptions-item :label="descriptionField.label" :span="descriptionField.span" v-for="descriptionField in descriptionFields" :key="descriptionField.key">
            <template v-slot:label>
              <span class="labelStyle">{{descriptionField.label}}</span>
            </template>
            <span v-if="descriptionField.type === 'Boolean'">
              {{ item.equipParams[descriptionField.key] ? '有' : '无' }}
            </span>
            <span v-else>{{ item.equipParams[descriptionField.key] }}</span>
          </a-descriptions-item>
        </a-descriptions>

        <div class="projectDet">
          <div>通用项目检测：</div>
        <TableForm :tableData="item.formData" tableType="eqptQualCtrlDetect" />
        </div>

        <div class="projectDet">
          <div>专用项目检测：</div>
        <TableForm :tableData="item.specializedFormData" :tableType="typeItem.key" />
        </div>

      </el-tab-pane>
    </el-tabs>
    </div>
    <div v-else class="noData">
    <img src="../../assets/nodata.png"></img>
    <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import TableForm from './TableForm'
import { descriptionFields } from '@/utils/tableFormFields.js'
import tableFormFields from '@/utils/tableFormFields.js'
export default {
  name: 'radProtDetectInWrkplc',
  components: {
    TableForm,
  },
  props: {
    typeItem: {
      type: Array
    },
    data: {
      type: Array
    },
  },
  data() {
    return {
      descriptionFields: [],
      curTab: '',
      dialogVisible: false,
      checkResult: null,
      edit: false
    }
  },
  created() {
    if (this.typeItem && this.typeItem.key && descriptionFields[this.typeItem.key]) {
      this.descriptionFields = descriptionFields[this.typeItem.key]
    }
  },
  mounted(){
    if (this.data && this.data[0] && this.data[0].name) {
      this.curTab = this.data[0].name
    }
    // 只监听一次
    const unWatch = this.$watch("data", (newVal, oldVal) => {
      if (newVal && newVal[0] && newVal[0].name) {
        this.curTab = newVal[0].name
      }
      unWatch(); // 取消监听
    });
  },
  methods: {
    
  },
}
</script>

<style lang="scss" scoped>
  .projectDet {
    margin-top: 20px;
  }
  ::v-deep .ant-descriptions-item-label {
    font-size: 12px;
    background-color: #fff;
  }
  ::v-deep .ant-descriptions-view {
    margin-left: 150px;
    width: 70%;
  }
    .noData{
  position: relative;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img{
    height: 16%;
  }
  p{
    font-size: 14px;
    margin: 30px 0 0;
    color: #424242;
  }
}
</style>