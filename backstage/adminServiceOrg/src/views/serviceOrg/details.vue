<template>
  <div class="wrap">
    <el-card class="box-card" shadow="always">
      <div slot="header" class="clearfix header">
        <span style="font-size:1.2em;font-weight: bold;color:#409EFF">{{ datas.name || '' }}</span>
        <span class="el-icon-error close2" @click="closeDetail"></span>
      </div>
      <div class="text item">
        <el-form :model="formInline" class="demo-form-inline" label-width="150px">
          <el-form-item label="统一社会信用代码：">
            <el-input :value="datas.organization" readonly></el-input>
          </el-form-item>
          <el-form-item label="法人代表：">
            <el-input :value="datas.corp" readonly></el-input>
          </el-form-item>
          <el-form-item label="注册类型：">
            <el-input :value="datas.regType" readonly></el-input>
          </el-form-item>
          <el-form-item label="注册区域：">
            <el-input :value="datas.regAddr?datas.regAddr.join(''):''" readonly></el-input>
          </el-form-item>
          <el-form-item label="联系人：" >
            <el-input :value="datas.regPerson" readonly></el-input>
          </el-form-item>
          <el-form-item label="联系电话：">
            <el-input :value="datas.regPhone" readonly></el-input>
          </el-form-item>
        </el-form>

        <el-form ref="form" :model="form" label-width="150px">
          <el-form-item label="注册地址：">
            <el-input :value="(datas.regAddr ? datas.regAddr.join('') : '') + datas.address" readonly></el-input>
          </el-form-item>
          <el-form-item label="服务区域：">
            <el-input :value="managersAndArea" readonly></el-input>
          </el-form-item>
          <el-form-item label="经营范围：" class="lineOfBusiness">
            <el-input type="textarea" :value="datas.lineOfBusiness || ''" autosize readonly></el-input>
          </el-form-item>
          <el-form-item label="营业执照：">
            <el-image v-if="datas.img" style="width: 100px; height: 80px; border-radius: 4px;"
                :src="datas.img" 
                :preview-src-list="imgSrcList">
            </el-image>
            <el-input value="未上传" readonly v-else></el-input>
          </el-form-item>
          <el-form-item label="资质证书：">           
            <ul class="el-upload-list">
              <li v-if="qualifieArr.length === 0">未上传</li>
              <li class="el-upload-list__item is-success" v-for="(item,index) in qualifieArr" :key="item._id">
                <el-image style="width: 100px; height: 100px"
                    ref="previewImage"
                    :src="item.img" 
                    :key="index"
                    :preview-src-list="[item.img]">
                </el-image>
                <div class="desc">
                    <p class="title2"> {{item.mechanism_name}} </p>
                    <p>等级： {{item.level || ''}} <span style="float: right; margin-right: 15px">有效期至：{{ item.validTime }} </span> </p>
                    <p class="lineOfBusiness">资质业务范围：{{ item.lineOfBusiness.join('，') }}</p>
                </div>
                <label class="el-upload-list__item-status-label">
                    <i class="el-icon-upload-success el-icon-check" style="color: #fff;"></i>
                </label>
              </li>
            </ul>
          </el-form-item>
        </el-form>
      </div>
  </el-card>
</div>
  
</template>
<script>
import { getQualifies } from "@/api/serviceOrg";
export default {
  name:"details",
  props: {
    orgDetail: {
      type: Object,
      default: {}
    }
  },
  data(){
    return {
      qualifieArr: [],
      imgSrcList: [],
      qualifiesSrcList: [],
      managersAndArea: '未登记', // 服务区域
    }
  },
  watch: {
   async datas(data){
      console.log('营业执照信息：', data);
      // 服务区域处理
      if(data.managersAndArea && data.managersAndArea.length){
        const managersAndArea = [];
        for(let i = 0; i < data.managersAndArea.length; i++){
          if(data.managersAndArea[i].isactive){
            const area = data.managersAndArea[i].AreaRegAddr.join('');
            if(!managersAndArea.includes(area)) managersAndArea.push(area);
          }
        }
        this.managersAndArea = managersAndArea.join('; ');
      }
      if(!this.managersAndArea) this.managersAndArea = '未登记';
      if(data.img) this.imgSrcList = [ data.img ];
      if(data.qualifies && data.qualifies.length){
        // 获取资质信息
        const idsArr = data.qualifies.map(ele => ele.id);
        await  getQualifies({idsArr}).then(res => {
          if(res.status == 200){
            this.qualifieArr = res.data;
            // console.log('qualifieArr:', this.qualifieArr);
            let tempArr = [];
            this.qualifieArr.forEach((ele) => {
              if(ele.img&&ele.status=='true') tempArr.push(ele.img);
            })
            this.qualifiesSrcList = tempArr;
          }
        })
      }else{
        this.qualifieArr = [];
        this.qualifiesSrcList = [];
      }
    }
  },
  computed: {
    datas(){
      return this.orgDetail.data || {}
    }
  },
  methods: {
    // 预览对应当前图片列表 - 资质证书
    handlePreviewImage(index) {
      console.log(index)
      let tempImgList = [...this.qualifiesSrcList];//所有图片地址
      if (index == 0) return tempImgList;
      // 调整图片顺序，把当前图片放在第一位
      let start = tempImgList.splice(index);
      console.log(start,"第一个没了")
      let remain = tempImgList.splice(0, index);
      console.log(remain)
      return start.concat(remain);//将当前图片调整成点击缩略图的那张图片
    },
    closeDetail(){
      this.orgDetail.show = false;
    }
  },
};
</script>

<style scoped lang="scss">
  .demo-form-inline .el-form-item{
    width: 50%;
    display: inline-block;
  }
  .wrap{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: (#000000), $alpha: 0.6);
    z-index: 2002;
    display: flex;
    align-items: center;
    justify-content: center;
    .box-card{
      padding: 10px 3% 30px;
      box-sizing: border-box;
      width: 70%;
      min-width: 500px;
      max-height: 88vh;
      overflow-y: hidden;
      position: relative;
      .el-row:hover{
        background-color: #f5f5f5;
      }
      
      .close2{
        float: right!important;; 
        padding: 3px 0;
        font-size: 1.3em;
        cursor: pointer;
        opacity: .7;
        :hover{
          opacity: .9;
        }
      }
      .el-row{
        padding: 10px 0;
      }
    }
  }
   .text {
    font-size: 15px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

  .el-upload-list{
    padding-bottom: 30px;
  }
  .el-upload-list li .el-image{
        vertical-align: middle;
        margin-right: 20px;
    }
    .el-upload-list .desc{
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 130px)
    }
    .el-upload-list .desc .title2{
        font-weight: bold;
        font-size: 15px;
        text-decoration: none;
        color: #606266;
    }
    .el-upload-list .desc p{
        margin: 0;
        font-size: 13px;
    }
    .el-image-viewer__mask{
        opacity: 0.8;
    }
    .el-image-viewer__btn.el-image-viewer__close{
        color: #ddd;
    }

    [v-cloak]{
        display: none;
    }

    .el-upload-list .el-upload-list__item {
      overflow: hidden;
      /* z-index: 0; */
      background-color: #fff;
      border: 1px solid #c0ccda;
      border-radius: 6px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      margin-top: 10px;
      padding: 10px 10px 10px 10px;
  }
  .el-upload-list .el-upload-list__item-status-label {
    position: absolute;
    right: -17px;
    top: -7px;
    width: 46px;
    height: 26px;
    background: #13ce66;
    text-align: center;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-box-shadow: 0 1px 1px #ccc;
    box-shadow: 0 1px 1px #ccc;
  }
  .el-upload-list .el-upload-list__item-status-label i {
    font-size: 12px;
    margin-top: 12px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }
  .lineOfBusiness{
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    
  }
  

  /* 滚动槽 */
  ::-webkit-scrollbar-track {
  border-radius:10px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
  border-radius:10px;
  /* background:black; */
  }
</style>

