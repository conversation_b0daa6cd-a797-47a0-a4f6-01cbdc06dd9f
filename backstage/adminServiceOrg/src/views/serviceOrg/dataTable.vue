<template>
  <el-row>
    <!-- <el-alert center type="success" :closable="false">
      <template slot="title">
        共有 <B>{{ totleCount || 0 }}</B> 家机构（灰底的为未注册机构）
      </template>
    </el-alert> -->

    <el-table align="center" v-loading="loading" ref="multipleTable" :data="orgList" tooltip-effect="dark" style="width: 100%" row-key="_id" default-expand-all :row-class-name="tableRowClassName":max-height="tableHeight" header-cell-style="background-color: #FAFAFA;height:60px">
      <el-table-column prop="name" label="机构名称" fixed min-width="110px">
        <template slot-scope="scope">
          <div @click="showDetail(scope.row)">{{ scope.row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="corp" label="法人代表" width="100px" align="center">
        <template slot-scope="scope">
          <div @click="showDetail(scope.row)">
            {{ scope.row.corp }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="organization" label="统一社会信用代码" min-width="100px">
        <template slot-scope="scope">
          <div @click="showDetail(scope.row)">
            {{ scope.row.organization }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="regAddr" label="注册地区" min-width="110px">
        <template slot-scope="scope">
          <div @click="showDetail(scope.row)">
            {{ scope.row.regAddr ? scope.row.regAddr.join('') : '' }} 
          </div>
        </template>
      </el-table-column>
      <el-table-column label="联系人" width="90px" prop="regPerson">
        <template slot-scope="scope">
          <div v-if="scope.row.administrators&&scope.row.administrators.length">{{ scope.row.administrators[0].name||scope.row.administrators[0].userName }}</div>
          <div v-else-if="scope.row.isReg">{{scope.row.isReg.regPerson}}</div>
          <div v-else-if="scope.row.contacts && scope.row.contacts[0]">{{scope.row.contacts[0].regPerson}}</div>
          <div v-else-if="scope.row.managersAndArea[0] && scope.row.managersAndArea[0].managers">{{scope.row.managersAndArea[0].regPerson}}</div>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" width="130px" prop="regPhone">
        <template slot-scope="scope">
          <div v-if="scope.row.administrators&&scope.row.administrators.length" style="cursor:pointer" @click="showPhone(scope.row,'administrators')">
          {{ scope.row.administrators[0].phoneNum }}
          </div>
          <div v-if="scope.row.isReg" @click="showPhone(scope.row,'isReg')" style="cursor:pointer">{{scope.row.isReg.regPhone}}</div>
          <div v-else-if="scope.row.contacts && scope.row.contacts[0]" @click="showPhone(scope.row,'contacts')" style="cursor:pointer">{{scope.row.contacts[0].regPhone}}</div>
          <div v-else-if="scope.row.managersAndArea[0] && scope.row.managersAndArea[0].managers" @click="showPhone(scope.row,'managersAndArea')" style="cursor:pointer">{{scope.row.managersAndArea[0].regPhone}}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="img" label="营业执照" width="100px" fixed="right">
        <template slot-scope="scope">
          <el-popover placement="top-start" :title="scope.row.name" width="400" trigger="hover">
            <el-image :src="scope.row.img" fit="contain" :preview-src-list="[scope.row.img]"></el-image>
            <el-image slot="reference" :src="scope.row.img" fit="contain" style="width: 36px; height: 22px;vertical-align: middle;"></el-image>
          </el-popover>
        </template>
      </el-table-column> -->
      <el-table-column prop="img" label="资质证书" width="100px">
        <template slot-scope="scope">
          <el-popover placement="top-start" :title="scope.row.name" width="400" trigger="hover" v-if="scope.row.qualifies.length" open-delay="400">
            <el-image :src="scope.row.qualifies[0] && scope.row.qualifies[0].img || ''" fit="contain" :preview-src-list="scope.row.qualifies.map(ele => ele.img||'')"></el-image>
            <el-image slot="reference" :src="scope.row.qualifies[0] && scope.row.qualifies[0].img || ''" fit="contain" style="width: 36px; height: 22px;vertical-align: middle;"></el-image>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label='资质有效期' width="110px">
        <template slot-scope="scope">
        <span
        v-if="scope.row.qualifies[0] && Date.parse(scope.row.qualifies[0].validTime)>Date.parse(new Date())"  style="color:gold">
          {{ scope.row.qualifies[0].validTime}}
        </span>
         <span
        v-if="scope.row.qualifies[0] && Date.parse(scope.row.qualifies[0].validTime)<Date.parse(new Date())"  style="color:red">
          {{ scope.row.qualifies[0].validTime}}
        </span>
        </template>
      </el-table-column>
      <!-- 此处项目数量只统计已报送的项目 -->
      <el-table-column label="项目数量" width="90px" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tooltip effect="dark" content="查看报送信息" placement="top" v-if="scope.row.projectsCount > 0">
            <el-link :underline="false" @click.stop.prevent="checkProjects(scope.row._id)">{{scope.row.projectsCount}}</el-link>
          </el-tooltip>
          <span v-else> {{ scope.row.projectsCount }} </span>
        </template> 
      </el-table-column>
      <el-table-column label="确认登记" width="110px" fixed="right" align="center">
        <template slot-scope="scope">
          <el-tooltip content="确认已知机构登记" placement="top" v-if="scope.row.isReg && !scope.row.isReg.isactive">
            <el-button size="mini" type="primary" @click.stop.prevent="countersign(scope.row._id, scope.row.isReg)">确认</el-button>
          </el-tooltip>
          <el-button size="mini" type="warning" @click.stop.prevent="countersign(scope.row._id, scope.row.isReg)" v-else-if="scope.row.isReg && scope.row.isReg.isactive">撤销</el-button>
        </template> 
      </el-table-column>
      <!-- <el-table-column  prop ='validTime' label='有效期' width="100px" align='center'>
        <template slot-scope="scope">
          <span  v-if="new Date(scope.row.validTime) < new Date()" style="color:red">{{doMomentmonthD(scope.row.validTime)}}</span>
          <span v-if="new Date(scope.row.validTime) >= new Date()" style="color:gold">{{doMomentmonthD(scope.row.validTime)}}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="操作" width="120" fixed="right">
        <template slot="header"> </template>
        <template slot-scope="scope">
          <el-tooltip content="机构详情" placement="top">
            <el-button size="mini" circle @click="detail(scope.$index, orgList)"><i class="el-icon-folder-opened"></i></el-button>
          </el-tooltip>
          <el-tooltip content="查看报送信息" placement="top" v-if="scope.row.projectsCount > 0">
            <el-button size="mini" circle @click="checkProjects(scope.row._id)"><i class="el-icon-s-order"></i></el-button>
          </el-tooltip>
        </template>
      </el-table-column> -->
    </el-table>
  </el-row>
</template>

<script>
import moment from 'moment';
import { goCountersign,checkPhoneLog } from '@/api/serviceOrg';

export default {
  props: {
    orgList: Array,
    orgDetail: Object,
    searchParams: Object,
    totleCount: Number,
  },
  data() {
    return {
      host: location.host,
      green: { color: "#13CE66" },
      red: { color: "#FF4949" },
      loading: false,
    };
  },
  methods: {
    // 查看手机号
    async showPhone(row,flag){
      let phoneNum = ''
      if(flag ==='administrators'){
        phoneNum = row.administrators[0].phoneNum2;
        row.administrators[0].phoneNum = row.administrators[0].phoneNum2;
      }
      if(flag === 'isReg'){
        phoneNum = row.isReg.regPhone2;
        row.isReg.regPhone = row.isReg.regPhone2;
        await checkPhoneLog({model:'ServiceUser',phoneNum})
      } 
      if(flag === 'contacts' && row.contacts.length && row.contacts[0].regPhone){
        phoneNum = row.contacts[0].regPhone2
        row.contacts[0].regPhone = row.contacts[0].regPhone2
        await checkPhoneLog({model:'ServiceOrg',phoneNum})
      }
      if(flag === 'managersAndArea' && row.managersAndArea && row.managersAndArea[0]&& row.managersAndArea[0].regPhone){
        phoneNum = row.managersAndArea[0].regPhone2
        row.managersAndArea[0].regPhone = row.managersAndArea[0].regPhone2
        await checkPhoneLog({model:'ServiceOrg',phoneNum})
      }
    },
    // 判断机构是否是自主增加服务区域来判断这一行的显示
    tableRowClassName({row, rowIndex}) {
      // const companyIn = row.isReg ? row.isReg.companyIn : (row.contacts && row.contacts[0] ? row.contacts[0].companyIn : row.managersAndArea[0].companyIn)
      if(row.companyIn){
        return 'cancel-row';
      }
      return '';
    },
    // 通过确认或撤销确认
    countersign(_id, isReg){
      isReg.isactive = !isReg.isactive;
      goCountersign({_id, isReg}).then(res => {
        if(res.status){
          const message = isReg.isactive ? '确认成功' : '撤销成功';
          this.$alert(message, '提示', {
            confirmButtonText: '确定',
          });
        }
      });
    },
    checkProjects(id){
      location.href = '/admin/adminServiceProject#' + id;
    },
    showDetail(row){
      this.orgDetail.show = true;
      this.orgDetail.data = row;
      console.log(this.orgDetail.data)
      if( new Date(this.orgDetail.data.qualifies[0].validTime) < new Date()){
         this.$alert('职业卫生技术服务机构资质证书已过期', '提示', {
          confirmButtonText: '确定'
        });
      }
    },
   
  },
   computed: {
    //将时间 转换成 简化的 
    doMomentyear(nowTime){
      return function(nowTime){
        return moment(nowTime).format('YYYY');
      }
    },
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
    tableHeight(){
      const innerHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return innerHeight - 220;
    },
  },
};
</script>
<style scoped>
  .el-icon-circle-close{
    color: #f5f5f5;
  }
  ::v-deep .el-table .cancel-row {
    background: rgb(204, 204, 204);
  }
  .orgName:hover{
    cursor: pointer;
    color: #409EFF;
  }
 .el-message-box {
    z-index: 9999;
  }
</style>