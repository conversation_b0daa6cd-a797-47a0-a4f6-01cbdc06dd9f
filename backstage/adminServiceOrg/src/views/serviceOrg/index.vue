<template>
  <div :class="classObj" class="adminUser">
    <div class="main-container">
      <transition name="el-zoom-in-center">
        <Details
          v-show="showDetail"
          :orgDetail="orgDetail"
          :orgList="orgList"
          ref="details"
        />
      </transition>

      <div class="dr-container">
        <div class="dr-datatable">
          <el-row>
            <el-col :span="24">
              <div class="dr-title">
                <span class="dr-title-left">
                  <span class="dr-title-text">查询条件</span>
                </span>
                <span class="dr-title-divider"><el-divider></el-divider></span>
                <span class="dr-title-btn"></span>
              </div>
              <TopBar
                :searchParams="searchParams"
                @search="getLits"
                @download="download"
                @reset="reset"
                @historyList="historyList"
                ref="detail"
              ></TopBar>
              <div class="dr-title">
                <span class="dr-title-left">
                  <span class="dr-title-text">机构列表</span>
                </span>
                <span class="dr-title-divider"><el-divider></el-divider></span>
                <span class="dr-title-btn"></span>
              </div>
              <DataTable
                :searchParams="searchParams"
                :orgList="orgList"
                :orgDetail="orgDetail"
                :totleCount="totleCount"
              ></DataTable>
            </el-col>
          </el-row>
          <Pagination
            :searchParams="searchParams"
            v-show="orgList.length > 0"
            :totleCount="totleCount"
          ></Pagination>
        </div>
      </div>
    </div>
    <el-dialog title="常用查询条件" :visible.sync="dialogVisible" width="800px">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="regAddr" label="机构服务地址">
          <template slot-scope="scope">
            <div>
              {{
                scope.row.regAddr
                  ? scope.row.regAddr[scope.row.regAddr.length - 1].merger_name
                  : ""
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="keyWords" label="搜索机构"> </el-table-column>
        <el-table-column prop="address" label="操作">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="seeLook(scope.row)"
              >选择</el-button
            >
            <el-button type="danger" size="mini" @click="delLook(scope.$index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import Details from "./details";
import DataTable from "./dataTable.vue";
import TopBar from "../common/TopBar.vue";
import Pagination from "../common/Pagination.vue";
import * as XLSX from "xlsx";
import { initEvent } from "@root/publicMethods/events";
import { serviceOrgs } from "@/api/serviceOrg";

export default {
  name: "serviceOrg",
  data() {
    return {
      orgList: [],
      searchParams: {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: "",
        status: 3, //申请状态
      },
      totleCount: 0, // 总条数
      orgDetail: {
        show: false,
        data: {},
      },
      sidebarOpened: false,
      device: "desktop",

      dialogVisible: false,
      tableData: [],
    };
  },
  components: {
    DataTable,
    TopBar,
    Details,
    Pagination,
  },
  watch: {
    searchParams: {
      deep: true,
      handler: function (newV, oldV) {
        this.getLits();
      },
    },
  },
  created() {
    this.getLits(); // 获取数据列表
    initEvent(this); // 初始化 hideSidebar & openSidebar
    this.tableData = localStorage.getItem("historyList")
      ? JSON.parse(localStorage.getItem("historyList"))
      : [];
  },
  computed: {
    showDetail() {
      return this.orgDetail.show;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    // 历史搜索记录
    historyList() {
      this.dialogVisible = true;
      let tableData = localStorage.getItem("historyList")
        ? JSON.parse(localStorage.getItem("historyList"))
        : [];
      // 去重实现
      this.tableData = [];
      this.tableData = tableData.filter(
        (item, index, self) =>
          index ===
          self.findIndex((t) => JSON.stringify(t) === JSON.stringify(item))
      );
      localStorage.setItem("historyList", JSON.stringify(this.tableData));
    },
    seeLook(row) {
      this.searchParams = row;
      this.getLits();
      this.dialogVisible = false;
    },
    delLook(index) {
      this.tableData.splice(index, 1);
      localStorage.setItem("historyList", JSON.stringify(this.tableData));
    },
    reset() {
      this.searchParams = {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: "",
      };
      this.getLits();
    },
    // 获取机构列表
    getLits() {
      serviceOrgs(this.searchParams).then((res) => {
        if (res.status == 200) {
          this.orgList = res.data;
          if (
            (this.searchParams.keyWords ||
              (this.searchParams.regAddr &&
                this.searchParams.regAddr.length > 0)) &&
            this.orgList.length > 0
          ) {
            let searchParams = JSON.parse(JSON.stringify(this.searchParams));
            this.tableData.push(searchParams);
            localStorage.setItem("historyList", JSON.stringify(this.tableData));
          }
          const reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
          for (let i = 0; i < this.orgList.length; i++) {
            let item = this.orgList[i];
            if (item.administrators && item.administrators.length) {
              item.administrators[0].phoneNum2 =
                item.administrators[0].phoneNum;
              item.administrators[0].phoneNum = item.administrators[0].phoneNum
                .toString()
                .replace(reg, "$1***$2");
            }
            if (item.isReg) {
              item.isReg.regPhone2 = item.isReg.regPhone;
              item.isReg.regPhone = item.isReg.regPhone
                .toString()
                .replace(reg, "$1***$2");
            } else if (
              item.contacts &&
              item.contacts.length &&
              item.contacts[0].regPhone
            ) {
              item.contacts[0].regPhone2 = item.contacts[0].regPhone;
              item.contacts[0].regPhone = item.contacts[0].regPhone
                .toString()
                .replace(reg, "$1***$2");
            } else if (
              item.managersAndArea &&
              item.managersAndArea[0] &&
              item.managersAndArea[0].regPhone
            ) {
              item.managersAndArea[0].regPhone2 =
                item.managersAndArea[0].regPhone;
              item.managersAndArea[0].regPhone =
                item.managersAndArea[0].regPhone
                  .toString()
                  .replace(reg, "$1***$2");
            }
          }
          // console.log("orgList3333333333333333:", this.orgList);
          this.totleCount = +res.count || 0;
        }
      });
    },
    download() {
      const downloadData = JSON.parse(JSON.stringify(this.orgList));

      // 定义表格的列
      const columns = [
        { title: "机构名称", key: "name" },
        { title: "法人代表", key: "corp" },
        { title: "统一社会信用代码", key: "organization" },
        { title: "注册地区", key: "regAddr" },
        { title: "联系人", key: "regPerson" },
        { title: "联系电话", key: "regPhone" },
        // { title: "资质证书", key: "qualifies" },
        { title: "资质有效期", key: "validTime" },
        { title: "项目数量", key: "projectsCount" },
      ];

      // 格式化数据
      const formattedData = downloadData.map((item) => {
        return {
          机构名称: item.name,
          法人代表: item.corp,
          统一社会信用代码: item.organization,
          注册地区: item.regAddr ? item.regAddr.join("") : "",
          联系人: this.getRegPerson(item),
          联系电话: this.getRegPhone(item),
          // qualifies: item.qualifies && item.qualifies.length > 0 ? item.qualifies[0].img : '',
          资质有效期:
            item.qualifies[0] &&
            Date.parse(item.qualifies[0].validTime) > Date.parse(new Date())
              ? item.qualifies[0].validTime
              : "-",
          项目数量: item.projectsCount,
        };
      });

      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(formattedData, {
        header: columns.map((col) => col.title),
      });

      // 创建工作表并导出
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "机构数据");

      // 导出为 Excel 文件
      XLSX.writeFile(wb, "机构数据.xlsx");
    },

    // 获取联系人
    getRegPerson(row) {
      if (row.administrators && row.administrators.length) {
        return row.administrators[0].name || row.administrators[0].userName;
      } else if (row.isReg) {
        return row.isReg.regPerson;
      } else if (row.contacts && row.contacts[0]) {
        return row.contacts[0].regPerson;
      } else if (row.managersAndArea[0] && row.managersAndArea[0].managers) {
        return row.managersAndArea[0].regPerson;
      }
      return "";
    },

    // 获取联系电话
    getRegPhone(row) {
      if (row.administrators && row.administrators.length) {
        return row.administrators[0].phoneNum;
      } else if (row.isReg) {
        return row.isReg.regPhone;
      } else if (row.contacts && row.contacts[0]) {
        return row.contacts[0].regPhone;
      } else if (row.managersAndArea[0] && row.managersAndArea[0].managers) {
        return row.managersAndArea[0].regPhone;
      }
      return "";
    },
  },
};
</script>

<style lang="scss" scoped>
.dr-container {
  padding: 20px;
  height: calc(100vh - 50px);

  .dr-datatable {
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .dr-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .dr-title-left {
      font-size: 16px;
      font-weight: 500;
      border-left: 8px solid #409eff;
      display: flex;
      height: 24px;
      line-height: 24px;
      .dr-title-text {
        margin-left: 10px;
      }
    }
    .dr-title-divider {
      flex: 1;
      padding: 0 10px;
      el-divider {
      }
    }
  }
}
</style>
