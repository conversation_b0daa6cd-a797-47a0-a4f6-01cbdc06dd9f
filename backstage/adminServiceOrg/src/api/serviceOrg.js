import request from '@root/publicMethods/request';

export function goCountersign(data) {
  return request({
    url: '/manage/adminServiceOrg/goCountersign',
    data,
    method: 'post',
  });
}

export function serviceOrgs(data) {
  return request({
    url: '/manage/adminServiceOrg/getAllOrg',
    data,
    method: 'post',
  });
}

// 根据筛选条件获取项目列表
export function getProjectList(data) {
  return request({
    url: '/manage/adminServiceProject/getProjects',
    data,
    method: 'post',
  });
}

export function getQualifies(data) {
  return request({
    url: '/manage/adminServiceOrg/getQualifies',
    data,
    method: 'post',
  });
}


export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}
// 查看手机号记录到日志中
export function checkPhoneLog(params) {
  return request({
    url: '/manage/superUser/checkPhoneLog',
    params,
    method: 'get',
  });
}

