<template>
  <div id="adminAppointment-app" class="adminAppointment">
    <router-view />
  </div>
</template>
<script>
export default {
  data() {
    return {
      sidebarOpened:false,
    }
  },
 
  components: {},
};
</script>
<style lang="scss">
.adminAppointment{
  .dr-pagination {
    text-align: center;
    margin: 15px auto 0;
  }
  .box-card .el-icon-circle-close{
    color: #f5f5ff!important;
  }
  .wrap .el-form-item {
    margin-bottom: 0px;
  }
  .wrap .el-form-item.lineOfBusiness {
    margin-bottom: 15px;
  }
  .el-card__body {
    height: 80vh;
    overflow-y: scroll;
  }
   /* 设置滚动条的样式 */
  .box-card ::-webkit-scrollbar {
    width:5px;
    background-color: #ddd;
  }
   /* 滚动槽 */
  .box-card ::-webkit-scrollbar-track {
    border-radius:4px;
    background-color: #fff;
  }

  /* 滚动条滑块 */
  .box-card ::-webkit-scrollbar-thumb {
    border-radius:4px;
    /* background:black; */
  }
  .el-input--medium .el-input__inner,
  .el-textarea__inner{
    border: none;
    font-weight: bold;
  }
  .wrap .el-form-item__label{
    color: #555;
    font-weight: normal;
  }

}
</style>
