<template>
  <div :class="classObj" class="adminUser">
    <div class="main-container">
      <transition name="el-zoom-in-center">
        <Details
          v-show="showDialog"
          :detail="tableRowDetail"
          @close="tableRowDetail.show = false"
          ref="details"
        />
      </transition>

      <div class="dr-container">
        <div class="dr-datatable">
          <el-row>
            <el-col :span="24">
              <div class="dr-title">
                <span class="dr-title-left">
                  <span class="dr-title-text">查询条件</span>
                </span>
                <span class="dr-title-divider"><el-divider></el-divider></span>
                <span class="dr-title-btn"></span>
              </div>
              <TopBar
                :searchParams="searchParams"
                @search="getLits"
                @reset="reset"
                ref="detail"
              ></TopBar>

              <div class="dr-title">
                <span class="dr-title-left">
                  <span class="dr-title-text">签约列表</span>
                </span>
                <span class="dr-title-divider"><el-divider></el-divider></span>
                <span class="dr-title-btn"></span>
              </div>
              <DataTable
                v-model="tableRowDetail"
                :loading="loading"
                :searchParams="searchParams"
                :contractList="contractList"
                :totleCount="totleCount"
              ></DataTable>
            </el-col>
          </el-row>
          <Pagination
            :searchParams="searchParams"
            v-show="contractList.length > 0"
            :totleCount="totleCount"
          ></Pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Details from "@/components/details/index.vue";
import DataTable from "./dataTable.vue";
import TopBar from "../common/TopBar.vue";
import Pagination from "../common/Pagination.vue";
import { initEvent } from "@root/publicMethods/events";
import { getAllContract } from "@/api";

export default {
  name: "adminAppointment",
  data() {
    return {
      contractList: [],
      loading: false,
      searchParams: {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: "",
      },
      totleCount: 0, // 总条数
      tableRowDetail: {
        type: "serviceOrg",
        show: false,
        data: {},
      },
      sidebarOpened: false,
      device: "desktop",
    };
  },
  components: {
    DataTable,
    TopBar,
    Details,
    Pagination,
  },
  watch: {
    searchParams: {
      deep: true,
      handler: function (newV, oldV) {
        this.getLits();
      },
    },
  },
  created() {
    this.getLits(); // 获取数据列表
    initEvent(this); // 初始化 hideSidebar & openSidebar
  },
  computed: {
    showDialog() {
      return this.tableRowDetail.show;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    reset() {
      this.searchParams = {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: "",
      };
      this.getLits();
    },
    getLits() {
      // getAllContract(this.searchParams).then((res) => {
      //   if (res.status == 200) {
      //     const resList = res.data.list || [];
      //     this.contractList = resList;
      //     this.totleCount = +res.data.count || 0;
      //   }
      // });
      // 模拟的本地请求数据
      console.log("========= getLits =========\n");
      this.loading = true;
      const mockData = {
        list: [
          {
            serviceOrgInfo: {
              _id: "nfmjo6faq",
              regAddr: ["建设兵团", "建设兵团第八师", "建设兵团第八师石河子市"],
              managers: ["DuznA5Y6KI"],
              lineOfBusiness: "",
              regType: "",
              qualifies: ["zG9eS8eM_w"],
              status: 3,
              blackTechnology: false,
              modifyLabApproval: false,
              modifyApproval: true,
              modifyRecord: false,
              rigorousInventory: false,
              comtractApproval: false,
              sourse: "operate",
              dingApproval: false,
              approvalSwitch: false,
              reportTransfer: false,
              name: "兵团职业病防治院",
              organization: "91650105099182317Y",
              address: "新疆乌鲁木齐市水磨沟区广源路100号创博智谷产业园B区4栋",
              landline: "",
              corp: "马文武",
              managersAndArea: [],
              ctime: "2025-01-02T04:42:22.508Z",
              __v: 0,
              administrator: "DuznA5Y6KI",
              img: "undefined",
              message: "",
              butlerService: 0,
            },
            cname: "乌鲁木齐航空有限责任公司",
            appointmentTime: "2025-01-03",
            inspectionName: "王彦",
            category: "职业卫生检测",
            testingItems: "职卫检评",
          },
          {
            serviceOrgInfo: {
              _id: "nfmjo6g2faq",
              regAddr: ["建设兵团", "建设兵团第八师", "建设兵团第八师石河子市"],
              managers: ["DuznA5Y6KI"],
              lineOfBusiness: "",
              regType: "",
              qualifies: ["zG9eS8eM_w"],
              status: 3,
              blackTechnology: false,
              modifyLabApproval: false,
              modifyApproval: true,
              modifyRecord: false,
              rigorousInventory: false,
              comtractApproval: false,
              sourse: "operate",
              dingApproval: false,
              approvalSwitch: false,
              reportTransfer: false,
              name: "兵团职业病防治院",
              organization: "91650105099182317Y",
              address: "新疆乌鲁木齐市水磨沟区广源路100号创博智谷产业园B区4栋",
              landline: "",
              corp: "马文武",
              managersAndArea: [],
              ctime: "2025-01-02T04:42:22.508Z",
              __v: 0,
              administrator: "DuznA5Y6KI",
              img: "undefined",
              message: "",
              butlerService: 0,
            },
            cname: "乌鲁木齐航空有限责任公司",
            appointmentTime: "2025-01-15",
            inspectionName: "王彦",
            category: "职业卫生检测",
            testingItems: "职卫检评",
          },
          {
            serviceOrgInfo: {
              _id: "nfmjo6g2gfaq",
              regAddr: ["建设兵团", "建设兵团第八师", "建设兵团第八师石河子市"],
              managers: ["DuznA5Y6KI"],
              lineOfBusiness: "",
              regType: "",
              qualifies: ["zG9eS8eM_w"],
              status: 3,
              blackTechnology: false,
              modifyLabApproval: false,
              modifyApproval: true,
              modifyRecord: false,
              rigorousInventory: false,
              comtractApproval: false,
              sourse: "operate",
              dingApproval: false,
              approvalSwitch: false,
              reportTransfer: false,
              name: "兵团职业病防治院",
              organization: "91650105099182317Y",
              address: "新疆乌鲁木齐市水磨沟区广源路100号创博智谷产业园B区4栋",
              landline: "",
              corp: "马文武",
              managersAndArea: [],
              ctime: "2025-01-02T04:42:22.508Z",
              __v: 0,
              administrator: "DuznA5Y6KI",
              img: "undefined",
              message: "",
              butlerService: 0,
            },
            cname: "开发区医院",
            appointmentTime: "2025-02-23",
            inspectionName: "王彦",
            category: "职业卫生检测",
            testingItems: "放射检评",
          },
          {
            serviceOrgInfo: {
              _id: "e423fg",
              regAddr: ["建设兵团", "建设兵团第一师"],
              managers: ["DuznA5Y6KI"],
              lineOfBusiness: "",
              regType: "",
              qualifies: ["zG9eS8eM_w"],
              status: 3,
              blackTechnology: false,
              modifyLabApproval: false,
              modifyApproval: true,
              modifyRecord: false,
              rigorousInventory: false,
              comtractApproval: false,
              sourse: "operate",
              dingApproval: false,
              approvalSwitch: false,
              reportTransfer: false,
              name: "第一师12团医院",
              organization: "91650105099182317Y",
              address: "新疆乌鲁木齐市水磨沟区广源路100号创博智谷产业园B区4栋",
              landline: "",
              corp: "张雷",
              managersAndArea: [],
              ctime: "2025-01-02T04:42:22.508Z",
              __v: 0,
              administrator: "DuznA5Y6KI",
              img: "undefined",
              message: "",
              butlerService: 0,
            },
            cname: "开发区医院",
            appointmentTime: "2025-03-06",
            inspectionName: "赵兴",
            category: "医疗放射检测",
            testingItems: "放射检评",
          },
        ],
        count: 4, // 模拟总条数
      };

      let regAddrSearch = "建设兵团";
      if (this.searchParams.regAddr && this.searchParams.regAddr.length > 0) {
        console.log(
          "========= this.searchParams.regAddr.z =========\n",
          this.searchParams.regAddr
        );
        const str = this.searchParams.regAddr.map((item) => item.name).join("");
        regAddrSearch += str;
      }
      // 模拟的过滤条件：根据 searchParams.keyWords 进行模糊搜索
      const filteredList = mockData.list.filter((item) => {
        const searchKeyword = this.searchParams.keyWords.toLowerCase();
        const cnameMatch = item.cname.toLowerCase().includes(searchKeyword);
        const serviceOrgCnameMatch = item.serviceOrgInfo.name
          .toLowerCase()
          .includes(searchKeyword);

        const regAddrMatch = item.serviceOrgInfo.regAddr.join("").includes(regAddrSearch);
        return regAddrMatch && (cnameMatch || serviceOrgCnameMatch);
      });

      // 更新 contractList 和 totleCount
      // setTimeout(() => {

      // }, 1000);
      this.contractList = filteredList;
      this.totleCount = filteredList.length; // 过滤后的总条数
      this.loading = false;
    },
    maskPhoneNumber(phoneNum) {
      const reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      if (phoneNum) {
        return phoneNum.toString().replace(reg, "\$1***\$2");
      }
      return phoneNum || "";
    },
  },
};
</script>

<style lang="scss" scoped>
.dr-container {
  padding: 20px;
  height: calc(100vh - 50px);

  .dr-datatable {
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .dr-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .dr-title-left {
      font-size: 16px;
      font-weight: 500;
      border-left: 8px solid #409eff;
      display: flex;
      height: 24px;
      line-height: 24px;
      .dr-title-text {
        margin-left: 10px;
      }
    }
    .dr-title-divider {
      flex: 1;
      padding: 0 10px;
      el-divider {
      }
    }
  }
}
</style>
