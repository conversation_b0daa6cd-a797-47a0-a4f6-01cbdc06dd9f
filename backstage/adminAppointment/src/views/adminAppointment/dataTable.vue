<template>
  <el-row>
    <!-- <el-alert center type="success" :closable="false">
      <template slot="title">
        共有 <B>{{ totleCount || 0 }}</B> 项签约合同（灰底的为未注册机构）
      </template>
    </el-alert> -->

    <el-table
      default-expand-all
      align="center"
      tooltip-effect="dark"
      style="width: 100%"
      row-key="_id"
      ref="multipleTable"
      header-cell-style="background-color: #FAFAFA;height:60px"
      v-loading="loading"
      :data="contractList"
      :row-class-name="tableRowClassName"
      :max-height="tableHeight"
    >
      <el-table-column
        prop="serviceOrgInfo.name"
        label="机构名称"
        fixed
        min-width="110px"
      >
        <template slot-scope="scope">
          <div style="cursor: pointer; color: #409eff" @click="showOrgDetail(scope.row)">
            {{ scope.row.serviceOrgInfo.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="cname"
        align="center"
        label="预约单位"
        fixed
        min-width="120px"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.cname }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="appointmentTime" align="center" label="预约时间">
      </el-table-column>
      <el-table-column prop="inspectionName" align="center" label="承检负责人">
      </el-table-column>
      <el-table-column prop="category" align="center" label="检测类别"> </el-table-column>
      <el-table-column prop="testingItems" align="center" label="检测项目">
      </el-table-column>
    </el-table>
  </el-row>
</template>

<script>
import moment from "moment";
// import { goCountersign,checkPhoneLog } from '@/api';

export default {
  props: {
    contractList: Array,
    value: Object,
    searchParams: Object,
    totleCount: Number,
    loading: Boolean,
  },
  data() {
    return {
      tableRowDetail: { ...this.value },
      // loading: false,
    };
  },
  watch: {
    // 监听 tableRowDetail 的变化并通过 $emit 更新父组件数据
    tableRowDetail: {
      handler(newValue) {
        this.$emit("input", newValue);
      },
      deep: true, // 深度监听对象的变化
    },
  },
  methods: {
    // 点击电话号码显示真实号码
    showPhone(phone) {
      phone.show = !phone.show;
    },
    // 判断机构是否是自主增加服务区域来判断这一行的显示
    tableRowClassName({ row, rowIndex }) {
      if (row.companyIn) {
        return "cancel-row";
      }
      return "";
    },

    showOrgDetail(row) {
      this.tableRowDetail.show = true;
      this.tableRowDetail.type = "serviceOrg";
      this.tableRowDetail.data = row.serviceOrgInfo;

      console.log("siisi", this.tableRowDetail.data);
      if (new Date(this.tableRowDetail.data.qualifies[0].validTime) < new Date()) {
        this.$alert("职业卫生技术服务机构资质证书已过期", "提示", {
          confirmButtonText: "确定",
        });
      }
    },
    showEntrustDetail(row) {
      this.tableRowDetail.show = true;
      this.tableRowDetail.type = "entrust";
      this.tableRowDetail.data = row;
    },
  },
  computed: {
    //将时间 转换成 简化的
    doMomentyear(nowTime) {
      return function (nowTime) {
        return moment(nowTime).format("YYYY");
      };
    },
    //将时间 转换成 简化的
    doMomentmonthD(nowTime) {
      return function (nowTime) {
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format("YYYY-MM-DD");
      };
    },
    tableHeight() {
      const innerHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight;
      return innerHeight - 220;
    },
  },
};
</script>
<style scoped>
.el-icon-circle-close {
  color: #f5f5f5;
}
::v-deep .el-table .cancel-row {
  background: rgb(204, 204, 204);
}
.orgName:hover {
  cursor: pointer;
  color: #409eff;
}
.el-message-box {
  z-index: 9999;
}
</style>
