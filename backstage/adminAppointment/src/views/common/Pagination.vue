<template>
  <div class="block dr-pagination">
    <div v-if="searchParams">
      <div>
        <el-pagination background
          :hide-on-single-page="false"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="searchParams.curPage"
          :page-sizes="[ 30, 50, 100]"
          :page-size="searchParams.limit"
          layout="total, sizes, prev, pager, next"
          :total="totleCount"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    searchParams: Object,
    totleCount: Number, // 总条数
  },
  methods: {
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchParams.limit = val;
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchParams.curPage = val;
    },
  },
  
};
</script>
