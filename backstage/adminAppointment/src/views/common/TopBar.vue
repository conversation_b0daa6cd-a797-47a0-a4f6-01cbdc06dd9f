<template>
  <div class="dr-toolbar">
    <span class="dr-searchInput">
      <span class="dr-searchInput_title"> 机构服务地区：</span>
      <span class="dr-searchInput_main">
        <el-cascader
          :props="districtListProps"
          @change="regaddChangeOptionFunc"
          v-model="searchParams.regAddr"
          clearable
          ref="regAddCas"
          size="small"
          placeholder="选择机构服务地区"
        >
          <template slot-scope="{ data }">
            <span>{{ data.label }}</span>
          </template>
        </el-cascader>
      </span>
    </span>
    <span class="dr-searchInput">
      <span class="dr-searchInput_title"> 搜索预约： </span>
      <span class="dr-searchInput_main">
        <el-input
          style="flex: 1"
          size="small"
          v-model="keyWords"
          suffix-icon="el-icon-search"
          @keyup.enter.native="$emit('search')"
        ></el-input>
      </span>
    </span>

    <span class="dr-searchBtn">
    <el-button type="primary" @click="$emit('search')" size="mini" icon="el-icon-search">查询</el-button>
    <el-button  size="mini" @click="$emit('reset')"  icon="el-icon-refresh">重置</el-button>

    </span>
  </div>
</template>
<script>
import { getDistrictList } from "@/api";
export default {
  props: {
    searchParams: Object,
  },
  data() {
    return {
      year: "", // 选择年份
      visible: true,
      keyWords: "", //关键字
      district: [],
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.value.area_code;
          }
          getDistrictList(params).then(({ data }) => {
            try {
              const nodes = Array.from(data.docs).map((item) => ({
                value: item,
                label: item.name,
                leaf: item.level >= 3,
                disabled: item.name === "市辖区" ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
    };
  },
  watch: {
    keyWords(newVal, oldVal) {
      if (newVal == oldVal) return;
      setTimeout(() => {
        this.searchParams.keyWords = this.keyWords;
      }, 1000);
    },
  },
  methods: {
    // 选择注册地
    regaddChangeOptionFunc(v) {
      this.searchParams.regAddr = v || [];
      this.searchParams.curPage = 1;
      this.$refs.regAddCas.dropDownVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.dr-toolbar{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.dr-searchInput {
  display: flex;
  font-size: 14px;
  align-items: center;
  padding-right:10px;
}
</style>
