<template>
  <div class="wrap">
    <serviceCard v-if="show && type === 'serviceOrg'" @close="closeDetail" :datas="datas" />
    <entrustCard v-if="show && type === 'entrust'" @close="closeDetail" :datas="datas" />
  </div>
</template>
<script>
import serviceCard from "./serviceCard.vue";
import entrustCard from "./entrustCard.vue";
export default {
  name: "details",
  props: {
    detail: {
      type: Object,
      default: {},
    },
  },
  components: {
    serviceCard,
    entrustCard
  },
  data() {
    return {};
  },
  computed: {
    datas() {
      return this.detail.data || {};
    },
    show() {
      return this.detail.show || false;
    },
    type() {
      return this.detail.type || "";
    },
  },
  methods: {
    closeDetail() {
      this.$emit("close");
    },
  },
};
</script>

<style scoped lang="scss">
.demo-form-inline .el-form-item {
  width: 50%;
  display: inline-block;
}
.wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(#000000, 0.6);
  z-index: 2002;
  display: flex;
  align-items: center;
  justify-content: center;
  .box-card {
    padding: 10px 3% 30px;
    box-sizing: border-box;
    width: 70%;
    min-width: 500px;
    max-height: 88vh;
    overflow-y: hidden;
    position: relative;
  }
}
</style>
