<template>
  <el-card class="box-card" shadow="always">
    <div slot="header" class="clearfix header">
      <span style="font-size: 1.2em; font-weight: bold; color: #409eff">{{
        (datas.cname || "") + getCommissionedType(type)
      }}</span>
      <span class="el-icon-error close2" @click="closeDetail"></span>
    </div>
    <div class="text item">
      <div class="form-title">签约合同信息</div>
      <el-form class="demo-form-inline" label-width="140px">
        <el-form-item label="项目编号：" style="width: 100%">
          <span v-if="!inspectionInfo.length">-</span>
          <span v-for="(item, index) in inspectionInfo" :key="item._id"
            ><el-tag v-if="item.projectSN" type="success" size="mini">{{
              item.projectSN
            }}</el-tag></span
          >
        </el-form-item>
        <el-form-item label="签订日期：">
          <el-input :value="doMomentmonthD(datas.entrustDate)" readonly></el-input>
        </el-form-item>
        <el-form-item label="检测类别：">
          <el-input :value="datas.detectionCategory || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="委托类型：">
          <el-input
            :value="getCommissionedType(datas.commissionedType)"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="承建方负责人：">
          <el-input :value="datas.inspectionManagerName || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="备注：" style="width: 100%">
          <el-input :value="datas.remark || ''" readonly></el-input>
        </el-form-item>
      </el-form>

      <div class="form-title">委托单位信息</div>
      <el-form class="demo-form-inline" label-width="140px">
        <el-form-item label="委托单位名称：" style="width: 100%">
          <el-input :value="datas.cname || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="委托单位地址：" style="width: 100%">
          <el-input :value="datas.regAdd || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="详细地址：" style="width: 100%">
          <el-input :value="datas.districtRegAdd.join('/')" readonly></el-input>
        </el-form-item>
        <el-form-item label="联系人：">
          <el-input :value="datas.contract || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="联系电话：">
          <el-input :value="datas.phoneNum.real || ''" readonly></el-input>
        </el-form-item>
      </el-form>

      <div class="form-title">委托方要求</div>
      <el-form class="demo-form-inline" label-width="180px">
        <el-form-item label="委托方是否使用非标方式：">
          <el-input :value="!!Number(datas.standard) ? '是' : '否'" readonly></el-input>
        </el-form-item>
        <el-form-item label="委托方是否同意使用分包：">
          <el-input
            :value="!!Number(datas.subcontract) ? '是' : '否'"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="报告是否作符合性声明：">
          <el-input :value="!!Number(datas.conformity) ? '是' : '否'" readonly></el-input>
        </el-form-item>
        <el-form-item label="退样方式：">
          <el-input :value="datas.sampleMethod || ''" readonly></el-input>
        </el-form-item>
      </el-form>

      <div class="form-title">受检单位信息</div>
      <el-tabs
        v-model="inspectionID"
        @tab-click="handleClick"
        class="form-inspection"
        type="border-card"
      >
        <el-tab-pane
          v-for="(item, index) in inspectionInfo"
          :label="item.cname"
          :name="item._id"
          :key="item._id"
        >
          <el-form class="demo-form-inline" label-width="140px">
            <el-form-item label="委托单位名称：">
              <el-input :value="item.cname || ''" readonly></el-input>
            </el-form-item>
            <el-form-item label="邮箱：">
              <el-input :value="item.email || ''" readonly></el-input>
            </el-form-item>
            <el-form-item label="联系人：">
              <el-input :value="item.contract || ''" readonly></el-input>
            </el-form-item>
            <el-form-item label="联系电话：">
              <el-input :value="item.phoneNum || ''" readonly></el-input>
            </el-form-item>
            <el-form-item label="受检地址：" style="width: 100%">
              <el-input :value="item.regAdd || ''" readonly></el-input>
            </el-form-item>
            <el-form-item label="详细地址：" style="width: 100%">
              <el-input :value="item.districtRegAdd.join('/')" readonly></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div class="form-title">项目费用/账户信息</div>
      <el-form class="demo-form-inline" label-width="140px">
        <el-form-item label="项目价格：" style="width: 100%">
          <el-input :value="datas.projectPrice || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="开户账号：">
          <el-input :value="datas.accountsNumber || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="税号：">
          <el-input :value="datas.tariffNumber || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="开户行：">
          <el-input :value="datas.accountsBank || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="开户地址：">
          <el-input :value="datas.accountsAddress || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="预计合作费用：">
          <el-input :value="datas.expectedCooperationFee || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="实际合作费用：">
          <el-input :value="datas.cooperationFee || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="预计评审费用：">
          <el-input :value="datas.expectedReviewFee || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="实际评审费用：">
          <el-input :value="datas.reviewFee || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="预计净项目额：">
          <el-input :value="datas.expectedNetEarnings || ''" readonly></el-input>
        </el-form-item>
        <el-form-item label="实际净项目额：">
          <el-input :value="datas.netEarnings || ''" readonly></el-input>
        </el-form-item>
      </el-form>
    </div>
  </el-card>
</template>

<script>
import moment from "moment";
export default {
  name: "entrustCard",
  props: {
    datas: Object,
  },
  data() {
    return {
      inspectionID: "", // 用于绑定选中的tab
      inspectionInfo: [], // 初始传入的数组
    };
  },
  created() {
    // 当 inspectionInfo 长度为 0 时，添加一个空对象

    console.log("========= siisi =========\n",this.datas);
    if (this.datas.inspectionProject&&this.datas.inspectionProject.length === 0) {
      this.inspectionInfo = [{ cname: "受检单位", _id: "" }]; // 默认添加一个空对象
    }else{
      this.inspectionInfo = this.datas.inspectionProject;
    }
    // 初始化选中的 tab 为第一个
    this.inspectionID = this.inspectionInfo[0]._id;
  },
  mounted() {},
  methods: {
    getCommissionedType(type) {
      return type === "radiate"
        ? "医疗放射项目委托"
        : type === "personal"
        ? "个人剂量项目委托"
        : "职业卫生项目委托";
    },
    closeDetail() {
      this.$emit("close");
    },
  },
  computed: {
    doMomentmonthD(nowTime) {
      return function (nowTime) {
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format("YYYY-MM-DD");
      };
    },
    inspectionInfo() {
      return this.datas.inspectionProject && this.datas.inspectionProject.length
        ? this.datas.inspectionProject
        : [{ _id: "" }];
    },
  },
};
</script>

<style lang="scss" scoped>
.box-card {
  .demo-form-inline .el-form-item {
    width: 50%;
    display: inline-block;
  }
  .form-title {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    &::before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: #409eff;
      margin-right: 6px;
    }
  }
  .el-row:hover {
    background-color: #f5f5f5;
  }

  .form-inspection {
    margin: 16px 0;
  }

  .close2 {
    float: right !important;
    padding: 3px 0;
    font-size: 1.3em;
    cursor: pointer;
    opacity: 0.7;
    :hover {
      opacity: 0.9;
    }
  }
}

.el-row {
  padding: 10px 0;
}
.text {
  font-size: 15px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}

.el-upload-list {
  padding-bottom: 30px;
}
.el-upload-list li .el-image {
  vertical-align: middle;
  margin-right: 20px;
}
.el-upload-list .desc {
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 130px);
}
.el-upload-list .desc .title2 {
  font-weight: bold;
  font-size: 15px;
  text-decoration: none;
  color: #606266;
}
.el-upload-list .desc p {
  margin: 0;
  font-size: 13px;
}
.el-image-viewer__mask {
  opacity: 0.8;
}
.el-image-viewer__btn.el-image-viewer__close {
  color: #ddd;
}

[v-cloak] {
  display: none;
}

.el-upload-list .el-upload-list__item {
  overflow: hidden;
  /* z-index: 0; */
  background-color: #fff;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-top: 10px;
  padding: 10px 10px 10px 10px;
}
.el-upload-list .el-upload-list__item-status-label {
  position: absolute;
  right: -17px;
  top: -7px;
  width: 46px;
  height: 26px;
  background: #13ce66;
  text-align: center;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-box-shadow: 0 1px 1px #ccc;
  box-shadow: 0 1px 1px #ccc;
}
.el-upload-list .el-upload-list__item-status-label i {
  font-size: 12px;
  margin-top: 12px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.lineOfBusiness {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /* background:black; */
}
</style>
