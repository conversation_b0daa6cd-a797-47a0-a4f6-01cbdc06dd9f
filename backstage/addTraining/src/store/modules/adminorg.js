import * as types from '../types.js';
import {
  adminorgList,
} from '@/api/adminorg';

const state = {
  remind: {
    isOnlineDeclaration: '',
    isjobHealth: '',
    isratioArr: '',
    isReported: '',
    isAssess: '',
  },
  formState: {
    show: false,
    edit: false,
    approveOrgBackShow: false,
    formData: {
      // STOREPROPSSTR
    },
  },
  sortType: {
    order: '',
    prop: '',
  },
  pageInfos: {
    current: 1,
    pageSize: 10,
  },
  list: {
    pageInfo: {},
    docs: [],
    statisticsData: {},
  },
  searchKeys: {
    searchkey: '',
    district: '',
    industryCategory: '',
    OnlineDeclarationOption: '',
    jobHealthOption: '',
    ratioArrOption: '',
    exceedOption: '',
    odiseasedOption: '',
    suspectedOption: '',
    selfAssessmentOption: '',
    assessmentResultOption: '',
    comprehensiveLevelOption: '',
    riskSortLevelOption: '',
    levelOption: '',
    punishOption: '',
    superviseOption: '',
    isDeleteOption: '',
  },
};

const mutations = {
  [types.ADMINORG_FORMSTATE](state, formState) {
    state.formState.show = formState.show;
    state.formState.edit = formState.edit;
    state.formState.type = formState.type;
    state.formState.approveOrgBackShow = formState.approveOrgBackShow;
    state.formState.formData = Object.assign({
    }, formState.formData);
  },
  [types.ADMINORG_LIST](state, list) {
    state.list = list;
  },
  [types.listPageInfo](state, pageInfos) {
    state.pageInfos.current = pageInfos.current;
    state.pageInfos.pageSize = pageInfos.pageSize;
  },
  [types.SORT_TYPE](state, sortType) {
    state.sortType.order = sortType.order;
    state.sortType.prop = sortType.prop;
  },
  [types.remind](state, remind) {
    state.remind.isOnlineDeclaration = remind.isOnlineDeclaration;
    state.remind.isjobHealth = remind.isjobHealth;
    state.remind.isratioArr = remind.isratioArr;
    state.remind.isReported = remind.isReported;
    state.remind.isAssess = remind.isAssess;
  },
  [types.SETSEARCHKEYS](state, data) {
    state.searchKeys.searchkey = data.searchkey;
    state.searchKeys.district = data.district;
    state.searchKeys.industryCategory = data.industryCategory;
    state.searchKeys.OnlineDeclarationOption = data.OnlineDeclarationOption;
    state.searchKeys.jobHealthOption = data.jobHealthOption;
    state.searchKeys.ratioArrOption = data.ratioArrOption;
    state.searchKeys.exceedOption = data.exceedOption;
    state.searchKeys.odiseasedOption = data.odiseasedOption;
    state.searchKeys.suspectedOption = data.suspectedOption;
    state.searchKeys.selfAssessmentOption = data.selfAssessmentOption;
    state.searchKeys.assessmentResultOption = data.assessmentResultOption;
    state.searchKeys.comprehensiveLevelOption = data.comprehensiveLevelOption;
    state.searchKeys.riskSortLevelOption = data.riskSortLevelOption;
    state.searchKeys.levelOption = data.levelOption;
    state.searchKeys.punishOption = data.punishOption;
    state.searchKeys.superviseOption = data.superviseOption;
    state.searchKeys.isDeleteOption = data.isDeleteOption;
  },

};

const actions = {

  async showAdminorgForm({
    commit,
  }, params = {
    EnterpriseID: '',
    edit: false,
    approveOrgBackShow: false,
    formData: {},
  }) {
    commit(types.SETENTERPRISEID, params.EnterpriseID);
    commit(types.ADMINORG_FORMSTATE, {
      show: params.show,
      edit: params.edit,
      approveOrgBackShow: params.approveOrgBackShow,
      formData: params.formData,
    });
  },

  hideAdminorgForm: ({
    commit,
  }) => {
    commit(types.ADMINORG_FORMSTATE, {
      show: false,
      approveOrgBackShow: false,
    });
  },

  async getAdminorgList({
    commit,
  }, params = {}) {
    const result = await adminorgList(params);
    console.log('列表返回', result);
    commit(types.ADMINORG_LIST, result.data);
  },

  setSearchKeys: ({
    commit,
  }, searchKeys) => {
    commit(types.SETSEARCHKEYS, searchKeys);
  },
  listPageInfo: ({
    commit,
  }, pageInfos) => {
    commit(types.listPageInfo, pageInfos);
  },
  sortTypeChange: ({
    commit,
  }, sortType) => {
    commit(types.SORT_TYPE, sortType);
  },
  remind: ({
    commit,
  }, remind) => {
    commit(types.remind, remind);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
