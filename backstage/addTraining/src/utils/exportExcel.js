import XLSX from 'xlsx';
import { saveAs } from 'file-saver';
// data：一个数组，包含要导出的数据对象
// fileName：要保存的文件名
// sheetName：要创建的工作表名（可选，默认为Sheet1）
export function exportExcel(data, fileName, sheetName) {
  // 创建工作簿
  const wb = XLSX.utils.book_new();
  // 创建工作表
  const ws = XLSX.utils.json_to_sheet(data);
  // 将工作表添加到工作簿中
  XLSX.utils.book_append_sheet(wb, ws, sheetName || 'Sheet1');
  // 将工作簿转换为二进制文件
  const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });
  // 保存文件
  // eslint-disable-next-line no-undef
  saveAs(new Blob([ s2ab(wbout) ], { type: 'application/octet-stream' }), fileName + '.xlsx');
}

// 字符串转ArrayBuffer
function s2ab(s) {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  // eslint-disable-next-line no-bitwise, eqeqeq
  for (let i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
  return buf;
}
