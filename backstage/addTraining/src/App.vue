<template>
  <div id="admintraining-app">
    <router-view v-if="isRouterAlive" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      sidebarOpened: false,
      isRouterAlive: true,
    };
  },
  provide() {
    return {
      reload: this.reload,
    };
  },
  methods: {
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
  },
  components: {},
};
</script>
<style>
.el-table {
  width: 99.9% !important;
}
#admintraining-app .el-dialog__body{
  padding-top: 0;
}
#admintraining-app .chooseYear .el-input__inner{
  border: 0;
  font-weight: bold;
}
#admintraining-app .chooseYear .el-date-editor.el-input, 
#admintraining-app .el-date-editor.el-input__inner{
  width: 105px;
}
</style>
