<template>
  <div>
    <div id="main"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    chartData: {
      type: Array,
      default: [],
    },
    titleCenter: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      
    };
  },
  methods: {
    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById("main"));
      // 绘制图表
      myChart.setOption(
        {
          title: {
            text: this.title,
            left: this.titleCenter ? 'center' : 20,
            textStyle: {
              fontSize: 15,
            }
          },
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
            trigger: "item",
            formatter: (params) => {
              return (
                params.name +
                "：" +
                params.value +
                "家<br/>占比：" +
                params.percent +
                "%"
              );
            },
          },
          legend: {
            orient: "vertical",
            right: '10%',
            top: 10,
            itemWidth: 12,
            itemHeight: 12,
            icon: 'circle',
            textStyle: {
              color:'#a9a9a9'
            }
          },
          series: [
            {
              name: "数量",
              type: "pie",
              radius: ['40%', '70%'],
              minAngle: 2,
              height: '80%',
              top: '13%',
              right: this.titleCenter ? '10%' : '7%',
              avoidLabelOverlap: false,
              label: {
                  show: true,
                  position: 'outside',
                  color: '#777',
                  formatter: val => {
                    return `${val.data.name} ${val.data.percent}`
                  },
              },
              labelLine: {
                  show: true,
                  color: '#ddd'
              },
              emphasis: {
                  label: {
                      show: true,
                      fontSize: '15',
                      fontWeight: 'bold',
                      formatter: val => {
                        // console.log(1111111, val.data)
                        return `${val.data.name}\n${val.data.percent}`
                      },
                      lineHeight: 22,
                  },
              },
              // center: ["50%", "55%"],
              data: this.chartData,
            },
          ],
          color: ["#ff746b", "#ffb749", "#4a7df9"],
        },
        true
      );
    },
  },
  mounted() {
    this.drawLine();
  },
  watch: {
    chartData: {
      deep: true,
      handler: function (newV, oldV) {
        console.log(1234567890);
        this.drawLine();
      },
    },
  },
  // inject: ["reload"],
};
</script>

<style scoped>
#main{
  width: 100%;
  height: calc(50vh - 115px);
}
</style>