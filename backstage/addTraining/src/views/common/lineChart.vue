<template>
  <div>
    <!-- <div id="main2"
      :style="{
        width: `${width / widthSize}px`,
        height: `${height / heightSize}px`,
      }"
    ></div> -->
    <div id="main2"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    widthSize: {
      type: Number,
      default: 3,
    },
    heightSize: {
      type: Number,
      default: 2.5,
    },
    title: {
      type: String,
      default: "啥也没有",
    },
    legendData: {
      type: Array,
      default: ["培训覆盖率", "培训完成率"],
    },
    xAxisData: {
      type: Array,
      default: ["2016年", "2017年", "2018年", "2019年", "2020年", "2021年"],
    },
    series: {
      type: Array,
      default: [
        {
          name: "培训覆盖率",
          type: "line",
          stack: "比例",
          data: [0.1, 0.3, 0.4, 0.5, 0.6, 0.8, 0.1],
        },
        {
          name: "培训完成率",
          type: "line",
          stack: "比例",
          data: [0.5, 0.3, 0.1, 0.9, 0.6, 0.4, 0.1],
        },
      ],
    },
  },
  data() {
    return {
      width: document.body.clientWidth,
      height: document.body.clientHeight,
    };
  },
  methods: {
    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById("main2"));
      // 绘制图表
      const option = {
        title: {
            text: this.title + '培训情况统计',
            left: 10,
            textStyle: {
              fontSize: 15,
            }
          },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          orient: "vertical",
          right: 10,
          top: 10,
          itemWidth: 12,
          itemHeight: 12,
          icon: 'circle',
          textStyle: {
            color:'#a9a9a9'
          },
          data: this.legendData,
        },
        // legend: {
        //   data: this.legendData,
        // },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        // toolbox: {
        //   right: 30,
        //   feature: {
        //     saveAsImage: {
        //       show: true,
        //       title: '下载为图片'
        //     },
        //   },
        // },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.xAxisData,
        },
        yAxis: {
          type: "value",
          name: "单位：%",
        },
        series: this.series,
        color: ["#4a7df9", "#67C23A"],
      };
      myChart.setOption(option);
    },
  },
  mounted() {
    // this.drawLine();
  },
  watch: {
    series: {
      deep: true,
      handler: function (newV, oldV) {
        console.log(1234567890);
        this.drawLine();
      },
    },
  },
  // inject: ["reload"],
};
</script>

<style scoped>
#main2{
  width: 100%;
  height: calc(50vh - 140px);
}
</style>