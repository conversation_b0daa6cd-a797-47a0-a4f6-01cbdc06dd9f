<template>
  <div>
    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
      <el-row class="container"><!-- :gutter="10" -->
        
        <el-col :xs="24" :sm="14" :md="8" :lg="8" :xl="8">
          <div>
            <pieChart
              :title="computedYear"
              :chartData="pieData"
              :widthSize="3.2"
              :heightSize="3.75"
            />
          </div>
        </el-col>
        <el-col :xs="24" :sm="10" :md="6" :lg="6" :xl="6" class="satisticData"> 
            <div>
              <el-row :gutter="20">
                <div class="chooseYear">
                  <span class="title">数据统计</span>
                  <el-date-picker
                    @change="selectYearMethod(selectYear)"
                    v-model="selectYear"
                    type="year"
                    placeholder="年度"
                  >
                  </el-date-picker>
                </div>
                <div class="text-satistic">
                  <p>培训总数：{{ satisticData.planCount }}次</p>
                  <p>应学企业：{{ satisticData.enterpriseCount }}家</p>
                  <p>参与人次：{{ satisticData.participantsCount }}人次</p>
                  <p>证书发放：{{ satisticData.certificate }}张</p>
                </div>
              </el-row>
            </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="10" :lg="10" :xl="10" class="lineContainer">
          <div>
            <lineChart
              :title="lineTitle"
              :widthSize="2.7"
              :heightSize="3.75"
              :series="coverage"
              :xAxisData="xAxisData"
            />
          </div>
        </el-col>
      </el-row>
    </el-col>
  </div>
</template>

<script>
import { getSatisticByYear, getCoverageLastManyYears } from "@/api/addTraining";
import pieChart from "../common/pieChart";
import lineChart from "../common/lineChart";
import * as echarts from 'echarts';
export default {
  components: {
    pieChart,
    lineChart,
  },
  props: {
    // satisticData: {
    //   type: Object,
    //   defalut: {
    //     planCount: 10,
    //   },
    // },
  },
  data() {
    return {
      xAxisData: [],
      coverage: [
        {
          name: "培训覆盖率",
          type: "line",
          stack: "比例",
          data: [],
          smooth: true,
          lineStyle: {
              width: 1,
              color: '#446cea' // 蓝色
          },
          showSymbol: false,
          areaStyle: {
              opacity: 0.6,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgba(7, 127, 250)'
              }, {
                  offset: 1,
                  color: 'rgba(255, 255, 255)'
              }])
          },
          emphasis: {
              focus: 'series'
          },
        },
        {
          name: "培训完成率",
          type: "line",
          stack: "比例",
          data: [],
          smooth: true,
          lineStyle: {
              width: 1,
              color: '#67C23A' // 绿色
          },
          showSymbol: false,
          areaStyle: {
              opacity: 0.6,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgba(103, 194, 58)'
                  // color: 'rgba(255, 176, 170)'
              }, {
                  offset: 1,
                  color: 'rgba(255, 255, 255)'
              }])
          },
          emphasis: {
              focus: 'series'
          },
        },
      ], // 覆盖率
      width: document.body.clientWidth,
      height: document.body.clientHeight,
      selectYear: "",
      satisticData: {
        certificate: 101,
        completedEnterprisesCount: 101,
        enterpriseCount: 16,
        participantsCount: 24,
        planCount: 25,
        allEnterpriseCount: 100,
      },
      pieData: [
        { value: 108, name: "未开始" },
        { value: 102, name: "进行中" },
        { value: 123, name: "已完成" },
      ],
      lineTitle: `${
        new Date().getFullYear() - 2
      }年~${new Date().getFullYear()}年`,
    };
  },
  created() {
    this.initToYear();
    this.initManyYear();
  },
  methods: {
    selectYearMethod(val) {
      this.initToYear();
    },
    initToYear() {
      getSatisticByYear({ searchYear: this.selectYear }).then(({ data }) => {
        console.log(1111, data);
        this.satisticData = data.satisticData;
        this.pieData = [
          {
            name: "未参与",
            value:
              this.satisticData.allEnterpriseCount -
              this.satisticData.enterpriseCount,
            percent:
              // "未参与 " +
              Math.floor(
                ((this.satisticData.allEnterpriseCount -
                  this.satisticData.enterpriseCount) /
                  this.satisticData.allEnterpriseCount) *
                  1000
              ) /
                10 +
              "%",
          },
          {
            name: "进行中",
            value:
              this.satisticData.enterpriseCount -
              this.satisticData.completedEnterprisesCount,
            percent:
              // "进行中" +
              Math.floor(
                ((this.satisticData.enterpriseCount -
                  this.satisticData.completedEnterprisesCount) /
                  this.satisticData.allEnterpriseCount) *
                  1000
              ) /
                10 +
              "%",
          },
          {
            name: "已完成",
            value: this.satisticData.completedEnterprisesCount,
            percent:
              // "已完成" +
              Math.floor(
                (this.satisticData.completedEnterprisesCount /
                  this.satisticData.allEnterpriseCount) *
                  1000
              ) /
                10 +
              "%",
          },
        ];
      });
    },
    initManyYear() {
      getCoverageLastManyYears({}).then(({ data }) => {
        if (data.message === "OK") {
          const coverage = data.result;
          coverage.sort((a, b) => {
            return a.year - b.year;
          });
          this.enterpriseCount = data.enterpriseCount;
          // 开始计算覆盖率

          for (let index = 0; index < coverage.length; index++) {
            if (this.enterpriseCount) {
              this.coverage[0].data.push(
                (
                  coverage[index].enterpriseCount / this.enterpriseCount  * 100
                ).toFixed(2)
              );
            } else {
              this.coverage[0].data.push(100);
            }
            if (coverage[index].enterpriseCount) {
              this.coverage[1].data.push(
                (
                  coverage[index].completedEnterprisesCount /
                  coverage[index].enterpriseCount  * 100
                ).toFixed(2)
              );
            } else {
              this.coverage[1].data.push(0);
            }
            this.xAxisData.push(coverage[index].year + "年");
          }
          console.log(this.xAxisData, this.coverage);
        }
      });
    },
  },

  // 计算属性，计算培训的年份
  computed: {
    computedYear() {
      let title = this.selectYear ? this.selectYear.getFullYear() + "年度" : "总年度"
      return `${title}职业卫生培训情况总览`
    }
  }
};
</script>

<style scoped lang="scss">

.title{
  font-weight: bold;
  font-size: 15px;
  font-family: PingFangSC-Medium, PingFang SC;
}
.chooseYear{
  margin-left: 10px;
  width: 200px;
}
.satisticData{
  display: flex;
  align-items: flex-start;
  justify-content: center;
  font-family: PingFangSC-Medium, PingFang SC;
  >div{
    .text-satistic {
      margin-left: 20px;
      font-size: 14px;
    }
  }
}

.container{
  display: flex;
  justify-content: space-between;
}

.lineContainer {
  width: 519px
}

</style>