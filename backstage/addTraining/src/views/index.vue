<template>
  <div :class="classObj">
    <div class="main-container">
      <div class="all-content">
        <el-row>
          <span class="title">统计信息</span>
          <span id="hide" @click="hideFlag = !hideFlag">
            {{hideFlag?'展开':'收起'}}
            <span :class="hideFlag?'el-icon-arrow-down':'el-icon-arrow-up'" class="hideIcon"></span>
          </span>
          <div class="card" >
            <el-card shadow="hover" v-show="!hideFlag">
              <div>
                <satistics />
              </div>
            </el-card>
          </div>
          <!-- <div style="width: 100%; height: 32px"></div> -->
          <span class="title">培训列表</span>
          <div class="card">
            <el-card shadow="hover">
              <create-plan ref="create" />
              <data-table @createNewNow="createNewNow"></data-table>
            </el-card>
          </div>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>
import dataTable from "./plan/dataTable.vue";
import createPlan from "./plan/createPlan";
import satistics from "./satistics/satistics";
import { initEvent } from "@root/publicMethods/events";

export default {
  name: "admintraining",
  data() {
    return {
      sidebarOpened: false,
      device: "desktop",
      hideFlag: false,
    };
  },
  components: {
    dataTable,
    createPlan,
    satistics,
  },
  methods: {
    createNewNow() {
      this.$refs["create"].createNewNow();
    },
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  async created() {
    initEvent(this);
  },
};
</script>

<style scoped>
#hide{
  float: right;
  font-weight: bold;
  color: #2A91FC;
  font-size: 13px;
}
#hide:hover{
  cursor: pointer;
}
.hideIcon{
  margin-left: 5px;
}
.title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  font-family: PingFangSC-Semibold, Ping;
  line-height: 16px;
  height: 16px;
}
.all-content {
  padding-top: 24px;
  padding-left: 20px;
  padding-right: 20px;
  background: #f4f6f9;

  height: 100%;
  width: 100%;
}



.card {
  margin-top: 20px;
  margin-bottom: 24px;
}
</style>