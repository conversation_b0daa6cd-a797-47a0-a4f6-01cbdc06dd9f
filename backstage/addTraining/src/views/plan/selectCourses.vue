<template>
  <div>
    <data-table
      ref="coursesList"
      :courseList="courseList"
      :count="count"
      @getList="getList"
      @modyfieWidth="modyfieWidth"
      :checkedCourses="requiredCourses"
    />
    <el-button class="button" @click="next" size="mini" type="primary">创建培训</el-button>
  </div>
</template>
<script>
import sortCourses from "./sortCourses";
import dataTable from "./courses/dataTable";
import { getCoursesList } from "@/api/courses";
export default {
  props: {
    needExam: Boolean,
    checkedCourses: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      activeName: "courses",
      sortCourses: false,
      sidebarOpened: true,
      device: "desktop",
      courseList: [],
      count: 1,
      searchkey: "",
      lesson: 0,
      courses: [],
      // afterSortCourses: [],
      checkedRequiredCourses: [],
      checkedElectiveCourses: [],
      // 只有ID的数据
      requiredCourses: [],
      electiveCourses: [],
      // 在设置不需要考试时，直接跳过考试信息创建，这里设置默认值，保持数据库记录格式一致性
      plan: {
        // name: "",
        examination: {
          singleChoice: {
            num: 0,
            scores: 0,
          },
          multipleChoice: {
            num: 0,
            scores: 0,
          },
          Judgment: {
            num: 0,
            scores: 0,
          },
          fillBlank: {
            num: 0,
            scores: 0,
          },
          essayQuestion: {
            num: 0,
            scores: 0,
          },
          passingGrate: 0,
          limitTime: 0,
        },
        allowTestOnly: false,
      },

      tableSize: 24,
    };
  },
  methods: {
    modyfieWidth() {
      this.$emit("modyfieWidth");
    },
    handleClick(tab, event) {
      if (tab.label === "选修课") {
      } else if (tab.label === "必修课") {
      }
    },
    goToort() {
      const { courses, lesson } = this.$refs["coursesList"].next();
      if (!courses.length) {
        this.$message.error("请先选择课程哦");
        return;
      }
      this.courses = courses;
      this.lesson = lesson;
      this.sortCourses = true;
    },
    finishSort(afterSortCourses) {
      this.afterSortCourses = afterSortCourses;
      this.sortCourses = false;
    },
    cancelSort() {
      this.sortCourses = false;
    },
    next() {
      const {
        requiredArray,
        electiveArray,
        requiredHourses,
        electiveHourses,
      } = this.$refs["coursesList"].next();
      this.requiredHourses = requiredHourses;
      this.electiveHourses = electiveHourses;

      if (!requiredArray.length) {
        this.$message.error("请先选择必修课哦");
        return;
      }
      // if (!electiveArray.length) {
      //   this.$message.error("请先选择选修课哦");
      //   return;
      // }

      this.$emit("checkedCourses", {
        requiredCoursesHours: this.requiredHourses,
        electivesCoursesHours: this.electiveHourses,
        coursesID: requiredArray,
        electives: electiveArray,
      });
    },
    previous() {
      this.$parent.$parent.previousCompany();
    },
    getList(page = {}, searchkey) {
      console.log(page);
      if (page.current) this.current = page.current;
      if (page.pageSize) this.pageSize = page.pageSize;
      if (searchkey !== undefined) this.searchkey = searchkey;
      this.$axios
        .get(getCoursesList, {
          current: this.current,
          searchkey: this.searchkey,
          pageSize: this.pageSize,
        })
        .then(({ data }) => {
          this.courseList = data.data.list;
          this.count = data.data.count;
        });
    },
    createNow() {
      const { courses, lesson } = this.$refs["coursesList"].next();
      this.courses = courses;
      this.lesson = lesson;
      if (!this.afterSortCourses.length) {
        this.courses.forEach((item) => {
          this.afterSortCourses.push(item._id);
        });
      }
      if (!this.afterSortCourses.length) {
        this.$message.error("请先选择课程哦");
        return;
      }
      this.$parent.$parent.createNewPlan(
        this.plan,
        this.afterSortCourses,
        this.lesson
      );
    },
  },
  created() {},
  components: {
    dataTable,
    sortCourses,
  },
  computed: {},
};
</script>

<style scoped>
/* 
如果考试开关设为需要考试，那么要出现新的开关，指定是培训结束考试还是课程结束考试

加一个视频是否要停止的开关（文字取啥我也不知道），指定是否要每隔一段时间暂停视频
如果需要，那就出现单选框（随机、指定间隔）
选择随机的话，就出现俩input输入框分别输入随机范围，单位分钟 类似这样 [xxxx]分钟
如果选择指定的话，那就出现一个输入框 单位也是分钟
 */
.button {
  display: block;
  text-align: center;
  margin-top: 10px;
}

.el-input {
  width: 180px;
  height: 32px;
}
.button {
  text-align: center;
  margin: 15px auto;
}
</style>


