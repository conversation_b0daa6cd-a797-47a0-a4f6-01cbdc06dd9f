<template>
  <div>
    <el-row>
      <el-col :span="24">
        <topbar type="adminorg" />
      </el-col>
      <el-col :span="24">
        <div>
          <data-table
            ref="companyList"
            :pageInfo="adminorgList.pageInfo"
            :dataList="adminorgList.docs"
            :comprehensiveLevels="adminorgList.pageInfo.comprehensiveLevels"
          />
        </div>
      </el-col>
      <el-col :span="24">
        <div>
          <pagination
            device="device"
            :pageInfo="adminorgList.pageInfo"
            :pageInfos="pageInfos"
            pageType="adminorg"
          />
        </div>
      </el-col>
      <el-col :span="24">
        <div class="button">
          <!-- <el-button @click="previous" type="primary">返回</el-button>
          <el-button @click="next" type="primary">选择课程</el-button> -->
          <el-button
            @click="previous"
            type="primary"
            size="mini"
            >返回</el-button
          >
          <el-button @click="next" type="primary" size="mini"
            >考卷设置</el-button
          >
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import pagination from "./companys/Pagination";
import topbar from "./companys/topbar";
import dataTable from "./companys/dataTable";
import { mapGetters } from "vuex";
export default {
  props: {
    needExam: Boolean,
  },
  data() {
    return {
      superviseInf: {},
    };
  },
  methods: {
    previous() {
      this.$emit('previousCourses');
    },
    next() {
      const companys = this.$refs["companyList"].next();
      console.log(companys);
      if (!companys.length) {
        this.$message.error("请先选择需要培训的企业哦");
        return;
      }
      this.$emit("toPlanInfo", companys);
    },
    createNow() {
      const companys = this.$refs["companyList"].next();
      if (!companys.length) {
        this.$message.error("请先选择需要培训的企业哦");
        return;
      }
      this.$parent.$parent.createNewPlan(this.plan, companys);
    },
  },
  created() {
    // 获取所有企业列表
    // console.log(this.$store.getters.searchKeys.district,'this.$store.getters.districts.searchkey');
    if (
      typeof this.$store.getters.searchKeys.district === "string" &&
      this.$store.getters.searchKeys.district !== ""
    ) {
      this.$store.getters.searchKeys.district = this.$store.getters.searchKeys.district.split(
        " "
      );
    }
    let obj = {
      sortType:{
        order: this.sortType.order ? this.sortType.order : '',
        prop:  this.sortType.prop ? this.sortType.prop : ''
      },
      pageInfos:{
        current: this.pageInfos.current ? this.pageInfos.current : 1,
        pageSize: this.pageInfos.pageSize ? this.pageInfos.pageSize : 10,
      },
      searchkey: this.$store.getters.searchKeys.searchkey ? this.$store.getters.searchKeys.searchkey : '',
      district: this.$store.getters.searchKeys.district ? this.$store.getters.searchKeys.district : '',
      industryCategory: this.$store.getters.searchKeys.industryCategory? this.$store.getters.searchKeys.industryCategory : '',
      levelOption: this.$store.getters.searchKeys.levelOption? this.$store.getters.searchKeys.levelOption : '',
      OnlineDeclarationOption: this.$store.getters.searchKeys.OnlineDeclarationOption ? this.$store.getters.searchKeys.OnlineDeclarationOption : '',
      jobHealthOption: this.$store.getters.searchKeys.jobHealthOption ? this.$store.getters.searchKeys.jobHealthOption : '',
      ratioArrOption: this.$store.getters.searchKeys.ratioArrOption ? this.$store.getters.searchKeys.ratioArrOption : '',
      exceedOption: this.$store.getters.searchKeys.exceedOption ? this.$store.getters.searchKeys.exceedOption : '',
      odiseasedOption: this.$store.getters.searchKeys.odiseasedOption ? this.$store.getters.searchKeys.odiseasedOption : '',
      suspectedOption: this.$store.getters.searchKeys.suspectedOption ? this.$store.getters.searchKeys.suspectedOption : '',
      selfAssessmentOption: this.$store.getters.searchKeys.selfAssessmentOption ? this.$store.getters.searchKeys.selfAssessmentOption : '',
      assessmentResultOption: this.$store.getters.searchKeys.assessmentResultOption ? this.$store.getters.searchKeys.assessmentResultOption : '',
      comprehensiveLevelOption: this.$store.getters.searchKeys.comprehensiveLevelOption ? this.$store.getters.searchKeys.comprehensiveLevelOption : '',
      riskSortLevelOption: this.$store.getters.searchKeys.riskSortLevelOption ? this.$store.getters.searchKeys.riskSortLevelOption : '',
      punishOption: this.$store.getters.searchKeys.punishOption ? this.$store.getters.searchKeys.punishOption : '',
      superviseOption: this.$store.getters.searchKeys.superviseOption ? this.$store.getters.searchKeys.superviseOption : '',
      isDeleteOption: this.$store.getters.searchKeys.isDeleteOption ? this.$store.getters.searchKeys.isDeleteOption : '1',
    };
    obj.district = JSON.stringify(obj.district);
    this.$store.dispatch("adminorg/getAdminorgList", obj);
  },
  components: {
    pagination,
    topbar,
    dataTable,
  },
  computed: {
    ...mapGetters(["adminorgList", "pageInfos", "sortType"]),
  },
};
</script>

<style scoped>
.button {
  display: block;
  text-align: center;
}
</style>