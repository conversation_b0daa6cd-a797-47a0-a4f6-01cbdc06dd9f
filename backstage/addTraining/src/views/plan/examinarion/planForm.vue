<template>
  <div>
    <el-form
      :model="plan"
      :rules="planRules"
      ref="planForm"
      class="demo-ruleForm"
      size="mini"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="单选题" prop="required">
            <el-row :gutter="10">
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <el-input
                  @change="subAllScores"
                  class="input-number"
                  type="number"
                  v-model="plan.examination.singleChoice.num"
                ></el-input>
                <span>&ensp;题</span>
              </el-col>
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <span>每题&ensp;</span>
                <el-input
                  @change="subAllScores"
                  class="input-number"
                  type="number"
                  v-model="plan.examination.singleChoice.scores"
                ></el-input>
                <span>&ensp;分</span>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="多选题" prop="required">
            <el-row :gutter="10">
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <el-input
                  @change="subAllScores"
                  class="input-number"
                  type="number"
                  v-model="plan.examination.multipleChoice.num"
                ></el-input>
                <span>&ensp;题</span>
              </el-col>
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <span>每题&ensp;</span>
                <el-input
                  @change="subAllScores"
                  class="input-number"
                  type="number"
                  v-model="plan.examination.multipleChoice.scores"
                ></el-input>
                <span>&ensp;分</span>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="判断题" prop="required">
            <el-row :gutter="10">
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <el-input
                  @change="subAllScores"
                  class="input-number"
                  type="number"
                  v-model="plan.examination.Judgment.num"
                ></el-input>
                <span>&ensp;题</span>
              </el-col>
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <span>每题&ensp;</span>
                <el-input
                  @change="subAllScores"
                  class="input-number"
                  type="number"
                  v-model="plan.examination.Judgment.scores"
                ></el-input>
                <span>&ensp;分</span>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="总分数" prop="required">
            <!-- <span>{{ subAllScores() }}</span> -->
            &thinsp;
            <el-input
              style="width: 50%"
              type="number"
              v-model="totalScore"
              disabled
            ></el-input
            ><span>&ensp;分</span>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="及格分占比">
            <el-input
              style="width: 40%"
              type="number"
              v-model="plan.examination.passingGrate"
            ></el-input>
            <span>&ensp;%</span>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="允许直接考试">
            <el-switch style="display: block" v-model="plan.allowTestOnly">
            </el-switch>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="考试时长">
            <el-input
              style="width: 50%"
              type="number"
              v-model="plan.examination.limitTime"
            ></el-input>
            <span>&ensp;分钟</span>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <div style="margin-top: 20px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-button
                  @click="previousSort"
                  style="width: 100%"
                  type="primary"
                  >返回</el-button
                >
              </el-col>
              <el-col :span="12">
                <el-button
                  @click="submitForm('planForm')"
                  style="width: 100%"
                  type="primary"
                  >立即创建</el-button
                >
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
// import { getCertificateList } from "@/api/addTraining";
export default {
  data() {
    var validatePass = (rule, value, callback) => {
      callback();
    };
    return {
      plan: {
        // name: "",
        examination: {
          singleChoice: {
            num: 10,
            scores: 5,
          },
          multipleChoice: {
            num: 10,
            scores: 5,
          },
          Judgment: {
            num: 0,
            scores: 0,
          },
          fillBlank: {
            num: 0,
            scores: 0,
          },
          essayQuestion: {
            num: 0,
            scores: 0,
          },
          passingGrate: 60,
          limitTime: 30,
        },
        allowTestOnly: false,
        completeTime: null,
      },
      planRules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "长度在 2 到 50 个字符",
            trigger: "blur",
          },
        ],
        required: [
          { required: true, validator: validatePass, trigger: "blur" },
        ],
        completeTime: [
          { required: true, message: "请选择计划完成时间", trigger: "change" },
        ],
      },
      totalScore: 100,
    };
  },
  methods: {
    subAllScores() {
      this.totalScore = Number(
        this.plan.examination.singleChoice.num *
          this.plan.examination.singleChoice.scores +
          this.plan.examination.multipleChoice.num *
            this.plan.examination.multipleChoice.scores +
          this.plan.examination.Judgment.num *
            this.plan.examination.Judgment.scores
      );
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log(this.plan);
          this.$parent.$parent.createNewPlan(this.plan);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    previousSort() {
      this.$parent.$parent.previousSort();
    },
  },
  created() {
    // getCertificateList({}).then(({ data }) => {
    //   this.certificateList = data.list;
    // });
  },
};
</script>

<style scoped>
.input-number {
  width: 60%;
}
</style>