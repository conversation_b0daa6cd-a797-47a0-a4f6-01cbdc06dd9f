<template>
  <div>
    <el-row>
      <el-col :span="24">
        <sort-table ref="sortList" :courseList="courseList" />
      </el-col>
      <el-col :span="24">
        <div style="margin-top: 20px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-button @click="previous" style="width: 100%" type="primary"
                >取消</el-button
              >
            </el-col>
            <el-col :span="12">
              <el-button @click="next" style="width: 100%" type="primary"
                >确定</el-button
              >
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import sortTable from "./courses/sort";
export default {
  props: {
    courseList: Array,
    needExam: Boolean,
  },
  components: {
    sortTable,
  },
  data() {
    return {
      // 在设置不需要考试时，直接跳过考试信息创建，这里设置默认值，保持数据库记录格式一致性
      plan: {
        // name: "",
        examination: {
          singleChoice: {
            num: 0,
            scores: 0,
          },
          multipleChoice: {
            num: 0,
            scores: 0,
          },
          Judgment: {
            num: 0,
            scores: 0,
          },
          fillBlank: {
            num: 0,
            scores: 0,
          },
          essayQuestion: {
            num: 0,
            scores: 0,
          },
          passingGrate: 0,
          limitTime: 0,
        },
        allowTestOnly: false,
        completeTime: null,
      },
    };
  },
  methods: {
    createNow(){
      const afterSortCourses = this.$refs["sortList"].next();
      this.$parent.$parent.createNewPlan(this.plan, afterSortCourses)
    },
    next() {
      const afterSortCourses = this.$refs["sortList"].next();
      console.log(afterSortCourses);
      this.$parent.$parent.finishSort(afterSortCourses);
      // console.log(this.$parent.$parent)
    },
    previous() {
      this.$parent.$parent.cancelSort();
    },
  },
};
</script>

<style scoped>
.button {
  display: block;
  text-align: center;
}
</style>