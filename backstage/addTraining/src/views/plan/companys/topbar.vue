<template>
  <div class="topbar">
    <el-row>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="OnlineDeclarationOption" placeholder="是否申报" size="small" clearable multiple collapse-tags @change="searchResult">
          <el-option
            v-for="item in OnlineDeclarationOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="jobHealthOption" placeholder="是否检测" size="small" clearable multiple collapse-tags @change="searchResult">
          <el-option
            v-for="item in jobHealthOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="ratioArrOption" placeholder="是否体检" size="small" clearable multiple collapse-tags @change="searchResult">
          <el-option
            v-for="item in ratioArrOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="exceedOption" placeholder="是否超标" size="small" clearable @change="searchResult">
          <el-option
            v-for="item in exceedOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="levelOption" placeholder="风险等级" size="small" clearable @change="searchResult">
          <el-option
            v-for="item in levelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="odiseasedOption" placeholder="是否有职业病" size="small" clearable @change="searchResult">
          <el-option
            v-for="item in odiseasedOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="4">
         <el-select v-model="suspectedOption" placeholder="是否有疑似职业病" size="small" clearable @change="searchResult" style="width:100%">
          <el-option
            v-for="item in suspectedOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="selfAssessmentOption" placeholder="责任自查" size="small" clearable multiple collapse-tags @change="searchResult">
          <el-option
            v-for="item in selfAssessmentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="assessmentResultOption" placeholder="危害暴露等级" size="small" clearable multiple collapse-tags @change="searchResult">
          <el-option
            v-for="item in assessmentResultOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="comprehensiveLevelOption" placeholder="综合风险等级" size="small" clearable multiple collapse-tags @change="searchResult">
          <el-option
            v-for="item in comprehensiveLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="riskSortLevelOption" placeholder="分类等级" size="small" clearable multiple collapse-tags @change="searchResult">
          <el-option
            v-for="item in riskSortLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="punishOption" placeholder="是否有处罚" size="small" clearable @change="searchResult">
          <el-option
            v-for="item in punishOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
        <el-cascader
          @visible-change='visibleChange'
          v-show="editStatus"
          style="width:100%"
          v-model="district"
          :props="districtListProps"
          @change="searchResult"
          clearable collapse-tags
          ref="regAddCas"
          size="small"
          placeholder="请选择地区"
        >
        </el-cascader>
        <el-input type="text" @focus.stop='focus' :value="this.district.map(item=>item.join('/')).join('、')" v-show="!editStatus" style="width:100%" placeholder="请选择地区" size="small"></el-input>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="4">
        <el-cascader
          style="width:100%"
          placeholder="请选择行业类别" size="mini"
          v-model="industryCategory"
          :options="industryCategoryOptions"
          :props="{ multiple: true, checkStrictly: true }"
          collapse-tags filterable
          @change="searchResult"
          ref="industryCas"
          clearable
        ></el-cascader>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="3">
         <el-select v-model="superviseOption" placeholder="是否监管" size="small" clearable @change="searchResult">
          <el-option
            v-for="item in superviseOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
      <el-col :xs="12" :sm="8" :md="6" :lg="6">
        <el-input
          class="dr-searchInput"
          size="small"
          clearable
          :placeholder="'可通过' + '[' + $t('adminorg.cname') + ']' + '或' + '[' + $t('adminorg.corp') + ']' + '来搜索'"
          v-model="searchkey"
          @keyup.enter.native="searchResult"
          @clear="searchResult"
        >
          <el-button slot="append" icon="el-icon-search" type="primary" @click="searchResult"></el-button>
        </el-input>
      </el-col>
      <el-col :span="2">
        <el-button type="warning" size="small" @click="deleteSearchKeys">重置</el-button>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { getDistrictList, getIndustryCategory } from "@/api/adminorg";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters(["adminorgList", "pageInfos"]),
  },
  data() {
    return {
      editStatus:false, // 地区筛选那里是显示级联选择器还是输入框
      showSaveLoad: false,
      isDeleteOption: "1", // 是否删除选择
      rules: {
        date: [{ required: true, message: "请选择培训日期", trigger: "blur" }],
        time: [{ required: true, message: "请选择培训开始时间", trigger: "blur" },],
        address: [{ required: true, message: "请输入培训地点", trigger: "blur" }],
      },
      training: false, // 是否是提醒培训
      area: '', //账号的区域
      thisRows:[], //所有需要发送消息的企业
      nowyear: 0, //当前年份
      superUserInfo:{},   //本账号的信息
      messageForm: {
        title: '',
        message: "",
        date: " ",
        time: " ",
        address: " ",
        comments: "",
        isShow: false,
        defaultInfo: {
          EnterpriseID: "",
          cname: "",
        },
      },
      searchkey: "",
      superviseOption: "",
      superviseOptions:[{
        label: "有",
        value: "1",
      },{
        label: "无",
        value: "0",
      }],
      punishOption: "",
      punishOptions:[{
        label: "有",
        value: "1",
      },{
        label: "无",
        value: "0",
      }],
      levelOption: "",
      levelOptions:[{
        label: "一般",
        value: "一般",
      },{
        label: "严重",
        value: "严重",
      }],
      assessmentResultOption: [],
      assessmentResultOptions:[{
        label: "低风险",
        value: "0",
      },{
        label: "中风险",
        value: "1",
      },{
        label: "高风险",
        value: "2",
      }],
      suspectedOption: "",
      suspectedOptions:[{
        label: "有疑似职业病",
        value: "1",
      },{
        label: "没有疑似职业病",
        value: "0",
      }],
      odiseasedOption: "",
      odiseasedOptions:[{
        label: "有职业病",
        value: "1",
      },{
        label: "没有职业病",
        value: "0",
      }],
      exceedOption: "",
      exceedOptions:[{
        label: "有超标",
        value: "1",
      },{
        label: "未超标",
        value: "0",
      }],
      ratioArrOption: [],
      ratioArrOptions:[{
        label: "已体检",
        value: "3",
      },{
        label: "将到期",
        value: "2",
      },{
        label: "已过期",
        value: "1",
      },{
        label: "未体检",
        value: "0",
      }],
      jobHealthOption: [],
      jobHealthOptions:[{
        label: "已检测",
        value: "3",
      },{
        label: "将到期",
        value: "2",
      },{
        label: "已过期",
        value: "1",
      },{
        label: "未检测",
        value: "0",
      }],
      OnlineDeclarationOption: [],
      OnlineDeclarationOptions:[{
        label: "已申报",
        value: "3",
      },{
        label: "将到期",
        value: "2",
      },{
        label: "已过期",
        value: "1",
      },{
        label: "未申报",
        value: "0",
      }],
      district: [],
      districtListProps: {
        multiple: true,
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.data.area_code;
          }
          getDistrictList(params).then((response) => {
            try {
              const districts = Array.from(response.data.docs);
              let nodes = districts.map((item) => ({
                value: item.name,
                label: item.name,
                area_code: item.area_code,
                // leaf: item.level >= 3,
                leaf: item.hasChildren ? item.level>=2 : item.level>=3 ,
                disabled: item.name === '市辖区' ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
      industryCategory:[],//选择的行业类别 
      industryCategoryOptions: [],   //所有行业分类
      selfAssessmentOption: [],
      selfAssessmentOptions:[{
        label: "C",
        value: '2',
      },{
        label: "B",
        value: '1',
      },{
        label: "A",
        value: '0',
      }],
      comprehensiveLevelOption: [],
      comprehensiveLevelOptions:[{
        label: "0级",
        value: "0",
      },{
        label: "Ⅰ级",
        value: "1",
      },{
        label: "Ⅱ级",
        value: "2",
      },{
        label: "Ⅲ级",
        value: "3",
      },{
        label: "Ⅳ级",
        value: "4",
      }],
      riskSortLevelOption: [],
      riskSortLevelOptions:[{
        label: "丙",
        value: "1",
      },{
        label: "乙",
        value: "2",
      },{
        label: "甲",
        value: "3",
      }],
    };
  },
  watch:{
    editStatus(val){
      // 是编辑状态的话展开下拉框
      if(val) this.$refs.regAddCas.dropDownVisible = true;
    },
  },
  created(){
    // 获取当前年份
    this.nowyear = new Date().getFullYear();
    // 得到所有的行业分类
    getIndustryCategory().then((res) => {
        try {
            this.industryCategoryOptions = res.data;
        } catch (error) {
            console.log(error);
        }
    });
    if (this.$store.getters.searchKeys.superviseOption){
      this.superviseOption = this.$store.getters.searchKeys.superviseOption;
    }
    if (this.$store.getters.searchKeys.punishOption){
      this.punishOption = this.$store.getters.searchKeys.punishOption;
    }
    if (this.$store.getters.searchKeys.OnlineDeclarationOption){
      this.OnlineDeclarationOption = JSON.parse(this.$store.getters.searchKeys.OnlineDeclarationOption);
    }
    if (this.$store.getters.searchKeys.jobHealthOption){
      this.jobHealthOption = JSON.parse(this.$store.getters.searchKeys.jobHealthOption);
    }
    if (this.$store.getters.searchKeys.ratioArrOption){
      this.ratioArrOption = JSON.parse(this.$store.getters.searchKeys.ratioArrOption);
    }
    if (this.$store.getters.searchKeys.exceedOption){
      this.exceedOption = this.$store.getters.searchKeys.exceedOption;
    }
    if (this.$store.getters.searchKeys.odiseasedOption){
      this.odiseasedOption = this.$store.getters.searchKeys.odiseasedOption;
    }
    if (this.$store.getters.searchKeys.suspectedOption){
      this.suspectedOption = this.$store.getters.searchKeys.suspectedOption;
    }
    // 风险评估
    if (this.$store.getters.searchKeys.levelOption){
      this.levelOption = this.$store.getters.searchKeys.levelOption;
    }
    // console.log(this.districtListProps,'this.$store.getters.searchKeys.district');
    // if(this.$store.getters.searchKeys.district &&  this.$store.getters.searchKeys.district[3] === '') {
    //   const district = this.$store.getters.searchKeys.district;
    //   this.district[0] = district[0];
    //   this.district[1] = district[1];
    //   this.district[2] = district[2];
    // }
    if(this.$store.getters.searchKeys.district){
      this.district = this.$store.getters.searchKeys.district;
    }else{
      this.district = [];
    }
    // console.log(this.district,typeof this.district,'4444444444444444444444444444');
    // if (this.$store.getters.searchKeys.selfAssessmentOption){
    //   this.selfAssessmentOption = this.$store.getters.searchKeys.selfAssessmentOption === '0' ? 'A' : (this.$store.getters.searchKeys.selfAssessmentOption === '1' ? 'B' : 'C')
    // }
    this.selfAssessmentOption = this.$store.getters.searchKeys.selfAssessmentOption ? JSON.parse(this.$store.getters.searchKeys.selfAssessmentOption) : [];
    // if (this.$store.getters.searchKeys.assessmentResultOption){
    //   if(this.$store.getters.searchKeys.assessmentResultOption === '2'){
    //     this.assessmentResultOption = '高风险';
    //   }else if(this.$store.getters.searchKeys.assessmentResultOption === '1'){
    //     this.assessmentResultOption = '中风险';
    //   }else{
    //     this.assessmentResultOption = '低风险';
    //   }
    // }
    this.assessmentResultOption = this.$store.getters.searchKeys.assessmentResultOption ? JSON.parse(this.$store.getters.searchKeys.assessmentResultOption) : [];
    // if (this.$store.getters.searchKeys.comprehensiveLevelOption){
    //   if(this.$store.getters.searchKeys.comprehensiveLevelOption === '4'){
    //     this.comprehensiveLevelOption = 'Ⅳ级';
    //   }else if(this.$store.getters.searchKeys.comprehensiveLevelOption === '3'){
    //     this.comprehensiveLevelOption = 'Ⅲ级';
    //   }if(this.$store.getters.searchKeys.comprehensiveLevelOption === '2'){
    //     this.comprehensiveLevelOption = 'Ⅱ级';
    //   }else if(this.$store.getters.searchKeys.comprehensiveLevelOption === '1'){
    //     this.comprehensiveLevelOption = 'Ⅰ级';
    //   }else{
    //     this.comprehensiveLevelOption = '0级';
    //   }
    // }
    this.comprehensiveLevelOption = this.$store.getters.searchKeys.comprehensiveLevelOption ? JSON.parse(this.$store.getters.searchKeys.comprehensiveLevelOption) : [];
    // if (this.$store.getters.searchKeys.riskSortLevelOption){
    //   if(this.$store.getters.searchKeys.riskSortLevelOption === '3'){
    //     this.riskSortLevelOption = '甲';
    //   }if(this.$store.getters.searchKeys.riskSortLevelOption === '2'){
    //     this.riskSortLevelOption = '乙';
    //   }else if(this.$store.getters.searchKeys.riskSortLevelOption === '1'){
    //     this.riskSortLevelOption = '丙';
    //   }
    // }
    this.riskSortLevelOption = this.$store.getters.searchKeys.riskSortLevelOption ? JSON.parse(this.$store.getters.searchKeys.riskSortLevelOption) : [];
    this.industryCategory = this.$store.getters.searchKeys.industryCategory ? JSON.parse(this.$store.getters.searchKeys.industryCategory) : [];
    this.searchkey = this.$store.getters.searchKeys.searchkey ? this.$store.getters.searchKeys.searchkey : '';
    this.isDeleteOption = this.$store.getters.searchKeys.isDeleteOption ? this.$store.getters.searchKeys.isDeleteOption : '1';
  },
  methods: {
    focus(){
      this.editStatus=true;
    },
    visibleChange(status){
      if(!status) this.editStatus = false;
    },
    // 重置搜索条件
    deleteSearchKeys(){
      this.$store.dispatch("adminorg/setSearchKeys", {
        district: [],
        searchkey: '',
        industryCategory: '',
        selfAssessmentOption: '',
        assessmentResultOption: [],
        comprehensiveLevelOption: '',
        riskSortLevelOption: '',
        OnlineDeclarationOption: '',
        jobHealthOption: '',
        ratioArrOption: '',
        exceedOption: '',
        suspectedOption: '',
        odiseasedOption: '',
        levelOption: '',
        punishOption: '',
        isDeleteOption: '',
      });
      this.$store.dispatch("adminorg/listPageInfo", {
        pageSize: 10,
        current: 0,
      });
      this.$store.dispatch("adminorg/sortTypeChange", {
        order: '',
        prop: '',
      });
      this.district = [];
      this.searchkey = this.industryCategory = this.selfAssessmentOption = this.assessmentResultOption = this.comprehensiveLevelOption = this.riskSortLevelOption = this.OnlineDeclarationOption = this.jobHealthOption = this.ratioArrOption = this.exceedOption = this.suspectedOption = this.odiseasedOption = this.levelOption = this.punishOption = '';
      let obj = {
        sortType:{
          order: this.$store.getters.sortType.order ? this.$store.getters.sortType.order : '',
          prop:  this.$store.getters.sortType.prop ? this.$store.getters.sortType.prop : ''
        },
        pageInfos:{
          current: this.$store.getters.pageInfos.current ? this.$store.getters.pageInfos.current : 1,
          pageSize: this.$store.getters.pageInfos.pageSize ? this.$store.getters.pageInfos.pageSize : 10,
        },
        searchkey: this.$store.getters.searchKeys.searchkey ? this.$store.getters.searchKeys.searchkey : '',
        district: this.$store.getters.searchKeys.district ? this.$store.getters.searchKeys.district : '',
        industryCategory: this.$store.getters.searchKeys.industryCategory? this.$store.getters.searchKeys.industryCategory : '',
        levelOption: this.$store.getters.searchKeys.levelOption? this.$store.getters.searchKeys.levelOption : '',
        OnlineDeclarationOption: this.$store.getters.searchKeys.OnlineDeclarationOption ? this.$store.getters.searchKeys.OnlineDeclarationOption : '',
        jobHealthOption: this.$store.getters.searchKeys.jobHealthOption ? this.$store.getters.searchKeys.jobHealthOption : '',
        ratioArrOption: this.$store.getters.searchKeys.ratioArrOption ? this.$store.getters.searchKeys.ratioArrOption : '',
        exceedOption: this.$store.getters.searchKeys.exceedOption ? this.$store.getters.searchKeys.exceedOption : '',
        odiseasedOption: this.$store.getters.searchKeys.odiseasedOption ? this.$store.getters.searchKeys.odiseasedOption : '',
        suspectedOption: this.$store.getters.searchKeys.suspectedOption ? this.$store.getters.searchKeys.suspectedOption : '',
        selfAssessmentOption: this.$store.getters.searchKeys.selfAssessmentOption ? this.$store.getters.searchKeys.selfAssessmentOption : '',
        assessmentResultOption: this.$store.getters.searchKeys.assessmentResultOption ? this.$store.getters.searchKeys.assessmentResultOption : '',
        comprehensiveLevelOption: this.$store.getters.searchKeys.comprehensiveLevelOption ? this.$store.getters.searchKeys.comprehensiveLevelOption : '',
        riskSortLevelOption: this.$store.getters.searchKeys.riskSortLevelOption ? this.$store.getters.searchKeys.riskSortLevelOption : '',
        punishOption: this.$store.getters.searchKeys.punishOption ? this.$store.getters.searchKeys.punishOption : '',
        superviseOption: this.$store.getters.searchKeys.superviseOption ? this.$store.getters.searchKeys.superviseOption : '',
        isDeleteOption: this.$store.getters.searchKeys.isDeleteOption ? this.$store.getters.searchKeys.isDeleteOption : '1',
      };
      obj.district = JSON.stringify(obj.district);
      this.$store.dispatch("adminorg/getAdminorgList", obj);
    },
   
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    searchResult() {
      this.pageInfos.current = 1;
      let params = {
        searchkey: this.searchkey,
      }
      params.isDeleteOption = this.isDeleteOption;
      // 当是否监管有选项时
      if (this.superviseOption) {
        params.superviseOption = this.superviseOption;
      }
      // 当是否有处罚有选项时
      if (this.punishOption) {
        params.punishOption = this.punishOption;
      }
      // 当风险等级有选项时
      if (this.levelOption) {
        params.levelOption = this.levelOption;
      }
      // 当责任自查有选项时
      if (this.selfAssessmentOption) {
        params.selfAssessmentOption = JSON.stringify(this.selfAssessmentOption);
      }
      // 当是否有疑似职业病有选项时
      if (this.suspectedOption) {
        params.suspectedOption = this.suspectedOption;
      }
      // 当是否有职业病有选项时
       if (this.odiseasedOption) {
        params.odiseasedOption = this.odiseasedOption;
      }
      // 当是否超标有选项时
       if (this.exceedOption) {
        params.exceedOption = this.exceedOption;
      }
      // 当是否体检有选项时
       if (this.ratioArrOption) {
        params.ratioArrOption = JSON.stringify(this.ratioArrOption);
      }
      // 当是否检测有选项时
      if (this.jobHealthOption) {
        params.jobHealthOption = JSON.stringify(this.jobHealthOption);
      }
      // 当是否申报有选项时
      if (this.OnlineDeclarationOption) {
        params.OnlineDeclarationOption = JSON.stringify(this.OnlineDeclarationOption);
      }
      // 当危害暴露等级有选项时
      if (this.assessmentResultOption) {
        params.assessmentResultOption = JSON.stringify(this.assessmentResultOption);
      }
      // 当综合风险等级有选项时
      if (this.comprehensiveLevelOption) {
        params.comprehensiveLevelOption = JSON.stringify(this.comprehensiveLevelOption);
      }
      // 当分类等级有选项时
      if (this.riskSortLevelOption) {
        params.riskSortLevelOption = JSON.stringify(this.riskSortLevelOption);
      }
      // 当行业分类有选择时
       if (this.industryCategory) {
        params.industryCategory = JSON.stringify(this.industryCategory);
      }
      // 当地区有选择时
       if (this.district) {
        params.district = JSON.stringify(this.district);
      }
      this.$store.dispatch("adminorg/setSearchKeys", {
        district: this.district,
        searchkey: this.searchkey,
        industryCategory: params.industryCategory,
        selfAssessmentOption: params.selfAssessmentOption,
        assessmentResultOption: params.assessmentResultOption,
        comprehensiveLevelOption:params.comprehensiveLevelOption,
        riskSortLevelOption:params.riskSortLevelOption,
        OnlineDeclarationOption: params.OnlineDeclarationOption,
        jobHealthOption: params.jobHealthOption,
        ratioArrOption: params.ratioArrOption,
        exceedOption: params.exceedOption,
        suspectedOption: params.suspectedOption,
        odiseasedOption: params.odiseasedOption,
        levelOption: this.levelOption, // 当风险等级有选项时
        punishOption: this.punishOption,
        superviseOption: this.superviseOption,
        isDeleteOption: this.isDeleteOption,
      });
      params.pageInfos = this.pageInfos;
      this.$store.dispatch("adminorg/getAdminorgList", params);
    },
    gotoExcelInput() {
      this.$router.push({
        name: "enterAdminorgGov"
      });
    },
  },
};
</script>
<style scoped>
  .topbar{
    margin-bottom: 10px;
  }
  .el-button--small{
    line-height: 1.2;
  }
</style>