<template>
  <div>
    <div style="margin-top: 2px">
      <el-row>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="jobHealthOption"
            placeholder="是否检测"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in jobHealthOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="ratioArrOption"
            placeholder="是否体检"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in ratioArrOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="exceedOption"
            placeholder="是否超标"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in exceedOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="levelOption"
            placeholder="风险等级"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in levelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="odiseasedOption"
            placeholder="是否有职业病"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in odiseasedOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="suspectedOption"
            placeholder="疑似职业病"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in suspectedOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="assessmentResultOption"
            placeholder="危害暴露等级"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in assessmentResultOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="comprehensiveLevelOption"
            placeholder="综合风险等级"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in comprehensiveLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="riskSortLevelOption"
            placeholder="分类等级"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in riskSortLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-cascader
            style="width: 100%"
            :props="districtListProps"
            @change="searchResult"
            v-model="district"
            clearable
            ref="regAddCas"
            size="small"
            placeholder="请选择地区"
          >
            <template slot-scope="{ data }">
              <span>{{ data.label }}</span>
            </template>
          </el-cascader>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-cascader
            style="width: 100%"
            placeholder="请选择行业类别"
            size="small"
            v-model="industryCategory"
            :options="industryCategoryOptions"
            :props="{ multiple: true, checkStrictly: true }"
            collapse-tags
            @change="searchResult"
            ref="industryCas"
            clearable
          ></el-cascader>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="3" :xl="4">
          <el-select
            v-model="trainState"
            placeholder="是否培训"
            size="small"
            clearable
            @change="searchResult"
          >
            <el-option
              v-for="item in trainStates"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="12" :xl="4">
          <el-input
            class="dr-searchInput"
            size="small"
            clearable
            :placeholder="
              '可通过' +
              '[' +
              $t('adminorg.cname') +
              ']' +
              '或' +
              '[' +
              $t('adminorg.corp') +
              ']' +
              '来搜索'
            "
            v-model="searchkey"
            suffix-icon="el-icon-search"
            @keyup.enter.native="searchResult"
            @clear="searchResult"
          ></el-input>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { getDistrictList, getIndustryCategory } from "@/api/adminorg";

import { mapGetters } from "vuex";
export default {
  data() {
    return {
      searchkey: "",
      levelOption: "",
      levelOptions: [
        {
          label: "一般",
          value: "一般",
        },
        // {
        //   label: "较重",
        //   value: "较重",
        // },
        {
          label: "严重",
          value: "严重",
        },
      ],
      assessmentResultOption: "",
      assessmentResultOptions: [
        {
          label: "低风险",
          value: "0",
        },
        {
          label: "中风险",
          value: "1",
        },
        {
          label: "高风险",
          value: "2",
        },
      ],
      suspectedOption: "",
      suspectedOptions: [
        {
          label: "有疑似职业病",
          value: "1",
        },
        {
          label: "没有疑似职业病",
          value: "0",
        },
      ],
      odiseasedOption: "",
      odiseasedOptions: [
        {
          label: "有职业病",
          value: "1",
        },
        {
          label: "没有职业病",
          value: "0",
        },
      ],
      exceedOption: "",
      exceedOptions: [
        {
          label: "有超标",
          value: "1",
        },
        {
          label: "未超标",
          value: "0",
        },
      ],
      ratioArrOption: "",
      ratioArrOptions: [
        {
          label: "已体检",
          value: "3",
        },
        {
          label: "将到期",
          value: "2",
        },
        {
          label: "已过期",
          value: "1",
        },
        {
          label: "未体检",
          value: "0",
        },
      ],
      jobHealthOption: "",
      jobHealthOptions: [
        {
          label: "已检测",
          value: "3",
        },
        {
          label: "将到期",
          value: "2",
        },
        {
          label: "已过期",
          value: "1",
        },
        {
          label: "未检测",
          value: "0",
        },
      ],
      OnlineDeclarationOption: "",
      OnlineDeclarationOptions: [
        {
          label: "已申报",
          value: "3",
        },
        {
          label: "将到期",
          value: "2",
        },
        {
          label: "已过期",
          value: "1",
        },
        {
          label: "未申报",
          value: "0",
        },
      ],
      district: [],
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.data.area_code;
          }
          getDistrictList(params).then((response) => {
            try {
              const districts = Array.from(response.data.docs);
              let nodes = districts.map((item) => ({
                value: item.name,
                label: item.name,
                area_code: item.area_code,
                leaf: item.level >= 3,
                disabled: item.name === "市辖区" ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
      industryCategory: [], //选择的行业类别
      industryCategoryOptions: [], //所有行业分类
      selfAssessmentOption: "",
      selfAssessmentOptions: [
        {
          label: "C",
          value: "2",
        },
        {
          label: "B",
          value: "1",
        },
        {
          label: "A",
          value: "0",
        },
      ],
      assessmentResultOption: "",
      assessmentResultOptions: [
        {
          label: "低风险",
          value: "0",
        },
        {
          label: "中风险",
          value: "1",
        },
        {
          label: "高风险",
          value: "2",
        },
      ],
      comprehensiveLevelOption: "",
      comprehensiveLevelOptions: [
        {
          label: "0级",
          value: "0",
        },
        {
          label: "Ⅰ级",
          value: "1",
        },
        {
          label: "Ⅱ级",
          value: "2",
        },
        {
          label: "Ⅲ级",
          value: "3",
        },
        {
          label: "Ⅳ级",
          value: "4",
        },
      ],
      riskSortLevelOption: "",
      riskSortLevelOptions: [
        {
          label: "丙",
          value: "1",
        },
        {
          label: "乙",
          value: "2",
        },
        {
          label: "甲",
          value: "3",
        },
      ],
      trainState: "",
      trainStates: [
        {
          label: "今年未参与培训",
          value: false,
        },
        {
          label: "今年已参与培训",
          value: true,
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["adminorgList", "pageInfos"]),
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    async searchResult() {
      this.pageInfos.current = 1;
      let params = {
        searchkey: this.searchkey,
      };
      // 当风险等级有选项时
      if (this.levelOption) {
        params.levelOption = this.levelOption;
      }
      // 当责任自查有选项时
      if (this.selfAssessmentOption) {
        params.selfAssessmentOption =
          this.selfAssessmentOption === "A"
            ? "0"
            : this.selfAssessmentOption === "B"
            ? "1"
            : this.selfAssessmentOption === "C"
            ? "2"
            : this.selfAssessmentOption;
      }
      // 当是否有疑似职业病有选项时
      if (this.suspectedOption) {
        // params.suspectedOption = Number(this.suspectedOption);
        params.suspectedOption =
          this.suspectedOption === "有疑似职业病"
            ? "1"
            : this.suspectedOption === "没有疑似职业病"
            ? "0"
            : this.suspectedOption;
      }
      // 当是否有职业病有选项时
      if (this.odiseasedOption) {
        // params.odiseasedOption = Number(this.odiseasedOption);
        params.odiseasedOption =
          this.odiseasedOption === "有职业病"
            ? "1"
            : this.odiseasedOption === "没有职业病"
            ? "0"
            : this.odiseasedOption;
      }
      // 当是否超标有选项时
      if (this.exceedOption) {
        // params.exceedOption = Number(this.exceedOption);
        params.exceedOption =
          this.exceedOption === "有超标"
            ? "1"
            : this.exceedOption === "未超标"
            ? "0"
            : this.exceedOption;
      }
      // 当是否体检有选项时
      if (this.ratioArrOption) {
        // params.ratioArrOption = Number(this.ratioArrOption);
        params.ratioArrOption =
          this.ratioArrOption === "已体检"
            ? "3"
            : this.ratioArrOption === "将到期"
            ? "2"
            : this.ratioArrOption === "已过期"
            ? "1"
            : this.ratioArrOption === "未体检"
            ? 0
            : this.ratioArrOption;
      }
      // 当是否检测有选项时
      if (this.jobHealthOption) {
        params.jobHealthOption =
          this.jobHealthOption === "已检测"
            ? "3"
            : this.jobHealthOption === "将到期"
            ? "2"
            : this.jobHealthOption === "已过期"
            ? "1"
            : this.jobHealthOption === "未检测"
            ? 0
            : this.jobHealthOption;
      }
      // 当是否申报有选项时
      if (this.OnlineDeclarationOption) {
        params.OnlineDeclarationOption =
          this.OnlineDeclarationOption === "已申报"
            ? "3"
            : this.OnlineDeclarationOption === "将到期"
            ? "2"
            : this.OnlineDeclarationOption === "已过期"
            ? "1"
            : this.OnlineDeclarationOption === "未申报"
            ? "0"
            : this.OnlineDeclarationOption;
      }
      // 当地区有选择时
      if (this.district.length > 0) {
        params.district = this.district[this.district.length - 1];
      }
      // 当危害暴露等级有选项时
      if (this.assessmentResultOption) {
        params.assessmentResultOption =
          this.assessmentResultOption === "高风险"
            ? "2"
            : this.assessmentResultOption === "中风险"
            ? "1"
            : this.assessmentResultOption === "低风险"
            ? "0"
            : this.assessmentResultOption;
      }
      // 当综合风险等级有选项时
      if (this.comprehensiveLevelOption) {
        params.comprehensiveLevelOption = this.comprehensiveLevelOption;
        params.comprehensiveLevelOption =
          this.comprehensiveLevelOption === "Ⅳ级"
            ? "4"
            : this.comprehensiveLevelOption === "Ⅲ级"
            ? "3"
            : this.comprehensiveLevelOption === "Ⅱ级"
            ? "2"
            : this.comprehensiveLevelOption === "Ⅰ级"
            ? "1"
            : this.comprehensiveLevelOption === "0级"
            ? "0"
            : this.comprehensiveLevelOption;
      }
      // 当分类等级有选项时
      if (this.riskSortLevelOption) {
        params.riskSortLevelOption = this.riskSortLevelOption;
        params.riskSortLevelOption =
          this.riskSortLevelOption === "甲"
            ? "3"
            : this.riskSortLevelOption === "乙"
            ? "2"
            : this.riskSortLevelOption === "丙"
            ? "1"
            : this.riskSortLevelOption;
      }
      // 当行业分类有选择时
      if (this.industryCategory) {
        params.industryCategory = JSON.stringify(this.industryCategory);
      }
      // 当地区有选择时
      if (this.district) {
        params.district = JSON.stringify(this.district);
      }
      if (this.trainState !== "" && this.trainState !== undefined) {
        params.trainState = this.trainState;
      }
      this.$store.dispatch("adminorg/setSearchKeys", {
        district: this.district,
        searchkey: this.searchkey,
        industryCategory: params.industryCategory,
        selfAssessmentOption: params.selfAssessmentOption,
        assessmentResultOption: params.assessmentResultOption,
        comprehensiveLevelOption: params.comprehensiveLevelOption,
        riskSortLevelOption: params.riskSortLevelOption,
        OnlineDeclarationOption: params.OnlineDeclarationOption,
        jobHealthOption: params.jobHealthOption,
        ratioArrOption: params.ratioArrOption,
        exceedOption: params.exceedOption,
        suspectedOption: params.suspectedOption,
        odiseasedOption: params.odiseasedOption,
        levelOption: this.levelOption, // 当风险等级有选项时
        trainState: this.trainState,
      });
      params.pageInfos = this.pageInfos;
      this.$store.dispatch("addTraining/setParams", params);
      await this.$store.dispatch("adminorg/getAdminorgList", params);
      // 搜索条件改变时，保持表格复选框状态
      // this.$parent.$parent.$parent.searchChange();
    },
  },
  created() {
    getIndustryCategory().then((res) => {
      try {
        this.industryCategoryOptions = res.data;
      } catch (error) {
        console.log(error);
      }
    });
  },
};
</script>

<style>
</style>