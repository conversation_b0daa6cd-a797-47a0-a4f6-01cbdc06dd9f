<template>
  <div class="block dr-pagination">
    <div v-if="pageInfo">
      <div v-if="device == 'mobile'">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfos.current"
          :page-size="pageInfos.pageSize"
          small
          layout="prev, pager, next"
          :total="pageInfo.totalItems"
          hide-on-single-page="true"
        ></el-pagination>
      </div>
      <div v-else>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfos.current"
          :page-sizes="[5, 10, 30, 50, 100, 300, 500]"
          :page-size="pageInfos.pageSize"
          layout="sizes, prev, pager, next"
          :total="pageInfo.totalItems"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
.dr-pagination {
  text-align: center;
  margin: 15px auto;
}
</style>
<script>
import { mapGetters } from "vuex";
export default {
  props: {
    device: String,
    pageInfo: Object,
    pageInfos: Object,
    statisticsData: Object,
    pageType: String,
  },
  computed: {
    ...mapGetters(["adminorgList", "pageInfos", "sortType"]),
  },
  methods: {
    renderPageList(current, pageSize) {
      let targetCurrent = current;
      let targetpageSize = pageSize;
      this.$store.dispatch("adminorg/listPageInfo", {
        pageSize: targetpageSize,
        current: targetCurrent,
      });
      let obj = {
        sortType: {
          order: this.sortType.order ? this.sortType.order : "",
          prop: this.sortType.prop ? this.sortType.prop : "",
        },
        pageInfos: {
          current: this.pageInfos.current ? this.pageInfos.current : 1,
          pageSize: this.pageInfos.pageSize ? this.pageInfos.pageSize : 10,
        },
        searchkey: this.$store.getters.searchKeys.searchkey
          ? this.$store.getters.searchKeys.searchkey
          : "",
        district: this.$store.getters.searchKeys.district
          ? this.$store.getters.searchKeys.district
          : "",
        industryCategory: this.$store.getters.searchKeys.industryCategory
          ? this.$store.getters.searchKeys.industryCategory
          : "",
        levelOption: this.$store.getters.searchKeys.levelOption
          ? this.$store.getters.searchKeys.levelOption
          : "",
        OnlineDeclarationOption: this.$store.getters.searchKeys
          .OnlineDeclarationOption
          ? this.$store.getters.searchKeys.OnlineDeclarationOption
          : "",
        jobHealthOption: this.$store.getters.searchKeys.jobHealthOption
          ? this.$store.getters.searchKeys.jobHealthOption
          : "",
        ratioArrOption: this.$store.getters.searchKeys.ratioArrOption
          ? this.$store.getters.searchKeys.ratioArrOption
          : "",
        exceedOption: this.$store.getters.searchKeys.exceedOption
          ? this.$store.getters.searchKeys.exceedOption
          : "",
        odiseasedOption: this.$store.getters.searchKeys.odiseasedOption
          ? this.$store.getters.searchKeys.odiseasedOption
          : "",
        suspectedOption: this.$store.getters.searchKeys.suspectedOption
          ? this.$store.getters.searchKeys.suspectedOption
          : "",
        selfAssessmentOption: this.$store.getters.searchKeys
          .selfAssessmentOption
          ? this.$store.getters.searchKeys.selfAssessmentOption
          : "",
        assessmentResultOption: this.$store.getters.searchKeys
          .assessmentResultOption
          ? this.$store.getters.searchKeys.assessmentResultOption
          : "",
        comprehensiveLevelOption: this.$store.getters.searchKeys
          .comprehensiveLevelOption
          ? this.$store.getters.searchKeys.comprehensiveLevelOption
          : "",
        riskSortLevelOption: this.$store.getters.searchKeys.riskSortLevelOption
          ? this.$store.getters.searchKeys.riskSortLevelOption
          : "",
        trainState: this.$store.getters.searchKeys.trainState,
      };
      obj.district = JSON.stringify(obj.district);
      this.$store.dispatch("adminorg/getAdminorgList", obj);
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      let current = this.pageInfos ? this.pageInfos.current : 1;
      this.renderPageList(current, val);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      let pageSize = this.pageInfos ? this.pageInfos.pageSize : 10;
      this.renderPageList(val, pageSize);
    },
  },
  data() {
    return {};
  },
};
</script>
