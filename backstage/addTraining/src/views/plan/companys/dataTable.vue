<template>
  <div>
    <el-row>
      <div style="margin-top: 5px">
        <el-button
          :disabled="!dataList.length"
          @click="selectAllAtThisCondition"
          type="primary"
          size="mini"
          >全选</el-button
        >
        <el-button size="mini" type="primary" @click="importExcel"
          >导入excel<i class="el-icon-upload el-icon--right"></i
        ></el-button>
        <el-button
          :disabled="!checkArray.size"
          @click="clearAllAtThisCondition"
          type="primary"
          size="mini"
          >取消</el-button
        >

        <span>&emsp; 已选择：{{ checkArray.size }} &thinsp;家企业</span>
      </div>
      <el-table
        size="mini"
        max-height="500"
        align="center"
        v-loading="loading"
        ref="multipleTable"
        :data="dataList"
        tooltip-effect="dark"
        style="width: 100%"
        row-key="_id"
        @sort-change="sortChange"
        default-expand-all
        @select="handleSelectionChange"
        @select-all="selectAllInPage"
      >
        <el-table-column
          type="selection"
          :selectable="checkboxSelect"
          :reserve-selection="true"
          width="45"
        >
        </el-table-column>
        <el-table-column
          prop="cname"
          :label="$t('adminorg.cname')"
          fixed
          width="150px"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.cname }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="workAddress.districts"
          label="地区"
          width="150px"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="industryCategory"
          :label="$t('adminorg.industryCategory')"
          width="150px"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.industryCategory
                ? scope.row.industryCategory.join("  &&  ")
                : ""
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="level"
          label="风险等级"
          width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <div slot="reference" class="name-wrapper">
              <!-- <el-link disabled type="warning" v-if="scope.row.level == '较重'">{{
              scope.row.level
            }}</el-link> -->
              <el-link
                disabled
                type="success"
                v-if="scope.row.level == '一般'"
                >{{ scope.row.level }}</el-link
              >
              <el-link
                disabled
                type="danger"
                v-if="scope.row.level == '严重'"
                >{{ scope.row.level }}</el-link
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="exposeRiskLevel"
          label="危害暴露等级"
          width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <div slot="reference" class="name-wrapper">
              <el-link
                disabled
                type="success"
                v-if="scope.row.exposeRiskLevel === 0"
                >低风险</el-link
              >
              <el-link
                disabled
                type="warning"
                v-if="scope.row.exposeRiskLevel === 1"
                >中风险</el-link
              >
              <el-link
                disabled
                type="danger"
                v-if="scope.row.exposeRiskLevel === 2"
                >高风险</el-link
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="comprehensiveLevel"
          label="综合风险等级"
          width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <div slot="reference" class="name-wrapper">
              <el-link
                disabled
                type="success"
                v-if="scope.row.comprehensiveLevel === 0"
                >0级</el-link
              >
              <el-link
                disabled
                type="primary"
                v-if="scope.row.comprehensiveLevel === 1"
                >Ⅰ级</el-link
              >
              <el-link
                disabled
                type="Info"
                v-if="scope.row.comprehensiveLevel === 2"
                >Ⅱ级</el-link
              >
              <el-link
                disabled
                type="warning"
                v-if="scope.row.comprehensiveLevel === 3"
                >Ⅲ级</el-link
              >
              <el-link
                disabled
                type="danger"
                v-if="scope.row.comprehensiveLevel === 4"
                >Ⅳ级</el-link
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="riskSortLevel" label="分类" align="center">
          <template slot-scope="scope">
            <div slot="reference" class="name-wrapper">
              <el-link
                disabled
                type="success"
                v-if="scope.row.riskSortLevel === 1"
                >丙</el-link
              >
              <el-link
                disabled
                type="warning"
                v-if="scope.row.riskSortLevel === 2"
                >乙</el-link
              >
              <el-link
                disabled
                type="danger"
                v-if="scope.row.riskSortLevel === 3"
                >甲</el-link
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="isJobhealths"
          label="检测情况"
          width="100px"
          align="center"
        >
          <template slot-scope="scope">
            <div slot="reference" class="name-wrapper">
              <el-link
                disabled
                type="success"
                v-if="scope.row.isJobhealths == 3"
                >已检测</el-link
              >
              <el-link type="warning" v-if="scope.row.isJobhealths == 2"
                >将到期</el-link
              >
              <el-link type="danger" v-if="scope.row.isJobhealths == 1"
                >已过期</el-link
              >
              <el-link type="danger" v-if="scope.row.isJobhealths == 0"
                >未检测</el-link
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="exceed"
          label="超标点数"
          width="100px"
          align="center"
        >
          <template slot-scope="scope">
            <div slot="reference" class="name-wrapper">
              <el-link disabled type="success" v-if="scope.row.exceed === 0">{{
                scope.row.exceed
              }}</el-link>
              <el-link disabled type="danger" v-if="scope.row.exceed !== 0">{{
                scope.row.exceed
              }}</el-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="ratioArr"
          label="体检情况"
          width="100px"
          align="center"
        >
          <template slot-scope="scope">
            <div slot="reference" class="name-wrapper">
              <el-link
                disabled
                type="success"
                v-if="scope.row.ratioArr > 80 && scope.row.isRatioArr == 3"
                >{{ scope.row.ratioArr.toFixed(1) + "%" }}</el-link
              >
              <el-link
                disabled
                type="success"
                v-if="
                  scope.row.ratioArr > 50 &&
                  scope.row.ratioArr <= 80 &&
                  scope.row.isRatioArr == 3
                "
                >{{ scope.row.ratioArr.toFixed(1) + "%" }}</el-link
              >
              <el-link
                disabled
                type="success"
                v-if="
                  scope.row.ratioArr <= 50 &&
                  scope.row.ratioArr > 0 &&
                  scope.row.isRatioArr == 3
                "
                >{{ scope.row.ratioArr.toFixed(1) + "%" }}</el-link
              >
              <el-link type="warning" v-if="scope.row.isRatioArr == 2"
                >将到期</el-link
              >
              <el-link type="danger" v-if="scope.row.isRatioArr == 1"
                >已过期</el-link
              >
              <el-link type="danger" v-if="scope.row.isRatioArr == 0"
                >未体检</el-link
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="odiseases"
          label="职业病"
          :sortable="'custom'"
          width="90px"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="suspected"
          label="疑似职业病"
          :sortable="'custom'"
          width="80px"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="forbid"
          label="职业禁忌证"
          :sortable="'custom'"
          width="80px"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="re_examination"
          label="复查人数"
          :sortable="'custom'"
          width="100px"
          align="center"
        ></el-table-column>
      </el-table>
    </el-row>

    <div>
      <el-drawer
        :close-on-press-escape="false"
        :wrapperClosable="false"
        title="导入企业"
        :visible.sync="drawer"
        direction="rtl"
        :before-close="handleClose"
      >
        <div style="padding: 32px">
          <el-button
            :loading="loadingFile"
            type="primary"
            @click="handleUpload"
            size="mini"
            icon="el-icon-upload"
          >
            导入Excel
          </el-button>
          <el-button size="mini" @click="download" icon="el-icon-download"
            >下载模板</el-button
          >
          <input
            ref="excel-upload-input"
            class="excel-upload-input"
            type="file"
            accept=".xlsx, .xls"
            @change="handleClick"
          />
          <el-button
            size="mini"
            @click="resetData"
            type="warning"
            icon="el-icon-refresh-left"
            >清空</el-button
          >
          <el-button
            :disabled="!realData.length"
            @click="addCompany"
            type="primary"
            size="mini"
            >确定</el-button
          >
          <el-table
            :data="realData"
            style="width: 100%"
            :row-class-name="tableRowClassName"
            size="mini"
            max-height="600"
          >
            <el-table-column prop="cname" label="单位名称" width="280">
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="danger"
                  @click="handleDelete(scope.$index, scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-drawer>
    </div>

    <div>
      <el-dialog
        width="30%"
        title="提示"
        :visible.sync="notExistsCompanyDialog"
        append-to-body
        :close-on-click-modal="false"
      >
        <div v-html="notExistsCompanyMsg"></div>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="gotoCompayList"
            ><a href="/admin/adminorgGov">前往企业列表</a></el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="notExistsCompanyDialog = false"
          >
            过滤以上企业
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { showList } from "@/api/adminorg";
import { isUndefined } from "pl-table/lib/utils/types";
import { mapGetters } from "vuex";
import {
  selectAllEnterprises,
  checkoutCompany,
  getCompanysId,
} from "@/api/addTraining";
import XLSX from "xlsx";
export default {
  props: {
    hasSelectArray: {
      type: Array,
      default: [],
    },
    dataList: Array,
    pageInfo: Object,
    comprehensiveLevels: Number,
  },
  computed: {
    ...mapGetters(["adminorgList", "pageInfos"]),
  },
  data() {
    return {
      thisRow: {}, //为发送短信时传递点击的那个企业的信息
      year: "2020",
      curCompanyId: "",
      nowyear: 0, //当前年份
      searchkey: false,
      // AllList: this.pageInfo.totalItems,
      superUserInfo: {}, //本账号的信息
      messageForm: {
        title: "",
        message: "",
        isShow: false,
        defaultInfo: {
          EnterpriseID: "",
          cname: "",
        },
      },
      rules: {
        title: [
          { required: true, message: "请输入消息标题", trigger: "blur" },
          {
            min: 2,
            max: 30,
            message: "长度在 2 到 30 个字符",
            trigger: "blur",
          },
        ],
        message: [
          { required: true, message: "请输入消息正文", trigger: "blur" },
          {
            min: 0,
            max: 400,
            message: "长度不得超过400 个字符",
            trigger: "blur",
          },
        ],
      },
      messageTitle: "",
      superviseList: [], //监管信息列表
      dialogVisible: false,
      green: { color: "#13CE66" },
      red: { color: "#FF4949" },
      loading: false,
      multipleSelection: [],
      approveOrgBackShow: false,

      checkArray: new Set(), // 多选结果，全是_id
      excelData: [],
      invalidData: [], // 记录空的错误的额excel数据
      realData: [],
      notExistsCompanyMsg: "",
      notExistsCompanyDialog: false,

      selectionLen: 0, // 用于记录复选框选择变化时上一次复选框数组长度，用于判断是取消选择还是加薪的

      historyDataList: [], // 用于存储历史请求的数据，保证不重复
      drawer: false,
    };
  },
  updated() {
    // 当有搜索的时候让’满足条件的‘那句话显示
    if (
      (this.$store.getters.searchKeys.OnlineDeclarationOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.OnlineDeclarationOption)) ||
      (this.$store.getters.searchKeys.suspectedOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.suspectedOption)) ||
      (this.$store.getters.searchKeys.exceedOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.exceedOption)) ||
      (this.$store.getters.searchKeys.jobHealthOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.jobHealthOption)) ||
      (this.$store.getters.searchKeys.levelOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.levelOption)) ||
      (this.$store.getters.searchKeys.odiseasedOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.odiseasedOption)) ||
      (this.$store.getters.searchKeys.ratioArrOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.ratioArrOption)) ||
      (this.$store.getters.searchKeys.selfAssessmentOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.selfAssessmentOption)) ||
      (this.$store.getters.searchKeys.assessmentResultOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.assessmentResultOption)) ||
      (this.$store.getters.searchKeys.comprehensiveLevelOption !== "" &&
        !isUndefined(
          this.$store.getters.searchKeys.comprehensiveLevelOption
        )) ||
      (this.$store.getters.searchKeys.riskSortLevelOption !== "" &&
        !isUndefined(this.$store.getters.searchKeys.riskSortLevelOption)) ||
      (this.$store.getters.searchKeys.district[0] !== "" &&
        !isUndefined(this.$store.getters.searchKeys.district) &&
        this.$store.getters.searchKeys.district.length > 0) ||
      (this.$store.getters.searchKeys.industryCategory !== "" &&
        this.$store.getters.searchKeys.industryCategory !== "[]") ||
      (this.pageInfo.searchkey !== "" && !isUndefined(this.pageInfo.searchkey))
    ) {
      this.searchkey = true;
    } else {
      this.searchkey = false;
    }
  },
  created() {
    // 获取当前年份
    this.nowyear = new Date().getFullYear();
  },
  methods: {
    // excel弹框
    importExcel() {
      this.drawer = true;
    },
    gotoCompayList() {
      window.location.href = "/admin/adminorgGov";
    },
    // 复选框disabled函数
    checkboxSelect(row) {
      // if (this.checkAll) return false;
      const itemEnterprise = this.hasSelectArray.find(function (item) {
        return item === row._id;
      });
      if (itemEnterprise) {
        // this.$refs.multipleTable.toggleRowSelection(row, true);
        return false;
      }
      return true;
    },
    // 展示监管列表
    goshowList(companyId, year) {
      this.dialogVisible = true;
      showList({
        companyId,
        year: year ? year : "",
      }).then((res) => {
        this.superviseList = res.data;
        this.curCompanyId = companyId;
      });
    },
    // 排序方式
    sortChange(sort) {
      this.$store.dispatch("adminorg/sortTypeChange", {
        order: sort.order,
        prop: sort.prop,
      });
      let obj = {
        sortType: {
          order: sort.order,
          prop: sort.prop,
        },
        pageInfos: {
          current: this.pageInfos.current ? this.pageInfos.current : 1,
          pageSize: this.pageInfos.pageSize ? this.pageInfos.pageSize : 10,
        },
        searchkey: this.$store.getters.searchKeys.searchkey
          ? this.$store.getters.searchKeys.searchkey
          : "",
        district: this.$store.getters.searchKeys.district
          ? this.$store.getters.searchKeys.district
          : "",
        industryCategory: this.$store.getters.searchKeys.industryCategory
          ? this.$store.getters.searchKeys.industryCategory
          : "",
        levelOption: this.$store.getters.searchKeys.levelOption
          ? this.$store.getters.searchKeys.levelOption
          : "",
        OnlineDeclarationOption: this.$store.getters.searchKeys
          .OnlineDeclarationOption
          ? this.$store.getters.searchKeys.OnlineDeclarationOption
          : "",
        jobHealthOption: this.$store.getters.searchKeys.jobHealthOption
          ? this.$store.getters.searchKeys.jobHealthOption
          : "",
        ratioArrOption: this.$store.getters.searchKeys.ratioArrOption
          ? this.$store.getters.searchKeys.ratioArrOption
          : "",
        exceedOption: this.$store.getters.searchKeys.exceedOption
          ? this.$store.getters.searchKeys.exceedOption
          : "",
        odiseasedOption: this.$store.getters.searchKeys.odiseasedOption
          ? this.$store.getters.searchKeys.odiseasedOption
          : "",
        suspectedOption: this.$store.getters.searchKeys.suspectedOption
          ? this.$store.getters.searchKeys.suspectedOption
          : "",
        selfAssessmentOption: this.$store.getters.searchKeys
          .selfAssessmentOption
          ? this.$store.getters.searchKeys.selfAssessmentOption
          : "",
        assessmentResultOption: this.$store.getters.searchKeys
          .assessmentResultOption
          ? this.$store.getters.searchKeys.assessmentResultOption
          : "",
        comprehensiveLevelOption: this.$store.getters.searchKeys
          .comprehensiveLevelOption
          ? this.$store.getters.searchKeys.comprehensiveLevelOption
          : "",
        riskSortLevelOption: this.$store.getters.searchKeys.riskSortLevelOption
          ? this.$store.getters.searchKeys.riskSortLevelOption
          : "",
      };
      obj.district = JSON.stringify(obj.district);
      this.$store.dispatch("adminorg/getAdminorgList", obj);
    },

    // 当用户手动勾选全选 Checkbox 时触发的事件
    selectAllInPage(selection) {
      console.log("全选", selection.length, this.selectionLen);
      if (selection.length > this.selectionLen) {
        // 新增
        const selectionLen = selection.length;
        for (let i = 0; i < selectionLen; i++)
          this.checkArray.add(selection[i]._id);
      } else {
        // 取消全选， 此时selection为空数组，就需要用this.dataList
        const dataListLen = this.dataList.length;
        for (let j = 0; j < dataListLen; j++)
          this.checkArray.delete(this.dataList[j]._id);
      }
      this.$forceUpdate();
      this.selectionLen = selection.length;
    },
    // 当用户手动勾选当前页数据行的 Checkbox 时触发的事件
    handleSelectionChange(selection, row) {
      console.log("单选", selection.length, this.selectionLen);
      if (selection.length > this.selectionLen) {
        // 新增
        this.checkArray.add(row._id);
      } else {
        // 删除
        this.checkArray.delete(row._id);
      }
      this.$forceUpdate();
      this.selectionLen = selection.length;
    },
    // 全选
    selectAllAtThisCondition() {
      const params = this.$store.state.addTraining.selectParams;
      selectAllEnterprises(params.params).then(({ data }) => {
        console.log(1111, params);
        if (data.dataList.length) {
          const dataListLen = data.dataList.length;
          for (let j = 0; j < dataListLen; j++) {
            this.checkArray.add(data.dataList[j]);
          }
          this.hasSelectArray.forEach((item) => {
            this.checkArray.delete(item);
          });
          this.$forceUpdate();
          this.checkChange();
          this.$message({
            message: "选择成功",
            type: "success",
          });
        }
      });
    },
    // 全清空
    clearAllAtThisCondition() {
      if (this.checkArray.size) {
        this.checkArray.clear();
        // this.hasSelectArray.forEach(item=>{
        //   this.checkArray.add(item);
        // })
        this.selectionLen = 0;
        this.$forceUpdate();
        this.checkChange();
        this.$message({
          message: "已取消选择",
          type: "success",
        });
      }
    },
    // 选择或者搜索条件改变时，修改复选框状态
    checkChange(dataChange = true) {
      this.$nextTick(() => {
        this.dataList.forEach((row) => {
          const checked = this.checkArray.has(row._id);
          if (dataChange && !this.historyDataList.includes(row._id) && checked)
            this.selectionLen++;
          this.$refs.multipleTable.toggleRowSelection(row, checked);
        });
      });
    },
    // 把选择的企业传出去
    next() {
      return Array.from(this.checkArray);
    },
    // 导入excel模板
    download() {
      const a = document.createElement("a"); // 创建a标签
      a.setAttribute("download", "培训企业模板");
      a.setAttribute("href", "/static/dataTemplate/培训企业模板.xlsx"); // href链接
      a.click(); // 自执行点击事件
    },
    // 添加文件
    handleUpload() {
      this.$refs["excel-upload-input"].click();
    },
    // input点击框
    handleClick(e) {
      const files = e.target.files;
      const rawFile = files[0]; // only use files[0]
      if (!rawFile) return;
      this.upload(rawFile);
    },
    // 必填字段校验
    filter(array) {
      let msg = "第 ";
      let mark = false;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (!element["单位名称"] || !element["单位名称"].length) {
          this.invalidData.push(this.excelData[0].results[index]);
          this.excelData[0].results.splice(index, 1);
          msg = msg + String(index) + "、";
          index--;
          continue;
        }
      }
      return msg;
    },
    // 上传按钮触发事件
    upload(rawFile) {
      this.$refs["excel-upload-input"].value = null;
      const suffix = rawFile.name && rawFile.name.split(".");
      if (!suffix || suffix[suffix.length - 1] !== "xlsx") {
        this.$message({
          message: "文件格式错误",
          type: "warning",
        });
        return;
      }
      if (!this.beforeUpload(rawFile)) {
        this.readData(rawFile);
        return;
      }
      const before = this.beforeUpload(rawFile);
      if (before) {
        this.readData(rawFile).then((data) => {
          this.excelData = [];
          this.excelData.push(data);
          const msg = this.filter(this.excelData[0].results);
          if (this.invalidData.length) {
            this.$confirm(`${msg}条数据缺少必填字段，是否继续`, "提示", {
              confirmButtonText: "继续",
              cancelButtonText: "重新导入",
              type: "warning",
            })
              .then(() => {
                this.parseData(this.excelData[0].results);
                return;
              })
              .catch(() => {
                this.excelData = [];
                return;
              });
          }
          this.parseData(this.excelData[0].results);
        });
      }
    },
    // 解析excel数据
    parseData(array) {
      const postData = [];
      for (let i = 0; i < array.length; i++) {
        const element = array[i];
        if (element["单位名称"]) {
          // this.realData.push({
          //   cname: element["单位名称"],
          //   exists: true,
          // });
          postData.push(element["单位名称"]);
        }
      }
      checkoutCompany({
        postData,
      }).then(({ data }) => {
        if (data.noExists.length) {
          let notExistsCompany = "";
          console.log(data.noExists);
          for (let index = 0; index < data.noExists.length; index++) {
            const element = data.noExists[index];
            notExistsCompany = notExistsCompany + element + "\n";
          }
          this.notExistsCompanyMsg = `<span>一下企业未在系统注册，请先前往 <a href="/admin/adminorgGov">企业列表</a> 中导入，否则将会被过滤</span><strong><span style="white-space: pre-line;">${notExistsCompany}</span></strong>`;
          this.notExistsCompanyDialog = true;
        }
        for (let i = 0; i < array.length; i++) {
          const element = array[i];
          if (element["单位名称"]) {
            this.realData.push({
              cname: element["单位名称"],
              exists: data.noExists.includes(element["单位名称"])
                ? false
                : true,
            });
          }
        }
      });
    },
    // 大小校验
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 / 10 < 1;
      if (isLt1M) {
        return true;
      }
      this.$message({
        message: "Please do not upload files larger than 10mb in size.",
        type: "warning",
      });
      return false;
    },
    readData(rawFile) {
      this.loading = true;
      return new Promise((resolve) => {
        if (!window.FileReader) {
          this.$message({
            message: "该浏览器不支持上传Excel文件",
            type: "warning",
          });
          return;
        }
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target.result;
          const workbook = XLSX.read(data, {
            type: "array",
            cellDates: true,
          });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          const header = this.getHeaderRow(worksheet);
          const results = XLSX.utils.sheet_to_json(worksheet, {
            defval: "",
          });
          console.log(header, results);
          this.loading = false;
          resolve({ header, results });
        };
        reader.readAsArrayBuffer(rawFile);
      });
    },
    getHeaderRow(sheet) {
      const headers = [];
      const range = XLSX.utils.decode_range(sheet["!ref"]);
      let C;
      const R = range.s.r;
      /* start in the first row */
      for (C = range.s.c; C <= range.e.c; ++C) {
        /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })];
        /* find the cell in the first row */
        let hdr = "UNKNOWN " + C; // <-- replace with your desired default
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell);
        headers.push(hdr);
      }
      return headers;
    },
    fillMergedRange(sheet, isFillEmptyCell = false) {
      //Get all merged ranges
      if (!sheet["!merges"]) {
        if (isFillEmptyCell)
          // this.fillEmptyCell(sheet);
          return;
      }
      for (let mRange of sheet["!merges"].values()) {
        let sS = XLSX.utils.encode_cell(mRange.s);
        //Get the start cell value
        let sVal = sheet[sS];
        //Fill all cells in the merged range, s=start cell, e=end cell
        for (let c = mRange.s.c; c <= mRange.e.c; c++) {
          for (let r = mRange.s.r; r <= mRange.e.r; r++) {
            let sCell = XLSX.utils.encode_cell({ c: c, r: r });
            sheet[sCell] = sVal;
          }
        }
      }
      //Clean up the merged array in worksheet
      delete sheet["!merges"];
      // if(isFillEmptyCell)
      //   this.fillEmptyCell(sheet);
    },

    // 导入的表格状态
    tableRowClassName({ row, rowIndex }) {
      console.log(row);
      if (!row.exists) {
        return "warning-row";
      } else {
        return "success-row";
      }
      return "";
    },
    // 抽屉主动关闭
    handleClose() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.drawer = false;
          done();
        })
        .catch((_) => {});
    },
    // 导入后确定按钮，获取id加入set
    addCompany() {
      const postData = [];
      for (let i = 0; i < this.excelData[0].results.length; i++) {
        const element = this.excelData[0].results[i];
        if (element["单位名称"]) {
          postData.push(element["单位名称"]);
        }
      }
      getCompanysId({
        postData,
      }).then(({ data }) => {
        console.log(data.result);
        for (let index = 0; index < data.result.length; index++) {
          this.checkArray.add(data.result[index]);
        }
        this.$forceUpdate();
        this.$message({
          message: "您已导入" + data.result.length + "家企业",
          type: "success",
        });
        this.drawer = false;
        this.excelData = [];
        this.realData = [];
      });
    },
    // 导入表格删除按钮
    handleDelete(index, row) {
      this.excelData[0].results.splice(index, 1);
      this.realData.splice(index, i);
    },
  },
  watch: {
    hasSelectArray: {
      deep: true,
      handler: function (newV, oldV) {
        // this.hasSelectArray.forEach(item=>{
        //   this.checkArray.add(item);
        // })
      },
    },
    year(newYear) {
      this.goshowList(this.curCompanyId, new Date(newYear).getFullYear());
    },
    dataList: {
      deep: true,
      handler: function (newV, oldV) {
        console.log(newV, oldV);
        const oldLen = oldV.length;
        for (let index = 0; index < oldLen; index++) {
          this.historyDataList.push(oldV[index]._id);
        }
        this.checkChange(true);
      },
    },
    checkArray: {
      deep: true,
      handler: function (newV, oldV) {
        // console.log(newV, this.handleSelection);
      },
    },
  },
};
</script>
<style scoped>
.el-table {
  width: 99.9% !important;
}
::v-deep .normalColumne {
  border: 0px;
}
::v-deep .activeColumne {
  animation: activeCColor 2s ease;
  border: 0px;
}
.excel-upload-input {
  display: none;
  z-index: -9999;
}
.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}
</style>
