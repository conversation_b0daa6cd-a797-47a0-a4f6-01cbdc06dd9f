<template>
  <div>
    <el-row>
      <el-col :xs="24" :sm="24" :md="17" :lg="17" :xl="17">
        <div>
          <el-card class="table-card" shadow="never">
            <el-table
              max-height="500"
              :data="courseList"
              style="width: 100%"
              row-key="_id"
              @selection-change="handleSelectionChange"
              size="mini"
            >
              <el-table-column fixed label="封面" width="96" align="center">
                <template slot-scope="scope">
                  <el-image
                    class="cover"
                    :src="scope.row.cover"
                    fit="scale-down"
                  ></el-image>
                </template>
              </el-table-column>

              <el-table-column
                align="center"
                show-overflow-tooltip
                fixed
                prop="name"
                label="课程名"
                width="120"
              >
              </el-table-column>
              <el-table-column prop="classHours" label="学时" width="60">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="explain"
                label="课程介绍"
                width="120"
              >
              </el-table-column>
              <el-table-column show-overflow-tooltip label="分类" width="180">
                <template slot-scope="scope">
                  <!-- {{scope.row.classification.join('➤')}} -->
                  <span>{{
                    joinClassification(scope.row.classification)
                  }}</span>
                </template>
              </el-table-column>

              <el-table-column label="发布时间" width="150">
                <template slot-scope="scope">
                  <span>{{
                    scope.row.allowToOpen
                      ? dateFormat(
                          "YYYY-mm-dd HH:MM",
                          new Date(scope.row.openTime)
                        )
                      : "暂未开放"
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                prop="updateTima"
                label="操作"
                width="180"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    @click="showCourseFun(scope.row)"
                    type="primary"
                    >预览</el-button
                  >
                  <el-button
                    size="mini"
                    @click="handleSelectionChange(scope.row, 'required')"
                    type="primary"
                    :disabled="checkDisplay(scope.row)"
                    >+必修</el-button
                  >
                  <el-button
                    size="mini"
                    @click="handleSelectionChange(scope.row, 'elective')"
                    type="primary"
                    :disabled="checkDisplay(scope.row)"
                    >+选修</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-card>
          <el-pagination
            @size-change="pageSizeChange"
            @current-change="currentChange"
            :current-page="current"
            :page-sizes="[5, 10, 30, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="count"
            class="page"
          >
          </el-pagination>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="7" :lg="7" :xl="7">
        <div class="checked-list">
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span class="checked-title"
                >必修课 已选{{ requiredHourses }}学时</span
              >
              <el-button
                @click="requiredDeleteAll()"
                style="float: right; padding: 3px 0"
                type="text"
              >
                清空
              </el-button>
            </div>
            <div
              v-for="course in requiredArray"
              :key="course"
              class="text item"
            >
              {{ course.name }}
              <el-button
                style="float: right; padding: 3px 0"
                @click="requiredDelete(course)"
                type="text"
              >
                取消
              </el-button>
            </div>
          </el-card>
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span class="checked-title"
                >选修课 已选{{ electiveHourses }}学时</span
              >
              <el-button
                style="float: right; padding: 3px 0"
                @click="electiveDeleteAll()"
                type="text"
              >
                清空
              </el-button>
            </div>
            <div
              v-for="course in electiveArray"
              :key="course"
              class="text item"
            >
              {{ course.name }}
              <el-button
                style="float: right; padding: 3px 0"
                @click="electiveDelete(course)"
                type="text"
                >取消</el-button
              >
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <el-dialog
      :close-on-click-modal="false"
      top="5vh"
      width="80%"
      :title="courseName"
      :visible.sync="showCourse"
      append-to-body
      :before-close="closeVideoDialog"
    >
      <courseLearning ref="courseLearning" :courseId="coursesID" />
    </el-dialog>
  </div>
</template>

<script>
import courseLearning from "./CourseLearning";
export default {
  props: {
    courseList: Array,
    count: Number,
  },
  data() {
    return {
      tableSize: 24,
      showCourse: false,
      courseName: "",
      requiredArray: [],
      electiveArray: [],
      requiredHourses: 0,
      electiveHourses: 0,
      current: 1,
      pageSize: 10,
      coursesID: "",

      startChcek: false,
    };
  },
  components: {
    courseLearning,
  },
  created() {
    console.log(this.$parent.$parent);
    this.$emit("getList", 1);
  },
  methods: {
    requiredDeleteAll() {
      this.requiredHourses = 0;
      this.requiredArray = [];
    },
    electiveDeleteAll() {
      this.electiveHourses = 0;
      this.electiveArray = [];
    },
    checkDisplay(course) {
      let mark = false;
      for (let index = 0; index < this.requiredArray.length; index++) {
        if (this.requiredArray[index]._id == course._id) {
          mark = true;
        }
      }
      if (!mark) {
        for (let index = 0; index < this.electiveArray.length; index++) {
          if (this.electiveArray[index]._id == course._id) {
            mark = true;
          }
        }
      }
      if (mark) {
        return true;
      }
      return false;
    },
    requiredDelete(course) {
      for (let index = 0; index < this.requiredArray.length; index++) {
        if (this.requiredArray[index]._id === course._id) {
          this.requiredHourses -= course.classHours;
          this.requiredArray.splice(index, 1);
        }
      }
    },
    electiveDelete(course) {
      for (let index = 0; index < this.electiveArray.length; index++) {
        if (this.electiveArray[index]._id === course._id) {
          this.electiveHourses -= course.classHours;
          this.electiveArray.splice(index, 1);
        }
      }
    },
    showCourseFun(course) {
      this.showCourse = true;
      this.coursesID = course._id;
      this.courseName = course.name;
    },
    joinClassification(array) {
      let result = "";
      const len = array.length;
      for (let index = 0; index < len; index++) {
        const element = array[index].name;
        result += element;
        if (index < len - 1) result += "➤";
      }
      return result;
    },
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(), // 年
        "m+": (date.getMonth() + 1).toString(), // 月
        "d+": date.getDate().toString(), // 日
        "H+": date.getHours().toString(), // 时
        "M+": date.getMinutes().toString(), // 分
        "S+": date.getSeconds().toString(), // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
          );
        }
      }
      return fmt;
    },
    edit(row) {
      console.log(row);
      this.$router.push({
        name: "courseForm",
        params: { id: row._id },
      });
    },
    handleSelectionChange(val, key) {
      this[key + "Array"].push(val);
      this[key + "Hourses"] += val.classHours;

      if (this.requiredArray.length || this.electiveArray.length) {
        if (!this.startChcek) {
          this.startChcek = true;
          this.tableSize = this.tableSize === 24 ? 16 : 24;
          console.log(this.tableSize);
          // this.$emit("modyfieWidth");
        }
      } else {
        if (this.startChcek) {
          this.startChcek = false;
          // 12 24 之间反复横跳
          this.tableSize = (this.tableSize % 24) + 12;
          // this.$emit("modyfieWidth");
        }
      }
      this.$forceUpdate();
    },
    // 把选择的企业传出去
    next() {
      const requiredArray = [];
      const electiveArray = [];
      this.requiredArray.forEach((element) => {
        requiredArray.push(element._id);
      });
      this.electiveArray.forEach((element) => {
        electiveArray.push(element._id);
      });
      return {
        requiredArray,
        electiveArray,
        requiredHourses: this.requiredHourses,
        electiveHourses: this.electiveHourses,
      };
    },
    currentChange(val) {
      console.log("页面", val);
      this.current = val;
      this.$emit("getList", {
        current: this.current,
        pageSize: this.pageSize,
      });
    },
    pageSizeChange(val) {
      console.log("页面大小", val);
      this.pageSize = val;
      this.$emit("getList", {
        current: this.current,
        pageSize: this.pageSize,
      });
    },
    closeVideoDialog(done) {
      this.$refs["courseLearning"].stop();
      done();
    },
  },
};
</script>

<style scoped>
.page {
  text-align: center;
  margin: 15px auto;
}
.cover {
  width: 96px;
  height: 54px;
  margin: 0px;
  padding: 0px;
}
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}

.box-card {
  width: 100%;
  height: 266px;
  /* margin-top: 16px; */
}
.checked-list {
  margin-left: 32px;
}
.table-card {
  width: 100%;
  height: 532px;
}
.checked-title {
  width: 52px;
  height: 14px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 14px;
}
</style>