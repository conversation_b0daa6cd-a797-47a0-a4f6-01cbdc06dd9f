<template>
  <div>
    <el-row>
      <el-col :span="24">
        <div>
          <el-table
            max-height="500"
            :data="courseList"
            border
            style="width: 100%"
            row-key="_id"
            @selection-change="handleSelectionChange"
            size="small"
          >
            <el-table-column fixed label="封面" width="96" align="center">
              <template slot-scope="scope">
                <el-image
                  class="cover"
                  :src="scope.row.cover"
                  fit="scale-down"
                ></el-image>
              </template>
            </el-table-column>

            <el-table-column
              align="center"
              show-overflow-tooltip
              fixed
              prop="name"
              label="课程名"
              width="120"
            >
            </el-table-column>
            <el-table-column prop="classHours" label="学时" width="60">
            </el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="explain"
              label="课程介绍"
              width="120"
            >
            </el-table-column>
            <el-table-column show-overflow-tooltip label="分类" width="180">
              <template slot-scope="scope">
                <!-- {{scope.row.classification.join('➤')}} -->
                <span>{{ joinClassification(scope.row.classification) }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="发布时间" width="150">
              <template slot-scope="scope">
                <span>{{
                  scope.row.allowToOpen
                    ? dateFormat(
                        "YYYY-mm-dd HH:MM",
                        new Date(scope.row.openTime)
                      )
                    : "暂未开放"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              prop="updateTima"
              label="操作"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="showCourseFun(scope.row)"
                  type="primary"
                  >预览</el-button
                >
                <el-button
                  size="mini"
                  @click="showCourseFun(scope.row)"
                  type="primary"
                  >+必修</el-button
                >
                <el-button
                  size="mini"
                  @click="showCourseFun(scope.row)"
                  type="primary"
                  >+选修</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>

    <el-dialog
      :close-on-click-modal="false"
      top="5vh"
      width="80%"
      :title="courseName"
      :visible.sync="showCourse"
      append-to-body
      :before-close="closeVideoDialog"
    >
      <courseLearning ref="courseLearning" :courseId="coursesID" />
    </el-dialog>
  </div>
</template>

<script>
import courseLearning from "./CourseLearning";
export default {
  props: {
    courseList: Array,
    count: Number,
  },
  data() {
    return {
      showCourse: false,
      courseName: "",
      checkArray: [],
      lesson: 0,
      current: 1,
      pageSize: 10,
      coursesID: "",

      startChcek: false,
    };
  },
  components: {
    courseLearning,
  },
  created() {
    console.log(this.$parent.$parent);
    this.$emit("getList", 1);
  },
  methods: {
    showCourseFun(course) {
      this.showCourse = true;
      this.coursesID = course._id;
      this.courseName = course.name;
    },
    joinClassification(array) {
      let result = "";
      const len = array.length;
      for (let index = 0; index < len; index++) {
        const element = array[index].name;
        result += element;
        if (index < len - 1) result += "➤";
      }
      return result;
    },
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(), // 年
        "m+": (date.getMonth() + 1).toString(), // 月
        "d+": date.getDate().toString(), // 日
        "H+": date.getHours().toString(), // 时
        "M+": date.getMinutes().toString(), // 分
        "S+": date.getSeconds().toString(), // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
          );
        }
      }
      return fmt;
    },
    edit(row) {
      console.log(row);
      this.$router.push({
        name: "courseForm",
        params: { id: row._id },
      });
    },
    handleSelectionChange(val) {
      this.checkArray = [];
      this.lesson = 0;
      for (let index = 0; index < val.length; index++) {
        this.checkArray.push(val[index]);
        this.lesson += val[index].classHours;
      }
      console.log(this.checkArray);

      if (this.checkArray.length) {
        if (!this.startChcek) {
          this.startChcek = true;
          this.$emit("modyfieWidth");
        }
      } else {
        if (this.startChcek) {
          this.startChcek = false;
          this.$emit("modyfieWidth");
        }
      }
    },
    // 把选择的企业传出去
    next() {
      return {
        courses: this.checkArray,
        lesson: this.lesson,
      };
    },
    currentChange(val) {
      console.log("页面", val);
      this.current = val;
      this.$emit("getList", {
        current: this.current,
        pageSize: this.pageSize,
      });
    },
    pageSizeChange(val) {
      console.log("页面大小", val);
      this.pageSize = val;
      this.$emit("getList", {
        current: this.current,
        pageSize: this.pageSize,
      });
    },
    closeVideoDialog(done) {
      this.$refs["courseLearning"].stop();
      done();
    },
  },
};
</script>

<style scoped>
.cover {
  width: 96px;
  height: 54px;
  margin: 0px;
  padding: 0px;
}
</style>