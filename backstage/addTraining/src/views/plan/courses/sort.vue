<template>
  <div>
    <!-- <span>温馨提示：请认真核对所选课程，创建后无法再添加或删除</span>
    <div style="height: 40px"></div> -->
    <ul>
      <li v-for="(item, index) in courseList" :key="item._id">
        <el-row :gutter="20">
          <el-col :span="18">
            <span>{{ item.name }}</span>
          </el-col>
          <el-col :span="6">
            <el-button
              size="mini"
              @click="up(index)"
              type="primary"
              icon="el-icon-caret-top"
              :disabled="!index"
              >上移</el-button
            >
          </el-col>
        </el-row>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    courseList: Array,
  },
  data() {
    return {
      afterSortCourses: [],
    };
  },
  methods: {
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(), // 年
        "m+": (date.getMonth() + 1).toString(), // 月
        "d+": date.getDate().toString(), // 日
        "H+": date.getHours().toString(), // 时
        "M+": date.getMinutes().toString(), // 分
        "S+": date.getSeconds().toString(), // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
          );
        }
      }
      return fmt;
    },
    up(index) {
      console.log(index);
      console.log(this.courseList[index - 1], this.courseList[index]);
      console.log(
        this.afterSortCourses[index - 1],
        this.afterSortCourses[index]
      );
      if (index) {
        [this.courseList[index - 1], this.courseList[index]] = [
          this.courseList[index],
          this.courseList[index - 1],
        ];
        [this.afterSortCourses[index - 1], this.afterSortCourses[index]] = [
          this.afterSortCourses[index],
          this.afterSortCourses[index - 1],
        ];
        this.$forceUpdate();
        console.log(this.courseList[index - 1], this.courseList[index]);
        console.log(
          this.afterSortCourses[index - 1],
          this.afterSortCourses[index]
        );
      }
    },
    next() {
      return this.afterSortCourses;
    },
    updateAfterSortCourses() {
      console.log("变化了");
      this.afterSortCourses = [];
      for (let index = 0; index < this.courseList.length; index++) {
        this.afterSortCourses.push(this.courseList[index]._id);
      }
    },
  },
  watch: {
    courseList: {
      handler(newVal, oldVal) {
        this.updateAfterSortCourses();
      },
      deep: true,
    },
  },
  mounted() {
    this.updateAfterSortCourses();
  },
};
</script>

<style scoped>
li {
  list-style-type: none;
  margin-bottom: 15px;
}
</style>