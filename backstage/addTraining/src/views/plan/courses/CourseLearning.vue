<template>
  <div class="main-container">
    <!-- <el-card shadow="hover"> -->
    <el-row :gutter="10">
      <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <div>
            <div class="title">
              <el-button
                @click="$router.go(-1)"
                type="mini"
                icon="el-icon-arrow-left"
                style="margin-right: 10px"
                >返回</el-button
              >
              <h2>{{ course.name }}</h2>
            </div>
          </div>
        </el-col> -->

      <!-- 播放器 -->
      <el-col :xs="24" :sm="24" :md="15" :lg="16" :xl="16">
        <div class="playerDiv">
          <div class="prism-player" id="player-con"></div>
          <!-- xxx人看过 xxx弹幕 -->
        </div>
      </el-col>

      <!-- 弹幕列表 -->

      <!-- 章节列表 -->
      <el-col :xs="24" :sm="24" :md="9" :lg="8" :xl="8">
        <div class="contentListDiv">
          <div
            class="contentList"
            :style="{ height: `${clientHeight / 1.4}px` }"
          >
            <div>
              <el-row>
                <el-col :span="12">
                  <div><span>视频选集</span></div>
                </el-col>
                <el-col :span="12">
                  <div>
                    <span class="aotuPlay"
                      >{{ autoNext ? "连续播放" : "手动播放" }} &ensp;</span
                    >
                    <el-switch
                      v-model="autoNext"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    >
                    </el-switch>
                  </div>
                </el-col>
              </el-row>
              <br />
            </div>

            <div
              class="content"
              v-for="(item, index) in contentList"
              :key="item._id"
              @click="gotoPlay(index, item, 0, false)"
            >
              <el-row :gutter="10">
                <el-col :xs="12" :sm="12" :md="8" :lg="8" :xl="7">
                  <div class="cover">
                    <el-image fit="fill" :src="item.cover" class="item-img">
                      <div slot="placeholder" class="image-slot">
                        加载中<span class="dot">...</span>
                      </div>
                    </el-image>
                  </div>
                </el-col>
                <el-col :xs="12" :sm="12" :md="16" :lg="16" :xl="17">
                  <div class="name">
                    <el-link :disabled="playingIndex === index">
                      <!-- <span>{{ index + 1 }}</span> -->
                      <!-- &nbsp; -->

                      <p class="content-title">
                        <i
                          :class="
                            playingIndex === index
                              ? 'el-icon-video-pause'
                              : 'el-icon-video-play'
                          "
                        ></i>
                        {{ item.name }}
                        <!-- 下潇洒潇洒下啥潇洒潇洒小撒潇洒潇洒小洒下 -->
                      </p>
                      <p style="font-size: 4px">观看：{{ item.times }}</p>
                      <!-- <i v-show="item.completeState" class="el-icon-check"></i> -->
                    </el-link>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- </el-card> -->

    <div>
      <el-dialog
        title="提示"
        :visible.sync="dialogNext"
        width="30%"
        :before-close="handleClose"
      >
        <span>即将进入下一节&nbsp; {{ countdown }}</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="autoNext = false">取 消</el-button>
          <!-- <el-button type="primary" @click="dialogVisible = false"
            >next now</el-button
          > -->
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import coursesApi from "@/api/coursesVideo";
// import axios from 'axios'
export default {
  props: {
    courseId: String,
  },
  data() {
    return {
      dialogNext: false,
      autoNext: true,
      clientWidth: document.body.clientWidth,
      clientHeight: document.body.clientHeight,
      // courseId: this.$route.query.course_id,
      // personalTrainingID: this.$route.query.personalTraining_id,
      course: {},
      contentList: [],
      playingIndex: 0,
      player: null,
      PlayAuth: "",
      personalTraining: {},
      timer: null,
      chapterPosition: "",
      countdown: 3,
      countdownTimer: null, // 倒计时计时器
      intervalTime: 5000,
      cnaPlay: false,
    };
  },
  methods: {
    // 初始化web播放器sdk，目前都不支持ECMAS和commonJs
    initPlayerSDK() {
      const playerScriptTag = document.createElement("script");
      playerScriptTag.type = "text/javascript";
      playerScriptTag.src =
        "https://g.alicdn.com/de/prismplayer/2.9.3/aliplayer-min.js";
      playerScriptTag.id = "playerScriptTag";
      playerScriptTag.charset = "utf-8";
      let head = document.getElementsByTagName("head")[0];
      head.appendChild(playerScriptTag);
      return new Promise((resolve, reject) => {
        // 监听是否加载完成，否则初始化播放器失败
        if (playerScriptTag.readyState) {
          // IE
          playerScriptTag.onreadystatechange = () => {
            if (
              playerScriptTag.readyState === "loaded" ||
              playerScriptTag.readyState === "complete"
            ) {
              playerScriptTag.onreadystatechange = null;
              resolve(1);
            }
          };
        } else {
          // 其他浏览器
          playerScriptTag.onload = () => {
            resolve(1);
          };
        }
      });
    },

    // 获取课程
    async initCourse() {
      await coursesApi
        .getCourseAllContent({
          _id: this.courseId,
        })
        .then(({ data }) => {
          if (data.message === "OK") this.course = data.course;
          this.contentList = data.contentList;
        });
    },
    countdownFun() {
      if (this.autoNext) {
        if (this.countdown) {
          this.countdown--;
          this.countdownTimer = setTimeout(this.countdownFun, 1000);
        } else {
          clearTimeout(this.countdownTimer);
          this.countdownTimer = null;
          // this.dialogNext = false;
          // 跳到下一节
          this.gotoPlay(
            this.playingIndex + 1,
            this.contentList[this.playingIndex + 1],
            0,
            false
          );
          this.countdown = 3;
        }
      } else {
        clearTimeout(this.countdownTimer);
        this.countdown = 3;
        this.countdownTimer = null;
        // this.dialogNext = false;
      }
    },

    // 订阅事件
    playerLoadReady() {
      console.log("player ready");
      this.player.off("ready", this.playerLoadReady);
    },
    playerCanPlay() {
      console.log("视频加载完成");
      this.cnaPlay = true;
      if (this.autoNext) {
        // this.player.play(); // 自动播放
      }
    },
    playerPlay() {
      console.log("start play");
    },
    playerPause() {
      console.log("pause");
    },
    playerPlaying() {
      console.log(this.player.getCurrentTime());
      console.log("playing");
      console.log("1234567890");
    },
    playerEnded(e) {
      console.log("end", e);
      console.log(this.playingIndex, this.contentList.length);
      if (this.playingIndex + 1 !== this.contentList.length) {
        if (this.autoNext) {
          // this.dialogNext = true;
          this.countdownFun();
        }
      }
    },
    playerError(e) {
      console.log(e);
      this.$message.error(e);
    },
    snapshoted(data) {
      const pictureData = data.paramData.base64;
      const downloadElement = document.createElement("a");
      downloadElement.setAttribute("href", pictureData);
      const fileName = "Aliplayer" + Date.now() + ".png";
      downloadElement.setAttribute("download", fileName);
      downloadElement.click();
      pictureData = null;
    },
    playerStartSeek(e) {
      console.log(e);
    },
    playerCompleteSeek(e) {
      console.log(e);
    },

    // 订阅初始化函数
    listenPlayer() {
      // 截图
      this.player.on("snapshoted", this.snapshoted);
      this.player.on("ready", this.playerLoadReady);
      this.player.on("canplay", this.playerCanPlay);
      this.player.on("play", this.playerPlay);
      this.player.on("playing", this.playerPlaying);
      this.player.on("pause", this.playerPause);
      this.player.on("ended", this.playerEnded);
      this.player.on("error", this.playerError);
      this.player.on("startSeek", this.playerStartSeek);
      this.player.on("completeSeek", this.playerCompleteSeek);
    },
    // 清除订阅
    clearPlayerListen() {
      this.player.off("snapshoted", this.snapshoted);
      this.player.off("play", this.playerPlay);
      this.player.off("playing", this.playerPlaying);
      this.player.off("pause", this.playerPause);
      this.player.off("ended", this.playerEnded);
      this.player.off("error", this.playerError);
      this.player.off("startSeek", this.playerStartSeek);
      this.player.off("completeSeek", this.playerCompleteSeek);
    },
    // 清除播放器
    clearPlayer() {
      if (this.player) {
        this.cnaPlay = false;
        this.clearPlayerListen();
        this.player.dispose();
      }
      this.player = null;
    },
    // 创建播放器
    createPlayer(video = {}, videoProgress = 0) {
      if (this.timer) this.clearTimer();
      this.player = new Aliplayer(
        {
          vid: video.VideoId,
          playauth: video.PlayAuth,
          cover: video.cover,
          id: "player-con",
          qualitySort: "asc",
          format: "mp4",
          mediaType: "video",
          width: "96%",
          height: `${this.clientHeight / 1.4}px`,
          autoplay: false,
          isLive: false,
          rePlay: false,
          playsinline: true,
          preload: true,
          language: "zh-cn",
          controlBarVisibility: "hover",
          useH5Prism: true,
          extraInfo: {
            crossOrigin: "anonymous",
          },
          skinLayout: [
            {
              name: "bigPlayButton",
              align: "blabs",
              x: 30,
              y: 80,
            },
            {
              name: "H5Loading",
              align: "cc",
            },
            {
              name: "errorDisplay",
              align: "tlabs",
              x: 0,
              y: 0,
            },
            {
              name: "infoDisplay",
            },
            {
              name: "tooltip",
              align: "blabs",
              x: 0,
              y: 56,
            },
            {
              name: "thumbnail",
            },
            {
              name: "controlBar",
              align: "blabs",
              x: 0,
              y: 0,
              children: [
                {
                  name: "progress",
                  align: "blabs",
                  x: 0,
                  y: 44,
                },
                {
                  name: "playButton",
                  align: "tl",
                  x: 15,
                  y: 12,
                },
                {
                  name: "timeDisplay",
                  align: "tl",
                  x: 10,
                  y: 7,
                },
                {
                  name: "fullScreenButton",
                  align: "tr",
                  x: 10,
                  y: 12,
                },
                {
                  name: "subtitle",
                  align: "tr",
                  x: 15,
                  y: 12,
                },
                {
                  name: "setting",
                  align: "tr",
                  x: 15,
                  y: 12,
                },
                {
                  name: "volume",
                  align: "tr",
                  x: 5,
                  y: 10,
                },
                {
                  name: "snapshot",
                  align: "tr",
                  x: 10,
                  y: 12,
                },
              ],
            },
          ],
        },
        (player) => {
          console.log("The player is created");
          this.listenPlayer();
          if (videoProgress) {
            this.player.seek(Math.floor(videoProgress));
          }
          console.log("自动播放", this.autoNext);
        }
      );
      console.log("123456", videoProgress);
    },

    // 章节跳转
    async gotoPlay(index, item, videoProgress = 0) {
      if (!item) {
        this.$message.error("该课程没有章节内容");
        // this.$router.go(-1);
        return;
      }
      console.log(index, item);
      // this.clearTimer();
      if (this.player) this.clearPlayer();
      // 跳转的时候更新一次章节、课程、培训完成状态
      if (item.contentType === "videoInfos") {
        this.chapterPosition = item._id;
        this.playingIndex = index;
        coursesApi
          .getVideoPlayAuth({
            VideoId: item.VideoId,
          })
          .then(({ data }) => {
            if (data && data.message === "OK") {
              const video = {
                cover: item.cover,
                VideoId: item.VideoId,
                PlayAuth: data.PlayAuth,
              };
              this.createPlayer(video, videoProgress);
            }
          })
          .catch((error) => {
            console.log(error);
            this.$message.error(JSON.stringfy(error));
          });
      } else if (item.contentType === "documents") {
        this.$message.error("这是文档");
      }
    },

    stop(){
      if (this.player) {
        this.cnaPlay = false;
        this.player.pause();
        this.player.dispose();
        this.player = void 0;
      }
    },
  },
  created() {
    // this.initProgress();
  },
  inject: ["reload"],
  async mounted() {
    // 初始化sdk，必须等待加载完
    this.initPlayerSDK().then(async (initsdk) => {
      if (initsdk) {
        await this.initCourse();
        await this.gotoPlay(0, this.contentList[0], 0);
        // await this.initContent(true);
        // this.reload();
      }
    });
  },
  watch: {
    async courseId(newVal, oldVal) {
      await this.initCourse();
      await this.gotoPlay(0, this.contentList[0], 0);
    },
  },
  destroyed() {},
};
</script>

<style scoped>
@import "https://g.alicdn.com/de/prismplayer/2.9.3/skins/default/aliplayer-min.css";
.home {
  width: 98%;
  /* margin: 20px auto; */
  padding-bottom: 48px;
}
.title {
  display: flex;
  align-items: center;
  font-weight: bold;
}
.playerDiv {
  margin-left: 40px;
}

.contentList {
  overflow-y: auto;
  overflow-x: hidden;
  background-color: rgba(235, 235, 235, 1);
  width: 95%;
  padding: 20px;
}

.contentListDiv {
  margin-right: 30px;
}

.content {
  margin-top: 16px;
  height: 60px;
  align-items: center;
}

li {
  list-style-type: none;
}

.aotuPlay {
  font-size: 13px;
}
.item-img {
  width: 90px;
  height: 60px;
}
.name {
  /* margin-left: 5px; */
  display: flex;
  align-items: center;
  height: 40px;
  /* font-size: 7px; */
}
.content-title {
  /* margin-left: 5px; */
  font-size: 14px;
}
</style>

