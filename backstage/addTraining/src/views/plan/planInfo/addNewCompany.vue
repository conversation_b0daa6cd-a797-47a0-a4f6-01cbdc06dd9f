<template>
  <div>
    <el-row>
      <el-col :span="24">
        <topbar type="adminorg" />
      </el-col>
      <el-col :span="24">
        <div>
          <data-table
            ref="companyList"
            :hasSelectArray="plan.EnterpriseID"
            :pageInfo="adminorgList.pageInfo"
            :dataList="adminorgList.docs"
            :comprehensiveLevels="adminorgList.pageInfo.comprehensiveLevels"
          />
        </div>
      </el-col>
      <el-col :span="24">
        <div>
          <pagination
            device="device"
            :pageInfo="adminorgList.pageInfo"
            :pageInfos="pageInfos"
            pageType="adminorg"
          />
        </div>
      </el-col>
      <el-col :span="24">
        <div>
          <el-button @click="next" style="width: 100%" type="primary"
            >确定</el-button
          >
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import pagination from "../companys/Pagination";
import topbar from "../companys/topbar";
import dataTable from "../companys/dataTable";
import { mapGetters } from "vuex";
import { updatePlan } from "@/api/addTraining";
export default {
  props: {
    plan: Object,
    _id: String,
  },
  data() {
    return {
      superviseInf: {},
    };
  },
  methods: {
    next() {
      const companys = this.$refs["companyList"].next();
      if (!companys.length) {
        this.$message.error("请先选择需要培训的企业哦");
        return;
      }
      updatePlan({
        newEnterprises: companys,
        _id: this._id,
      }).then(({ data }) => {
        console.log(data);
        this.$parent.$parent.reloadMethod();
      });
    },
  },
  created() {
    // 获取所有企业列表
    // console.log(this.$store.getters.searchKeys.district,'this.$store.getters.districts.searchkey');
    if (
      typeof this.$store.getters.searchKeys.district === "string" &&
      this.$store.getters.searchKeys.district !== ""
    ) {
      this.$store.getters.searchKeys.district = this.$store.getters.searchKeys.district.split(
        " "
      );
    }
    let obj = {
      sortType: {
        order: this.sortType.order ? this.sortType.order : "",
        prop: this.sortType.prop ? this.sortType.prop : "",
      },
      pageInfos: {
        current: this.pageInfos.current ? this.pageInfos.current : 1,
        pageSize: this.pageInfos.pageSize ? this.pageInfos.pageSize : 10,
      },
      searchkey: this.$store.getters.searchKeys.searchkey
        ? this.$store.getters.searchKeys.searchkey
        : "",
      district: this.$store.getters.searchKeys.district
        ? this.$store.getters.searchKeys.district
        : "",
      industryCategory: this.$store.getters.searchKeys.industryCategory
        ? this.$store.getters.searchKeys.industryCategory
        : "",
      levelOption: this.$store.getters.searchKeys.levelOption
        ? this.$store.getters.searchKeys.levelOption
        : "",
      OnlineDeclarationOption: this.$store.getters.searchKeys
        .OnlineDeclarationOption
        ? this.$store.getters.searchKeys.OnlineDeclarationOption
        : "",
      jobHealthOption: this.$store.getters.searchKeys.jobHealthOption
        ? this.$store.getters.searchKeys.jobHealthOption
        : "",
      ratioArrOption: this.$store.getters.searchKeys.ratioArrOption
        ? this.$store.getters.searchKeys.ratioArrOption
        : "",
      exceedOption: this.$store.getters.searchKeys.exceedOption
        ? this.$store.getters.searchKeys.exceedOption
        : "",
      odiseasedOption: this.$store.getters.searchKeys.odiseasedOption
        ? this.$store.getters.searchKeys.odiseasedOption
        : "",
      suspectedOption: this.$store.getters.searchKeys.suspectedOption
        ? this.$store.getters.searchKeys.suspectedOption
        : "",
      selfAssessmentOption: this.$store.getters.searchKeys.selfAssessmentOption
        ? this.$store.getters.searchKeys.selfAssessmentOption
        : "",
      assessmentResultOption: this.$store.getters.searchKeys
        .assessmentResultOption
        ? this.$store.getters.searchKeys.assessmentResultOption
        : "",
      comprehensiveLevelOption: this.$store.getters.searchKeys
        .comprehensiveLevelOption
        ? this.$store.getters.searchKeys.comprehensiveLevelOption
        : "",
      riskSortLevelOption: this.$store.getters.searchKeys.riskSortLevelOption
        ? this.$store.getters.searchKeys.riskSortLevelOption
        : "",
    };
    obj.district = JSON.stringify(obj.district);
    this.$store.dispatch("adminorg/getAdminorgList", obj);
  },
  components: {
    pagination,
    topbar,
    dataTable,
  },
  computed: {
    ...mapGetters(["adminorgList", "pageInfos", "sortType"]),
  },
};
</script>

<style>
</style>