<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="必修课" name="first">
        <el-table
          :max-height="height / 2.8"
          :data="plan.coursesID"
          :row-class-name="tableRowClassName"
        >
          <el-table-column prop="cname" label="封面" width="120">
            <template slot-scope="scope">
              <el-image
                style="width: 80px; height: 50px"
                :src="scope.row.cover"
                fit="scale-down"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="name"
            label="课程名称"
            width="150"
          >
          </el-table-column>
          <el-table-column prop="classHours" label="总课时" width="80" align="center">
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="120">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="up(scope.$index, scope.row, 'coursesID')"
                :disabled="!scope.$index"
                >上移</el-button
              >
              <el-button
                size="mini"
                @click="showCourseFun(scope.row)"
                type="primary"
                >预览</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="选修课" name="second">
        <el-table
          
          :data="plan.electives"
          :row-class-name="tableRowClassName"
        >
          <el-table-column prop="cname" label="封面" width="120">
            <template slot-scope="scope">
              <el-image
                style="width: 80px; height: 50px"
                :src="scope.row.cover"
                fit="scale-down"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="name"
            label="课程名称"
            width="150"
          >
          </el-table-column>
          <el-table-column prop="classHours" label="总课时" width="80">
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="120">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="up(scope.$index, scope.row, 'electives')"
                :disabled="!scope.$index"
                >上移</el-button
              >
              <el-button
                size="mini"
                @click="showCourseFun(scope.row)"
                type="primary"
                >预览</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      :close-on-click-modal="false"
      top="5vh"
      width="80%"
      :title="courseName"
      :visible.sync="showCourse"
      append-to-body
    >
      <courseLearning :courseId="coursesID" />
    </el-dialog>
  </div>
</template>

<script>
import courseLearning from "../courses/CourseLearning";
import { upCourse } from "@/api/addTraining";
import { deleteCourse } from "@/api/addTraining";
export default {
  data() {
    return {
      activeName: "first",
      coursesID: "",
      showCourse: false,
      courseName: "",
    };
  },
  props: {
    plan: Object,
  },
  components: {
    courseLearning,
  },
  created() {},
  inject: ["reload"],
  methods: {
    showCourseFun(course) {
      this.showCourse = true;
      this.coursesID = course._id;
      this.courseName = course.name;
    },
    up(index, row, key) {
      console.log(index, row._id);
      upCourse({ [key]: row._id, _id: this.$route.params.id }).then(
        ({ data }) => {
          // this.$router.go(0);
          this.reload();
        }
      );
    },
    handleDelete(coursesID) {
      deleteCourse({
        coursesID,
        _id: this.$route.params.id,
      }).then(({ data }) => {
        this.$router.go(0);
      });
    },
  },
};
</script>

<style scoped>

</style>