<template>
  <div :class="classObj">
    <div class="main-container">
      <div class="content">
        <div class="items left">
            <!-- <div class="table"> -->
          <el-card shadow="hover">
            <div class="topbar">
              <el-button
                size="mini"
                @click="$router.go(-1)"
                icon="el-icon-arrow-left"
                style="margin-right: 10px"
                >返回</el-button
              >
              <el-input
                size="mini"
                placeholder="可根据[企业名称]搜素"
                prefix-icon="el-icon-search"
                v-model="serachCompanyKey"
                clearable
                @clear="clearValue"
                @change="searchEnterprises"
              >
              </el-input>
              <el-select class="selectStatus"
                @change="searchEnterprises"
                v-model="searchEmterpriseStatus"
                clearable
                placeholder="完成状态"
                size="mini"
              >
                <el-option
                  v-for="item in enterpriseTrainingStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-button
                @click="dialogaddNewCompany = true"
                size="mini"
                type="primary"
                >新增企业
              </el-button>
              <el-button size="mini" type="primary" @click="remindCompanys">
                一键提醒
              </el-button>
              <el-button @click="outputAll" size="mini" type="primary"
                >一键导出
              </el-button>
              <el-button @click="handleImport" size="mini" type="primary" v-if="branch ==='fz'">
                导入培训清单
              </el-button>
            </div>
            <el-table stripe
              class="table"
              size="medium"
              :height="tableConfig.height"
              :data="tempEnterpriseID"
            >
              <el-table-column
                show-overflow-tooltip
                prop="cname"
                label="单位名称"
                width="230"
              >
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="adminUserId.phoneNum"
                label="联系方式"
                width="120"
              >
              <template slot-scope="scope">
                <div @click="showPhone(scope.row)" style="cursor:pointer">
                  {{ scope.row.adminUserId?scope.row.adminUserId.phoneNum:'' }}
                </div>
              </template>
              </el-table-column>
              <el-table-column align="center"
                prop="needTrainNum"
                label="应培训人数"
                width="90"
              >
              </el-table-column>
              <el-table-column align="center"
                prop="numberOfPlanParticipant"
                label="已参加"
                width="70"
              >
              </el-table-column>
              <el-table-column align="center"
                prop="hasCompleted"
                label="已完成"
                width="70"
              >
              </el-table-column>
              <el-table-column label="企业培训状态" width="110" align="center">
                <template slot-scope="scope">
                  {{ scope.row.completeState ? "已完成" : "未完成" }}
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="120">
                <template slot-scope="scope">
                  <el-button plain
                    size="mini"
                    type="primary"
                    :disabled="scope.row.completeState"
                    @click="remindCompany(scope.$index, scope.row)"
                    >提醒</el-button
                  >
                  <el-button plain
                    @click="deletecompany(scope.$index, scope.row)"
                    size="mini"
                    type="danger"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <!-- </div> -->
           </el-card>
          <div class="info-pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="enterpriseCurrent"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total">
            </el-pagination>
          </div>

           
          <!-- <div class="oneSave">一键保存</div> -->
         
        </div>
       
        <div class="items right">
          <el-card shadow="hover" class="pie">
            <div>
              <el-row>
                <el-col>
                  <span class="title"><strong>统计信息</strong></span>
                </el-col>
              </el-row>
              <el-row>
                <el-col>
                  <pie-chart :title="title" :chartData="chartData" :titleCenter="true"/>
                </el-col>
              </el-row>
            </div>
          </el-card>

          <el-card shadow="hover">
            <div>
              <el-row>
                <span class="title"><strong>课程管理</strong></span>

                <el-button
                  class="testDetail"
                  v-if="plan.needExam"
                  @click="dialogExamination = true"
                  size="mini"
                  type="primary"
                >
                  查看考卷信息
                </el-button>
              </el-row>
              <courses :plan="plan" :_id="_id" />
            </div>
          </el-card>
        </div>
      </div>

      <div>
        <el-dialog
          width="30%"
          title="请选择新课程"
          :visible.sync="dialogaddNewCourse"
        >
          <add-new-course :_id="_id" :selectedCourses="selectedCourses" />
        </el-dialog>

        <el-dialog
          top="0.5vh"
          width="75%"
          title="请选择新企业"
          :visible.sync="dialogaddNewCompany"
        >
          <add-new-company :plan="plan" :_id="_id" />
        </el-dialog>
      </div>

      <div>
        <el-dialog
          width="40%"
          title="考卷信息"
          :visible.sync="dialogExamination"
        >
          <!-- <el-card shadow="hover"> -->
          <div>
            <el-row :gutter="20"> </el-row>
            <br />
            <examination-info ref="examination" :plan="plan" />
          </div>
          <!-- </el-card> -->
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { getBranch } from "@/api/addTraining";
import { initEvent } from "@root/publicMethods/events";
import pieChart from "@/views/common/pieChart";
import examinationInfo from "./examinationInfo";
import addNewCompany from "./addNewCompany";
import XLSX from "xlsx";
// import XLSX_STYLE from "xlsx-style";

import {
  getPlanOne,
  deleteEnterprise,
  remindEnterprises,
  remindOne,
  getParticipatingEnterprises,
  outputAll,
  checkPhoneLog
} from "@/api/addTraining";
import addNewCourse from "./addNewCourse";
import courses from "./courses";
export default {
  components: {
    pieChart,
    examinationInfo,
    addNewCourse,
    courses,
    addNewCompany,
  },
  data() {
    return {
      dialogaddNewCourse: false,
      dialogaddNewCompany: false,
      dialogExamination: false,
      width: document.body.clientWidth,
      height: document.body.clientHeight,
      sidebarOpened: true,
      device: "desktop",
      _id: "",
      plan: {
        EnterpriseID: [],
        coursesID: [],
        completedEnterprise: [],
        examination: {},
        lesson: 0,
        name: "",
        superID: "",
        updateTime: [],
        allowTestOnly: false,
        Introduction: "",
        certificateID: "",
      },
      title: {},
      chartData: [],
      selectedCourses: [],
      // selectedCompanys: [],
      serachCompanyKey: "",

      // 企业表格页数
      enterpriseCurrent: 1,

      enterpriseTrainingStatus: [
        {
          value: true,
          label: "已完成",
        },
        // {
        //   value: "ing",
        //   label: "进行中",
        // },
        {
          value: false,
          label: "未完成",
        },
      ],
      searchEmterpriseStatus: void 0,
      tempEnterpriseID: [],
      pageSize: 10,
      total: 0,
      tempCompletedEnterprise: [],
      tableConfig: {
        height: window.innerHeight - 230
      }
    };
  },
  methods: {
    // 查看手机号
   async showPhone(row){
      if(row.adminUserId&&row.adminUserId.phoneNum){
        row.adminUserId.phoneNum =row.adminUserId.phoneNum2
        const res = await checkPhoneLog({model:'AdminUser',phoneNum:row.adminUserId.phoneNum2})
      }
    },
    getHeight () {
      this.tableConfig.height = window.innerHeight - 230;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.loadEnterprises()
    },
    handleCurrentChange(val) {
      this.enterpriseCurrent = val;
      this.loadEnterprises();
    },
    clearValue() {
      getPlanOne({ _id: this._id }).then(({ data }) => {
        this.total = data.plan.EnterpriseID.length;
      })
    },
     // 上传excel
     handleImport() {
      console.error(110000);
      // 跳转到importTrain页面
      console.error(*********,this._id);
      this.$router.push({
        name: "importTrain",
        params: {
          id: this._id,
        },
      });
      // this.$refs.fileInput.click();
    },
    searchEnterprises() {
      // console.log(this.searchEmterpriseStatus);
      this.tempEnterpriseID = [];
      this.loadEnterprises(this.serachCompanyKey);
    },
    loadEnterprises(inputValue) {
      const params = {
        current: this.enterpriseCurrent,
        pageSize: this.pageSize,
        _id: this._id,
        cname: this.serachCompanyKey,
      };
      if (this.searchEmterpriseStatus !== undefined)
        params.completeState = this.searchEmterpriseStatus;
      if(inputValue) {
        params.current = 1;
      }
      getParticipatingEnterprises(params).then(({ data }) => {
        if(inputValue) {
          this.total = data.enterprises.length;
        }
        for (let j = 0; j < data.enterprises.length; j++) {
          const element = data.enterprises[j];
          element["completeState"] = false;
          if (this.tempCompletedEnterprise.indexOf(element._id) >= 0)
            element.completeState = true;
        }
        const reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
        this.tempEnterpriseID = data.enterprises;
        console.log(this.tempEnterpriseID,'this.tempEnterpriseID---');
        for(let i = 0;i<this.tempEnterpriseID.length;i++){
          let item = this.tempEnterpriseID[i];
          if(item.adminUserId &&item.adminUserId.phoneNum){
            item.adminUserId.phoneNum2 = item.adminUserId.phoneNum
            item.adminUserId.phoneNum = item.adminUserId.phoneNum.toString().replace(reg, '$1***$2');
          }
        }
      });
    },
    serachCompany() {
      if (this.serachCompanyKey.length === 0) {
        this.plan.EnterpriseID = this.tempEnterpriseID;
      } else {
        const key = new RegExp(this.serachCompanyKey, "i");
        this.plan.EnterpriseID = this.tempEnterpriseID.filter((item) => {
          return key.test(item.cname);
        });
      }
    },
    filterCompany(value, row, column) {
      // console.log(value, row, column)
      return row.completeState === value;
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.completeState) return "success-row";
      else return "warning-row";
    },
    // 一键提醒
    remindCompanys() {
      console.log(12345);
      remindEnterprises({
        planID: this._id,
      }).then(({ data }) => {
        this.$message("批量提醒成功");
      });
    },
    // 单独提醒
    remindCompany(index, row) {
      remindOne({
        planID: this._id,
        EnterpriseID: [row._id],
      }).then(({ data }) => {
        this.$message("提醒成功");
      });
    },
    deletecompany(index, row) {
      this.$confirm(`此操作将从企业列表中删除${row.cname}, 是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(row);
          deleteEnterprise({
            _id: this.$route.params.id,
            EnterpriseID: row._id,
          }).then(({ data }) => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.reload();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    reloadMethod() {
      this.init();
      this.reload();
    },
    init() {
      this.plan = {};
      getPlanOne({ _id: this._id }).then(({ data }) => {
        this.total = data.plan.EnterpriseID.length;
        this.plan = data.plan;
        this.$forceUpdate();
        this.tempCompletedEnterprise = this.plan.completedEnterprise;
        this.title = this.plan.name;

        for (let index = 0; index < data.plan.coursesID.length; index++) {
          const element = data.plan.coursesID[index];
          this.selectedCourses.push(element._id);
        }
        if (!this.plan.incompleteEnterprise)
          this.plan.incompleteEnterprise = [];
        if (!this.plan.completedEnterprise) this.plan.completedEnterprise = [];

        this.chartData = [
          {
            name: "未开始",
            value:
              this.plan.EnterpriseID.length -
              this.plan.incompleteEnterprise.length,
            percent:
              Math.floor(
                ((this.plan.EnterpriseID.length -
                  this.plan.incompleteEnterprise.length) /
                  this.plan.EnterpriseID.length) *
                  1000
              ) /
                10 +
              "%",
          },
          {
            name: "进行中",
            value:
              this.plan.incompleteEnterprise.length -
              this.plan.completedEnterprise.length,
            percent:
              Math.floor(
                ((this.plan.incompleteEnterprise.length -
                  this.plan.completedEnterprise.length) /
                  this.plan.EnterpriseID.length) *
                  1000
              ) /
                10 +
              "%",
          },
          {
            name: "已完成",
            value: this.plan.completedEnterprise.length,
            percent:
              Math.floor(
                (this.plan.completedEnterprise.length /
                  this.plan.EnterpriseID.length) *
                  1000
              ) /
                10 +
              "%",
          },
        ];
      });
    },
    updateExamination() {
      this.$refs["examination"].submit();
    },
    // 一键导出
    outputAll() {
      outputAll({
        trainingID: this._id,
      }).then(({ data }) => {
        const rangeCompany = [];
        const rangePerson = [];
        const merges = [];
        const companyJson = [];
        const personJson = [];
        for (let index = 0; index < data.result.companys.length; index++) {
          const element = data.result.companys[index];
          // console.log(333333, element);
          companyJson.push({
            企业名称: element.cname,
            信用代码: element.code,
            应培训人数: element.adminArray.length || '/',
            已参加: (function (personalTraining){
              const set = new Set();
              if (personalTraining && personalTraining.length) {
                personalTraining.forEach((item) => {
                  if (item.status) set.add(item.adminuser._id);
                });
              }
              return set.size;
            })(element.personalTraining),
            已完成: (function (personalTraining) {
              const set = new Set();
              if (personalTraining && personalTraining.length) {
                personalTraining.forEach((item) => {
                  if (item.completeState && item.status) set.add(item.adminuser._id);
                });
              }
              return set.size;
            })(element.personalTraining),
            完成状态: (function (plan, company) {
              return plan.completedEnterprise.includes(company._id)
                ? "已完成"
                : "未完成";
            })(data.result, element),
          });
          // rangeCompany.push(2 + )
          for (let j = 0; j < element.personalTraining.length; j++) {
            const person = element.personalTraining[j];
            personJson.push({
              // _id: person._id,
              企业名称: element.cname,
              姓名: person.adminuser.name || "",
              完成状态: person.completeState ? "已完成" : "进行中",
              考试次数: person.bigTestList ? person.bigTestList.length : 0,
              每次分数: (function (person) {
                if (person.bigTestList && person.bigTestList.length) {
                  let result = `总分：${person.bigTestList[0].resultStatistics.totleScore}。  得分：`;
                  for (
                    let index = 0;
                    index < person.bigTestList.length;
                    index++
                  ) {
                    const test = person.bigTestList[index];
                    result = result + test.resultStatistics.actualScore + "、";
                  }
                  return result;
                }
                return "";
              })(person),
              证书编号:
                person.certificate && person.certificate.length
                  ? person.certificate[0].number
                  : "未获取证书",
              记录状态: person.status ? "正在使用" : "作废或者被删除",
              // 视频播放情况: (function(person){
              //   let electives = ''; // 选修
              //   let courses = ''; // 必修

              // })(person)
            });
          }
        }
        const qy = XLSX.utils.json_to_sheet(companyJson, { origin: 0 });
        const pe = XLSX.utils.json_to_sheet(personJson, { origin: 0 });
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, qy, "企业情况");
        XLSX.utils.book_append_sheet(wb, pe, "个人情况");
        XLSX.writeFile(wb, "培训.xlsx");
      });
    },
  },
  created() {
    window.addEventListener('resize', this.getHeight);
    initEvent(this);
    this._id = this.$route.params.id;
    // if (!this._id) this.$router.go(-1);
    // debugger
    this.init();
    getBranch().then(res=>{
      if(res.status=="200"){
        this.branch = res.data.branch;
    }})
  },
  mounted() {
      this.loadEnterprises();
    },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    }
  },
  inject: ["reload"],
};
</script>

<style scoped>
element.style {
  overflow-y: auto;
  height: 600px;
}
</style>

<style scoped lang="scss">
.main-container {
  padding: 0 20px;
}
@media screen and (max-width: 810px) {
  body {
    background: seagreen !important;
  }
  .content {
    flex-direction: column;
    .right {
      width: 100%;
      flex: 3;
      padding: 25px 0;
    }
  }
}

.content {
  display: flex;
  height: calc(100vh - 100px);
  padding: 10px;
  .left {
    flex: 1.2;
    display: flex;
    flex-direction: column;
    // .el-card {
    //   flex: 1 1;
    // }
    .table {
      margin-top:40px;
    }
    .info-pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 15px;
    }
  }
  .right {
    flex: .5;
    min-width: 320px;
    padding-left: 20px;
    display: flex;
    flex-direction: column;
    .el-card {
      flex: 1 1;
    }
    .el-card.pie {
      margin-bottom: 20px;
    }
  }
}

.title{
  font-size: 2ch;
}
.title strong{
  font-size: 16px;
}
.testDetail {
  margin-left: 20px;
}
.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}

.topbar {
  float: left;
  display: flex;
}

.oneSave {
  position: absolute;
  top: 60px;
  right: 50px;
  background: #f2f5f6;
  color: #409eff;
  padding: 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  animation: breathing 2s ease-in-out infinite alternate;
  border: 1px solid rgba(76, 176, 249, 0.9);
  transition: color, background 0.5s;
  z-index: 99;
}
.oneSave:hover {
  background: #409eff;
  color: #f2f5f6;
}
.selectStatus{
  margin: 0 10px;
  width: 220px;
}
</style>