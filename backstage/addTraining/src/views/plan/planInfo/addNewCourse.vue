<template>
  <div>
    <el-row :gutter="10">
      <el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16">
        <div>
          <el-select
            filterable
            remote
            v-model="selectList"
            multiple
            placeholder="请选择"
            :remote-method="getCourseWithoutSelected"
            :loading="loading"
          >
            <el-option
              v-for="item in courseWithoutSelected"
              :key="item._id"
              :label="item.name"
              :value="item._id"
            >
            </el-option>
          </el-select>
        </div>
      </el-col>
      <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
        <div>
          <el-button @click="submit" size="small" type="primary"
            >立即添加</el-button
          >
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getCoursesList } from "@/api/courses";
import { updatePlan } from "@/api/addTraining";
export default {
  props: {
    selectedCourses: {
      type: Array,
      default: [],
    },
    _id: String,
  },
  data() {
    return {
      searchKey: "",
      courseWithoutSelected: [], // 除了已选的课程之外的课程
      selectList: [],
    };
  },
  methods: {
    remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        this.getCourseWithoutSelected().then(({ data }) => {
          this.loading = false;
          return data.data.list;
        });
        this.loading = false;
      } else {
        this.options = [];
      }
    },
    // 需要除去已经选了的课程
    getCourseWithoutSelected() {
      return this.$axios.get(getCoursesList, {
        current: 1,
        searchkey: this.searchKey,
        selectedCourses: JSON.stringify(this.selectedCourses),
      });
      // .then(({ data }) => {
      //   this.courseList = data.data.list;
      //   this.count = data.data.count;
      // });
    },
    submit() {
      
      if (this.selectList.length) {
        updatePlan({
          newCourses: this.selectList,
          _id: this._id,
        }).then(({data})=>{
          console.log(data)
          this.$parent.$parent.reloadMethod()
        })
      } else {
        this.$message({
          message: "请选择需要添加的课程",
          type: "warning",
        });
      }
    },
  },
  created() {
    this.getCourseWithoutSelected().then(({ data }) => {
      this.courseWithoutSelected = data.data.list;
      this.count = data.data.count;
    });
  },
  watch: {
    selectedCourses: {
      deep: true,
      handler: function (newV, oldV) {
        this.getCourseWithoutSelected();
      },
    },
  },
};
</script>

<style>
</style>