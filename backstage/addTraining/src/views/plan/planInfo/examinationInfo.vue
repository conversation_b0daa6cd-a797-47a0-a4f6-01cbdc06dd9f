<template>
  <div>
    <el-form
      :model="plan"
      :rules="planRules"
      ref="planForm"
      class="demo-ruleForm"
      size="mini"
      label-position="right"
    >
      <el-row :gutter="20">
        <!-- <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-form-item label="培训名称" prop="name">
            <el-input v-model="plan.name"></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="计划完成时间" prop="completeTime">
            <el-date-picker
              v-model="plan.completeTime"
              type="date"
              placeholder="选择日期"
              size="mini"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="培训完成证书" prop="certificateID">
            <el-select
              v-model="plan.certificateID"
              filterable
              placeholder="可搜索名称"
            >
              <el-option
                v-for="item in certificateList"
                :key="item.name"
                :label="item.name"
                :value="item._id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="单选题" prop="required">
            <el-row :gutter="10">
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <el-input
                  disabled
                  class="input-number"
                  type="number"
                  v-model="plan.examination.singleChoice.num"
                ></el-input>
                <span>&ensp;题</span>
              </el-col>
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <span>每题&ensp;</span>
                <el-input
                  disabled
                  class="input-number"
                  type="number"
                  v-model="plan.examination.singleChoice.scores"
                ></el-input>
                <span>&ensp;分</span>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="多选题" prop="required">
            <el-row :gutter="10">
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <el-input
                  disabled
                  class="input-number"
                  type="number"
                  v-model="plan.examination.multipleChoice.num"
                ></el-input>
                <span>&ensp;题</span>
              </el-col>
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <span>每题&ensp;</span>
                <el-input
                  disabled
                  class="input-number"
                  type="number"
                  v-model="plan.examination.multipleChoice.scores"
                ></el-input>
                <span>&ensp;分</span>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="判断题" prop="required">
            <el-row :gutter="10">
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <el-input
                  disabled
                  class="input-number"
                  type="number"
                  v-model="plan.examination.Judgment.num"
                ></el-input>
                <span>&ensp;题</span>
              </el-col>
              <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                <span>每题&ensp;</span>
                <el-input
                  disabled
                  class="input-number"
                  type="number"
                  v-model="plan.examination.Judgment.scores"
                ></el-input>
                <span>&ensp;分</span>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="总分数" prop="required">
            <!-- <span>{{ subAllScores() }}</span> -->
            &thinsp;
            <el-input
              style="width: 50%"
              type="number"
              v-model="totalScore"
              disabled
            ></el-input
            ><span>&ensp;分</span>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="及格分占比">
            <el-input
              disabled
              style="width: 50%"
              type="number"
              v-model="plan.examination.passingGrate"
            ></el-input>
            <span>&ensp;%</span>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="允许直接考试">
            <el-switch disabled style="display: block" v-model="plan.allowTestOnly">
            </el-switch>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="考试时长">
            <el-input
              disabled
              style="width: 50%"
              type="number"
              v-model="plan.examination.limitTime"
            ></el-input>
            <span>&ensp;分钟</span>
          </el-form-item>
        </el-col>

        <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="培训介绍" prop="Introduction">
            <el-input
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 8 }"
              placeholder="请输入内容"
              v-model="plan.Introduction"
            >
            </el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :xs="20" :sm="20" :md="20" :lg="20" :xl="20" :offset="2">
          <div>
            <el-button
              style="width: 100%"
              type="primary"
              @click="submitForm('planForm')"
              >保存</el-button
            >
          </div>
          <br />
        </el-col> -->
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getCertificateList, updatePlan } from "@/api/addTraining";
export default {
  props: {
    plan: Object,
  },
  watch:{
    plan:{
      deep: true,
      handler: function (newV, oldV) {
        console.log(111111, newV)
      },
    },
  },
  data() {
    let validatePass = (rule, value, callback) => {
      callback();
    };
    return {
      planRules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "长度在 2 到 50 个字符",
            trigger: "blur",
          },
        ],
        required: [
          { required: true, validator: validatePass, trigger: "blur" },
        ],
        completeTime: [
          { required: true, message: "请选择计划完成时间", trigger: "change" },
        ],
        // certificateID: [
        //   { required: true, message: "请选择计划完成时间", trigger: "change" },
        // ],
      },
      // certificateList: [],
      totalScore: 0,
    };
  },
  methods: {
    subAllScores() {
      this.totalScore = Number(
        this.plan.examination.singleChoice.num *
          this.plan.examination.singleChoice.scores +
          this.plan.examination.multipleChoice.num *
            this.plan.examination.multipleChoice.scores +
          this.plan.examination.Judgment.num *
            this.plan.examination.Judgment.scores
      );
    },
    submit() {
      this.$refs["planForm"].validate((valid) => {
        if (valid) {
          const newExaminationInfo = {
            examination: this.plan.examination,
            name: this.plan.name,
            completeTime: this.plan.completeTime,
            allowTestOnly: this.plan.allowTestOnly,
          };
          updatePlan({
            newExaminationInfo,
            _id: this.$route.params.id,
          }).then(({ data }) => {
            this.$router.go(0);
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
  created() {
    // getCertificateList({}).then(({ data }) => {
    //   this.certificateList = data.list;
    // });
    this.subAllScores();
  },
};
</script>

<style scoped>
.el-table {
  width: 99.9% !important;
}
.input-number {
  width: 60%;
}
</style>