// 导入培训清单数据展示
<template>
  <div :class="classObj">
    <div
      class="main-container"
      style="padding: 20px; height: calc(100vh - 130px)"
    >
      <vxe-grid
        ref="xGrid"
        v-bind="gridOptions"
        @proxy-query="proxyQueryEvent"
        @proxy-delete="proxyDeleteEvent"
        @proxy-save="proxySaveEvent"
      >
        <template #toolbar_buttons>
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="beforeSave">保存</el-button>
          <el-button @click="insertEvent">新增</el-button>
          <el-button @click="removeEvent">删除</el-button>
          <el-button
            type="text"
            icon="el-icon-document"
            @click="downloadTemplate"
            >下载模板</el-button
          >
        </template>
        <template #date1_edit="{ row }">
          <vxe-input class="my-input1"  v-model="row.issuanceTime" type="date" transfer placeholder="请选择日期" @change="$refs.xGrid.updateStatus(scope)" :edit-render="{}"></vxe-input>
        </template>
        <template #date1_default="{ row }">
          <span>{{ row.issuanceTime }}</span>
        </template>
        <template #date2_edit="{ row }">
          <vxe-input class="my-input2"  v-model="row.effectiveTime" type="date" transfer placeholder="请选择日期" @change="$refs.xGrid.updateStatus(scope)" :edit-render="{}"></vxe-input>
        </template>
        <template #date2_default="{ row }">
          <span>{{ row.effectiveTime }}</span>
        </template>
        
      </vxe-grid>
      <el-dialog title="培训信息" :visible.sync="dialogFormVisible">
        <el-form
          :model="form"
          :rules="trainRules"
          ref="trainForm"
          label-position="right"
          label-width="100px"
        >
          <el-form-item label="培训名称：" prop="name">
            <el-input v-model="form.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="培训介绍" prop="Introduction">
            <el-input
              v-model="form.Introduction"
              type="textarea"
              autocomplete="off"
            ></el-input>
          </el-form-item>
            <el-form-item label="培训类型" prop="trainType">
              <el-select v-model="form.trainType" placeholder="请选择">
                <el-option
                  v-for="item in trainTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
          </el-form-item>
          <el-form-item label="学时：" prop="requiredCoursesHours">
            <el-input
              v-model="form.requiredCoursesHours"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="开始时间：" prop="date">
            <el-date-picker
              v-model="form.date"
              type="date"
              placeholder="选择日期"
              size="small"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间：" prop="completeTime">
            <el-date-picker
              v-model="form.completeTime"
              type="date"
              placeholder="选择日期"
              size="small"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="dialogSave">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import XEUtils from "xe-utils";
import VXETable from "vxe-table";
import { initEvent } from "@root/publicMethods/events";
import { importExcel } from "@/utils/importExcel";
import { importTrainList } from "@/api/addTraining";
import { exportExcel } from "@/utils/exportExcel";
export default {
  name: "importTrain",
  data() {
    const nameValid = ({ cellValue }) => {
      if (cellValue && !/^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(cellValue)) {
        return new Error("姓名格式不正确，必须中文、字母或数字");
      }
    };
    const validatePosition = ({ cellValue }) => {
      // 匹配职业卫生管理人员，主要负责人
      const regex = /^职业卫生管理人员|主要负责人|劳动者$/;
      if (!regex.test(cellValue)) {
        return new Error("必须职业卫生管理人员或主要负责人或劳动者");
      }
    };
    const validateDate = ({ cellValue }) => {
      // 正则表达式匹配日期格式
      const regex = /^\d{4}\/\d{1,2}\/\d{1,2}$/;
      if (!regex.test(cellValue))
        return new Error("日期格式不正确");
    };
    return {
      trainPlanId: "",
      isImport: false,
      importData: [],
      sidebarOpened: false,
      device: "desktop",
      hideFlag: false,
      gridOptions: {
        border: true,
        showHeaderOverflow: true,
        showOverflow: true,
        keepSource: true,
        id: "full_edit_1",
        // height:600,
        rowId: "_id",
        checkboxConfig: {
          // 设置复选框支持分页勾选，需要设置 rowId 行数据主键
          reserve: true,
        },
        rowConfig: {
          isHover: true,
        },
        columnConfig: {
          resizable: true,
        },
        customConfig: {
          storage: true,
          checkMethod: this.checkColumnMethod,
        },
        sortConfig: {
          trigger: "cell",
          remote: true,
        },
        filterConfig: {
          remote: true,
        },
        formConfig: {
          titleWidth: 100,
          titleAlign: "right",
        },
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
          import: true,
          export: true,
        },
        columns: [
          { type: "checkbox", width: "4%" },
          { type: "seq", width: "4%" },
          {
            field: "name",
            title: "姓名",
            width: "11%",
            editRender: {
              name: "input",
              attrs: { placeholder: "请输入姓名" },
            },
          },
          {
            field: "enterprise",
            title: "单位",
            width: "15%",
            editRender: {
              name: "$input",
              props: { placeholder: "请输入单位名称" },
            },
          },
          {
            field: "position",
            title: "职务",
            width: "11%",
            filters: [
              { label: "主要负责人", value: "主要负责人" },
              { label: "职业卫生管理人员", value: "职业卫生管理人员" },
              { label: "劳动者", value: "劳动者" },
            ],
            editRender: {
              name: "$select",
              props: { placeholder: "请输入昵称" },
            },
          },
          {
            field: "sex",
            title: "性别",
            width: "11%",
            filters: [
              { label: "男", value: "1" },
              { label: "女", value: "0" },
            ],
            editRender: {
              name: "$select",
              options: [],
              props: { placeholder: "请选择性别" },
            },
          },
          {
            field: "idCard",
            title: "身份证号",
            width: "11%",
            editRender: {
              name: "$input",
              props: { placeholder: "请输入身份证号" },
            },
          },
          {
            field: "unit",
            title: "培训单位",
            width: "11%",
            editRender: {
              name: "$input",
              props: { placeholder: "请输入培训单位" },
            },
          },
          {
            field: "issuanceTime",
            title: "发证日期",
            width: "11%",
            editRender: {autofocus: '.my-input1'},
            slots: { default: 'date1_default',edit: 'date1_edit' },
          },
          {
            field: "effectiveTime",
            title: "有效时间",
            width: "11%",
            editRender: {autofocus: '.my-input'},
            slots: { default: 'date2_default',edit: 'date2_edit' },
          },
        ],
        importConfig: {
          remote: true,
          importMethod: this.importMethod,
          types: ["xlsx"],
          modes: ["insert"],
        },
        exportConfig: {
          remote: true,
          exportMethod: this.exportMethod,
          types: ["xlsx"],
          modes: ["all"],
          filename: "培训清单导入失败数据",
          sheetName: "sheet1",
        },
        checkboxConfig: {
          labelField: "id",
          reserve: true,
          highlight: true,
          range: true,
        },
        editRules: {
          name: [
            { validator: nameValid },
            { required: true, message: "姓名必须填写" },
          ],
          enterprise: [{ required: true, message: "单位必须填写" }],
          position: [
            { validator: validatePosition },
            { required: true, message: "职务必须填写" },
          ],
          sex: [{ required: true, message: "性别必须填写" }],
          idCard: [{ required: true, message: "身份证号必须填写" }],
          unit: [{ required: true, message: "培训单位必须填写" }],
          issuanceTime: [
            { required: true, message: "发证日期必须填写" },
          ],
          effectiveTime: [
            {required: true, message: "有效时间必须填写" },
          ],
        },
        editConfig: {
          trigger: "click",
          mode: "row",
          showStatus: true,
        },
        data: [],
      },
      dialogFormVisible: false,
      form: {
        name: "",
        Introduction: "",
        date: new Date(),
        completeTime: new Date(),
        requiredCoursesHours: 0,
      },
      trainRules: {
        name: [
          { required: true, message: "请输入名称", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "长度在 2 到 50 个字符",
            trigger: "blur",
          },
        ],
        trainType: [
          { required: true, message: "请选择培训类型", trigger: "blur" },
        ],
        Introduction: [
          { required: true, message: "请输入简介", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "长度在 2 到 50 个字符",
            trigger: "blur",
          },
        ],
        date: [
          { required: true, message: "请选择计划开始时间", trigger: "change" },
        ],
        completeTime: [
          { required: true, message: "请选择计划完成时间", trigger: "change" },
        ],
        requiredCoursesHours: [
          { required: true, message: "请填写学时", trigger: "blur" },
        ],
      },
      trainTypeList: [
        { label: "管理员培训", value: 1 },
        { label: "劳动者培训", value: 3 },
      ],
    };
  },
  computed: {
    ...mapState(["serveApiUrl"]),
  },
  async created() {
    this.findSexList();
    this.findPositionList();
    initEvent(this);
    //获取通过vue的push路由跳转携带的prams传过来的参数
    this.trainPlanId = this.$route.params.id;
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    insertEvent() {
      const $grid = this.$refs.xGrid;
      $grid.insert({
        name: "",
      });
      this.gridOptions.data.push(...$grid.getInsertRecords());
    },
    async removeEvent() {
      const type = await VXETable.modal.confirm("您确定要删除该数据?");
      const $grid = this.$refs.xGrid;
      const selectRecords = $grid.getCheckboxRecords();
      if (type === 'confirm') {
        this.gridOptions.data = this.gridOptions.data.filter(e => !selectRecords.includes(e));
      }
    },
    goBack() {
      this.$router.go(-1);
    },
    // 下载模板
    downloadTemplate() {
      const a = document.createElement("a");
      a.href = "/static/dataTemplate/培训导入模板.xlsx";
      a.click();
    },
    async beforeSave() {
      const $grid = this.$refs.xGrid;
      const errMap = await $grid.validate(true).catch((errMap) => errMap);
      if (errMap) {
        VXETable.modal.message({ status: "error", message: "校验不通过！" });
        return false;
      } else {
        VXETable.modal.message({ status: "success", message: "校验成功！" });
      }
      if (this.gridOptions.data.length == 0 && $grid.getInsertRecords().length == 0) {
        this.$message({
          message: "请导入数据",
          type: "error",
        });
        return;
      }
      if (this.trainPlanId) {
        this.saveEvent();
      } else {
        (this.form = {
          name: "",
          Introduction: "",
          date: new Date(),
          completeTime: new Date(),
        }),
          (this.dialogFormVisible = true);
      }
    },
    dialogCancel() {
      this.dialogFormVisible = false;
    },
    dialogSave() {
      this.$refs.trainForm.validate((valid) => {
        if (valid) {
          this.saveEvent();
          console.log("submit!",this.form)
          this.dialogFormVisible = false;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    saveEvent() {
      // this.gridOptions.data.forEach((item) => {
      //   item.issuanceTime = new Date(item.issuanceTime)
      //   item.effectiveTime = new Date(item.effectiveTime)
      // });
      console.log('-----保存', this.gridOptions.data);
      importTrainList({
        list: this.gridOptions.data,
        params: this.form,
        trainPlanId: this.trainPlanId || "",
      }).then((res) => {
        if (res.status == 200) {
          if (res.data.failList.length > 0 && res.data.fail > 0) {
            this.$message({
              message: `保存成功${res.data.success}条,失败${res.data.fail}条，可导出失败列表`,
              type: "error",
            });
            const lastTitle = this.gridOptions.columns[this.gridOptions.columns.length - 1].title;
            if (lastTitle !== "失败原因") {
              this.gridOptions.columns.push({
              field: "reason",
              title: "失败原因",
              width: 300,
            });
            }
            this.gridOptions.data = res.data.failList;
          } else {
            this.$message({
              message: `保存成功${res.data.success}条`,
              type: "success",
            });
            this.gridOptions.data = [];
          }
        } else {
          this.$message({
            message: "保存失败",
            type: "error",
          });
        }
      });
    },
    findSexList() {
      setTimeout(() => {
        const sexList = [
          { label: "男", value: "1" },
          { label: "女", value: "0" },
        ];
        // 异步更新下拉选项
        this.sexList = sexList;
        const $grid = this.$refs.xGrid;
        if ($grid) {
          const sexColumn = $grid.getColumnByField("sex");
          sexColumn.editRender.options = sexList;
          const sexItem = $grid.getFormItems(3);
          sexItem.itemRender.options = sexList;
        }
      }, 100);
    },
    findPositionList() {
      setTimeout(() => {
        const positionList = [
          { label: "主要负责人", value: "主要负责人" },
          { label: "职业卫生管理人员", value: "职业卫生管理人员" },
          { label: '劳动者', value: '劳动者' }
        ];
        // 异步更新下拉选项
        this.positionList = positionList;
        const $grid = this.$refs.xGrid;
        if ($grid) {
          const positionColumn = $grid.getColumnByField("position");
          positionColumn.editRender.options = positionList;
          const positionItem = $grid.getFormItems(4);
          positionItem.itemRender.options = positionList;
        }
      }, 100);
    },
    formatAmount({ cellValue }) {
      return cellValue
        ? `￥${XEUtils.commafy(XEUtils.toNumber(cellValue), { digits: 2 })}`
        : "";
    },
    formatDate({ cellValue }) {
      console.log("----cellValue", cellValue);
      return XEUtils.toDateString(cellValue, "yyyy-MM-dd HH:ss:mm");
    },
    checkColumnMethod({ column }) {
      if (["nickname", "role"].includes(column.property)) {
        return false;
      }
      return true;
    },
    // 自定义服务端导入
    async importMethod({ file }) {
      try {
        const jsonData = await importExcel(file);
        console.log(jsonData);
        //jsonData 是解析出来的数据，格式是数组 jsonDate[0] 是表头
        // ['考试签到', '单位', '性别', '职务', '身份证号码']
        const headers = [
          {
            key: "name",
            label: "姓名",
          },
          {
            key: "enterprise",
            label: "单位",
          },
          {
            key: "sex",
            label: "性别",
          },
          {
            key: "position",
            label: "职务",
          },
          {
            key: "idCard",
            label: "身份证号码",
          },
          {
            key: "unit",
            label: "培训单位",
          },
          {
            key: "issuanceTime",
            label: "发证日期",
          },
          {
            key: "effectiveTime",
            label: "有效时间",
          },
        ];
        // 判断表头是否和headers一致
        if (jsonData[0].every((item, index) => item === headers[index].label)) {
          jsonData.shift();
          console.log(jsonData);
          // 处理导入的 JSON 数据 将JSON数据转换成需要的格式
          const data = jsonData.map((item) => {
            const obj = {};
            headers.forEach((header, index) => {
              console.log(item[index]);
              if (
                header.key == "issuanceTime" ||
                header.key == "effectiveTime"
              ) {
                obj[header.key] = item[index]
                  ? new Date((item[index] - 25569) * 86400 * 1000)
                    .toLocaleDateString()
                    .replace(
                      /(\d{4})\/(\d{1,2})\/(\d{1,2})/,
                      function (match, p1, p2, p3) {
                        return `${p1}-${p2.padStart(2, "0")}-${p3.padStart(
                          2,
                          "0"
                        )}`;
                      }
                    )
                  : "";
              } else {
                obj[header.key] = item[index];
              }
            });
            console.log("-------日期", obj);
            return obj;
          });
          console.log(data);
          this.isImport = true;
          this.gridOptions.data = data;
          // const $grid = this.$refs.xGrid;
          VXETable.modal.message({
            content: `成功导入 ${data.length} 条记录！`,
            status: "success",
          });
          // 导入完成，刷新表格
          // $grid.commitProxy("query");
        } else {
          VXETable.modal.message({
            content: "导入失败，请检查数据是否正确！",
            status: "error",
          });
          return;
        }
        // 处理导入的 JSON 数据
      } catch (error) {
        console.error(error);
        VXETable.modal.message({
          content: "导入失败，请检查数据是否正确！",
          status: "error",
        });
      }
    },
    // 自定义服务端导出
    async exportMethod({ options }) {
      console.log(options);
      if (options.data.length == 0) {
        VXETable.modal.message({
          content: "导出失败，无内容！",
          status: "error",
        });
        return;
      }
      // return
      // 开始本地导出
      // 转化成需要的格式
      const headers = [
        {
          key: "name",
          label: "考试签到",
        },
        {
          key: "enterprise",
          label: "单位",
        },
        {
          key: "sex",
          label: "性别",
        },
        {
          key: "position",
          label: "职务",
        },
        {
          key: "idCard",
          label: "身份证号码",
        },
        {
          key: "reason",
          label: "失败原因",
        },
      ];
      let exportData = options.data.map((item) => {
        const obj = {};
        headers.forEach((header, index) => {
          obj[header.label] = item[header.key];
        });
        return obj;
      });

      await exportExcel(exportData, options.filename, options.sheetName);
    },
  },
};
</script>
<style lang="scss" scoped></style>
