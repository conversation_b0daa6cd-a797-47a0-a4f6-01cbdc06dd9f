import request from '@root/publicMethods/request';

export function getBranch() {
  return request({
    url: '/api/getBranch',
    method: 'get',
  });
}

export function createPlan(data) {
  return request({
    url: '/manage/adminTraining/createPlan',
    method: 'post',
    data,
  });
}

export function getPlanList(params) {
  return request({
    url: '/manage/adminTraining/getPlanList',
    method: 'get',
    params,
  });
}

export function getPlanOne(params) {
  return request({
    url: '/manage/adminTraining/getPlanOne',
    method: 'get',
    params,
  });
}

export function updatePlan(data) {
  return request({
    url: '/manage/adminTraining/updatePlan',
    method: 'post',
    data,
  });
}

export function getCertificateList(params) {
  return request({
    url: '/manage/adminTraining/getCertificateList',
    method: 'get',
    params,
  });
}

export function deleteEnterprise(data) {
  return request({
    url: '/manage/adminTraining/deleteEnterprise',
    method: 'post',
    data,
  });
}

export function upCourse(data) {
  return request({
    url: '/manage/adminTraining/upCourse',
    method: 'post',
    data,
  });
}

export function deleteCourse(data) {
  return request({
    url: '/manage/adminTraining/deleteCourse',
    method: 'post',
    data,
  });
}

export function remindEnterprises(data) {
  return request({
    url: '/manage/adminTraining/remindEnterprises',
    method: 'post',
    data,
  });
}

export function remindOne(data) {
  return request({
    url: '/manage/adminTraining/remindOne',
    method: 'post',
    data,
  });
}

export function getCompanyList(params) {
  return request({
    url: '/manage/adminTraining/getCompanyList',
    method: 'get',
    params,
  });
}

export function getSatisticByYear(params) {
  return request({
    url: '/manage/adminTraining/getSatisticByYear',
    method: 'get',
    params,
  });
}

export function getCoverageLastManyYears(params) {
  return request({
    url: '/manage/adminTraining/getCoverageLastManyYears',
    method: 'get',
    params,
  });
}

export function selectAllEnterprises(params) {
  return request({
    url: '/manage/adminTraining/selectAllEnterprises',
    method: 'get',
    params,
  });
}

export function getParticipatingEnterprises(params) {
  return request({
    url: '/manage/adminTraining/getParticipatingEnterprises',
    method: 'get',
    params,
  });
}

export function deletePlan(params) {
  return request({
    url: '/manage/adminTraining/deletePlan',
    method: 'get',
    params,
  });
}

export function checkoutCompany(data) {
  return request({
    url: '/manage/adminTraining/checkoutCompany',
    method: 'post',
    data,
  });
}

export function getCompanysId(data) {
  return request({
    url: '/manage/adminTraining/getCompanysId',
    method: 'post',
    data,
  });
}

export function outputAll(data) {
  return request({
    url: '/manage/adminTraining/outputAll',
    method: 'post',
    data,
  });
}

export function importTrainList(data) {
  return request({
    url: '/manage/adminTraining/importTrainList',
    method: 'post',
    data,
  });
}

export function checkPhoneLog(params) {
  console.log(params, 'ssssssssss');
  return request({
    url: '/manage/superUser/checkPhoneLog',
    params,
    method: 'get',
  });
}
