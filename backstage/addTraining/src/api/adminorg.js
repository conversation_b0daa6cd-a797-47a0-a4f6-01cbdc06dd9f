import request from '@root/publicMethods/request';

export function repulse(params) {
  return request({
    url: '/manage/adminorgGov/repulse',
    params,
    method: 'get',
  });
}
export function getAllAssessmentInfo(data) {
  return request({
    url: '/manage/adminorgGov/getAllAssessmentInfo',
    data,
    method: 'post',
  });
}
export function getsuperUserInfo(params) {
  return request({
    url: '/api/adminorgGov/getsuperUserInfo',
    params,
    method: 'get',
  });
}
export function gosendMessages(data) {
  return request({
    url: '/api/adminorgGov/gosendMessages',
    data,
    method: 'post',
  });
}
export function count(params) {
  return request({
    url: '/manage/supervision/count',
    params,
    method: 'get',
  });
}
export function showList(data) {
  return request({
    url: '/manage/supervision/list',
    data,
    method: 'post',
  });
}
export function goAdd(data) {
  return request({
    url: '/manage/supervision/add',
    data,
    method: 'post',
  });
}
export function findAssess(data) {
  return request({
    url: '/manage/adminorgGov/findAssess',
    data,
    method: 'post',
  });
}
export function findApplyForm(data) {
  return request({
    url: '/manage/adminorgGov/findApplyForm',
    data,
    method: 'post',
  });
}
export function chooseYear(data) {
  return request({
    url: '/manage/adminorgGov/chooseYear',
    data,
    method: 'post',
  });
}

export function findCheckResult(data) {
  return request({
    url: '/manage/adminorgGov/findCheckResult',
    data,
    method: 'post',
  });
}

export function getIndustryCategory() {
  return request({
    method: 'get',
    url: '/api/adminorgGov/getIndustryCategory',
  });
}

export function adminorgList(params) {
  return request({
    url: '/manage/adminorgGov/getList',
    params,
    method: 'get',
  });
}

export function getOneAdminorg(params) {
  return request({
    url: '/manage/adminorgGov/getCompany',
    params,
    method: 'get',
  });
}

export function passAudit(data) {
  return request({
    url: '/manage/adminorgGov/passAudit',
    data,
    method: 'post',
  });
}

export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

export function addCompany(data) {
  return request({
    url: '/api/adminorgGov/addCompany',
    data,
    method: 'post',
  });
}

