<template>
  <el-row>
    <!-- <el-alert center type="success" :closable="false">
      <template slot="title">
        共有 <B>{{ totleCount || 0 }}</B> 项签约合同（灰底的为未注册机构）
      </template>
    </el-alert> -->

    <el-table
      align="center"
      tooltip-effect="dark"
      style="width: 100%"
      row-key="_id"
      ref="multipleTable"
      header-cell-style="background-color: #FAFAFA;height:60px"
      v-loading="loading"
      :data="contractList"
      :row-class-name="tableRowClassName"
      :max-height="tableHeight"
    >
      <el-table-column fixed type="expand">
        <template slot-scope="props">
          <tableExpandItem v-model="tableExpandDetail" :row="props.row" />
        </template>
      </el-table-column>
      <el-table-column
        prop="projectSN"
        align="center"
        min-width="80px"
        fixed
        label="项目编号"
      >
      </el-table-column>
      <el-table-column
        prop="serviceOrgInfo.name"
        label="机构名称"
        align="center"
        fixed
        min-width="140px"
      >
        <template slot-scope="scope">
          <div style="cursor: pointer" @click="showOrgDetail(scope.row)">
            {{ scope.row.serviceOrgInfo.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="cname"
        align="center"
        label="委托单位"
        fixed
        min-width="140px"
      >
      </el-table-column>

      <el-table-column
        prop="projectName"
        align="center"
        min-width="180px"
        label="项目名称"
      >
      </el-table-column>
      <el-table-column
        prop="serviceType"
        align="center"
        min-width="120px"
        label="项目类别"
      >
      </el-table-column>
      <el-table-column
        prop="contract"
        align="center"
        min-width="120px"
        label="委托方联系人"
      >
      </el-table-column>
      <el-table-column
        prop="phoneNum"
        align="center"
        label="委托方联系电话"
        min-width="120px"
      >
      </el-table-column>
      <el-table-column
        prop="entrustDate"
        align="center"
        label="委托时间"
        min-width="180px"
      >
        <template slot-scope="scope">
          {{ doMomentmonthD(scope.row.entrustDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="sampleNum" align="center" min-width="80px" label="样品数目">
      </el-table-column>
      <el-table-column
        prop="sampleNum"
        align="center"
        min-width="220px"
        label="职业病危害因素"
      >
      </el-table-column>
    </el-table>
  </el-row>
</template>

<script>
import moment from "moment";
import tableExpandItem from "@/components/tableExpandItem";
export default {
  props: {
    contractList: Array,
    value: Object,
    searchParams: Object,
    totleCount: Number,
  },
  components: {
    tableExpandItem,
  },
  data() {
    return {
      tableRowDetail: { ...this.value },
      tableExpandDetail: {
        show: false,
        data: {},
        type: "labTestReport",
      },
      loading: false,
    };
  },
  watch: {
    // 监听 tableRowDetail 的变化并通过 $emit 更新父组件数据
    tableRowDetail: {
      handler(newValue) {
        this.$emit("input", newValue);
      },
      deep: true, // 深度监听对象的变化
    },
    // 监听 tableExpandDetail 的变化并通过 $emit 更新父组件数据
    tableExpandDetail: {
      handler(newValue) {
        this.$emit("input", newValue);
      },
      deep: true, // 深度监听对象的变化
    },
  },
  methods: {
    // 查看手机号
    async showPhone(row, flag) {
      let phoneNum = "";
      if (flag === "administrators") {
        phoneNum = row.administrators[0].phoneNum2;
        row.administrators[0].phoneNum = row.administrators[0].phoneNum2;
      }
      if (flag === "isReg") {
        phoneNum = row.isReg.regPhone2;
        row.isReg.regPhone = row.isReg.regPhone2;
        // await checkPhoneLog({model:'ServiceUser',phoneNum})
      }
      if (flag === "contacts" && row.contacts.length && row.contacts[0].regPhone) {
        phoneNum = row.contacts[0].regPhone2;
        row.contacts[0].regPhone = row.contacts[0].regPhone2;
        // await checkPhoneLog({model:'inspectionReport',phoneNum})
      }
      if (
        flag === "managersAndArea" &&
        row.managersAndArea &&
        row.managersAndArea[0] &&
        row.managersAndArea[0].regPhone
      ) {
        phoneNum = row.managersAndArea[0].regPhone2;
        row.managersAndArea[0].regPhone = row.managersAndArea[0].regPhone2;
        // await checkPhoneLog({model:'inspectionReport',phoneNum})
      }
    },
    // 判断机构是否是自主增加服务区域来判断这一行的显示
    tableRowClassName({ row, rowIndex }) {
      if (row.companyIn) {
        return "cancel-row";
      }
      return "";
    },

    showOrgDetail(row) {
      this.tableRowDetail.show = true;
      this.tableRowDetail.type = "serviceOrg";
      this.tableRowDetail.data = row.serviceOrgInfo;

      console.log("siisi", this.tableRowDetail.data);
      if (new Date(this.tableRowDetail.data.qualifies[0].validTime) < new Date()) {
        this.$alert("职业卫生技术服务机构资质证书已过期", "提示", {
          confirmButtonText: "确定",
        });
      }
    },
  },
  computed: {
    //将时间 转换成 简化的
    doMomentyear(nowTime) {
      return function (nowTime) {
        return moment(nowTime).format("YYYY");
      };
    },
    //将时间 转换成 简化的
    doMomentmonthD(nowTime) {
      return function (nowTime) {
        return moment(nowTime).format("YYYY-MM-DD");
      };
    },
    tableHeight() {
      const innerHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight;
      return innerHeight - 220;
    },
  },
};
</script>
<style scoped>
.el-icon-circle-close {
  color: #f5f5f5;
}
::v-deep .el-table .cancel-row {
  background: rgb(204, 204, 204);
}
.orgName:hover {
  cursor: pointer;
  color: #409eff;
}
.el-message-box {
  z-index: 9999;
}
</style>
