<template>
  <div :class="classObj" class="adminUser">
    <div class="main-container">
      <transition name="el-zoom-in-center">
        <Details
          v-show="showDialog"
          :detail="tableRowDetail"
          @close="tableRowDetail.show = false"
          ref="details"
        />
      </transition>

      <div class="dr-container">
        <div class="dr-datatable">
          <el-row>
            <el-col :span="24">
              <div class="dr-title">
                <span class="dr-title-left">
                  <span class="dr-title-text">查询条件</span>
                </span>
                <span class="dr-title-divider"><el-divider></el-divider></span>
                <span class="dr-title-btn"></span>
              </div>
              <TopBar
                :searchParams="searchParams"
                @search="getLits"
                 @reset="reset"
                ref="detail"
              ></TopBar>
              <div class="dr-title">
                <span class="dr-title-left">
                  <span class="dr-title-text">报告列表</span>
                </span>
                <span class="dr-title-divider"><el-divider></el-divider></span>
                <span class="dr-title-btn"></span>
              </div>
              <DataTable
                v-model="tableRowDetail"
                :searchParams="searchParams"
                :contractList="contractList"
                :totleCount="totleCount"
              ></DataTable>
            </el-col>
          </el-row>
          <Pagination
            :searchParams="searchParams"
            v-show="contractList.length > 0"
            :totleCount="totleCount"
          ></Pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Details from "@/components/details/index.vue";
import DataTable from "./dataTable.vue";
import TopBar from "../common/TopBar.vue";
import Pagination from "../common/Pagination.vue";
import { initEvent } from "@root/publicMethods/events";
import { getAllReport } from "@/api";

export default {
  name: "inspectionReport",
  data() {
    return {
      contractList: [],
      searchParams: {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: "",
      },
      totleCount: 0, // 总条数
      tableRowDetail: {
        type: "serviceOrg",
        show: false,
        data: {},
      },
      sidebarOpened: false,
      device: "desktop",
    };
  },
  components: {
    DataTable,
    TopBar,
    Details,
    Pagination,
  },
  watch: {
    searchParams: {
      deep: true,
      handler: function (newV, oldV) {
        this.getLits();
      },
    },
  },
  created() {
    this.getLits(); // 获取数据列表
    initEvent(this); // 初始化 hideSidebar & openSidebar
  },
  computed: {
    reset() {
      this.searchParams = {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: "",
      };
      this.getLits();
    },
    showDialog() {
      return this.tableRowDetail.show;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    getLits() {
      getAllReport(this.searchParams).then((res) => {
        if (res.status == 200) {
          this.contractList = res.data.list;
          this.totleCount = +res.data.count || 0;
        }
      });
    },
    maskPhoneNumber(phoneNum) {
      const reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      if (phoneNum) {
        return phoneNum.toString().replace(reg, "\$1***\$2");
      }
      return phoneNum;
    },
  },
};
</script>

<style lang="scss" scoped>
.dr-container {
  padding: 20px;
  height: calc(100vh - 50px);

  .dr-datatable {
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .dr-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .dr-title-left {
      font-size: 16px;
      font-weight: 500;
      border-left: 8px solid #409eff;
      display: flex;
      height: 24px;
      line-height: 24px;
      .dr-title-text {
        margin-left: 10px;
      }
    }
    .dr-title-divider {
      flex: 1;
      padding: 0 10px;
      el-divider {
      }
    }
  }
}</style>
