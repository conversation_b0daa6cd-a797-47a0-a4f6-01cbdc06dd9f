<template>
  <div class="report-card-content">
    <span id="leftTitle">{{ labTestReportInfo.orgName }}</span>
    <div id="rightTitle">
      <span>{{ labTestReportInfo.project_num }}</span>
    </div>
    <hr />
    <h3 align="center">检测结果报告单</h3>
    <el-table :data="formData.baseInfo" border :show-header="false">
      <el-table-column prop="name" width="180"> </el-table-column>
      <el-table-column prop="value">
        <template slot-scope="scope">
          <div style="white-space: pre-line">{{ scope.row.value }}</div>
        </template>
      </el-table-column>
    </el-table>
    <br />

    <div
      v-for="(tableItem, index) in tableHeaders"
      :key="index"
      v-if="formData[tableItem.fieldName] && formData[tableItem.fieldName].length > 0"
    >
      <h2 align="center">{{ tableItem.name }}检测结果</h2>
      <lb-table
        :column="tableItem.fields || getFields(tableItem.commonFields)"
        :merge="[...defaultMergeColumns, ...(tableItem.mergeColumns || [])]"
        border
        :data="item.list"
        v-for="(item, i) in formData[tableItem.fieldName]"
        :key="i"
        :header-cell-style="getCellStyle(tableItem, i)"
      ></lb-table>
      <el-table v-if="tableItem.hasComment" :data="comment" border :show-header="false">
        <el-table-column prop="name" width="200"> </el-table-column>
        <el-table-column prop="value"> </el-table-column>
      </el-table>
      <br />
    </div>
    <br />
    <div id="rightButton" style="display: flex">
      <div v-if="formData.noPassWordFileName" style="margin-right: 10px">
        <el-button
          v-if="formData.noPassWordFileName && formData.noPassWordFileName.url"
          type="primary"
          @click="downloadNoPass(true)"
        >
          未通过参数认证报告下载
        </el-button>
      </div>
      <div
        style="display: flex"
        v-if="
          formData.wordFileName &&
          (formData.wordFileName.signedUrl || formData.wordFileName.noStampUrl)
        "
      >
        <el-button
          v-if="formData.wordFileName && formData.wordFileName.signedUrl"
          type="primary"
          @click="download(true)"
          >有章版下载</el-button
        >
        <el-button
          v-if="formData.wordFileName && formData.wordFileName.noStampUrl"
          type="primary"
          @click="download(false)"
          >无章版下载</el-button
        >
      </div>
      <el-button v-else type="primary" @click="anewDown">下载</el-button>
    </div>
  </div>
</template>
<script>
import Illuminancy from "./illuminancy.vue";
import TwoParams from "./twoParams.vue";
import FourParams from "./fourParams.vue";
import moment from "moment";
import lbTable from "@/components/lb-table/lb-table";
export default {
  name: "labTestReport",
  props: {
    labTestReportInfo: Object,
  },
  data() {
    return {
      branch: "",
      requireError: [],
      dialogTip: false,
      showSignet: false,
      showLabTestReport: false,
      defaultMergeColumns: ["checkAddress", "workspaces", "workstation", "projectName"],
      tableHeaders: [
        {
          name: "工作场所照度",
          fieldName: "illuminancy",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "measureAvg",
              label: "测量值（lx）",
              width: "150px",
            },
          ],
        },
        {
          name: "工作场所工频电场",
          fieldName: "powerFrequencyElectric",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "measureAvg",
              label: "电场强度测量值（V/m）",
              width: "150px",
            },
          ],
        },
        {
          name: "工作场所高频电磁场",
          fieldName: "electromagnetic",
          // mergeColumns: ["projectName"],
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "magnetic",
              label: "磁场强度实际值（A/m）",
            },
            {
              prop: "electric",
              label: "电场强度实际值（V/m）",
            },
          ],
        },
        {
          name: "工作场所紫外辐射",
          fieldName: "ultravioletLight",
          commonFields: "ultravioletLight",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "waveBand",
              label: "波段",
            },
            {
              label: "检测结果（µW/cm2）",
              children: [
                {
                  prop: "eyeMeasureValue",
                  label: "眼部",
                },
                {
                  prop: "faceMeasureValue",
                  label: "面部",
                },
                {
                  prop: "limbsMeasureValue",
                  label: "肢体",
                },
              ],
            },
          ],
        },
        {
          name: "工作场所电焊弧光",
          fieldName: "arcWelding",
          commonFields: "ultravioletLight",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "waveBand",
              label: "波段",
            },
            {
              label: "检测结果（µW/cm2）",
              children: [
                {
                  prop: "eyeMeasureValue",
                  label: "眼部",
                },
                {
                  prop: "faceMeasureValue",
                  label: "面部",
                },
                {
                  prop: "limbsMeasureValue",
                  label: "肢体",
                },
              ],
            },
          ],
        },
        {
          name: "工作场所手传振动",
          fieldName: "vibrance",
          mergeColumns: ["ahw"],
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "ahw",
              label: "ahw（m/s2）",
            },
            {
              prop: "measureValue",
              label: "测量值",
            },
          ],
        },
        {
          name: "工作场所高温",
          cellStyle: "headTwo",
          fieldName: "highTemperature",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "contactPosition",
              label: "WBGT测量值（℃）",
              children: [
                {
                  prop: "contactPosition",
                  label: "WBGT测量值（℃）",
                },
                {
                  prop: "WBGTValue",
                  label: "WBGT测量值（℃）",
                },
              ],
            },
          ],
        },
        {
          name: "工作场所化学有害物质",
          fieldName: "tableData",
          hasComment: true,
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "code",
              label: "样品编号",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "concentration",
              label: "空气中浓度（mg/m³）",
            },
          ],
        },
        {
          name: "工作场所游离二氧化硅",
          fieldName: "SiO2",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "code",
              label: "样品编号",
            },
            {
              prop: "quondamName",
              label: "样品名称",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "concentration",
              label: "检测结果",
            },
          ],
        },
        {
          name: "噪声工作场所噪声",
          fieldName: "nonsteadyNoise",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "max",
              label: "检测最大值dB(A)",
              width: "150px",
            },
            {
              prop: "value",
              label: "Leq(A)值dB(A)",
              width: "145px",
            },
          ],
        },
        {
          name: "噪声工作场所噪声",
          fieldName: "steadyStatueNoise",
          fields: [
            // 稳态噪声，个体噪声
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "value",
              label: "检测值dB(A)",
            },
          ],
        },
        {
          name: "噪声工作场所噪声",
          fieldName: "impulseNoise",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "value",
              label: "声压级峰值dB(A)",
            },
          ],
        },
        {
          name: "非噪声工作场所噪声",
          fieldName: "nonnoiseWorkingSite",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "value",
              label: "噪声值dB(A)",
            },
          ],
        },
        {
          name: "工作场所一氧化碳",
          fieldName: "coColumn",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "measureValue",
              label: "空气中浓度（mg/m3）",
            },
          ],
        },
        {
          name: "工作场所激光辐射",
          // mergeColumns: ["projectName"],
          fieldName: "laserRadiation",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "measureValue",
              label: "测量值（mW）",
            },
          ],
        },
        {
          name: "工作场所微波辐射",
          fieldName: "microwaveRadiation",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "contactPosition",
              label: "检测结果（µW/cm2）",
              children: [
                {
                  prop: "headMeasureValue",
                  NoColumnWhenNoData: true,
                  label: "头",
                },
                {
                  prop: "chestMeasureValue",
                  NoColumnWhenNoData: true,
                  label: "胸",
                },
                {
                  prop: "abdomenMeasureValue",
                  NoColumnWhenNoData: true,
                  label: "腹",
                },
                {
                  prop: "localMeasureValue",
                  NoColumnWhenNoData: true,
                  label: "局部",
                },
              ],
            },
          ],
        },
        {
          name: "工作场所超高频辐射",
          fieldName: "ultraHighRadiation",
          fields: [
            {
              prop: "checkAddress",
              label: "检测地点",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "measureValue",
              label: "电场强度测量值（V/m）",
            },
          ],
        },
        {
          name: "工作场所微小气",
          fieldName: "microclimate",
          fields: [
            {
              prop: "workspaces",
              label: "车间",
            },
            {
              prop: "workstation",
              label: "岗位",
            },
            {
              prop: "projectName",
              label: "检测项目",
            },
            {
              prop: "temperature",
              label: "温度",
            },
            {
              prop: "windSpeed",
              label: "风速",
            },
            {
              prop: "humidity",
              label: "相对湿度",
            },
            {
              prop: "pressure",
              label: "大气压",
            },
          ],
        },
      ],
      comment: [], // 化学的备注
      width: "75%",
      stations: [],
      title: "",
      props: {
        children: "harmFactors",
        value: "label",
      },
      harmFactor: "", // 重新选择的危害因素
      harmDialogVisible: false,
      page: 1,
      total: 2,
      spanArr: [],
      SiO2spanArr: [],
      EnterpriseID: "", //企业id
      data: {},
      formData: {
        baseInfo: [],
        tableData: [], //化学因素
        SiO2: [], //游离SiO2
        steadyStatueNoise: [], // 稳态噪声
        nonsteadyNoise: [], // 非稳态噪声
        nonnoiseWorkingSite: [], // 非噪声作业场所
        illuminancy: [], // 照度
        arcWelding: [], // 电焊弧光
        electromagnetic: [], // 高频电磁场
        vibrance: [],
        highTemperature: [],
        co: [],
        co2: [],
        microwaveRadiation: [],
        microclimate: [],
        ultravioletLight: [],
      },
      testingBasis: "",
    };
  },
  components: {
    Illuminancy,
    TwoParams,
    FourParams,
    lbTable,
  },
  methods: {
    updateDialogVisible(val) {
      this.dialogTip = val;
    },
    getCellStyle(item, i) {
      if (item.cellStyle) {
        return this[item.cellStyle];
      } else {
        return i > 0 ? this.headFirst : "";
      }
    },
    initTableFields() {
      const formData = this.formData;
      // 微波辐射没测的部位不展示
      let microwaveObj = {
        abdomenMeasureValue: false,
        chestMeasureValue: false,
        headMeasureValue: false,
        localMeasureValue: false,
      };
      this.handleEmptyColumn(this.formData.microwaveRadiation, microwaveObj);
      const electromagneticObj = {
        magnetic: false,
      };
      this.handleEmptyColumn(this.formData.electromagnetic, electromagneticObj);
      const ultravioletLightObj = {
        eyeMeasureValue: false,
        faceMeasureValue: false,
        limbsMeasureValue: false,
      };
      const arcWeldingObj = JSON.parse(JSON.stringify(ultravioletLightObj));
      this.handleEmptyColumn(this.formData.ultravioletLight, ultravioletLightObj);
      this.handleEmptyColumn(this.formData.arcWelding, arcWeldingObj);
      console.log(arcWeldingObj, this.formData.arcWelding);
      for (let i = 0; i < this.tableHeaders.length; i++) {
        let item = this.tableHeaders[i];
        if (
          item.fieldName === "electromagnetic" ||
          item.fieldName === "microwaveRadiation" ||
          item.fieldName === "ultravioletLight" ||
          item.fieldName === "arcWelding"
        ) {
          let i2 = item.fields.length;
          while (i2--) {
            let item2 = item.fields[i2];
            if (
              item.fieldName === "electromagnetic" &&
              item2.prop === "magnetic" &&
              !electromagneticObj.magnetic
            ) {
              item.fields.splice(i2, 1);
            }
            if (item2.children) {
              let i3 = item2.children.length;
              while (i3--) {
                let item3 = item2.children[i3];
                if (
                  item.fieldName === "microwaveRadiation" &&
                  ((item3.prop === "abdomenMeasureValue" &&
                    !microwaveObj.abdomenMeasureValue) ||
                    (item3.prop === "chestMeasureValue" &&
                      !microwaveObj.chestMeasureValue) ||
                    (item3.prop === "headMeasureValue" &&
                      !microwaveObj.headMeasureValue) ||
                    (item3.prop === "localMeasureValue" &&
                      !microwaveObj.localMeasureValue))
                ) {
                  item2.children.splice(i3, 1);
                }
                if (
                  item.fieldName === "ultravioletLight" &&
                  ((item3.prop === "eyeMeasureValue" &&
                    !ultravioletLightObj.eyeMeasureValue) ||
                    (item3.prop === "faceMeasureValue" &&
                      !ultravioletLightObj.faceMeasureValue) ||
                    (item3.prop === "limbsMeasureValue" &&
                      !ultravioletLightObj.limbsMeasureValue))
                ) {
                  item2.children.splice(i3, 1);
                }
                if (
                  item.fieldName === "arcWelding" &&
                  ((item3.prop === "eyeMeasureValue" && !arcWeldingObj.eyeMeasureValue) ||
                    (item3.prop === "faceMeasureValue" &&
                      !arcWeldingObj.faceMeasureValue) ||
                    (item3.prop === "limbsMeasureValue" &&
                      !arcWeldingObj.limbsMeasureValue))
                ) {
                  console.log(item3.prop, arcWeldingObj[item3.prop]);

                  item2.children.splice(i3, 1);
                }
              }
            }
          }
        }
      }
    },
    handleEmptyColumn(formData, obj) {
      if (formData && formData.length) {
        formData.forEach((item) => {
          item.list.forEach((item2, i2) => {
            if (i2 !== 0) {
              Object.keys(obj).forEach((item3) => {
                if (item2[item3]) obj[item3] = true;
              });
            }
          });
        });
      }
    },
    getFields(fieldName) {
      console.log(this[fieldName]);
      return this[fieldName];
    },
    headFirst({ row, colunm, rowIndex, columnIndex }) {
      if (rowIndex === 0) {
        //这里为了是将第二行的表头隐藏，就形成了合并表头的效果
        return { display: "none" };
      }
    },
    // 合并表头
    headTwo({ row, colunm, rowIndex, columnIndex }) {
      if (rowIndex === 1) {
        //这里为了是将第二行的表头隐藏，就形成了合并表头的效果
        return { display: "none" };
      }
    },

    getOriginalRecord() {
      // 获取项目信息、采样计划单、原始记录单
      const id = this.labTestReportInfo.id;
      const project_num = this.labTestReportInfo.project_num;
      console.log("========= this.labTestReportInfo =========\n", this.labTestReportInfo);
      this.labTestReportInfo.orgName = this.labTestReportInfo.name;
      this.testingBasis = this.labTestReportInfo.testingBasis;
      this.comment = [
        {
          name: "备注",
          value: this.labTestReportInfo.comment || "",
          val: "comments",
        },
      ];
      this.data = JSON.parse(JSON.stringify(this.labTestReportInfo));
      this.EnterpriseID = this.labTestReportInfo.EnterpriseID;
      this.formData = this.labTestReportInfo;
      this.initTableFields();
      this.formData.baseInfo = [];
      this.formData.baseInfo.push(
        {
          name: "采样单位",
          value: this.labTestReportInfo.name,
          val: "serviceOrgName",
        },
        {
          name: "受检单位",
          value: this.labTestReportInfo.companyName,
          val: "campanyName",
        },
        {
          name: "检测类别",
          value: this.labTestReportInfo.serviceType || "",
          val: "serviceType",
        },
        {
          name: "样品数量",
          value: this.labTestReportInfo.sampleNum,
          val: "sampleNum",
        },
        {
          name: "样品接收日期",
          // value: moment(this.labTestReportInfo.sample_send_time).format("YYYY年MM月DD日"),
          value: this.labTestReportInfo.sample_send_time,
          val: "sampleReceivDate",
        },
        {
          name: "采样日期",
          value: this.labTestReportInfo.sampleDate,
          val: "sampleDate",
        },
        {
          name: "检测日期",
          // value: moment(this.labTestReportInfo.TTD).format("YYYY年MM月DD日"),
          value:
            this.labTestReportInfo.TTD === "无"
              ? this.labTestReportInfo.sampleDate
              : this.labTestReportInfo.TTD,
          val: "detectionDate",
        },
        {
          name: "检测环境温度",
          value: this.labTestReportInfo.temperature
            ? this.labTestReportInfo.temperature
            : "",
          val: "temperature",
        },
        {
          name: "检测环境湿度",
          value: this.labTestReportInfo.humidity ? this.labTestReportInfo.humidity : "",
          val: "humidity",
        },
        {
          name: "样品状态描述",
          value: this.labTestReportInfo.sampleStatusDescription,
          val: "sampleStatusDescription",
        },
        {
          name: "职业病危害因素名称",
          value: this.labTestReportInfo.harmFactorsName || "-",
          val: "checkProjects",
        },
        {
          name: "主要检测设备",
          value: this.labTestReportInfo.detectionDevice || "-",
          val: "detectionDevice",
        },
        {
          name: "检测依据",
          value:
            this.labTestReportInfo.testingBasis &&
            this.labTestReportInfo.testingBasis.length
              ? this.labTestReportInfo.testingBasis
                  .map((item) => item.replace(/[\r\n]/g, " "))
                  .join("\n")
              : "-",
          val: "standard",
        },
        {
          name: "备注",
          value: "此栏空白",
          val: "comment",
        }
      );
    },
    async showDownload(path, staticName) {
      let downloadElement = document.createElement("a");
      downloadElement.href = path;
      downloadElement.download = staticName;
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement); // 下载完成移除元素
      window.URL.revokeObjectURL(path); // 释放掉blob对象
    },
    async anewDown() {
      if (!this.formData.wordFileName) {
        this.$message.warning("请先到实验室分析中保存检测结果报告单");
        return;
      }
      await this.showDownload(
        this.formData.wordFileName.url,
        this.formData.wordFileName.name
      );
    },
    async download(stamp) {
      try {
        let url = stamp
          ? this.formData.wordFileName.signedUrl
          : this.formData.wordFileName.noStampUrl;
        console.log(url);
        if (url.indexOf("?") !== -1) {
          url += new Date().getTime();
        } else {
          url += `?${new Date().getTime()}`;
        }
        const response = await fetch(url); // 发起GET请求获取Blob数据
        if (!response.ok) {
          this.$message.warning(`无法下载文件，状态码：${response.status}`);
          return;
        }
        const blob = await response.blob(); // 将响应体转为Blob

        // 构建下载链接
        const downloadUrl = window.URL.createObjectURL(blob);
        const downloadElement = document.createElement("a");
        downloadElement.href = downloadUrl;

        downloadElement.download = `${this.labTestReportInfo.project_num}_${
          this.formData.companyName
        }_实验室检测结果报告单${url.includes(".pdf") ? ".pdf" : ".docx"}`; // 根据文件类型调整扩展名
        document.body.appendChild(downloadElement);
        downloadElement.click(); // 模拟点击下载
        document.body.removeChild(downloadElement); // 下载完成后移除元素
        window.URL.revokeObjectURL(downloadUrl); // 释放Object URL以避免内存泄漏
      } catch (error) {
        console.error("下载文件时发生错误:", error);
        this.$message.error("下载文件时发生错误，请重试。");
      }
    },
    async downloadNoPass(stamp) {
      try {
        let url = this.formData.noPassWordFileName.url;
        console.log(url);
        if (url.indexOf("?") !== -1) {
          url += new Date().getTime();
        } else {
          url += `?${new Date().getTime()}`;
        }
        const response = await fetch(url); // 发起GET请求获取Blob数据
        if (!response.ok) {
          this.$message.warning(`无法下载文件，状态码：${response.status}`);
          return;
        }
        const blob = await response.blob(); // 将响应体转为Blob

        // 构建下载链接
        const downloadUrl = window.URL.createObjectURL(blob);
        const downloadElement = document.createElement("a");
        downloadElement.href = downloadUrl;

        downloadElement.download = this.formData.noPassWordFileName.name;
        document.body.appendChild(downloadElement);
        downloadElement.click(); // 模拟点击下载
        document.body.removeChild(downloadElement); // 下载完成后移除元素
        window.URL.revokeObjectURL(downloadUrl); // 释放Object URL以避免内存泄漏
      } catch (error) {
        console.error("下载文件时发生错误:", error);
        this.$message.error("下载文件时发生错误，请重试。");
      }
    },
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  mounted() {
    console.log("========= init =========\n",);
  },
};
</script>

<style scoped lang="scss">
.report-card-content {
}
#submit {
  display: block;
  margin: 0 auto;
}
#leftTitle {
  position: absolute;
  left: 17px;
  top: 40px;
}
#rightTitle {
  display: inline-block;
  position: absolute;
  top: 40px;
  right: 19px;
  text-align: right;
}
#blank {
  position: absolute;
  padding-top: 10px;
  left: 0px;
}

#rightButton {
  position: absolute;
  right: 0;
  top: -45px;
}
#sign {
  position: relative;
  left: 0px;
  top: 100px;
  /* display: -webkit-inline-box; */
  display: inline-block;
}
#signet {
  display: inline-block;
  position: relative;
  left: 72%;
  top: 150px;
}
.table {
  font-size: 18px;
}
.stations {
  display: flex;
  align-items: center;
}
.test {
  /* font-size: 18px; */
  color: #606266;
  word-break: break-all;
  word-wrap: break-word;
  width: 670px;
  white-space: pre-wrap;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei,
    Arial, sans-serif;
}
.showSignet {
  position: absolute;
  right: 260px;
  top: -48px;
}
/deep/ .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #fff;
}
</style>
