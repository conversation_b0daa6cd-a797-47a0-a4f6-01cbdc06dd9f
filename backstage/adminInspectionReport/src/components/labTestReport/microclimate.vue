<template>
  <div class="microclimate">
    <template>
      <el-table
        :data="microclimate"
        border
        style="width: 877px"
        row-style="height:30px" 
        cell-style="padding:0"
        class="table"
        :span-method="microclimateMethod"
        >
        <el-table-column
          prop="address"
          label="检测地点"
          width="215"
          align= "center">
        </el-table-column>
        <el-table-column
          prop="projectName"
          label="检测项目"
          width="167"
          align= "center">
        </el-table-column>
        <el-table-column
          prop="waveBand"
          label="波段"
          width="90"
          align= "center">
        </el-table-column>
        <el-table-column
          prop="measuredValue"
          label="检测结果(μW/cm^2)"
          width="306"
          align= "center">
            <el-table-column 
              prop="head"
              label="头"
              width="101"
              align= "center">
            </el-table-column>
            <el-table-column 
              prop="chest"
              label="胸"
              width="101"
              align= "center">
            </el-table-column>
            <el-table-column 
              prop="belly"
              label="腹"
              width="101"
              align= "center">
            </el-table-column>
            <el-table-column 
              prop="leg"
              label="肢体"
              width="101"
              align= "center">
            </el-table-column>
        </el-table-column>
      </el-table>
    </template>
  </div>
</template>

<script>
export default {
  name: 'microclimate',
  data () {
    return {
      microclimateArr: [],
      microclimate: [{
          address: '公共工程及辅助生产设施机修电焊岗位',
          projectName: '电焊弧光',
          waveBand: 'C254',
          head: 'w33',
          chest: '<0.1',
          belly: '223',
          leg: '/'
        },{
          address: '公共工程及辅助生产设施机修电焊岗位',
          projectName: '电焊弧光',
          waveBand: 'B297',
          head: 'w33',
          chest: '<0.1',
          belly: '223',
          leg: '/'
        },{
          address: '公共工程及辅助生产设施机修电焊岗位',
          projectName: '电焊弧光',
          waveBand: 'C254',
          head: 'w33',
          chest: '<0.1',
          belly: '223',
          leg: '/'
        },],
    }
  },
  methods:{
      getmicroclimateArr(data) {　
        for (var i = 0; i < data.length; i++) {
          console.log(data[i]);
          if (i === 0) {
            this.microclimateArr.push(1);
            this.pos = 0
          } else {
            // 判断当前元素与上一个元素是否相同
          if (data[i].address === data[i - 1].address) {
              this.microclimateArr[this.pos] += 1;
              this.microclimateArr.push(0);
            } else {
              this.microclimateArr.push(1);
              this.pos = i;
            }
          }
          // console.log(this.microclimate, '11111111111111111111111')q
      }
    },
    microclimateMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        const _row = this.microclimateArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        console.log(`rowspan:${_row} colspan:${_col}`)
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
              rowspan: _row,
              colspan: _col
        }
      }
    },
  },
   mounted() {
    this.getfourParamsArr(this.microclimate);
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.table{
  font-size: 18px;
}
</style>
