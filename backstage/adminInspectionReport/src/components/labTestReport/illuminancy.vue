<template>
  <div class="illuminancy">
      <lb-table :column="illuminancyColumn" :merge="['workspaces','workstation']" border :data="illuminancy" v-if="illuminancy.length>0"></lb-table>
  </div>
</template>

<script>
import lbTable from "@/components/lb-table/lb-table";
export default {
  name: 'illuminancy',
  props: {
    illuminancy: Object,
  },
  data () {
    return {
        illuminancyArr: [],
        illuminancyColumn: [
        {
         prop:"workspaces",
         label:"车间"
        },
        {
          prop: 'workstation',
          label: '岗位'
        },
        {
          prop: 'projectName',
          label: '检测项目'
        },
        {
          prop: 'measureAvg',
          label: '平均值（lx）',
          width: '150px'
        },],
      illuminancy: [],
    }
  },

  methods:{
  },
  components: {
    lbTable,
  },
   created() {
  }
}
</script>

<style scoped>
.table{
  font-size: 18px;
}
</style>
