<template>
  <div class="twoParams">
    <template>
      <el-table
        :data="twoParams"
        border
        style="width: 877px"
        row-style="height:30px" 
        cell-style="padding:0"
        class="table"
        :span-method="twoParamsMethod"
        >
        <el-table-column
          prop="address"
          label="检测地点"
          width="235"
          align= "center">
        </el-table-column>
        <el-table-column
          prop="projectName"
          label="检测项目"
          width="231"
          align= "center">
        </el-table-column>
        <el-table-column
          prop="oneparam"
          label="参数一"
          width="205"
          align= "center">
        </el-table-column>
        <el-table-column
          prop="twoparam"
          label="参数二"
          width="205"
          align= "center">
        </el-table-column>
      </el-table>
    </template>
  </div>
</template>

<script>
export default {
  name: 'twoParams',
  data () {
    return {
      twoParamsArr: [],
      twoParams: [{ // 工作场所照度
            address: '消洗间',
            projectName: '照度',
            oneparam: '406',
            twoparam: '12'
          },{
            address: '消洗间',
            projectName: '照度',
            oneparam: '436',
            twoparam: '165'
          },{
            address: '消洗间',
            projectName: '照度',
            oneparam: '506',
            twoparam: '122'
          },{
            address: '十号间',
            projectName: '照度',
            oneparam: '243',
            twoparam: '56'
          },{
            address: '十号间',
            projectName: '照度',
            oneparam: '76',
            twoparam: '808'
          },{
            address: '十号间',
            projectName: '照度',
            oneparam: '406',
            twoparam: '12'
          },{
            address: '二号间',
            projectName: '照度',
            oneparam: '406',
            twoparam: '12'
          },{
            address: '二号间',
            projectName: '照度',
            oneparam: '406',
            twoparam: '12'
          },{
            address: '二号间',
            projectName: '照度',
            oneparam: '406',
            twoparam: '12'
          }],
    }
  },
  methods:{
      gettwoParamsArr(data) {　
        for (var i = 0; i < data.length; i++) {
          // console.log(data[i]);
          if (i === 0) {
            this.twoParamsArr.push(1);
            this.pos = 0
          } else {
            // 判断当前元素与上一个元素是否相同
          if (data[i].address === data[i - 1].address) {
              this.twoParamsArr[this.pos] += 1;
              this.twoParamsArr.push(0);
            } else {
              this.twoParamsArr.push(1);
              this.pos = i;
            }
          }
          // console.log(this.twoParams, '11111111111111111111111')
      }
    },
    twoParamsMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        const _row = this.twoParamsArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        // console.log(`rowspan:${_row} colspan:${_col}`)
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
              rowspan: _row,
              colspan: _col
        }
      }
    },
  },
   mounted() {
    this.gettwoParamsArr(this.twoParams);
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.table{
  font-size: 18px;
}
</style>
