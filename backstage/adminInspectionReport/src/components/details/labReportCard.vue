<template>
  <el-card class="box-card" shadow="always">
    <div slot="header" class="clearfix header">
      <span style="font-size: 1.2em; font-weight: bold; color: #409eff">{{
        datas.name || ""
      }}</span>
      <span class="el-icon-error close2" @click="closeDetail"></span>
    </div>
    <labTestReport :labTestReportInfo="datas" />
  </el-card>
</template>

<script>
import { getQualifies } from "@/api";
import labTestReport from "@/components/labTestReport/index.vue";
export default {
  name: "labTestReportCard",
  props: {
    datas: Object,
  },
  components: {
    labTestReport,
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    closeDetail() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.box-card {
  .demo-form-inline .el-form-item {
    width: 50%;
    display: inline-block;
  }
  .el-row:hover {
    background-color: #f5f5f5;
  }

  .close2 {
    float: right !important;
    padding: 3px 0;
    font-size: 1.3em;
    cursor: pointer;
    opacity: 0.7;
    :hover {
      opacity: 0.9;
    }
  }
}

.el-row {
  padding: 10px 0;
}
.text {
  font-size: 15px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}

.el-upload-list {
  padding-bottom: 30px;
}
.el-upload-list li .el-image {
  vertical-align: middle;
  margin-right: 20px;
}
.el-upload-list .desc {
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 130px);
}
.el-upload-list .desc .title2 {
  font-weight: bold;
  font-size: 15px;
  text-decoration: none;
  color: #606266;
}
.el-upload-list .desc p {
  margin: 0;
  font-size: 13px;
}
.el-image-viewer__mask {
  opacity: 0.8;
}
.el-image-viewer__btn.el-image-viewer__close {
  color: #ddd;
}

[v-cloak] {
  display: none;
}

.el-upload-list .el-upload-list__item {
  overflow: hidden;
  /* z-index: 0; */
  background-color: #fff;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-top: 10px;
  padding: 10px 10px 10px 10px;
}
.el-upload-list .el-upload-list__item-status-label {
  position: absolute;
  right: -17px;
  top: -7px;
  width: 46px;
  height: 26px;
  background: #13ce66;
  text-align: center;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-box-shadow: 0 1px 1px #ccc;
  box-shadow: 0 1px 1px #ccc;
}
.el-upload-list .el-upload-list__item-status-label i {
  font-size: 12px;
  margin-top: 12px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.lineOfBusiness {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /* background:black; */
}
</style>
