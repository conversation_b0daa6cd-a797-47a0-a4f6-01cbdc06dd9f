<template>
  <div class="table-expand-item">
    <div v-if="loading" class="loading-container">
      <i class="el-icon-loading"></i>
      <div class="loading-text">正在加载中...</div>
    </div>
    <div v-else class="table-expand-item_content">
      <el-row :gutter="20" class="table-expand-row">
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">受检单位</span>
            <span class="table-expand_value">{{ row.EnterpriseName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">项目类别</span>
            <span class="table-expand_value">{{ serviceTypeStatus(row) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">样品数量</span>
            <span class="table-expand_value">{{ projectReport.sampleNum }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="table-expand-row">
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">接收日期</span>
            <span class="table-expand_value">{{ projectReport.sample_send_time }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">采样日期</span>
            <span class="table-expand_value">{{ projectReport.sampleDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">检测日期</span>
            <span class="table-expand_value">{{ projectReport.detectionDate }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="table-expand-row">
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">检测环境温度</span>
            <span class="table-expand_value">{{ projectReport.temperature }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">检测环境湿度</span>
            <span class="table-expand_value">{{ projectReport.humidity }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content">
            <span class="table-expand_lable">样品状态</span>
            <span class="table-expand_value">{{
              projectReport.sampleStatusDescription
            }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="table-expand-row">
        <el-col :span="24">
          <div class="grid-content">
            <span class="table-expand_lable">检测设备</span>
            <span class="table-expand_value">{{ projectReport.detectionDevice }} </span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="table-expand-row">
        <el-col :span="24">
          <div class="grid-content">
            <span class="table-expand_lable">检测依据</span>
            <span class="table-expand_value">
              <div v-for="(item, index) in projectReport.standard" :key="index">
                {{ item }}
              </div>
            </span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="table-expand-row">
        <el-col :span="24">
          <div class="grid-content">
            <span class="table-expand_lable">职业病危害因素</span>
            <span class="table-expand_value">
              <div class="table-expand_harmFactor">
                <span class="table-expand_harmFactor_item">
                  {{ projectReport.checkProjects }}</span
                >
                <span class="table-expand_harmFactor_operation">
                  <span class="harmFactor_operation_btn" @click="viewLabTestReport"
                    >查看</span
                  >
                </span>
              </div>
            </span>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- <LabTestReport
      :labTestReport="labTestReportDialog"
      :labTestReportInfo="labTestReportInfo"
      @close="labTestReportDialog = false"
    ></LabTestReport> -->
  </div>
</template>

<script>
import { getReportDetails } from "@/api";
import lbTable from "@/components/lb-table/lb-table";
import Details from "@/components/details/index.vue";
export default {
  name: "tableExpandItem",
  props: {
    row: {
      type: Object,
      default: () => {},
    },
    value: Object,
  },
  components: {
    lbTable,
    Details,
  },
  watch: {
    // 监听 tableRowDetail 的变化并通过 $emit 更新父组件数据
    tableRowDetail: {
      handler(newValue) {
        this.$emit("input", newValue);
      },
      deep: true, // 深度监听对象的变化
    },
  },
  data() {
    return {
      loading: true,
      tableRowDetail: { ...this.value },
      labTestReportDialog: false,
      labTestReportInfo: {},
      projectReport: {
        serviceOrgName: "",
        campanyName: "",
        serviceType: "",
        sampleNum: "",
        sample_send_time: "",
        sampleDate: "",
        detectionDate: "",
        temperature: "",
        humidity: "",
        sampleStatusDescription: "",
        checkProjects: "",
        detectionDevice: "",
        standard: "",
        comment: "",
      },
      debounceTimer: null, // 用于存储定时器ID
    };
  },
  async mounted() {
    await this.getReportInfo();
  },
  computed: {},
  methods: {
    viewLabTestReport() {
      this.tableRowDetail.show = true;
      this.tableRowDetail.type = "labTestReport";
      this.tableRowDetail.data = this.labTestReportInfo;
    },
    async getReportInfo() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer); // 清除上次定时器
      }
      this.debounceTimer = setTimeout(async () => {
        this.loading = true;
        const res = await getReportDetails({
          id: this.row._id,
          project_num: this.row.projectSN,
        });

        if (res.status === 200) {
          const resData = res.data;
          this.labTestReportInfo = {
            ...resData,
            id: this.row._id,
            project_num: this.row.projectSN,
          };

          this.projectReport.serviceOrgName = resData.name;
          this.projectReport.campanyName = resData.companyName;
          this.projectReport.serviceType = resData.serviceType;
          this.projectReport.sampleNum = resData.sampleNum;
          this.projectReport.sample_send_time = resData.sample_send_time;
          this.projectReport.sampleDate = resData.sampleDate;
          this.projectReport.detectionDate =
            resData.TTD === "无" ? resData.sampleDate : resData.TTD;
          this.projectReport.temperature = resData.temperature || "-";
          this.projectReport.humidity = resData.humidity || "-";
          this.projectReport.sampleStatusDescription =
            resData.sampleStatusDescription || "-";
          this.projectReport.checkProjects = resData.harmFactorsName || "-";
          this.projectReport.detectionDevice = resData.detectionDevice || "-";
          this.projectReport.standard =
            resData.testingBasis && resData.testingBasis.length
              ? resData.testingBasis.map((item) => item.replace(/[\r\n]/g, " "))
              : [];
          this.projectReport.comment = resData.comment || "-";
        } else {
          this.$message.error(res.message);
        }

        this.loading = false;
      }, 500); // 延迟 500ms 执行
    },
    serviceTypeStatus(item) {
      if (item.serviceType === "职业病危害因素日常监测") {
        return "职卫监测";
      }
      if (item.serviceType === "职业病危害预评价") {
        return "职卫预评";
      }
      if (item.serviceType === "职业病危害现状评价") {
        return "职卫现状";
      }
      if (item.serviceType === "职业病危害因素检测") {
        return "职卫检评";
      }
      if (item.serviceType === "职业病危害因素监督检测") {
        return "职卫监督";
      }
      if (item.serviceType === "职业病防护设备设施与防护用品效果评价") {
        return "职业病防护设备设施与防护用品效果评价";
      }
      if (item.serviceType === "其他") {
        return "其他服务";
      }
      if (item.serviceType === "职卫专篇") {
        return "职卫专篇";
      }
      if (item.serviceType === "职卫档案制作") {
        return "职卫档案制作";
      }
      if (item.serviceType === "职业病危害控制效果评价") {
        return "职卫控评";
      }
      return item.serviceType;
    },
  },
};
</script>

<style lang="scss" scoped>
.table-expand-item {
  .loading-container,
  .table-expand-item_content {
    min-height: 100px;
  }
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    i {
      font-size: 24px;
      color: #409eff;
    }
    .loading-text {
      font-size: 14px;
      color: #409eff;
      margin-top: 8px;
    }
  }
  .table-expand-row {
    margin-bottom: 10px;
    font-size: 12px;
  }
  .grid-content {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;

    .table-expand_lable {
      color: #333;
      width: 100px;
      font-weight: 600;
      padding-top: 1px;
    }
    .table-expand_value {
      color: #999;
      flex: 1;
      width: 100%;
      .table-expand_harmFactor {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        .table-expand_harmFactor_item {
          max-width: 90%;
        }
        .table-expand_harmFactor_operation {
          padding-left: 10px;
          .harmFactor_operation_btn {
            font-size: 14px;
            color: #409eff;
            cursor: pointer;
            user-select: none;
            padding: 4px;

            &:hover {
              color: #66b1ff;
            }

            &:active {
              color: #409eff;
            }
          }
        }
      }
    }
  }
}
</style>
