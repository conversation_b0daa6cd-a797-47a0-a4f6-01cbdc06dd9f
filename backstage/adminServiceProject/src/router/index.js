import Vue from 'vue';
import Router from 'vue-router';
import settings from '@root/publicMethods/settings';
import ServiceOrg from '@/views/serviceOrg';
import yearAssessInfo from '@/components/yearAssessInfo.vue';
import yearRportInfo from '@/components/yearRportInfo.vue';
import givebackRecord from '@/views/serviceOrg/givebackRecord';

Vue.use(Router);

const createRouter = () => new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  scrollBehavior: to => ({
    y: to.hash || 0,
  }),
  routes: [{
    path: settings.admin_base_path + '/adminServiceProject',
    name: 'serviceOrg',
    component: ServiceOrg,
  }, {
    path: settings.admin_base_path + '/adminServiceProject/yearAssessInfo',
    name: 'yearAssessInfo',
    component: yearAssessInfo,
  }, {
    path: settings.admin_base_path + '/adminServiceProject/yearRportInfo',
    name: 'yearRportInfo',
    component: yearRportInfo,
  }, {
    path: settings.admin_base_path + '/adminServiceProject/givebackRecord',
    name: 'givebackRecord',
    component: givebackRecord,
  }],
});

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
