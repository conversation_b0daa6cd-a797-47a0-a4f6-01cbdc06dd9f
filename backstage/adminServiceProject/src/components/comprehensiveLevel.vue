<template>
  <div class="comprehensiveLevel">
    <div v-if="this.isReported">
      <el-row class="toolBar">
        <el-row type="flex" justify="center" style="margin: ">
          <el-col :span="12">
            <el-button-group>
              <el-button
                class="toolItem"
                type="primary"
                size="mini"
                v-if="this.isReported === 1"
                :href="assessmentReport.tableFilePath"
                icon="el-icon-download"
                round
                @click="downLoadBtnClick('table')"
              >
                自查评分表
              </el-button>
              <el-button
                class="toolItem"
                type="primary"
                size="mini"
                v-if="this.isReported === 1"
                icon="el-icon-download"
                round
                @click="downLoadBtnClick('report')"
              >
                评估报告
              </el-button>
              <el-button
                class="toolItem"
                type="primary"
                size="mini"
                v-if="this.isReported === 1"
                icon="el-icon-download"
                round
                @click="downLoadBtnClick('expose')"
              >
                风险评估表
              </el-button>
              <el-button
                class="toolItem"
                type="primary"
                size="mini"
                round
                @click="repulse(1)"
                v-if="this.isReported === 1 && this.status === 0"
                icon="el-icon-check"
                >通过</el-button
              >
              <el-tooltip class="item" effect="dark" content="取消通过" placement="top" v-if="this.isReported === 1 && this.status === 1">
                <el-button
                  class="toolItem"
                  type="primary"
                  size="mini"
                  round
                  @click="repulse(0)"
                  >已通过</el-button
                >
              </el-tooltip>
              <el-button
                class="toolItem"
                type="primary"
                size="mini"
                round
                @click="repulse(2)"
                icon="el-icon-close"
                v-if="this.isReported === 1 && this.status === 0"
                >打回重新提交</el-button
              >
            </el-button-group>
          </el-col>
        </el-row>
      </el-row>
      <el-row class="reportBody" type="flex" justify="center">
        <el-card class="box-card" shadow="hover" style="margin-top: 30px;">
          <div class="reportCardBody">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="title">企业信息</div>
              </el-col>
              <el-col :span="12">
                <span class="labelText">单位名称</span>
                {{ assessmentReport.cname }}
              </el-col>
              <el-col :span="12">
                <span class="labelText">统一社会信用代码（或统一社会信用代码）</span>
                {{ assessmentReport.code }}
              </el-col>
            </el-row>
            <p />
            <el-row :gutter="20">
              <el-col :span="12">
                <span class="labelText">单位注册地址</span>
                {{assessmentReport.districtRegAdd}}
              </el-col>
              <el-col :span="12">
                <span class="labelText">工作场所地址</span>
                <p v-for="(item, i) in assessmentReport.workAddress" :key="i">
                  {{ item }}
                </p>
              </el-col>
            </el-row>
            <p />
            <el-row :gutter="20">
              <el-col :span="12">
                <span class="labelText">单位规模</span>
                {{ assessmentReport.companyScale || "" }}
              </el-col>
              <el-col :span="12">
                <span class="labelText">行业分类</span>
                <p v-for="(item, i) in assessmentReport.industryCategory" :key="i">
                  {{ item }}
                </p>
              </el-col>
            </el-row>
            <p />
            <el-row :gutter="20">
              <el-col :span="12">
                <span class="labelText">上属单位</span>
                {{ assessmentReport.parentCompany }}
              </el-col>
              <el-col :span="12">
                <span class="labelText">注册类型</span>
                {{ assessmentReport.regType || "" }}
              </el-col>
            </el-row>
            <p />
            <el-row :gutter="20">
              <el-col :span="12">
                <span class="labelText">法定代表人</span>

                {{ assessmentReport.corp }}
              </el-col>
              <el-col :span="12">
                <span class="labelText">联系电话</span>
                {{ assessmentReport.corpPhoneNumber }}
              </el-col>
            </el-row>

            <!-- 分隔线 -->
            <el-row>
              <el-col :span="24">
                <el-progress
                  :text-inside="true"
                  :stroke-width="0"
                  :percentage="0"
                ></el-progress>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <div class="title">职业卫生管理</div>
              </el-col>
              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 24, offset: 0 }"
                :md="{ span: 8, offset: 0 }"
                :lg="{ span: 6, offset: 0 }"
                :xl="{ span: 6, offset: 0 }"
              >
                <span class="labelText">职业卫生管理机构</span>
                {{ assessmentReport.hasRole ? "有" : "无" }}
              </el-col>
              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 24, offset: 0 }"
                :md="{ span: 8, offset: 0 }"
                :lg="{ span: 6, offset: 0 }"
                :xl="{ span: 6, offset: 0 }"
              >
                <span class="labelText">专职</span>
                {{ assessmentReport.majorManegerCount }}
                <!-- {{ assessmentReport.managerCount.zhuanzhi }} 人 -->
              </el-col>
              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 24, offset: 0 }"
                :md="{ span: 8, offset: 0 }"
                :lg="{ span: 6, offset: 0 }"
                :xl="{ span: 6, offset: 0 }"
              >
                <span class="labelText">兼职</span>
                {{ assessmentReport.partTimeManegerCount }}
                <!-- {{ assessmentReport.managerCount.jianzhi }} 人 -->
              </el-col>
            </el-row>

            <!-- 分隔线 -->
            <el-row>
              <el-col :span="24">
                <el-progress
                  :text-inside="true"
                  :stroke-width="0"
                  :percentage="0"
                ></el-progress>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <div class="title">职业健康检查人数（含外委、劳务派遣）</div>
                <!-- <div class="formSubTitle">职业健康检查人数</div> -->
              </el-col>

              <el-col
                :xs="{ span: 12, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 4, offset: 0 }"
                :lg="{ span: 4, offset: 0 }"
                :xl="{ span: 4, offset: 0 }"
              >
                <span class="labelText">上岗人数（应检）</span>
                <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                  {{ assessmentReport.healthCheckCount.shangGangInspectionRequired }}
                </span>
              </el-col>
              <el-col
                :xs="{ span: 12, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 4, offset: 0 }"
                :lg="{ span: 4, offset: 0 }"
                :xl="{ span: 4, offset: 0 }"
              >
                <span class="labelText">上岗人数（实检）</span>
                <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                  {{ assessmentReport.healthCheckCount.shangGang }}
                </span>
              </el-col>

              <el-col
                :xs="{ span: 12, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 4, offset: 0 }"
                :lg="{ span: 4, offset: 0 }"
                :xl="{ span: 4, offset: 0 }"
              >
                <span class="labelText">在岗人数（应检）</span>
                <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                  {{ assessmentReport.healthCheckCount.zaiGangInspectionRequired }}
                </span>
              </el-col>
              <el-col
                :xs="{ span: 12, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 4, offset: 0 }"
                :lg="{ span: 4, offset: 0 }"
                :xl="{ span: 4, offset: 0 }"
              >
                <span class="labelText">在岗人数（实检）</span>
                <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                  {{ assessmentReport.healthCheckCount.zaiGang }}
                </span>
              </el-col>

              <el-col
                :xs="{ span: 12, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 4, offset: 0 }"
                :lg="{ span: 4, offset: 0 }"
                :xl="{ span: 4, offset: 0 }"
              >
                <span class="labelText">离岗人数（应检）</span>
                <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                  {{ assessmentReport.healthCheckCount.liGangInspectionRequired }}
                </span>
              </el-col>
              <el-col
                :xs="{ span: 12, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 4, offset: 0 }"
                :lg="{ span: 4, offset: 0 }"
                :xl="{ span: 4, offset: 0 }"
              >
                <span class="labelText">离岗人数（实检）</span>
                <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                  {{ assessmentReport.healthCheckCount.liGang }}
                </span>
              </el-col>
            </el-row>

            <!-- 分隔线 -->
            <el-row>
              <el-col :span="24">
                <el-progress
                  :text-inside="true"
                  :stroke-width="0"
                  :percentage="0"
                ></el-progress>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <div class="title">职业卫生概况</div>
                <!-- <div class="formSubTitle">职业卫生概况</div> -->
              </el-col>

              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 6, offset: 0 }"
                :lg="{ span: 6, offset: 0 }"
                :xl="{ span: 6, offset: 0 }"
              >
                <span class="labelText">劳动者总人数（含外委、劳务派遣）</span>
                {{ assessmentReport.allEmployeesCount }}
              </el-col>

              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 8, offset: 0 }"
                :lg="{ span: 8, offset: 0 }"
                :xl="{ span: 8, offset: 0 }"
              >
                <span class="labelText">接触职业病危害总人数（含外委、劳务派遣）</span>
                {{ assessmentReport.contactHazardCount }}
              </el-col>

              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 5, offset: 0 }"
                :lg="{ span: 5, offset: 0 }"
                :xl="{ span: 5, offset: 0 }"
              >
                <span class="labelText">职业病累计人数 - 目前在岗</span>
                {{ assessmentReport.ODzaigangCount }}
              </el-col>

              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 12, offset: 0 }"
                :md="{ span: 5, offset: 0 }"
                :lg="{ span: 5, offset: 0 }"
                :xl="{ span: 5, offset: 0 }"
              >
                <span class="labelText">职业病累计人数 - 历年累计</span>
                {{ assessmentReport.cumulativeTotalCount }}
              </el-col>
            </el-row>

            <!-- 分隔线 -->
            <el-row>
              <el-col :span="24">
                <el-progress
                  :text-inside="true"
                  :stroke-width="0"
                  :percentage="0"
                ></el-progress>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <div class="title">
                  职业病危害因素接触情况（按类填写具体危害因素）
                  <span class="smallText">
                    危害因素分类：粉尘、化学物质、物理因素、生物因素、放射性物质
                  </span>
                </div>
              </el-col>
              <el-col :span="24">
                <el-table border :data="assessmentReport.harmFactorTouchInfo">
                  <el-table-column
                    align="center"
                    v-for="(item, i) in harmFactorsFields"
                    :key="i"
                    :label="item.label"
                    :prop="item.value"
                  >
                    <template slot-scope="scope">
                      <div v-if="item.type === 'array'">
                        {{ scope.row[item.value] && scope.row[item.value][0] && scope.row[item.value].join("、") || '-' }}
                      </div>
                      <div v-else>
                        {{ scope.row[item.value] || "-" }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>

            <!-- 分隔线 -->
            <el-row>
              <el-col :span="24">
                <el-progress
                  :text-inside="true"
                  :stroke-width="0"
                  :percentage="0"
                ></el-progress>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <div class="formSubTitle">
                  <div class="title">职业病危害接触水平</div>
                  <span class="smallText">
                    危害接触水平分为：一般职业病危害因素；严重职业病危害因素；
                  </span>
                </div>
              </el-col>

              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 24, offset: 0 }"
                :md="{ span: 12, offset: 0 }"
                :lg="{ span: 12, offset: 0 }"
                :xl="{ span: 12, offset: 0 }"
              >
                <el-card
                  class="harmCardBody"
                  :body-style="{ padding: '0px' }"
                  shadow="hover"
                >
                  <div class="harmCardContent">
                    <div class="harmTitleBox">
                      <span class="">一般职业病危害因素 </span>
                    </div>

                    <div class="harmCountBox">
                      <el-col :span="12" class="harmCount">
                        <span class="labelText">超标人数</span>
                        <span style="color: #f56c6c">{{
                          assessmentReport.noSeriousHarmExceedEmployees || 0
                        }}</span>
                      </el-col>
                      <el-col :span="12" class="harmCount">
                        <span class="labelText">不超标人数</span>
                        <span>{{
                          assessmentReport.noSeriousHarmNoExceedEmployees || 0
                        }}</span>
                      </el-col>
                    </div>
                  </div>
                </el-card>
              </el-col>

              <el-col
                :xs="{ span: 24, offset: 0 }"
                :sm="{ span: 24, offset: 0 }"
                :md="{ span: 12, offset: 0 }"
                :lg="{ span: 12, offset: 0 }"
                :xl="{ span: 12, offset: 0 }"
              >
                <el-card
                  class="harmCardBody"
                  :body-style="{ padding: '0px' }"
                  shadow="hover"
                >
                  <div class="harmCardContent">
                    <div class="harmTitleBox">
                      <span class="">严重职业病危害因素 </span>
                    </div>

                    <div class="harmCountBox">
                      <el-col :span="12" class="harmCount">
                        <span class="labelText">超标人数</span>
                        <span style="color: #f56c6c">{{
                          assessmentReport.seriousHarmExceedEmployees || 0
                        }}</span>
                      </el-col>
                      <el-col :span="12" class="harmCount">
                        <span class="labelText">不超标人数</span>
                        <span>{{
                          assessmentReport.seriousHarmNoExceedEmployees || 0
                        }}</span>
                      </el-col>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <!-- 分隔线 -->
            <el-row>
              <el-col :span="24">
                <el-progress
                  :text-inside="true"
                  :stroke-width="0"
                  :percentage="0"
                ></el-progress>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <div class="title">职业卫生自查风险评估</div>
              </el-col>

              <el-col :span="8">
                <span class="labelText">防治责任自查等级
                  <el-button type="primary" size="mini" plain @click="handlePreview('table')">查看详情</el-button>
                </span>
                  <span style="color: #409eff; margin-right:20px;font-size:22px;">{{ selfCheckLevel[assessmentReport.assessManageLevel] }}</span>
              </el-col>

              <el-col :span="8">
                <span class="labelText">暴露风险等级
                  <el-button type="primary" size="mini" plain @click="handlePreview('report')">查看详情</el-button>
                </span>
                <span style="color: #409eff; margin-right:20px;font-size:22px;">{{ exposeLevel[assessmentReport.assessExposeLevel] }}</span>
              </el-col>

              <el-col :span="8">
                <span class="labelText" style="height:28px;padding-top:5px">综合风险等级</span>
                <span style="color: #409eff; margin-right:20px;font-size:22px;">{{ result[assessmentReport.assessmentResult] }}</span>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-row>

      <!-- 这里表单开始，在申报的时候填写。 -->
      <el-form ref="form">
        <el-row>
          <el-col :span="24">
            <el-card
              class="box-card"
              shadow="hover"
              style="margin-top: 20px; margin-bottom: 30px"
            >
              <div slot="header" class="clearfix">
                <div class="title">承诺声明</div>
              </div>
              <el-row :gutter="20">
                <el-col
                  :xs="{ span: 24, offset: 0 }"
                  :sm="{ span: 24, offset: 0 }"
                  :md="{ span: 8, offset: 0 }"
                  :lg="{ span: 8, offset: 0 }"
                  :xl="{ span: 8, offset: 0 }"
                >
                  <el-form-item label="评估人">
                    {{assessmentReport.fillerName}}
                  </el-form-item>
                </el-col>
                <el-col
                  :xs="{ span: 24, offset: 0 }"
                  :sm="{ span: 24, offset: 0 }"
                  :md="{ span: 8, offset: 0 }"
                  :lg="{ span: 8, offset: 0 }"
                  :xl="{ span: 8, offset: 0 }"
                >
                  <el-form-item label="评估日期">
                    {{doMomentmonthD(assessmentReportfillDate)}}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col
                  :xs="{ span: 24, offset: 0 }"
                  :sm="{ span: 24, offset: 0 }"
                  :md="{ span: 24, offset: 0 }"
                  :lg="{ span: 24, offset: 0 }"
                  :xl="{ span: 24, offset: 0 }"
                >
                  <el-form-item label="发现问题:">
                    {{assessmentReport.problem ? assessmentReport.problem : '暂无'}}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div v-else style="font-size:20px;margin-top:30px;">
      未上报风险评估
    </div>
    <!-- 责任自查与风险评估报告打回的弹框 -->
    <el-dialog title="打回责任自查与风险评估报告" :visible.sync="dialogVisible">
      <el-form
        :model="messageForm2"
        ref="messageForm2"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="不通过原因" prop="message">
          <el-input type="textarea" v-model="repulseReason"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="goRepulse('messageForm2')"
            >立即发送</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { getAllAssessmentInfo, getsuperUserInfo, gosendMessages, repulse, message_send } from "@/api/adminorg";
import moment from 'moment';
export default {
  props: {
    EnterpriseID: String,
    year: String,
    jobHealthId: String,
  },
  data(){
    return{
      messageForm2: {
      },
      repulseReason: "", //不通过原因
      dialogVisible: false,
      superUserInfo: {},
      activeName: "化学有害因素",
      harmFactorsFields: [
        { label: "危害因素类别", value: "category" },
        { label: "危害因素", value: "harmFactors", type: "array" },
        {
          label: "接触人数",
          value: "touchEmployeesCount",
        },
      ],
      harmFactorsData: [],
      selfCheckLevel: ["A级", "B级", "C级"],
      exposeLevel: ["Ⅰ", "Ⅱ", "Ⅲ", "-"],
      result: [  "丙类","乙类", "甲类","-"],
      assessmentReport:{},
      isReported:false,
      status: 0,
    }
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created(){
    this.getAllAssessmentInfo();
  },
  methods:{
    // 打回责任自查与风险评估报告
    repulse(status) {
      this.status = status;
      if(status === 2){
        this.dialogVisible = true;
      } else {
        this.goRepulse('messageForm2');
      }
    },
    goRepulse(formName) {
      this.dialogVisible = false;
      // 去改变上报的状态
      const obj = {
        EnterpriseID: this.EnterpriseID,
        jobHealthId: this.jobHealthId,
        status: this.status,
        isReported: 1,
      }
      if(this.status === 2) {
        obj.isReported = 0;
        obj.repulseReason = this.repulseReason;
      }
      repulse(obj).then(res => {
        this.getAllAssessmentInfo();
      });
    },
    getAllAssessmentInfo(){
      let obj = {
        EnterpriseID: this.EnterpriseID,
        // year: new Date('2022'),
        year: this.year,
        jobHealthId: this.jobHealthId
      }
      // 通过当年年份和企业id获取该企业今年的责任自查与风险评估信息
      getAllAssessmentInfo(obj).then(async res=>{
        if(res.status === 200 && res.data._id){
          this.assessmentReport = res.data;
          this.isReported = this.assessmentReport.isReported ? this.assessmentReport.isReported : false;
          this.status = this.assessmentReport.status ? this.assessmentReport.status : 0;
          this.assessmentReport.workAddress = this.assessmentReport.workAddress.map(
            (item) => {
              if (item.districts) {
                item = item.districts.join("") + item.address;
              }
              return item;
            }
          );
          switch (this.assessmentReport.assessManageLevel) {
            case "A":
              this.assessmentReport.assessManageLevel = 0;
              break;
            case "B":
              this.assessmentReport.assessManageLevel = 1;
              break;
            case "C":
              this.assessmentReport.assessManageLevel = 2;
              break;
          }
          this.assessmentReport.fillDate = Date.now();
          // console.log(this.isReported, 'rrrrrrrrrrrrrrrrrrrrrrrrrr');
        }
      })
    },
    downLoadBtnClick(type) {
      let path = "";
      let fileName = "";
      if (type === "report") {
        path = this.assessmentReport.reportFile;
        fileName = "用人单位落实职业病防治责任自查及风险评估报告.docx";
      } else if (type === "table") {
        path = this.assessmentReport.tableFile;
        fileName = "用人单位落实职业病防治责任自查表.docx";
      } else if (type === "expose") {
        path = this.assessmentReport.exposeFile;
        fileName = "用人单位职业病危害暴露情况调查和风险评估表.docx";
      }
      let downloadElement = document.createElement("a");
      downloadElement.href = path;
      downloadElement.download = `${this.year}年度${this.assessmentReport.cname}${fileName}`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement); // 下载完成移除元素
      window.URL.revokeObjectURL(path); // 释放掉blob对象
    },
    //预览,跳转页面，新开标签页
    handlePreview(type) {
      // console.log(this.year, this.EnterpriseID, 'ddddddddddddddddd');
      let routeUrl = {};
      if(type === 'table') {
        // window.open(`/admin/adminServiceProject/yearAssessInfo?year=${this.year}?EnterpriseID=${this.EnterpriseID}`, "_blank");
        routeUrl = this.$router.resolve({
          path: "/admin/adminServiceProject/yearAssessInfo",
          query: {year:this.year,EnterpriseID:this.EnterpriseID,jobHealthId:this.jobHealthId}
        });
      } else if(type === 'report') {
        routeUrl = this.$router.resolve({
          path: "/admin/adminServiceProject/yearRportInfo",
          query: {year:this.year,EnterpriseID:this.EnterpriseID,jobHealthId:this.jobHealthId}
        });
      }
      window.open(routeUrl.href, '_blank');
    },
  },
}
</script>

<style scoped>
  .assessmentSortStyle{
    height: 38px;
  }
  .pageTitle {
    font-size: 20px;
    line-height: 32px;
  }
  .reportCardBody {
    margin-top: 0;
  }
  .smallText {
    font-size: 12px;
    color: #c0c4cc;
  }
  .harmTitleBox {
    text-align: center;
  }
  .harmCardContent {
    position: relative;
    padding: 14px;
    height: 125px;
    overflow: hidden;
  }
  .harmCountBox {
    position: absolute;
    left: 0;
    bottom: 15px;
    width: 100%;
    font-size: 20px;
  }
  .harmCount {
    text-align: center;
  }
  .labelText {
    /* font-size: 12px; */
    color: #c0c4cc;
    display: block;
    margin-bottom: 10px;
  }
  .formSubTitle {
    padding: 10px 0;
    font-size: 13pt;
  }
  .harmCardBody {
    margin: 5px;
  }
  .chartsCardHeader {
    text-align: center;
    /* font-size: 16px; */
    width: 100%;
    display: inline-block;
  }
  .chartsBody {
    text-align: center;
    width: 100%;
    height: 120px;
  }
  .chartsBody>div{
    margin: 0 auto !important;
  }
  .title {
    margin-bottom: 15px;
    font-weight: 700;
    border-left: 0.275rem solid #25aff3;
    padding: 3px 10px;
    font-size: 15px;
    color: #333;
  }
  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
    clear: both
  }
</style>