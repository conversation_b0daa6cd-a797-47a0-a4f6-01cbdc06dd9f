<template>
  <div class="yearAssessInfo">
    <page-header
      :title="`${new Date(
        year
      ).getFullYear()}年度用人单位落实职业防治责任自查表及风险评估报告`"
    ></page-header>

    <div class="result">
      <el-card v-for="(item, i) in resultCards" :key="i">
        <div class="sort infoItem">
          <img class="bgImg" :src="item.bgImg" />
          <div class="infoItemInner">
            <div class="text">
              {{ item.name }}
            </div>
            <div class="sortNum" :style="{ color: item.textColor }">{{ item.value }}</div>
          </div>
        </div>
      </el-card>
    </div>
    <el-divider></el-divider>
    <el-card>
      <div class="assessInfoMain">
        <div class="assessInfoTitle">
          <el-col :span="12">
            排查项目列表
            <el-radio-group
              @change="findAssessInfoByYear"
              :style="{ marginLeft: '15px' }"
              v-model="queryRadio"
              size="mini"
            >
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="不符合">不符合</el-radio-button>
              <!-- <el-radio-button label="自查报告">自查报告</el-radio-button> -->
              <el-radio-button label="合理缺项">合理缺项</el-radio-button>
            </el-radio-group>
          </el-col>
        </div>
        <div v-if="reportPreview">合理缺项</div>
        <div v-else>
          <el-collapse v-model="activeNames">
            <el-collapse-item v-for="(item, i) in info.formData" :key="i" :name="i">
              <template slot="title">
                {{ item._id }}
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="含有不合格项"
                  placement="top-start"
                >
                  <i
                    v-show="
                      item.items.filter((curItem) => curItem.selected === '不符合')
                        .length > 0
                    "
                    :style="{ color: 'rgb(245, 108, 108)', marginLeft: '6px' }"
                    class="el-icon-warning"
                  ></i>
                </el-tooltip>
              </template>
              <el-table stripe :border="true" :data="item.items">
                <el-table-column prop="name" label="类目名称"></el-table-column>
                <el-table-column prop="selected" label="是否合格">
                  <template slot-scope="scope">{{ scope.row.selected || "-" }}</template>
                </el-table-column>
                <el-table-column label="是否存在隐患">
                  <template slot="label">
                    <el-link :style="{ marginLeft: '10px' }">点击查看详情</el-link>
                  </template>
                  <template slot-scope="scope">
                    {{
                      !scope.row.selected
                        ? "-"
                        : scope.row.selected === "符合" ||
                          scope.row.selected === "基本符合"
                        ? "否"
                        : "是"
                    }}
                  </template>
                </el-table-column>

                <el-table-column label="整改意见">
                  <template slot-scope="scope">
                    {{ scope.row.inconformityText || "-" }}
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
          <div class="noData" v-show="!info._id">
            <img src="@/assets/nodata.png" />
            <p class="noDataText">该年份职业防治责任自查暂无不符合项</p>
          </div>
        </div>
      </div>
    </el-card>
    <el-backtop></el-backtop>
  </div>
</template>
<script>
import { findbyYear } from "@/api/serviceOrg";
import { getAllAssessmentInfo } from "@/api/adminorg";
import _ from "lodash";
export default {
  data() {
    return {
      exposeLevel: ["低", "中", "高", "-"],
      result: ["甲类", "乙类", "丙类", "-"],
      queryRadio: "",
      reportPreview: false,
      info: {},
      activeNames: [],
      resultCards: [],
      reportCards: [],
      EnterpriseID: "",
      year: new Date(this.$route.query.year).getFullYear(),
    };
  },
  async created() {
    // console.log(this.$route.query, 'yyyyyyyyyyyyyyyyyyyyyyyyyyyyyy')
    this.year = this.$route.query.year;
    // this.year = '2022';
    this.EnterpriseID = this.$route.query.EnterpriseID;
    //判断是否有参数传入  是否是
    if (this.$route.query && this.$route.query.params1 === "checkInconformity") {
      //点击更多  查看不符合项   checkInconformity
      this.queryRadio = "不符合";
      this.findAssessInfoByYear();
    } else if (
      this.$route.query &&
      this.$route.query.params1 === "reasonableDeficiencyCount"
    ) {
      //点击更多  查看不符合项   checkInconformity
      this.queryRadio = "合理缺项";
      this.findAssessInfoByYear();
    } else {
      this.findAssessInfoByYear();
    }
  },
  methods: {
    hasHarm(selected) {
      if (selected === "符合" || "基本符合") {
        return "否";
      } else {
        return "是";
      }
    },
    async findAssessInfoByYear() {
      let res = await findbyYear({
        year: this.year,
        EnterpriseID: this.EnterpriseID,
        selected: this.queryRadio,
      });
      this.reportPreview = false;
      if (res.status === 200) {
        this.info = res.data;
        if (JSON.stringify(res.data) == "{}") return;
        const obj = {
          year:this.year,
          EnterpriseID:this.EnterpriseID,
        }
        this.assessmentReport = {};
        await getAllAssessmentInfo(obj).then(async res=>{
          // console.log(res, '8888888888888888888888');
          if(res.status === 200 && res.data._id){
            this.assessmentReport = res.data;
          }
        });
        let resultCards = [
          { name: "类别", value: this.info.formData.length },
          { name: "自查项目", value: this.info.length },
          { name: "不符合项", value: this.info.unConformityCount || 0 },
          { name: "合理缺项", value: this.info.reasonableDeficiencyCount || 0 },
          { name: "得分", value: this.info.mark || 0 },
          { name: "职业健康管理状况", value: this.info.level ? this.info.level + '级' : this.info.level },
        ];
        let bgImg = "";
        let textColor = "";
        resultCards.forEach((item, i) => {
          // console.log(item,i%2)
          bgImg =
            i % 2 === 0
              ? require("../assets/bg1.jpg")
              : require("../assets/bg2.jpg");

          item.bgImg = bgImg;
          textColor = i % 2 === 0 ? "#1caf9a" : "#ff962a";
          item.textColor = textColor;
        });
        this.resultCards = resultCards;
        const open = [];
        for(let i = 0; i < this.info.formData.length; i++) {
          open.push(i);
        }
        this.activeNames = open;
      }
    }
  },
  async mounted() {
    // console.log(this.$route.query, 'yyyyyyyyyyyyyyyyyyyyyyyyyyyyyy')
    // const obj = this.$route.query;
    // obj.year = '2020';
    // await this.$store.dispatch("adminorg/getAssessReport", obj);
  },
  // computed: {
  //   assessmentReport() {
  //     let report = this.$store.getters.report;
  //     console.log(report, 'ttttttttttttttttttt');
  //     if (report && report.code) {
  //       switch (this.$store.getters.report.assessManageLevel) {
  //         case "A":
  //           this.$store.getters.report.assessManageLevel = 0;
  //           break;
  //         case "B":
  //           this.$store.getters.report.assessManageLevel = 1;
  //           break;
  //         case "C":
  //           this.$store.getters.report.assessManageLevel = 2;
  //           break;
  //       }
  //       return this.$store.getters.report;
  //     }
  //   },
  // },
};
</script>

<style scoped="scoped">
.noData {
  text-align: center;
  padding: 50px;
  margin-top: 30px;
}
.backBtn {
  text-align: center;
  margin-top: 10px;
}
.el-table {
  border: 1px dotted #53c9ff;
}
.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: aliceblue;
}
.el-card.is-always-shadow,
.el-card.is-hover-shadow:focus,
.el-card.is-hover-shadow:hover {
  margin-right: 10px;
}
.yearAssessInfo {
  padding: 16px;
}
.infoItem .text {
  color: #999;
  font-size: 14px;
  margin-bottom: 20px;
}
.result {
  display: flex;
  justify-content: space-around;
  margin-top: 1.25rem;
}
.infoItem {
  width: 240px;
  height: 88px;
  padding: 15px;
  position: relative;
}
.infoItemInner {
  position: absolute;
  left: 15px;
  top: 15px;
}
.bgImg {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 240px;
  object-fit: scale-down;
}
.assessInfoTitle {
  margin-bottom: 10px;
  color: #53c9ff;
  align-items: center;
  display: flex;
}
.sortNum {
  color: #1caf9a;
  font-size: 22px;
}
.chartsCardHeader {
  text-align: center;
  /* font-size: 16px; */
  width: 100%;
  display: inline-block;
}
.chartsBody {
  width: 100%;
  height: 120px;
}
</style>
