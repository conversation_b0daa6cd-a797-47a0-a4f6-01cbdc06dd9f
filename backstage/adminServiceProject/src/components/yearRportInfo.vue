<template>
  <div class="yearReportInfo">
    <el-card>
      <div slot="header" class="editAdminorgHeader">
        <div class="titles">{{new Date(year).getFullYear()}}年度用人单位职业病危害暴露情况调查和风险评估表</div>
      </div>
      <div class="table">
        <el-table :data="tableData" border size="small" :header-cell-style="{color:'#474747'}">
          <el-table-column align="center" label="序号" type="index"></el-table-column>
          <el-table-column align="center" label="车间" prop="workspace"></el-table-column>
          <el-table-column align="center" label="工种/岗位" prop="station"></el-table-column>
          <el-table-column align="center" label="定员" prop="touchEmployees"></el-table-column>
          <el-table-column align="center" label="职业病危害因素名称" prop="checkProject"></el-table-column>
          <el-table-column align="center" label="职业病危害因素检测结果">
            <el-table-column align="center" label="CTWA" prop="TWA">
              <template slot="header">
                <div>C<sub>TWA</sub></div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="STEL">
              <template slot="header">
                <div>C<sub>STE</sub></div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="MAC">
              <template slot="header">
                <div>C<sub>ME</sub></div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="divisionData">
              <template slot="header">
                <div>C<sub>PE</sub></div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="噪声等效声级" prop="equalLevel"></el-table-column>
            <el-table-column align="center" label="其他因素" prop="checkData"></el-table-column>
          </el-table-column>
          <el-table-column align="center" label="接触水平" prop="checkResult"></el-table-column>
          <el-table-column align="center" label="职业病危害因素性质" prop="type"></el-table-column>
        </el-table>
      </div>
      <el-row class="table" type="flex" justify="center">
        <el-col :span="12">
          <div style="font-size:16px;font-weight: 600; margin-bottom: 30px;text-align: center;">用人单位职业病危害风险等级判定表</div>
          <el-table :data="data" border size="small" :cell-class-name="tableCellClassName" :span-method="objectSpanMethod" :header-cell-style="{color:'#474747'}">
            <el-table-column align="center" label="职业病危害因素性质" prop="one"></el-table-column>
            <el-table-column align="center" label="接触水平" prop="two"></el-table-column>
            <el-table-column align="center" label="接触人数" prop="three"></el-table-column>
            <el-table-column align="center" label="风险等级" prop="four"></el-table-column>
            <el-table-column align="center" label="风险等级判定" prop="five"></el-table-column>
          </el-table>
        </el-col>
        <el-col :span="12">
          <div class="description">
              <span style="font-size:18px;font-weight: 600;">注：</span>
              <div style="display: inline-block; margin-left: 5px;">
                  <div class="line">①职业病危害因素名称和检测结果根据有效期内的用人单位职业病危害现状评价报告或定期检测与评价报告填写。</div>
                  <div class="line">②C<sub>TWA</sub>为时间加权平均接触浓度；C<sub>STE</sub>为短时间接触浓度；C<sub>ME</sub>为最高浓度；C<sub>PE</sub>为峰接触浓度。
                      当同一岗位或地点具有多个检测结果时，应填报最高值。
                  </div>
              </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { getRiskAssessmentInfo } from "@/api/serviceOrg";
import _ from "lodash";
export default {
  data() {
    return {
      exposeLevel: ["Ⅰ", "Ⅱ", "Ⅲ", "-"],
      result: [ "丙", "乙","甲", "-"],
      tableData: [],
      data: [],
      activeNames: [],
      EnterpriseID: "",
      jobHealthId: '',
      year: new Date(this.$route.query.year).getFullYear(),
      spanArr: [],   //存放车间记录的合并数
      position: 0,   //spanArr的索引
    };
  },
  async created() {
    // console.log(this.$route.query, 'yyyyyyyyyyyyyyyyyyyyyyyyyyyyyy')
    this.year = this.$route.query.year;
    // this.year = '2022';
    this.EnterpriseID = this.$route.query.EnterpriseID;
    this.jobHealthId = this.$route.query.jobHealthId;
    this.getRiskAssessmentInfo();
  },
  methods: {
    async getRiskAssessmentInfo() {
      let res = await getRiskAssessmentInfo({
        year: this.year,
        EnterpriseID: this.EnterpriseID,
        jobHealthId: this.jobHealthId,
      });
      this.tableData = res.data.data;
      for(let i = 0; i < 4; i++){
        const employees = i === 0 ? 'noSeriousHarmNoExceedEmployees' : (i === 1 ? 'noSeriousHarmExceedEmployees' : (i === 2 ? 'seriousHarmNoExceedEmployees' : 'seriousHarmExceedEmployees'));
        const exceedLevel = i === 0 ? 'noSeriousHarmNoExceedLevel' : (i === 1 ? 'noSeriousHarmExceedLevel' : (i === 2 ? 'seriousHarmNoExceedLevel' : 'seriousHarmExceedLevel'));
        if(res.data.assessExposeLevel==='低风险'){
          res.data.assessExposeLevel = 'Ⅰ'
        }
        if(res.data.assessExposeLevel==='中风险'){
          res.data.assessExposeLevel = 'Ⅱ'
        }
        if(res.data.assessExposeLevel==='高风险'){
          res.data.assessExposeLevel = 'Ⅲ'
        }
        const item = {
          one:i < 2 ?'一般' : '严重',
          two:(i === 0 || i === 2) ? '符合' : '不符合',
          three:(res.data[employees] + '') ? res.data[employees] : '',
          four:(res.data[exceedLevel] + '') ? res.data[exceedLevel] : '',
          five:(res.data.assessExposeLevel + '') ? res.data.assessExposeLevel : '',
        }
        this.data.push(item);
      }
      this.rowspan();
      // console.log(res, 'ssssssssssssssssssssss');
    },
    tableCellClassName({row, column, rowIndex, columnIndex}){//注意这里是解构
      //利用单元格的 className 的回调方法，给行列索引赋值
      row.index=rowIndex;
      column.index=columnIndex;
    },
    rowspan() {
      this.spanArr = [];
      this.position = 0;
      // console.log(this.data);
      this.data.forEach((item,index) => {
        if(index === 0){
          this.spanArr.push(1);
          this.position = 0;
      // console.log(this.spanArr,'this.spanArr111');
        }else{
          // 让下一行与上一行作比较，比较第一列的市
          if(this.data[index].one === this.data[index-1].one ){
            this.spanArr[this.position] += 1;
            this.spanArr.push(0);
          }else{
            this.spanArr.push(1);
            this.position = index;
          }
        }
      })
      // console.log(this.spanArr,'this.spanArr111');
    },
    //表格合并行
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {  
      // console.log(this.spanArr,'this.spanArr');
      // row是当前行，column是当前列，rowIndex是当前行的索引，columnIndex是当前列的索引
      if(columnIndex === 0){
        const _row = this.spanArr[rowIndex];  //合并行的行数，1代表独占一行，比1大代表合并若干行，0代表不显示
        const _col = _row>0 ? 1 : 0;   //行如果存在那么列就存在，行不存在那么列也就不存在
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      if(columnIndex === 4){
        const _row = 4;  //合并行的行数，1代表独占一行，比1大代表合并若干行，0代表不显示
        const _col = 1;   //行如果存在那么列就存在，行不存在那么列也就不存在
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
  },
  computed: {
  },
};
</script>

<style scoped="scoped">
.titles {
  padding-left: 20px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  border-left: 2px solid #dcdfe6;
  margin: 0 30px 0 20px;
  height: 50px;
  line-height: 50px;
}
.editAdminorgHeader {
  align-items: center;
  display: flex;
}
.line{
  /* height: 25px; */
  line-height: 25px;
}
.table{
  margin: 30px;
}
.noData {
  text-align: center;
  padding: 50px;
  margin-top: 30px;
}
.backBtn {
  text-align: center;
  margin-top: 10px;
}
.el-table {
  border: 1px dotted #53c9ff;
}
.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: aliceblue;
}
.el-card.is-always-shadow,
.el-card.is-hover-shadow:focus,
.el-card.is-hover-shadow:hover {
  margin-right: 10px;
}
.yearReportInfo {
  padding: 20px;
}
.infoItem .text {
  color: #999;
  font-size: 14px;
  margin-bottom: 20px;
}
.result {
  display: flex;
  justify-content: space-around;
  margin-top: 1.25rem;
}
.infoItem {
  width: 240px;
  height: 88px;
  padding: 15px;
  position: relative;
}
.infoItemInner {
  position: absolute;
  left: 15px;
  top: 15px;
}
.bgImg {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 240px;
  object-fit: scale-down;
}
.assessInfoTitle {
  margin-bottom: 10px;
  color: #53c9ff;
  align-items: center;
  display: flex;
}
.sortNum {
  color: #1caf9a;
  font-size: 22px;
}
.chartsCardHeader {
  text-align: center;
  /* font-size: 16px; */
  width: 100%;
  display: inline-block;
}
.chartsBody {
  width: 100%;
  height: 120px;
}
.description {
  display: flex;
  align-items: flex-start;
}
</style>
