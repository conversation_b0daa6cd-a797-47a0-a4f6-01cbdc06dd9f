<template>
  <div>
    <div class="clearfix header">
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane name="info1">
          <span slot="label">评估报告</span>
        </el-tab-pane>
        <el-tab-pane name="info2">
          <span slot="label">责任自查</span>
        </el-tab-pane>
        <el-tab-pane name="info3">
          <span slot="label">暴露风险等级</span>
        </el-tab-pane>
        <el-tab-pane name="info4">
          <span slot="label">分类</span>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="text item" ref="content" >
      <el-form class="applyForm info1" v-show="activeName==='info1'">
        <div class="reportCardBody">
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">企业信息</div>
            </el-col>
            <el-col :span="12">
              <span class="labelText">单位名称</span>

              {{ assessmentReport.cname }}
            </el-col>

            <el-col :span="12">
              <span class="labelText">统一社会信用代码（或统一社会信用代码）</span>
              {{ assessmentReport.code }}
            </el-col>
          </el-row>
          <p />
          <el-row>
            <el-col :span="12">
              <span class="labelText">单位注册地址</span>
              {{
                assessmentReport.districtRegAdd &&
                assessmentReport.districtRegAdd.join("-")
              }}
              {{ assessmentReport.regAdd }}
            </el-col>
            <el-col :span="12">
              <span class="labelText">工作场所地址</span>
              <p v-for="(item, i) in assessmentReport.workAddress" :key="i">
                {{ item }}
              </p>
            </el-col>
          </el-row>
          <p />
          <el-row>
            <el-col :span="12">
              <span class="labelText">单位规模</span>
              {{ assessmentReport.companyScale || "" }} 型
            </el-col>
            <el-col :span="12">
              <span class="labelText">行业分类</span>
              <p v-for="(item, i) in assessmentReport.industryCategory" :key="i">
                {{ item }}
              </p>
            </el-col>
          </el-row>
          <p />
          <el-row>
            <el-col :span="12">
              <span class="labelText">上属单位</span>
              {{ assessmentReport.parentCompany }}
            </el-col>
            <el-col :span="12">
              <span class="labelText">注册类型</span>
              {{ assessmentReport.regType || "" }}
            </el-col>
          </el-row>
          <p />
          <el-row>
            <el-col :span="12">
              <span class="labelText">法定代表人</span>

              {{ assessmentReport.corp }}
            </el-col>
            <el-col :span="12">
              <span class="labelText">联系电话</span>
              {{ assessmentReport.corpPhoneNumber }}
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">职业卫生管理</div>
            </el-col>
            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">职业卫生管理机构</span>
              {{ assessmentReport.hasRole ? "有" : "无" }}
            </el-col>
            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">专职</span>
              {{ assessmentReport.majorManegerCount }}
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">兼职</span>
              {{ assessmentReport.partTimeManegerCount }}
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">职业健康检查人数</div>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">上岗人数</span>
              <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                {{ assessmentReport.healthCheckCount.shangGang }}
              </span>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">在岗人数</span>
              <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                {{ assessmentReport.healthCheckCount.zaiGang }}
              </span>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 8, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">离岗人数</span>
              <span v-if="assessmentReport && assessmentReport.healthCheckCount">
                {{ assessmentReport.healthCheckCount.liGang }}
              </span>
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row v-if="assessmentReport.isHealthCheck">
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">职业卫生概况</div>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 6, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">劳动者总人数</span>
              {{ assessmentReport.allEmployeesCount }}
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 6, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">接触职业病危害总人数</span>
              {{ assessmentReport.contactHazardCount }}
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 6, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">职业病累计人数 - 目前在岗</span>
              {{ assessmentReport.ODzaigangCount }}
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 6, offset: 0 }"
              :lg="{ span: 6, offset: 0 }"
              :xl="{ span: 6, offset: 0 }"
            >
              <span class="labelText">职业病累计人数 - 历年累计</span>
              {{ assessmentReport.cumulativeTotalCount }}
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <div class="formSubTitle">
                职业病危害因素接触情况
                <span class="smallText">
                  危害因素分类：粉尘、化学物质、物理因素、生物因素、放射性物质
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <!-- <h4>职业病危害因素种类</h4> -->
              <!-- {{ assessmentReport.HazardFactors }} -->
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane
                  v-for="(item, i) in assessmentReport.HazardFactors"
                  :key="i"
                  :name="item.label"
                >
                  <div slot="label">
                    {{ item.label }}
                    <span
                      v-if="!item.harmFactors || item.harmFactors.length === 0"
                      style="color: #f56c6c; font-size: 12px"
                      >（无）</span
                    >
                  </div>
                  <el-table border :data="item.harmFactors">
                    <el-table-column
                      align="center"
                      v-for="(item2, i2) in harmFactorsFields"
                      :key="i2"
                      :label="item2.label"
                      :prop="item2.value"
                    >
                      <template slot-scope="scope">
                        {{ scope.row[item2.value] || 0 }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
              </el-tabs>
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">
                职业病危害接触水平
                <span class="smallText">
                  危害接触水平分为：一般职业病危害因素；严重职业病危害因素；
                </span>
              </div>
            </el-col>

            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 12, offset: 0 }"
              :lg="{ span: 12, offset: 0 }"
              :xl="{ span: 12, offset: 0 }"
            >
              <el-card
                class="harmCardBody"
                :body-style="{ padding: '0px' }"
                shadow="hover"
              >
                <div class="harmCardContent">
                  <div class="harmTitleBox">
                    <span class="">一般职业病危害因素 </span>
                  </div>

                  <div class="harmCountBox">
                    <el-col :span="12" class="harmCount">
                      <span class="labelText">超标人数</span>
                      <span style="color: #f56c6c">{{
                        assessmentReport.noSeriousHarmExceedEmployees || 0
                      }}</span>
                    </el-col>
                    <el-col :span="12" class="harmCount">
                      <span class="labelText">不超标人数</span>
                      <span>{{
                        assessmentReport.noSeriousHarmNoExceedEmployees || 0
                      }}</span>
                    </el-col>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col
              :xs="{ span: 24, offset: 0 }"
              :sm="{ span: 24, offset: 0 }"
              :md="{ span: 12, offset: 0 }"
              :lg="{ span: 12, offset: 0 }"
              :xl="{ span: 12, offset: 0 }"
            >
              <el-card
                class="harmCardBody"
                :body-style="{ padding: '0px' }"
                shadow="hover"
              >
                <div class="harmCardContent">
                  <div class="harmTitleBox">
                    <span class="">严重职业病危害因素 </span>
                  </div>

                  <div class="harmCountBox">
                    <el-col :span="12" class="harmCount">
                      <span class="labelText">超标人数</span>
                      <span style="color: #f56c6c">{{
                        assessmentReport.seriousHarmExceedEmployees || 0
                      }}</span>
                    </el-col>
                    <el-col :span="12" class="harmCount">
                      <span class="labelText">不超标人数</span>
                      <span>{{
                        assessmentReport.seriousHarmNoExceedEmployees || 0
                      }}</span>
                    </el-col>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 分隔线 -->
          <el-row>
            <el-col :span="24">
              <el-progress
                :text-inside="true"
                :stroke-width="2"
                :percentage="0"
              ></el-progress>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="formSubTitle">职业卫生自查风险评估</div>
            </el-col>

            <el-col :span="8">
              <span class="labelText">防治责任自查等级</span>
              {{ selfCheckLevel[assessmentReport.assessManageLevel] }}
            </el-col>

            <el-col :span="8">
              <span class="labelText">暴露风险等级</span>
              {{ exposeLevel[assessmentReport.assessExposeLevel] }}
            </el-col>

            <el-col :span="8">
              <span class="labelText">综合风险等级</span>
              {{ result[assessmentReport.assessmentResult] }}
            </el-col>
          </el-row>
        </div>
      </el-form>
      <el-form class="applyForm info2" v-show="activeName==='info2'">
      </el-form>
      <el-form class="applyForm info3" v-show="activeName==='info3'">
      </el-form>
      <el-form class="applyForm info4" v-show="activeName==='info4'">
      </el-form>
    </div>
  </div>
</template>

<script>

export default {
  name: 'classify',
  props: {
  },
  data() {
    return {
      activeName: 'info1',
      selfCheckLevel: ["A", "B", "C"],
      exposeLevel: ["Ⅰ", "Ⅱ", "Ⅲ", "-"],
      result: [ "丙", "乙","甲", "-"],
    }
  },
  computed: {
    assessmentReport() {
      let report = this.$store.getters.report;
      // console.log('111111111111111111',report);
      if (report && report.code) {
        this.$store.getters.report.workAddress = this.$store.getters.report.workAddress.map(
          (item) => {
            if (item.districts) {
              item = item.districts.join("-") + item.address;
            }
            return item;
          }
        );
        switch (this.$store.getters.report.assessManageLevel) {
          case "A":
            this.$store.getters.report.assessManageLevel = 0;
            break;
          case "B":
            this.$store.getters.report.assessManageLevel = 1;
            break;
          case "C":
            this.$store.getters.report.assessManageLevel = 2;
            break;
        }
        return this.$store.getters.report;
      } else {
        if (report && report.data && report.data.message)
          this.$message.error(report.data.message);
        return { isReported: 3 }; //这里填3是为了报告状态tag的正确显示
      }
    },
  },
  methods: {
    // 点击tab
    handleClick(tab) {
      this.activeName = tab.name;
      // console.log(this.assessmentReport, 'ttttttttttttttttttt');
    },
  },
}
</script>

<style scoped>
.reportCardBody {
  margin-top: 0;
}
.formSubTitle {
  padding: 10px 0;
  font-size: 13pt;
}
.smallText {
  font-size: 12px;
  color: #c0c4cc;
}
.harmCardBody {
  margin: 5px;
}
.harmCardContent {
  position: relative;
  padding: 14px;
  height: 125px;
  overflow: hidden;
}
.harmTitleBox {
  text-align: center;
}
.harmCardTitle {
  font-size: 12px;
  font-weight: bold;
}
.harmCountBox {
  position: absolute;
  left: 0;
  bottom: 15px;
  width: 100%;
  font-size: 24px;
}
.harmCount {
  text-align: center;
}
.el-progress {
  margin-top: 10px;
}
.labelText {
  font-size: 12px;
  color: #606266;
  display: block;
  margin-bottom: 10px;
}
</style>
