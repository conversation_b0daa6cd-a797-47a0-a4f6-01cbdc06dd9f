<template>
  <div class="physicFactors">
    <el-form>
      <el-table :data="datas" style="width: 100%" border size="small">
        <el-table-column align="center" min-width="100px" prop="workType" fixed label="工种">
        </el-table-column>
        <el-table-column min-width="100px" align="center" label="检测岗位" prop="checkAddress">
        </el-table-column>
        <el-table-column align="center" min-width="150px" prop="harmFactors" label="职业病危害因素名称">
        </el-table-column>
        <el-table-column align="center" min-width="120px" prop="radiationHZ" label="辐射频率（MHz）">
        </el-table-column>
        <el-table-column align="center" min-width="160px" prop="electricIntensityData" label="电场强度测量值（V/m）">
        </el-table-column>
        <el-table-column align="center" min-width="160px" prop="magneticIntensityData" label="磁场强度测量值（A/m）">
        </el-table-column>
        <el-table-column align="center" min-width="120px" prop="electricIntensity" label="电场强度（V/m）">
        </el-table-column>
        <el-table-column align="center" min-width="120px" prop="magneticIntensity" label="磁场强度（A/m）">
        </el-table-column>
        <el-table-column prop="checkResult" label="判定结果" align="center">
          <template slot-scope="props">
            <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
          <template slot-scope="props">
            {{doMomentmonthD(props.row.reportTime)}}
          </template>
        </el-table-column> -->
      </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
      showEdit: {},
    };
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created() {
  },
  methods: {
  },
};
</script>