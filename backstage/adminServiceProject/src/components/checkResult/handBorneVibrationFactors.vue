<template>
  <div class="physicFactors">
    <el-form>
      <el-table :data="datas" style="width: 100%" border size="small">
        <el-table-column align="center" min-width="80px" prop="workType" label="工种">
        </el-table-column>
        <el-table-column align="center" label="检测点位置" prop="checkAddress" min-width="100px">
          <template slot-scope="props">
            {{props.row.checkAddress || (props.row.workspace + ' - ' + props.row.station)}}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="ahw" label="频率计权振动加速度测量值ahw（m/s^2）" min-width="170px">
        </el-table-column>
        <el-table-column prop="dayTouchTime" label="日接触时间（h）" align="center" min-width="120px">
        </el-table-column>
        <el-table-column prop="fourHoursAccelerated" label="4h等能量频率计权振动加速度值（m/s^2）" align="center" min-width="170px">
        </el-table-column>
        <el-table-column prop="touchLimit" label="接触限值（m/s^2）" align="center" min-width="130px">
        </el-table-column>
        <el-table-column prop="checkResult" label="判定结果" align="center">
          <template slot-scope="props">
            <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
          <template slot-scope="props">
            {{doMomentmonthD(props.row.reportTime)}}
          </template>
        </el-table-column> -->
      </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
      showEdit: {},
    };
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created() {
  },
  methods: {
  },
};
</script>
