<template>
  <div v-if="this.$store.getters.remind.isOnlineDeclaration !== '未申报'">
    <el-form>
     <el-table
      :data="tableData"
      style="width: 100%" border>
      <el-table-column
        prop="supervise"
        label="申报监管部门" align="center">
      </el-table-column>
      <el-table-column
        prop="url"
        label="申报表及回执" align="center">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" content="点击查看" placement="top">
            <el-button type="text" @click="show(scope.row.url)">{{scope.row.fileName}}</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="year"
        label="申报年份" align="center">
        <template slot-scope="props"> 
          <span>{{doMomentyear(props.row.year)}}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="monthD"
        label="具体申报时间" align="center">
        <template slot-scope="props"> 
          <span>{{doMomentmonthD(props.row.monthD)}}</span>
        </template>
      </el-table-column>
    </el-table>
    </el-form>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>

<script>
import { findApplyForm} from "@/api/adminorg";
import moment from 'moment';
export default {
  props: {
    id: String,
  },
  data(){
    return{
      nowyear: 0,
      dialogImageUrl: "",
      dialogVisible: false,
      filePath: '',
      companyCode:'',
      tableData:[],
      editItem: {},
      showEdit: {},
    }
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentyear(nowTime){
      return function(nowTime){
        return moment(nowTime).format('YYYY');
      }
    },
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created(){
    // 获取当前年份
    this.nowyear = new Date().getFullYear();
    this.findApplyForm()
  },
  methods:{
    // 点击查看按钮
    show(url) {
      const index = url.indexOf('.');
      const fileName = url.slice(index+1);
      const file = this.filePath + '/' + url;
      if(fileName !== 'pdf'){
        this.dialogImageUrl = file;
        this.dialogVisible = true;
      }else{
        let a = document.createElement("a");
        a.href = file;
        a.target = "_blank";
        a.click();
      }
    },
    async findApplyForm(){
        const nowYear = this.nowyear + '';
        let res = await findApplyForm({EnterpriseID: this.id,year:nowYear});
        if (res.status === 200) {
          res.message = res.message === '已完成' ? '已申报' : (res.message === '未完成' ? '未申报' : res.message)
          this.$store.dispatch("adminorg/remind", {
            isOnlineDeclaration: res.message,
            monthD: res.monthD,
            isjobHealth: this.$store.getters.remind.isjobHealth,
            reportTimes: this.$store.getters.remind.reportTimes,
            isratioArr: this.$store.getters.remind.isratioArr,
            checkDate: this.$store.getters.remind.checkDate,
            isAssess: this.$store.getters.remind.isAssess,
            isReported: this.$store.getters.remind.isReported,
          });
          res.data.forEach(item=>{
              this.filePath = res.filePath + '/' + item.EnterpriseID;
              if(item.tableFiles.length === 0){
                const obj = {
                  EnterpriseID: item.EnterpriseID,
                  supervise: item.supervise,
                  year: item.year,
                  monthD: item.monthD,
                  fileName: '',
                  url: '',
                }
                this.tableData.push(obj);
              }else{
                item.tableFiles.forEach(item2=>{
                  const obj = {
                    EnterpriseID: item.EnterpriseID,
                    supervise: item.supervise,
                    year: item.year,
                    monthD: item.monthD,
                    fileName: item2.fileName,
                    url: item2.url,
                  }
                  this.tableData.push(obj);
                });
              }
          });
        }
    },
  }
}
</script>
