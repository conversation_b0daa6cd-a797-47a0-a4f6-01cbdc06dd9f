<template>
  <div class="physicFactors">
    <el-form>
      <el-table :data="datas" style="width: 100%" border size="small">
        <el-table-column align="center" min-width="100px" prop="workType" label="工种">
        </el-table-column>
        <el-table-column align="center" label="检测地点（岗位）" prop="checkAddress" min-width="120px">
          <template slot-scope="props">
            {{props.row.checkAddress || (props.row.workspace + ' - ' + props.row.station)}}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="irradiance" label="有效辐照度（μW/cm^2）" min-width="150px">
        </el-table-column>
        <el-table-column align="center" prop="eightHoursTouchLimit" label="8h职业接触限值（μW/cm^2）" min-width="160px">
        </el-table-column>
        <el-table-column prop="checkResult" label="判定结果" align="center" min-width="70px">
          <template slot-scope="props">
            <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
          <template slot-scope="props">
            {{doMomentmonthD(props.row.reportTime)}}
          </template>
        </el-table-column> -->
      </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
      showEdit: {},
    };
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created() {
  },
  methods: {
  },
};
</script>
