<template>
  <div class="physicFactors">
    <el-form>
      <el-table :data="datas" style="width: 100%" border size="small">
        <el-table-column align="center" min-width="100px" prop="workType" label="工种">
        </el-table-column>
        <el-table-column min-width="150px" align="center" label="检测点位置" prop="checkAddress">
        </el-table-column>
        <el-table-column align="center" min-width="150px" prop="checkProject" label="检测项目">
        </el-table-column>
        <el-table-column label="结果" align="center">
          <el-table-column min-width="150px" label="MAC(孢子数/m³)" prop="MAC" align="center">
          </el-table-column>
          <el-table-column min-width="150px" label="TWA(ng/m³)" prop="TWA" align="center">
          </el-table-column>
          <el-table-column min-width="150px" label="STEL(ng/m³)" prop="STEL" align="center">
          </el-table-column>
        </el-table-column>
        <el-table-column prop="checkResult" label="判定结果" align="center">
          <template slot-scope="props">
            <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
          <template slot-scope="props">
            {{doMomentmonthD(props.row.reportTime)}}
          </template>
        </el-table-column> -->
      </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
      showEdit: {},
    };
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created() {
  },
  methods: {
  },
};
</script>
