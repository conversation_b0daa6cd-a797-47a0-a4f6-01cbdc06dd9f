<template>
  <div>
    <el-form>
    <el-table :data="datas" style="width: 100%" border size="small" :span-method="spanMethod">
      <el-table-column width="200px" align="center" label="检测点位置" fixed="left">
        <el-table-column align="center" label="车间" prop="workspace">
        </el-table-column>
        <el-table-column align="center" label="岗位" prop="station">
        </el-table-column>
      </el-table-column>
      <el-table-column min-width="100px" align="center" prop="workType" label="工种" fixed="left">
      </el-table-column>

      <el-table-column label="工种检测结果" align="center" v-if="this.branch === 'xhl'">
        <el-table-column width="70px" label="8/40h等效声级dB(A)" prop="equalLevel_i" align="center">
        </el-table-column>
        <el-table-column width="78px" label="职业接触限值" prop="touchLimit_i" align="center">
        </el-table-column>
        <el-table-column width="78px" label="结果判定" prop="checkResult_i" align="center">
        </el-table-column>
      </el-table-column>
      <el-table-column v-if="branch === 'xhl'" width="78px" label="检测点位置" prop="checkAddress" align="center">
      </el-table-column>

      <el-table-column align="center" label="检测值[dB(A)]" prop="checkData">
      </el-table-column>
      <el-table-column align="center" label="接触时间" prop="touchTime">
      </el-table-column>
      <el-table-column align="center" label="8/40h等效声级检测数值[dB(A)]" prop="equalLevel">
      </el-table-column>
      <el-table-column align="center" label="职业接触限值dB(A)" prop="touchLimit">
      </el-table-column>
      <el-table-column prop="checkResult" label="判定结果" align="center">
          <template slot-scope="props">
            <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult}}</span>
          </template>
        </el-table-column>
      <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
        <template slot-scope="props">
          {{doMomentmonthD(props.row.reportTime)}}
        </template>
      </el-table-column> -->
    </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
    branch: String,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
    };
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  async created() {
  },
  watch: {
    datas: {
      handler: function (newVal, val) {
        if (newVal) {
          this.getSpanArr(newVal ? newVal : []);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.branch !== 'xhl') {
        return
      }
      let needSpanIndex = [ 0, 1, 2, 3, 4 , 5 ];

      if (column.label === '车间') {
        if (needSpanIndex.includes(columnIndex)) {
          const _row = this.workspaceArr[rowIndex];
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col,
          };
        }
      } else if (column.label === '岗位') {
        if (needSpanIndex.includes(columnIndex)) {
          const _row = this.stationArr[rowIndex];
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col,
          };
        }
      } else if (column.label === '工种') {
        if (needSpanIndex.includes(columnIndex)) {
          const _row = this.spanArr[rowIndex];
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col,
          };
        }
      } else if (column.label === '检测项目') {
        if (needSpanIndex.includes(columnIndex)) {
          const _row = this.checkProjectArr[rowIndex];
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col,
          };
        }
      } else {
        if (needSpanIndex.includes(columnIndex)) {
          const _row = this.spanArr[rowIndex];
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col,
          };
        }
      }
      
    },
    mergeInit() {
      this.spanArr = []; //合并工种
      this.pos = 0; //合并工种索引
      this.stationArr = []; //合并岗位
      this.stationIndex = 0; //合并岗位索引
      this.workspaceArr = []; //合并车间
      this.workspaceIndex = 0; //合并车间索引
      this.checkProjectArr = []; //合并危害因素
      this.checkProjectIndex = 0; //合并危害因素索引
    },
    // 判断合并行 data传的是上面的数组
    getSpanArr(data) {
      this.mergeInit()
      for (let i = 0; i < data.length; i++) {
        if (i == 0) {
          this.spanArr.push(1);
          this.pos = 0;

          this.stationArr.push(1);
          this.stationIndex = 0;

          this.workspaceArr.push(1);
          this.workspaceIndex = 0;

          this.checkProjectArr.push(1);
          this.checkProjectIndex = 0;
        } else {
          if (data[i].workTypeId == data[i - 1].workTypeId) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }

          if (data[i].station == data[i - 1].station) {
            this.stationArr[this.stationIndex] += 1;
            this.stationArr.push(0);
          } else {
            this.stationArr.push(1);
            this.stationIndex = i;
          }

          if (data[i].workspace == data[i - 1].workspace) {
            this.workspaceArr[this.workspaceIndex] += 1;
            this.workspaceArr.push(0);
          } else {
            this.workspaceArr.push(1);
            this.workspaceIndex = i;
          }

          if (data[i].checkProject == data[i - 1].checkProject) {
            this.checkProjectArr[this.checkProjectIndex] += 1;
            this.checkProjectArr.push(0);
          } else {
            this.checkProjectArr.push(1);
            this.checkProjectIndex = i;
          }
        }
      }
    },
  },
};
</script>

<style>
</style>