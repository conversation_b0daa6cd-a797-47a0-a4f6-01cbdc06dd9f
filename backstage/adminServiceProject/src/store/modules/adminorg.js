import * as types from '../types.js';
import {
  adminorgList,
  getMainData,
  getCheckStatisticsApi,
  getFilesCompleteness,
} from '@/api/adminorg';
import {
  getAllAssessmentInfo,
} from '@/api/adminorg';

const state = {
  serviceAreaOptions: [{// 技术服务领域
    label: '采矿业',
    value: '1',
  }, {
    label: '化工、石化及医药',
    value: '2',
  }, {
    label: '冶金、建材',
    value: '3',
  }, {
    label: '机械制造、电力、纺织、建筑和交通运输等行业领域',
    value: '4',
  }, {
    label: '核设施',
    value: '5',
  }, {
    label: '核技术应用',
    value: '6',
  }],
  filesCompleteness: null, // 档案完成率
  checkStatistics: {}, // 体检数据统计
  allPersion: 0, // 员工总人数
  // healthHeaders: [], // 职业卫生负责人
  riskLevel: '',
  levels: {},
  healthData: {}, // 健康监护
  personNum: 0, // 接触人数
  // 检测合格率
  factors: [
    [ 'product', '总点数', '超标点数' ],
    [ '粉尘', 0, 0, 0 ],
    [ '化学物质', 0, 0, 0 ],
    [ '物理因素', 0, 0, 0 ],
    [ '放射因素', 0, 0, 0 ],
    [ '其他因素', 0, 0, 0 ],
  ], // 危害检测
  remind: {
    isOnlineDeclaration: '',
    monthD: '',
    isjobHealth: '',
    reportTimes: '',
    isratioArr: '',
    checkDate: '',
    isReported: '',
    isAssess: '',
  },
  formState: {
    show: false,
    edit: false,
    approveOrgBackShow: false,
    formData: {
      // STOREPROPSSTR
    },
  },
  sortType: {
    order: '',
    prop: '',
  },
  pageInfos: {
    current: 1,
    pageSize: 10,
  },
  deletepageInfos: {
    current: 1,
    pageSize: 10,
  },
  list: {
    pageInfo: {
      comprehensiveLevels: 0,
      count: 0,
      searchkey: '',
      thisProvince: '',
      totalItems: 0,
    },
    docs: [],
    statisticsData: {},
  },
  searchKeys: {
    searchkey: '',
    district: '',
    industryCategory: '',
    OnlineDeclarationOption: '',
    jobHealthOption: '',
    ratioArrOption: '',
    exceedOption: '',
    odiseasedOption: '',
    suspectedOption: '',
    selfAssessmentOption: '',
    assessmentResultOption: '',
    comprehensiveLevelOption: '',
    riskSortLevelOption: '',
    levelOption: '',
    punishOption: '',
    forbidOption: '',
    superviseOption: '',
    isDeleteOption: '',
  },
  report: {},
};

const mutations = {
  [types.REPORT](state, params) {
    let tempModule = state.reportModule;
    tempModule = params;
    tempModule = params;
    state.report = tempModule;
  },
  // 档案
  setFilesCompleteness(state, filesCompleteness) {
    // console.log('22222222222222 mutations', filesCompleteness);
    state.filesCompleteness = filesCompleteness;
  },
  // 检测合格率
  getCheckStatistics(state, checkStatistics) {
    state.checkStatistics = checkStatistics;
    window.localStorage.checkStatistics = JSON.stringify(checkStatistics);
  },
  [types.GET_MAIN_DATA](state, payload) {
    Object.keys(payload).forEach(item => {
      if (item === 'checkStatistics') {
        // console.log('if2222222222222');
        window.localStorage.checkStatistics = JSON.stringify(payload[item]);
      }
      state[item] = payload[item];
    });
  },
  [types.GET_HARM_CHECK_DATA](state, payload) {
    state.factors = payload;
  },
  [types.ADMINORG_FORMSTATE](state, formState) {
    state.formState.show = formState.show;
    state.formState.edit = formState.edit;
    state.formState.type = formState.type;
    state.formState.approveOrgBackShow = formState.approveOrgBackShow;
    state.formState.formData = Object.assign({
    }, formState.formData);
  },
  [types.ADMINORG_LIST](state, list) {
    state.list = list;
  },
  [types.DELETElistPageInfo](state, deletepageInfos) {
    state.deletepageInfos.current = deletepageInfos.current;
    state.deletepageInfos.pageSize = deletepageInfos.pageSize;
  },
  [types.listPageInfo](state, pageInfos) {
    state.pageInfos.current = pageInfos.current;
    state.pageInfos.pageSize = pageInfos.pageSize;
  },
  [types.SORT_TYPE](state, sortType) {
    state.sortType.order = sortType.order;
    state.sortType.prop = sortType.prop;
  },
  [types.remind](state, remind) {
    // console.log(remind, 'ddddddddddddddddddddddddddddddddd');
    state.remind.isOnlineDeclaration = remind.isOnlineDeclaration;
    state.remind.monthD = remind.monthD;
    state.remind.isjobHealth = remind.isjobHealth;
    state.remind.reportTimes = remind.reportTimes;
    state.remind.isratioArr = remind.isratioArr;
    state.remind.checkDate = remind.checkDate;
    state.remind.isReported = remind.isReported;
    state.remind.isAssess = remind.isAssess;
  },
  [types.SETSEARCHKEYS](state, data) {
    state.searchKeys.searchkey = data.searchkey;
    state.searchKeys.district = data.district;
    state.searchKeys.industryCategory = data.industryCategory;
    state.searchKeys.OnlineDeclarationOption = data.OnlineDeclarationOption;
    state.searchKeys.jobHealthOption = data.jobHealthOption;
    state.searchKeys.ratioArrOption = data.ratioArrOption;
    state.searchKeys.exceedOption = data.exceedOption;
    state.searchKeys.odiseasedOption = data.odiseasedOption;
    state.searchKeys.suspectedOption = data.suspectedOption;
    state.searchKeys.selfAssessmentOption = data.selfAssessmentOption;
    state.searchKeys.assessmentResultOption = data.assessmentResultOption;
    state.searchKeys.comprehensiveLevelOption = data.comprehensiveLevelOption;
    state.searchKeys.riskSortLevelOption = data.riskSortLevelOption;
    state.searchKeys.levelOption = data.levelOption;
    state.searchKeys.punishOption = data.punishOption;
    state.searchKeys.forbidOption = data.forbidOption;
    state.searchKeys.superviseOption = data.superviseOption;
    state.searchKeys.isDeleteOption = data.isDeleteOption;
  },
};

const actions = {
  async getAssessReport({
    commit,
  }, params = {}) {
    // console.log(params, 'ssssssssssssssssssssssssssss');
    const reportRes = await getAllAssessmentInfo(params);
    if (reportRes && reportRes.data && reportRes.status === 200) {
      await commit(types.REPORT, reportRes.data);
    } else {
      await commit(types.REPORT, reportRes);
    }
  },
  // 总参数
  async getMainData({ commit }, EnterpriseID) {
    const res = await getMainData({ EnterpriseID });
    // console.log(res, '所有数据111111111111111');
    const filesCompleteness = await getFilesCompleteness({ EnterpriseID });
    await commit('setFilesCompleteness', filesCompleteness.data);
    await commit(types.GET_MAIN_DATA, res.data);
    const res2 = await getCheckStatisticsApi({ EnterpriseID });
    await commit(types.GET_MAIN_DATA, res2.data);
  },
  // 检测合格率
  async getHarmCheckData({ commit }) {
    const res = [
      [ 'product', '总点数', '超标点数' ],
      [ '粉尘', parseInt(Math.random() * 10) + 2, 2 ],
      [ '化学物质', parseInt(Math.random() * 10) + 1, 1 ],
      [ '物理因素', parseInt(Math.random() * 10) + 1, 1 ],
      [ '放射因素', 0, 0 ],
      [ '其他因素', 0, 0 ],
    ];
    // console.log(res, '到底是啥玩意');
    await commit(types.GET_HARM_CHECK_DATA, res);
  },
  async showAdminorgForm({
    commit,
  }, params = {
    EnterpriseID: '',
    edit: false,
    approveOrgBackShow: false,
    formData: {},
  }) {
    commit(types.SETENTERPRISEID, params.EnterpriseID);
    commit(types.ADMINORG_FORMSTATE, {
      show: params.show,
      edit: params.edit,
      approveOrgBackShow: params.approveOrgBackShow,
      formData: params.formData,
    });
  },

  hideAdminorgForm: ({
    commit,
  }) => {
    commit(types.ADMINORG_FORMSTATE, {
      show: false,
      approveOrgBackShow: false,
    });
  },

  async getAdminorgList({
    commit,
  }, params = {}) {
    const result = await adminorgList(params);
    console.log('列表返回', result);
    commit(types.ADMINORG_LIST, result.data);
  },

  setSearchKeys: ({
    commit,
  }, searchKeys) => {
    commit(types.SETSEARCHKEYS, searchKeys);
  },
  deletelistPageInfo: ({
    commit,
  }, deletepageInfos) => {
    commit(types.DELETElistPageInfo, deletepageInfos);
  },
  listPageInfo: ({
    commit,
  }, pageInfos) => {
    commit(types.listPageInfo, pageInfos);
  },
  sortTypeChange: ({
    commit,
  }, sortType) => {
    commit(types.SORT_TYPE, sortType);
  },
  remind: ({
    commit,
  }, remind) => {
    console.log(remind, '66666666666');
    commit(types.remind, remind);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
