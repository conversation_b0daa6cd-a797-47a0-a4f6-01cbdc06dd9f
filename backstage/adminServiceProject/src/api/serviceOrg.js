import request from '@root/publicMethods/request';

export function getRiskAssessmentInfo(params) {
  return request({
    url: '/manage/adminServiceProject/getRiskAssessmentInfo',
    params,
    method: 'get',
  });
}
export function findbyYear(data) {
  return request({
    url: '/manage/adminorgGov/findbyYear',
    data,
    method: 'post',
  });
}
export function getAllAssessmentInfo(data) {
  return request({
    url: '/manage/adminorgGov/getAllAssessmentInfo',
    data,
    method: 'post',
  });
}
// 根据机构id获取用人单位（企业）列表
export function getList(data) {
  return request({
    url: '/manage/adminServiceProject/getList',
    data,
    method: 'post',
  });
}
// 根绝企筛选条件获取项目列表
export function getProjectList(data) {
  return request({
    url: '/manage/adminServiceProject/getProjects',
    data,
    method: 'post',
  });
}

// 获取管辖区获取机构列表
export function serviceOrgs(data) {
  return request({
    url: '/manage/adminServiceOrg/getAllOrg',
    data,
    method: 'post',
  });
}

// 获取所有可选行业
export function getIndustry(data) {
  return request({
    url: '/manage/adminServiceProject/getIndustry',
    data,
    method: 'get',
  });
}

export function getQualifies(data) {
  return request({
    url: '/manage/adminServiceOrg/getQualifies',
    data,
    method: 'post',
  });
}


export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}
// 获取项目检测结果
export function getCheckAssessment(params) {
  return request({
    url: '/manage/adminServiceProject/getCheckAssessment',
    params,
    method: 'get',
  });
}
// 查看手机号记录到日志中

export function checkPhoneLog(params) {
  return request({
    url: '/manage/superUser/checkPhoneLog',
    params,
    method: 'get',
  });
}
