import Vue from 'vue';
import VueI18n from 'vue-i18n';
import Cookies from 'js-cookie';
import {
  baseEn,
  baseZhTW,
  baseZhCN,
} from '@root/publicMethods/baseLang';
import elementEnLocale from 'element-ui/lib/locale/lang/en'; // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'; // element-ui lang
import elementJaLocale from 'element-ui/lib/locale/lang/ja'; // element-ui lang
import enLocale from './en';
import zhLocale from './zh';
import jaLocale from './ja';

Vue.use(VueI18n);

const messages = {
  en: {
    ...baseEn,
    ...enLocale,
    ...elementEnLocale,
  },
  zh: {
    ...baseZhCN,
    ...zhLocale,
    ...elementZhLocale,
  },
  ja: {
    ...baseZhTW,
    ...jaLocale,
    ...elementJaLocale,
  },
};

const i18n = new VueI18n({
  // set locale
  // options: en or zh
  locale: Cookies.get('language') || 'zh',
  // set locale messages
  messages,
});

export default i18n;
