module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const SensitiveDataApproval = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    // 敏感数据id
    dataId: {
      type: String,
      refPath: 'dataModel',
    },

    // 数据名称 劳动者姓名/企业名称
    dataName: String,

    // 数据模型
    dataModel: {
      type: String,
      required: true,
      enum: [ 'Employees', 'Adminorg' ],
    },

    // 当前审批状态
    status: {
      type: Number,
      enum: [ 0, 1 ], // 0 审批中 1 审批通过
      default: 0,
    },

    // 当前审批节点 团场-师市-兵团
    currentNode: {
      type: String,
      enum: [ '团场', '师市', '兵团' ],
    },

    // 审批过程
    process: [{
      _id: false,
      approver: { type: String, ref: 'SuperUser' }, // 审批人
      time: Date, // 审批时间
      result: { // 审批结果
        type: Number,
        enum: [ 0, 1 ], // 0 审批驳回 1 审批通过
      },
      comment: String, // 审批意见
      // 审批单位级别 省市区
      level: {
        type: String,
        enum: [ 'province', 'city', 'district' ],
      },
    }],

    // 上报单位id
    superUserId: {
      type: String,
      ref: 'SuperUser',
    },
    // 上报单位名称
    superUserName: String,

  }, {
    // 上报时间 即 创建时间
    timestamps: true,
  });

  return mongoose.model('SensitiveDataApproval', SensitiveDataApproval, 'sensitiveDataApproval');
};
